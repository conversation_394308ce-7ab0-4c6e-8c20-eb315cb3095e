#include "UI28_animation.h"
#if (UI28_ANIMATION_ENABLED == 1)

/* 动画剩余次数表,0xFF表示不停止 */
volatile unsigned char ui28_animation_Ao_list[UI28_ANIMATION_ITEM_NUM] =
    {
#define UI28_ANIMATION_DEBUG_X(Aname, COLOR_0_, COLOR_1_, Ax, Ay, Aw, Ah, Asd, Ad, As, Aa) 0,
        UI28_ANIMATION_ITEM_LIST
#undef UI28_ANIMATION_DEBUG_X
};

/* 动画当前播放帧 */
volatile unsigned char ui28_animation_current_playback_frame_list[UI28_ANIMATION_ITEM_NUM] =
    {
#define UI28_ANIMATION_DEBUG_X(Aname, COLOR_0_, COLOR_1_, Ax, Ay, Aw, Ah, Asd, Ad, As, Aa) 0,
        UI28_ANIMATION_ITEM_LIST
#undef UI28_ANIMATION_DEBUG_X
};

/* 动画延时缓存 */
volatile unsigned char ui28_animation_delayed_list[UI28_ANIMATION_ITEM_NUM] =
    {
#define UI28_ANIMATION_DEBUG_X(Aname, COLOR_0_, COLOR_1_, Ax, Ay, Aw, Ah, Asd, Ad, As, Aa) 0,
        UI28_ANIMATION_ITEM_LIST
#undef UI28_ANIMATION_DEBUG_X
};

/* 帧表 */
#define UI28_ANIMATION_FRAMES_DEBUG_X(Lname, ...) const unsigned char *ui28_animation_frames##Lname##_list[] = __VA_ARGS__;
UI28_ANIMATION_FRAMES_ITEM_LIST
#undef UI28_ANIMATION_FRAMES_DEBUG_X

/* 帧表总数 */
const unsigned char ui28_animation_frames_total_list[UI28_ANIMATION_FRAMES_ITEM_NUM] =
    {
#define UI28_ANIMATION_FRAMES_DEBUG_X(Lname, ...) (sizeof(ui28_animation_frames##Lname##_list) / sizeof(unsigned char *)),
        UI28_ANIMATION_FRAMES_ITEM_LIST
#undef UI28_ANIMATION_FRAMES_DEBUG_X
};

/* 动画背景颜色表 */
const unsigned short ui28_animation_COLOR_0_list[UI28_ANIMATION_ITEM_NUM] =
    {
#define UI28_ANIMATION_DEBUG_X(Aname, COLOR_0_, COLOR_1_, Ax, Ay, Aw, Ah, Asd, Ad, As, Aa) COLOR_0_,
        UI28_ANIMATION_ITEM_LIST
#undef UI28_ANIMATION_DEBUG_X
};

/* 动画文字颜色表 */
const unsigned short ui28_animation_COLOR_1_list[UI28_ANIMATION_ITEM_NUM] =
    {
#define UI28_ANIMATION_DEBUG_X(Aname, COLOR_0_, COLOR_1_, Ax, Ay, Aw, Ah, Asd, Ad, As, Aa) COLOR_1_,
        UI28_ANIMATION_ITEM_LIST
#undef UI28_ANIMATION_DEBUG_X
};

/* 动画X坐标表 */
const unsigned short ui28_animation_Ax_list[UI28_ANIMATION_ITEM_NUM] =
    {
#define UI28_ANIMATION_DEBUG_X(Aname, COLOR_0_, COLOR_1_, Ax, Ay, Aw, Ah, Asd, Ad, As, Aa) Ax,
        UI28_ANIMATION_ITEM_LIST
#undef UI28_ANIMATION_DEBUG_X
};

/* 动画Y坐标表 */
const unsigned short ui28_animation_Ay_list[UI28_ANIMATION_ITEM_NUM] =
    {
#define UI28_ANIMATION_DEBUG_X(Aname, COLOR_0_, COLOR_1_, Ax, Ay, Aw, Ah, Asd, Ad, As, Aa) Ay,
        UI28_ANIMATION_ITEM_LIST
#undef UI28_ANIMATION_DEBUG_X
};

/* 动画宽表 */
const unsigned short ui28_animation_Aw_list[UI28_ANIMATION_ITEM_NUM] =
    {
#define UI28_ANIMATION_DEBUG_X(Aname, COLOR_0_, COLOR_1_, Ax, Ay, Aw, Ah, Asd, Ad, As, Aa) Aw,
        UI28_ANIMATION_ITEM_LIST
#undef UI28_ANIMATION_DEBUG_X
};

/* 动画高表 */
const unsigned short ui28_animation_Ah_list[UI28_ANIMATION_ITEM_NUM] =
    {
#define UI28_ANIMATION_DEBUG_X(Aname, COLOR_0_, COLOR_1_, Ax, Ay, Aw, Ah, Asd, Ad, As, Aa) Ah,
        UI28_ANIMATION_ITEM_LIST
#undef UI28_ANIMATION_DEBUG_X
};

/* 动画延时表 */
const unsigned char ui28_animation_Asd_list[UI28_ANIMATION_ITEM_NUM] =
    {
#define UI28_ANIMATION_DEBUG_X(Aname, COLOR_0_, COLOR_1_, Ax, Ay, Aw, Ah, Asd, Ad, As, Aa) Asd,
        UI28_ANIMATION_ITEM_LIST
#undef UI28_ANIMATION_DEBUG_X
};

/* 动画运动方向表 */
const unsigned char ui28_animation_Ad_list[UI28_ANIMATION_ITEM_NUM] =
    {
#define UI28_ANIMATION_DEBUG_X(Aname, COLOR_0_, COLOR_1_, Ax, Ay, Aw, Ah, Asd, Ad, As, Aa) Ad,
        UI28_ANIMATION_ITEM_LIST
#undef UI28_ANIMATION_DEBUG_X
};

/* 动画每帧运动像素距离表 */
const unsigned char ui28_animation_As_list[UI28_ANIMATION_ITEM_NUM] =
    {
#define UI28_ANIMATION_DEBUG_X(Aname, COLOR_0_, COLOR_1_, Ax, Ay, Aw, Ah, Asd, Ad, As, Aa) As,
        UI28_ANIMATION_ITEM_LIST
#undef UI28_ANIMATION_DEBUG_X
};

/* 动画帧表 */
const unsigned char ui28_animation_Aa_list[UI28_ANIMATION_ITEM_NUM] =
    {
#define UI28_ANIMATION_DEBUG_X(Aname, COLOR_0_, COLOR_1_, Ax, Ay, Aw, Ah, Asd, Ad, As, Aa) Aa,
        UI28_ANIMATION_ITEM_LIST
#undef UI28_ANIMATION_DEBUG_X
};

/* 更新动画相册 */
void UI28_update_Animation(enum UI28_ANIMATION_e_ ANIMATION_)
{
    unsigned short usWidthWin = ui28_animation_Aw_list[ANIMATION_];
    unsigned short usHeightWin = ui28_animation_Ah_list[ANIMATION_];
    short usX = 0;
    short usY = 0;
    short usX_ = ui28_animation_Ax_list[ANIMATION_];
    short usY_ = ui28_animation_Ay_list[ANIMATION_];
    
    if (ui28_animation_Ao_list[ANIMATION_] == 0)
    {
        return;
    }

    if (ui28_animation_delayed_list[ANIMATION_] /* 动画延时缓存 */ >= ui28_animation_Asd_list[ANIMATION_])
    {
        ui28_animation_delayed_list[ANIMATION_] = 0;
    }
    else
    {
        ui28_animation_delayed_list[ANIMATION_]++;
    }

    if (ui28_animation_delayed_list[ANIMATION_]!=0)
    {
        return;
    }

    // 根据运动帧开辟缓存窗口
    if (ui28_animation_Ad_list[ANIMATION_] != ui28_a_mot /* 运动方向（不移动） */ && ui28_animation_As_list[ANIMATION_] != 0)
    {
        if (ui28_animation_Ad_list[ANIMATION_] <= ui28_a_below /* 运动方向（下） */)
        {
            usHeightWin += ui28_animation_As_list[ANIMATION_];
            if (ui28_animation_Ad_list[ANIMATION_] == ui28_a_below /* 运动方向（下） */)
            {
                usY_ += (ui28_animation_current_playback_frame_list[ANIMATION_] * ui28_animation_As_list[ANIMATION_]);
            }
            else
            {
                usY_ -= (ui28_animation_current_playback_frame_list[ANIMATION_] * ui28_animation_As_list[ANIMATION_]);
            }
        }
        else
        {
            /*对二维像素页缓存-开窗(自定义)（窗口内bit全为0）*/
            usWidthWin += ui28_animation_As_list[ANIMATION_];
            if (ui28_animation_Ad_list[ANIMATION_] == ui28_a_right /* 运动方向（右） */)
            {
                usX_ += (ui28_animation_current_playback_frame_list[ANIMATION_] * ui28_animation_As_list[ANIMATION_]);
            }
            else
            {
                usX_ -= (ui28_animation_current_playback_frame_list[ANIMATION_] * ui28_animation_As_list[ANIMATION_]);
            }
        }

        if (ui28_animation_Ad_list[ANIMATION_] == ui28_a_below /* 运动方向（下） */)
        {
            usY += ui28_animation_As_list[ANIMATION_];
        }
        else if (ui28_animation_Ad_list[ANIMATION_] == ui28_a_right /* 运动方向（右） */)
        {
            usX += ui28_animation_As_list[ANIMATION_];
        }

        /*对二维像素页缓存-开窗(自定义)（窗口内bit全为0）*/
        PixelPageCache2D_OpenWin_customize(usWidthWin, usHeightWin, 0);
    }

    // 刷图片
    /*对二维像素页缓存的某一窗口刷图*/
    switch (ui28_animation_Aa_list[ANIMATION_])
    {
#define UI28_ANIMATION_FRAMES_DEBUG_X(Lname, ...)                                                                                                                                                                                                                          \
    case ui28_##Lname##_frames:                                                                                                                                                                                                                                            \
        PixelPageCache2D_TwoColorChart_customize(usWidthWin, usHeightWin, usX, usY, ui28_animation_Aw_list[ANIMATION_], ui28_animation_Ah_list[ANIMATION_], (unsigned char *)ui28_animation_frames##Lname##_list[ui28_animation_current_playback_frame_list[ANIMATION_]]); \
        break;
        UI28_ANIMATION_FRAMES_ITEM_LIST
#undef UI28_ANIMATION_FRAMES_DEBUG_X
    default:
        break;
    }

    // 更新下一帧播放
    if (ui28_animation_current_playback_frame_list[ANIMATION_] >= (ui28_animation_frames_total_list[ANIMATION_] - 1))
    {
        ui28_animation_current_playback_frame_list[ANIMATION_] = 0;
        if (ui28_animation_Ao_list[ANIMATION_] != 0xFF)
        {

            ui28_animation_Ao_list[ANIMATION_]--;
        }
    }
    else
    {
        ui28_animation_current_playback_frame_list[ANIMATION_]++;
    }
    // 对ILI9340X显示器的某一窗口刷双色图
    ILI9340X_TwoColorChart(usX_, usY_, usWidthWin, usHeightWin, ui28_animation_COLOR_0_list[ANIMATION_], ui28_animation_COLOR_1_list[ANIMATION_], (unsigned char *)&PixelPageCache2D[0][0]);
}

/* 初始化动画 */
void ui28_Init_Animation(enum UI28_ANIMATION_e_ ANIMATION_)
{
    ui28_animation_current_playback_frame_list[ANIMATION_] = 0;
}

/* 设置动画次数 */
void ui28_Play_Count_Animation(enum UI28_ANIMATION_e_ ANIMATION_, unsigned char Count)
{
    ui28_animation_Ao_list[ANIMATION_] = Count;
}
#endif