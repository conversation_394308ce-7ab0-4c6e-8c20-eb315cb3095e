#include "bsp.h"
/* 温度探头类型表 */
const uint8_t temp_probe_c[TEMP_PROBE_ITEM_NUM] =
    {
#define TEMP_PROBE_DEBUG_X(name, class, pass, Win, weight) class,
        TEMP_PROBE_ITEM_LIST
#undef TEMP_PROBE_DEBUG_X
};
/* 温度探头通道表 */
const uint8_t temp_probe_pa[TEMP_PROBE_ITEM_NUM] =
    {
#define TEMP_PROBE_DEBUG_X(name, class, pass, Win, weight) pass,
        TEMP_PROBE_ITEM_LIST
#undef TEMP_PROBE_DEBUG_X
};
#if WINDOW_FILTERING_SWITCH
/* 温度探头窗口滤波个数表 */
#define TEMP_PROBE_DEBUG_X(name, class, pass, Win, weight) volatile int16_t Filter_BUFF_AVE##name[Win + 1]; // 数据缓冲区
TEMP_PROBE_ITEM_LIST
#undef TEMP_PROBE_DEBUG_X

volatile FilterObjectType Filter_Win_t[TEMP_PROBE_ITEM_NUM] =
    {
#define TEMP_PROBE_DEBUG_X(name, class, pass, Win, weight) {&Filter_BUFF_AVE##name[0], Win, 0},
        TEMP_PROBE_ITEM_LIST
#undef TEMP_PROBE_DEBUG_X
}; /* 窗口平滑滤波结构体 */

#endif

#if (REDUCE_WEIGHT_FILTERING_SWITCH == 1) /* 降权重滤波启用开关 */
/* 温度探头降权重滤波值表 */
const int16_t temp_probe_wei[TEMP_PROBE_ITEM_NUM] =
    {
#define TEMP_PROBE_DEBUG_X(name, class, pass, Win, weight) weight,
        TEMP_PROBE_ITEM_LIST
#undef TEMP_PROBE_DEBUG_X
};
#endif
/* 温度探头结构体 */
volatile temp_probe_t temp_p_t[TEMP_PROBE_ITEM_NUM] =
    {
#define TEMP_PROBE_DEBUG_X(name, class, pass, Win, weight) {0, 0, 0},
        TEMP_PROBE_ITEM_LIST
#undef TEMP_PROBE_DEBUG_X
};
/* 温度偏移表 */
const int16_t temperature_drift[TEMPERATURE_ITEM_NUM] =
    {
#define TEMPERATURE_DEBUG_X(name, tpid, desired, drift) drift,
        TEMPERATURE_ITEM_LIST
#undef TEMPERATURE_DEBUG_X
};
/* 温度结构体 */
volatile temperature_t temp_e_t[TEMPERATURE_ITEM_NUM] =
    {
#define TEMPERATURE_DEBUG_X(name, tpid, desired, drift) {(temp_probe_t *)&temp_p_t[tpid], desired, 0, 0, 0},
        TEMPERATURE_ITEM_LIST
#undef TEMPERATURE_DEBUG_X
};

volatile int16_t filtering_process_cache[TEMP_PROBE_ITEM_NUM] = {0}; /* 滤波进程缓存 */
#if (KALMAN_SWITCH == 1)                                             /* 卡尔曼滤波器开关 */
#if (KALMAN_SHARE_SWITCH == 1)
#define KALMAN_MAX_COUNT ((1 << KALMAN_NUMBER * TEMP_PROBE_ITEM_NUM)) // 卡尔曼最大计数
#define KALMAN_MAX_REMAINDER ((1 << (KALMAN_NUMBER)) - 1)             // 卡尔曼最大余数
volatile Kalman_InitDef Kalman_x = {0};                               /* 卡尔曼滤波器 */
#elif (KALMAN_SHARE_SWITCH == 0)
#define KALMAN_MAX_COUNT (1 << KALMAN_NUMBER)             // 卡尔曼最大计数
#define KALMAN_MAX_REMAINDER ((1 << (KALMAN_NUMBER)) - 1) // 卡尔曼最大余数
volatile Kalman_InitDef Kalman_x[TEMP_PROBE_ITEM_NUM] = {0}; /* 卡尔曼滤波器 */
#endif
#endif

/*
*********************************************************************************************************
*	函 数 名: bsp_TempProbeScan1ms
*	形    参: 无
*	返 回 值: 无
*	功能说明: 扫描所有探头，做滤波处理，被1ms周期性的调用
*********************************************************************************************************
*/
void bsp_TempProbeScan1ms(void)
{
#if (KALMAN_SWITCH == 1)             /* 卡尔曼滤波器开关 */
    static uint16_t KALMAN_STEP = 0; /* 卡尔曼计算步 */
#endif
#if (REDUCE_WEIGHT_FILTERING_SWITCH == 1) /* 降权重滤波启用开关 */
    static uint16_t WEIGHT_TIME = 0;      /* 降权重计时 */
#endif
    static uint16_t DISPLAY_TIME = 0; /* 显示温度计时 */
    uint8_t i;
/* 第1步：获取原始温度 */
#ifdef NTC_TEMP
    NTC_DRI;
#endif
#ifdef K_TEMP
    K_DRI;
#endif
#ifdef OTHER_TEMP
    OTHER_DRI;
#endif
    for (i = 0; i < TEMP_PROBE_ITEM_NUM; i++)
    {
        switch (temp_probe_c[i])
        {
#ifdef NTC_TEMP
        case NTC_TEMP /* NTC探头 */:
            NTC_ODT_TEMP(i);
            break;
#endif
#ifdef K_TEMP
        case K_TEMP /* K型热偶探头 */:
            K_ODT_TEMP(i);
            break;
#endif
#ifdef OTHER_TEMP
        case OTHER_TEMP /* 其他探头 */:
            OTHER_ODT_TEMP(i);
            break;
#endif
        default:
            break;
        }
    }
/* 第2步：卡尔曼滤波 */
#if (KALMAN_SWITCH == 1)       /* 卡尔曼滤波器开关 */
#if (KALMAN_SHARE_SWITCH == 1) /* 共用卡尔曼滤波器 */
    if ((KALMAN_STEP & KALMAN_MAX_REMAINDER) == 0)
    {                                                                                                                                               /* 每次新一轮卡尔曼滤波初始化 */
        Kaleman_Parameter_Init((Kalman_InitDef *)&Kalman_x, (float)temp_p_t[(KALMAN_STEP >> KALMAN_NUMBER)].True_temp, KALMAN_E_mea, KALMAN_E_est); /* 初始化卡尔曼结构体 */
    }
    else
    {
        Kalman_X_K_Calculation((Kalman_InitDef *)&Kalman_x, (float)temp_p_t[(KALMAN_STEP >> KALMAN_NUMBER)].True_temp); /* 卡尔曼估计值计算 */
        if ((KALMAN_STEP & KALMAN_MAX_REMAINDER) == KALMAN_MAX_REMAINDER)
        { /* 每一轮最后卡尔曼滤波完成 */
            filtering_process_cache[(KALMAN_STEP >> KALMAN_NUMBER)] = (int16_t)Kalman_x.X_k;
        }
    }
    if (++KALMAN_STEP == KALMAN_MAX_COUNT)
    {
        KALMAN_STEP = 0;
    }
#elif (KALMAN_SHARE_SWITCH == 0) /* 独立卡尔曼滤波器 */
    if (KALMAN_STEP == 0)
    { /* 每次新一轮卡尔曼滤波初始化 */
        for (i = 0; i < TEMP_PROBE_ITEM_NUM; i++)
        {
            Kaleman_Parameter_Init((Kalman_InitDef *)&Kalman_x[i], (float)temp_p_t[i].True_temp, KALMAN_E_mea, KALMAN_E_est); /* 初始化卡尔曼结构体 */
        }
    }
    else
    {
        for (i = 0; i < TEMP_PROBE_ITEM_NUM; i++)
        {
            Kalman_X_K_Calculation((Kalman_InitDef *)&Kalman_x[i], (float)temp_p_t[i].True_temp); /* 卡尔曼估计值计算 */
        }
        if (KALMAN_STEP == KALMAN_MAX_REMAINDER)
        { /* 每一轮最后卡尔曼滤波完成 */
            for (i = 0; i < TEMP_PROBE_ITEM_NUM; i++)
            {
                filtering_process_cache[i] = (int16_t)Kalman_x[i].X_k;
            }
        }
    }
    if (++KALMAN_STEP == KALMAN_MAX_COUNT)
    {
        KALMAN_STEP = 0;
    }
#endif
#endif

#if (KALMAN_SWITCH == 1) /* 卡尔曼滤波器开关 */
    if (KALMAN_STEP == 0)
    {
#endif
/* 第3步：窗口平滑滤波 */
#if (WINDOW_FILTERING_SWITCH == 1)
        for (i = 0; i < TEMP_PROBE_ITEM_NUM; i++)
        {
            if (Filter_Win_t[i].bufCount > 1 /* 滤波数量缓存必须大于1 */)
            {
#if (KALMAN_SWITCH == 1)                                                                                                        /* 卡尔曼滤波器开关 */
                filtering_process_cache[i] = SmoothingFilter((FilterObjectType *)&Filter_Win_t[i], filtering_process_cache[i]); /* 将转换后的数值进行处理 */
#else
                filtering_process_cache[i] = SmoothingFilter((FilterObjectType *)&Filter_Win_t[i], temp_p_t[i].True_temp); /* 将转换后的数值进行处理 */
#endif
            }
        }
#endif
/* 第4步：降低下一次权重滤波 */
#if (REDUCE_WEIGHT_FILTERING_SWITCH == 1) /* 降权重滤波启用开关 */
        if (++WEIGHT_TIME == REDUCE_WEIGHT_PERIOD)
        {
            for (i = 0; i < TEMP_PROBE_ITEM_NUM; i++)
            {
#if (KALMAN_SWITCH == 1 || WINDOW_FILTERING_SWITCH == 1) /* 卡尔曼滤波器开关 或者 窗口滤波启用开关 */
                if (temp_probe_wei[i] > 1 /* 降权值必须大于1 */)
                {
                    temp_p_t[i].Control_temp = filter1Step(temp_p_t[i].Control_temp, filtering_process_cache[i], temp_probe_wei[i]); /* 降权重更新一次温度 */
                }
                else
                {
                    temp_p_t[i].Control_temp = filtering_process_cache[i];
                }
#else
                if (temp_probe_wei[i] > 1 /* 降权值必须大于1 */)
                {
                    temp_p_t[i].Control_temp = filter1Step(temp_p_t[i].Control_temp, temp_p_t[i].True_temp, temp_probe_wei[i]); /* 降权重更新一次温度 */
                }
                else
                {
                    temp_p_t[i].Control_temp = temp_p_t[i].True_temp;
                }
#endif
            }

            /* 第5步：根据探头类型确认探头是否异常 */
            for (i = 0; i < TEMP_PROBE_ITEM_NUM; i++)
            {
                switch (temp_probe_c[i])
                {
#ifdef NTC_TEMP
                case NTC_TEMP /* NTC探头 */:
                    NTC_FAULT_CONDITIONS();
                    break;
#endif
#ifdef K_TEMP
                case K_TEMP /* K型热偶探头 */:
                    K_FAULT_CONDITIONS();
                    break;
#endif
#ifdef OTHER_TEMP
                case OTHER_TEMP /* 其他探头 */:
                    OTHER_FAULT_CONDITIONS();
                    break;
#endif
                default:
                    break;
                }
            }

            WEIGHT_TIME = 0;
        }
#else
    /* 第5步：根据探头类型确认探头是否异常 */
    for (i = 0; i < TEMP_PROBE_ITEM_NUM; i++)
    {
#if (KALMAN_SWITCH == 1 || WINDOW_FILTERING_SWITCH == 1) /* 卡尔曼滤波器开关 或者 窗口滤波启用开关 */
        temp_p_t[i].Control_temp = filtering_process_cache[i];
#else
        temp_p_t[i].Control_temp = temp_p_t[i].True_temp;
#endif
        switch (temp_probe_c[i])
        {
#ifdef NTC_TEMP
        case NTC_TEMP /* NTC探头 */:
            NTC_FAULT_CONDITIONS();
            break;
#endif
#ifdef K_TEMP
        case K_TEMP /* K型热偶探头 */:
            K_FAULT_CONDITIONS();
            break;
#endif
#ifdef OTHER_TEMP
        case OTHER_TEMP /* 其他探头 */:
            OTHER_FAULT_CONDITIONS();
            break;
#endif
        default:
            break;
        }
    }
#endif
#if (KALMAN_SWITCH == 1) /* 卡尔曼滤波器开关 */
    }
#endif
    /* 第6步：更新下一次显示温度，更新显示温度 */
    if (++DISPLAY_TIME == DISPLAY_REFRESH_CYCLE)
    {
        for (i = 0; i < TEMPERATURE_ITEM_NUM; i++)
        {
            if (temp_e_t[i].probe[0].abnormal == 0) /* 如果探头无异常 */
            {
                if (temp_e_t[i].Desired_temp == NULL)                                                                                                    /* 目标温度指针为空 */
                {                                                                                                                                        /* 普通滤波 */
                    temp_e_t[i].NextDisplaytemp = filter1Step(temp_e_t[i].NextDisplaytemp, temp_e_t[i].probe[0].Control_temp, DISPLAY_FILTERING_WEIGHT); /* 降权重更新一次显示温度 */
                }
                else
                {                                                                                                                                                            /* 磁吸滤波 */
                    if ((temp_e_t[i].Desired_temp[0] - temp_e_t[i].probe[0].Control_temp) > 100 || (temp_e_t[i].Desired_temp[0] - temp_e_t[i].probe[0].Control_temp) < -100) /* 如果使用温度与目标温度的绝对值大于10℃ */
                    {
                        temp_e_t[i].NextDisplaytemp = filter1Step(temp_e_t[i].NextDisplaytemp, temp_e_t[i].probe[0].Control_temp, DISPLAY_FILTERING_WEIGHT); /* 降权重更新一次显示温度 */
                    }
                    else if ((temp_e_t[i].Desired_temp[0] - temp_e_t[i].probe[0].Control_temp) < -30) /* 如果使用温度比目标温度大3℃ */
                    {
                        if (temp_e_t[i].NextDisplaytemp > temp_e_t[i].probe[0].Control_temp) /* 显示温度比使用温度大 */
                        {
                            temp_e_t[i].NextDisplaytemp = filter1Step(temp_e_t[i].NextDisplaytemp, temp_e_t[i].probe[0].Control_temp, DISPLAY_FILTERING_WEIGHT); /* 降权重更新一次显示温度 */
                        }
                        else
                        {
                            temp_e_t[i].NextDisplaytemp = filter1Step(temp_e_t[i].NextDisplaytemp, temp_e_t[i].probe[0].Control_temp - 30, DISPLAY_FILTERING_WEIGHT); /* 降权重更新一次显示温度 */
                        }
                    }
                    else if ((temp_e_t[i].Desired_temp[0] - temp_e_t[i].probe[0].Control_temp) > 30) /* 如果使用温度比目标温度小3℃ */
                    {
                        if (temp_e_t[i].NextDisplaytemp < temp_e_t[i].probe[0].Control_temp) // 显示温度比实际温度小
                        {
                            temp_e_t[i].NextDisplaytemp = filter1Step(temp_e_t[i].NextDisplaytemp, temp_e_t[i].probe[0].Control_temp, DISPLAY_FILTERING_WEIGHT); /* 降权重更新一次显示温度 */
                        }
                        else
                        {
                            temp_e_t[i].NextDisplaytemp = filter1Step(temp_e_t[i].NextDisplaytemp, temp_e_t[i].probe[0].Control_temp + 30, DISPLAY_FILTERING_WEIGHT); /* 降权重更新一次显示温度 */
                        }
                    }
                    else
                    {
                        temp_e_t[i].NextDisplaytemp = filter1Step(temp_e_t[i].NextDisplaytemp, temp_e_t[i].Desired_temp[0], 3); /* 降权重更新一次显示温度 */
                    }
                }

                if ((temp_e_t[i].NextDisplaytemp - temp_e_t[i].LastDisplaytemp) > 5 || (temp_e_t[i].NextDisplaytemp - temp_e_t[i].LastDisplaytemp) < -5)
                {
                    /* 下一次温度与上一次显示温度的差值大于0.5摄氏度 */
                    /* 更新上一次温度与显示温度 */
                    temp_e_t[i].LastDisplaytemp = temp_e_t[i].NextDisplaytemp;
                    temp_e_t[i].Displaytemp = temp_e_t[i].LastDisplaytemp + temperature_drift[i];
                }
            }
        }
        DISPLAY_TIME = 0;
    }
}
