#ifndef _UI28_ROLLING_ALBUM_
#define _UI28_ROLLING_ALBUM_
#include "UI28.h"
#if (UI28_ROLLING_ALBUM_ENABLED == 1)
#define UI28_ROLLING_ALBUM_TRANSPARENCY_ENABLED 0 /* 启用相册表透明度 开关 */
#define UI28_ROLLING_ALBUM_VARIABLE_COLOR_ENABLED 1 /* 启用相册表可变颜色 开关 */

/*创建图片表*/
#define UI28_ALBUM_PHOTO_ITEM_LIST                                                                                                                                                                                                                                                                             \
	UI28_ALBUM_PHOTO_DEBUG_X(1 /*名字*/, {&Pic_120x120[PIC_120_120_RUN_B0WL].dat[0], &Pic_120x120[PIC_120_120_SET_UP].dat[0], &Pic_120x120[PIC_120_120_DEBUG].dat[0], &Pic_120x120[PIC_120_120_QR_CODE].dat[0], &Pic_120x120[PIC_120_120_COUNT].dat[0]}) \
	UI28_ALBUM_PHOTO_DEBUG_X(2 /*名字*/, {&Font72_Ascii[0].dat[0], &Font72_Ascii[1].dat[0], &Font72_Ascii[2].dat[0], &Font72_Ascii[3].dat[0], &Font72_Ascii[4].dat[0], &Font72_Ascii[5].dat[0], &Font72_Ascii[6].dat[0], &Font72_Ascii[7].dat[0], &Font72_Ascii[8].dat[0], &Font72_Ascii[9].dat[0], &Font72_Ascii[10].dat[0]}) 
/* 图片表ID */
enum UI28_ALBUM_PHOTO_e_
{
#define UI28_ALBUM_PHOTO_DEBUG_X(Lname, ...) ui28_##Lname##_album_photo,
	UI28_ALBUM_PHOTO_ITEM_LIST
#undef UI28_ALBUM_PHOTO_DEBUG_X
		UI28_ALBUM_PHOTO_ITEM_NUM,
};

/*创建相册表*/
#define UI28_ALBUM_ITEM_LIST                                                                                                                                                                                                                                        \
	UI28_ALBUM_DEBUG_X(1 /*名字*/, BLACK /*背景颜色*/, WHITE /*文字颜色*/, 0 /* 相册X坐标 */, 40 /*相册Y坐标*/, 320 /*相册宽*/, 120 /*相册高*/, 120 /*图片长(滚动方向)*/, 60 /*图片间距(滚动方向)*/, 0 /*滚动方向0：左右 1：上下*/, ui28_1_album_photo /* 图片表 */)   \
	UI28_ALBUM_DEBUG_X(2 /*名字*/, BLACK /*背景颜色*/, WHITE /*文字颜色*/, 80 /* 相册X坐标 */, 12 /*相册Y坐标*/, 36 /*相册宽*/, 72 /*相册高*/, 72 /*图片长(滚动方向)*/, 0 /*图片间距(滚动方向)*/, 1 /*滚动方向0：左右 1：上下*/, ui28_2_album_photo /* 图片表 */) \
	UI28_ALBUM_DEBUG_X(3 /*名字*/, BLACK /*背景颜色*/, WHITE /*文字颜色*/,126 /* 相册X坐标 */, 12 /*相册Y坐标*/, 36 /*相册宽*/, 72 /*相册高*/, 72 /*图片长(滚动方向)*/, 0 /*图片间距(滚动方向)*/, 1 /*滚动方向0：左右 1：上下*/, ui28_2_album_photo /* 图片表 */) \
	UI28_ALBUM_DEBUG_X(4 /*名字*/, BLACK /*背景颜色*/, WHITE /*文字颜色*/, 80 /* 相册X坐标 */,157 /*相册Y坐标*/, 36 /*相册宽*/, 72 /*相册高*/, 72 /*图片长(滚动方向)*/, 0 /*图片间距(滚动方向)*/, 1 /*滚动方向0：左右 1：上下*/, ui28_2_album_photo /* 图片表 */) \
	UI28_ALBUM_DEBUG_X(5 /*名字*/, BLACK /*背景颜色*/, WHITE /*文字颜色*/,126 /* 相册X坐标 */,157 /*相册Y坐标*/, 36 /*相册宽*/, 72 /*相册高*/, 72 /*图片长(滚动方向)*/, 0 /*图片间距(滚动方向)*/, 1 /*滚动方向0：左右 1：上下*/, ui28_2_album_photo /* 图片表 */)
/* 相册ID */
enum UI28_ALBUM_e_
{
#define UI28_ALBUM_DEBUG_X(Lname, COLOR_0_, COLOR_1_, Lx, Ly, Lw, Lh, Ll, Ls, Ld, La) ui28_##Lname##_album,
	UI28_ALBUM_ITEM_LIST
#undef UI28_ALBUM_DEBUG_X
		UI28_ALBUM_ITEM_NUM,
};

// #if (UI28_ALBUM_ITEM_NUM > 64)
// #error "相册表不能超过64"
// #elif (UI28_ALBUM_ITEM_NUM > 32)
// typedef unsigned long long uint_AlbumDis_t;
// #elif (UI28_ALBUM_ITEM_NUM > 16)
typedef unsigned int uint_AlbumDis_t;
// #elif (UI28_ALBUM_ITEM_NUM > 8)
// typedef unsigned short uint_AlbumDis_t;
// #else
// typedef unsigned char uint_AlbumDis_t;
// #ends

extern volatile uint_AlbumDis_t ui28_Rolling_Album_display;					 /* 滚动相册显示标志位 */
extern volatile unsigned char ui28_album_photo_cur_val[UI28_ALBUM_ITEM_NUM]; /* 相册当前光标值 */
extern volatile char ALBUM_ANIMATION_OVERAMPLITUDE; /* 超幅程度值 */

/* 菜单显示开关（关实际只是不刷新了） */
void UI28_display_onoff_album(enum UI28_ALBUM_e_ ALBUM_, unsigned char _onoff_);
/* 设置光标位置 */
void UI28_change_cursor_value_album(enum UI28_ALBUM_e_ ALBUM_, unsigned char cur_val);
/* 获取光标位置 */
unsigned char UI28_obtain_cursor_value_album(enum UI28_ALBUM_e_ ALBUM_);
/* 单步调节（上下） */
unsigned char UI28_single_step_album(enum UI28_ALBUM_e_ ALBUM_, enum UI28_STEP_ step__);
#if (UI28_ROLLING_ALBUM_TRANSPARENCY_ENABLED == 1)
/* 更改透明度 */
void UI28_change_transparency_album(enum UI28_ALBUM_e_ ALBUM_, unsigned char alpha);
#endif
#if (UI28_ROLLING_ALBUM_VARIABLE_COLOR_ENABLED == 1)
/* 相册更改文字颜色 */
void UI28_change_color_album(enum UI28_ALBUM_e_ ALBUM_, unsigned short color);
#endif

/* 更新滚动相册 */
void UI28_update_Rolling_Album(enum UI28_ALBUM_e_ ALBUM_);
/* 根据目标图片更新当前 */
void Update_the_current_image_based_on_the_target_image_Album(enum UI28_ALBUM_e_ ALBUM_);
/* 初始化滚动相册 */
void ui28_Init_Rolling_Album(enum UI28_ALBUM_e_ ALBUM_);
/* 工程初始化滚动相册（在系统上电执行一次） */
void ui28_Init_Rolling_Album_Engineering(void);
#endif
#endif
