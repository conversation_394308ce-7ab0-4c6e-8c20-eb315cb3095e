<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [.\OBJ\AIR.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image .\OBJ\AIR.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 6160001: Last Updated: Sun Jun 22 16:27:46 2025
<BR><P>
<H3>Maximum Stack Usage =        256 bytes + Unknown(Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
page_check_update &rArr; UI28_draw_arc &rArr; UI28_Draw_a_table_arc &rArr; UI28_Draw_a_table_based_on_the_angle_arc
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[1c]">ADC1_2_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[1c]">ADC1_2_IRQHandler</a><BR>
 <LI><a href="#[4]">BusFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[4]">BusFault_Handler</a><BR>
 <LI><a href="#[2]">HardFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[2]">HardFault_Handler</a><BR>
 <LI><a href="#[3]">MemManage_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[3]">MemManage_Handler</a><BR>
 <LI><a href="#[5]">UsageFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[5]">UsageFault_Handler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[1c]">ADC1_2_IRQHandler</a> from startup_air32f10x.o(.text) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[39]">ADC3_IRQHandler</a> from startup_air32f10x.o(.text) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[4c]">BootAppReceiveDataProcessing</a> from bsp_boot.o(.text.BootAppReceiveDataProcessing) referenced 2 times from bsp_uart_fifo.o(.text.bsp_InitUart)
 <LI><a href="#[4]">BusFault_Handler</a> from air32f10x_it.o(.text.BusFault_Handler) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[63]">Business_EE_immediate_storage_and_storage_management</a> from pro_workflow.o(.text.Business_EE_immediate_storage_and_storage_management) referenced 2 times from parame.o(.rodata.parames_config_update)
 <LI><a href="#[1f]">CAN1_RX1_IRQHandler</a> from startup_air32f10x.o(.text) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[20]">CAN1_SCE_IRQHandler</a> from startup_air32f10x.o(.text) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[15]">DMA1_Channel1_IRQHandler</a> from startup_air32f10x.o(.text) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[16]">DMA1_Channel2_IRQHandler</a> from startup_air32f10x.o(.text) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[17]">DMA1_Channel3_IRQHandler</a> from startup_air32f10x.o(.text) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[18]">DMA1_Channel4_IRQHandler</a> from startup_air32f10x.o(.text) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[19]">DMA1_Channel5_IRQHandler</a> from startup_air32f10x.o(.text) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[1a]">DMA1_Channel6_IRQHandler</a> from startup_air32f10x.o(.text) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[1b]">DMA1_Channel7_IRQHandler</a> from startup_air32f10x.o(.text) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[42]">DMA2_Channel1_IRQHandler</a> from startup_air32f10x.o(.text) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[43]">DMA2_Channel2_IRQHandler</a> from startup_air32f10x.o(.text) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[44]">DMA2_Channel3_IRQHandler</a> from startup_air32f10x.o(.text) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[45]">DMA2_Channel4_5_IRQHandler</a> from startup_air32f10x.o(.text) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[7]">DebugMon_Handler</a> from air32f10x_it.o(.text.DebugMon_Handler) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[10]">EXTI0_IRQHandler</a> from startup_air32f10x.o(.text) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[32]">EXTI15_10_IRQHandler</a> from startup_air32f10x.o(.text) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[11]">EXTI1_IRQHandler</a> from startup_air32f10x.o(.text) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[12]">EXTI2_IRQHandler</a> from startup_air32f10x.o(.text) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[13]">EXTI3_IRQHandler</a> from startup_air32f10x.o(.text) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[14]">EXTI4_IRQHandler</a> from startup_air32f10x.o(.text) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[21]">EXTI9_5_IRQHandler</a> from startup_air32f10x.o(.text) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[e]">FLASH_IRQHandler</a> from startup_air32f10x.o(.text) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[3a]">FSMC_IRQHandler</a> from startup_air32f10x.o(.text) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[2]">HardFault_Handler</a> from air32f10x_it.o(.text.HardFault_Handler) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[2a]">I2C1_ER_IRQHandler</a> from startup_air32f10x.o(.text) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[29]">I2C1_EV_IRQHandler</a> from startup_air32f10x.o(.text) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[2c]">I2C2_ER_IRQHandler</a> from startup_air32f10x.o(.text) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[2b]">I2C2_EV_IRQHandler</a> from startup_air32f10x.o(.text) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[3]">MemManage_Handler</a> from air32f10x_it.o(.text.MemManage_Handler) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[1]">NMI_Handler</a> from air32f10x_it.o(.text.NMI_Handler) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[b]">PVD_IRQHandler</a> from startup_air32f10x.o(.text) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[8]">PendSV_Handler</a> from air32f10x_it.o(.text.PendSV_Handler) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[f]">RCC_IRQHandler</a> from startup_air32f10x.o(.text) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[47]">RNG_IRQHandler</a> from startup_air32f10x.o(.text) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[33]">RTCAlarm_IRQHandler</a> from startup_air32f10x.o(.text) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[d]">RTC_IRQHandler</a> from startup_air32f10x.o(.text) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[0]">Reset_Handler</a> from startup_air32f10x.o(.text) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[3b]">SDIO_IRQHandler</a> from startup_air32f10x.o(.text) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[48]">SENSOR_IRQHandler</a> from startup_air32f10x.o(.text) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[2d]">SPI1_IRQHandler</a> from startup_air32f10x.o(.text) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[2e]">SPI2_IRQHandler</a> from startup_air32f10x.o(.text) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[3d]">SPI3_IRQHandler</a> from startup_air32f10x.o(.text) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[6]">SVC_Handler</a> from air32f10x_it.o(.text.SVC_Handler) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[46]">SYMC_IRQHandler</a> from startup_air32f10x.o(.text) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[9]">SysTick_Handler</a> from bsp_timer.o(.text.SysTick_Handler) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[4a]">SystemInit</a> from system_air32f10x.o(.text.SystemInit) referenced from startup_air32f10x.o(.text)
 <LI><a href="#[c]">TAMPER_IRQHandler</a> from startup_air32f10x.o(.text) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[22]">TIM1_BRK_IRQHandler</a> from startup_air32f10x.o(.text) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[25]">TIM1_CC_IRQHandler</a> from startup_air32f10x.o(.text) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[24]">TIM1_TRG_COM_IRQHandler</a> from startup_air32f10x.o(.text) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[23]">TIM1_UP_IRQHandler</a> from startup_air32f10x.o(.text) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[26]">TIM2_IRQHandler</a> from startup_air32f10x.o(.text) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[27]">TIM3_IRQHandler</a> from startup_air32f10x.o(.text) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[28]">TIM4_IRQHandler</a> from startup_air32f10x.o(.text) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[3c]">TIM5_IRQHandler</a> from startup_air32f10x.o(.text) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[40]">TIM6_IRQHandler</a> from startup_air32f10x.o(.text) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[41]">TIM7_IRQHandler</a> from startup_air32f10x.o(.text) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[35]">TIM8_BRK_IRQHandler</a> from startup_air32f10x.o(.text) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[38]">TIM8_CC_IRQHandler</a> from startup_air32f10x.o(.text) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[37]">TIM8_TRG_COM_IRQHandler</a> from startup_air32f10x.o(.text) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[36]">TIM8_UP_IRQHandler</a> from startup_air32f10x.o(.text) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[3e]">UART4_IRQHandler</a> from startup_air32f10x.o(.text) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[3f]">UART5_IRQHandler</a> from startup_air32f10x.o(.text) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[2f]">USART1_IRQHandler</a> from bsp_uart_fifo.o(.text.USART1_IRQHandler) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[30]">USART2_IRQHandler</a> from startup_air32f10x.o(.text) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[31]">USART3_IRQHandler</a> from startup_air32f10x.o(.text) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[34]">USBWakeUp_IRQHandler</a> from startup_air32f10x.o(.text) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[1d]">USB_HP_CAN1_TX_IRQHandler</a> from startup_air32f10x.o(.text) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[1e]">USB_LP_CAN1_RX0_IRQHandler</a> from startup_air32f10x.o(.text) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[5]">UsageFault_Handler</a> from air32f10x_it.o(.text.UsageFault_Handler) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[a]">WWDG_IRQHandler</a> from startup_air32f10x.o(.text) referenced from startup_air32f10x.o(RESET)
 <LI><a href="#[4b]">__main</a> from entry.o(.ARM.Collect$$$$00000000) referenced from startup_air32f10x.o(.text)
 <LI><a href="#[4d]">_sputc</a> from printf6.o(i._sputc) referenced from printf6.o(i.__0sprintf$6)
 <LI><a href="#[49]">main</a> from main.o(.text.main) referenced from entry9a.o(.ARM.Collect$$$$0000000B)
 <LI><a href="#[60]">page_check_update</a> from page_check.o(.text.page_check_update) referenced from page.o(.rodata.pa_tVar)
 <LI><a href="#[5f]">page_check_view_load</a> from page_check.o(.text.page_check_view_load) referenced from page.o(.rodata.pa_tVar)
 <LI><a href="#[5a]">page_debug_update</a> from page_debug.o(.text.page_debug_update) referenced from page.o(.rodata.pa_tVar)
 <LI><a href="#[59]">page_debug_view_load</a> from page_debug.o(.text.page_debug_view_load) referenced from page.o(.rodata.pa_tVar)
 <LI><a href="#[62]">page_developer_update</a> from page_developer.o(.text.page_developer_update) referenced from page.o(.rodata.pa_tVar)
 <LI><a href="#[61]">page_developer_view_load</a> from page_developer.o(.text.page_developer_view_load) referenced from page.o(.rodata.pa_tVar)
 <LI><a href="#[58]">page_parameter_update</a> from page_parameter.o(.text.page_parameter_update) referenced from page.o(.rodata.pa_tVar)
 <LI><a href="#[57]">page_parameter_view_load</a> from page_parameter.o(.text.page_parameter_view_load) referenced from page.o(.rodata.pa_tVar)
 <LI><a href="#[5c]">page_qr_code_update</a> from page_qr_code.o(.text.page_qr_code_update) referenced from page.o(.rodata.pa_tVar)
 <LI><a href="#[5b]">page_qr_code_view_load</a> from page_qr_code.o(.text.page_qr_code_view_load) referenced from page.o(.rodata.pa_tVar)
 <LI><a href="#[54]">page_run_root_load</a> from page_run.o(.text.page_run_root_load) referenced from page.o(.rodata.pa_tVar)
 <LI><a href="#[56]">page_run_update</a> from page_run.o(.text.page_run_update) referenced from page.o(.rodata.pa_tVar)
 <LI><a href="#[55]">page_run_view_load</a> from page_run.o(.text.page_run_view_load) referenced from page.o(.rodata.pa_tVar)
 <LI><a href="#[51]">page_standby_root_load</a> from page_standby.o(.text.page_standby_root_load) referenced from page.o(.rodata.pa_tVar)
 <LI><a href="#[53]">page_standby_update</a> from page_standby.o(.text.page_standby_update) referenced from page.o(.rodata.pa_tVar)
 <LI><a href="#[52]">page_standby_view_load</a> from page_standby.o(.text.page_standby_view_load) referenced from page.o(.rodata.pa_tVar)
 <LI><a href="#[4e]">page_start_root_load</a> from page_start.o(.text.page_start_root_load) referenced from page.o(.rodata.pa_tVar)
 <LI><a href="#[50]">page_start_update</a> from page_start.o(.text.page_start_update) referenced from page.o(.rodata.pa_tVar)
 <LI><a href="#[4f]">page_start_view_load</a> from page_start.o(.text.page_start_view_load) referenced from page.o(.rodata.pa_tVar)
 <LI><a href="#[5e]">page_statistics_update</a> from page_statistics.o(.text.page_statistics_update) referenced from page.o(.rodata.pa_tVar)
 <LI><a href="#[5d]">page_statistics_view_load</a> from page_statistics.o(.text.page_statistics_view_load) referenced from page.o(.rodata.pa_tVar)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[4b]"></a>__main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry.o(.ARM.Collect$$$$00000000))
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(.text)
</UL>
<P><STRONG><a name="[126]"></a>_main_stk</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry2.o(.ARM.Collect$$$$00000001))

<P><STRONG><a name="[64]"></a>_main_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[69]"></a>__main_after_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[127]"></a>_main_clock</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry7b.o(.ARM.Collect$$$$00000008))

<P><STRONG><a name="[128]"></a>_main_cpp_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry8b.o(.ARM.Collect$$$$0000000A))

<P><STRONG><a name="[129]"></a>_main_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry9a.o(.ARM.Collect$$$$0000000B))

<P><STRONG><a name="[12a]"></a>__rt_final_cpp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry10a.o(.ARM.Collect$$$$0000000D))

<P><STRONG><a name="[12b]"></a>__rt_final_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry11a.o(.ARM.Collect$$$$0000000F))

<P><STRONG><a name="[12c]"></a>BOOT_RAM</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, startup_air32f10x.o(.text), UNUSED)

<P><STRONG><a name="[0]"></a>Reset_Handler</STRONG> (Thumb, 76 bytes, Stack size 0 bytes, startup_air32f10x.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>ADC1_2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_air32f10x.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[1c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC1_2_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[1c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC1_2_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[39]"></a>ADC3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_air32f10x.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[1f]"></a>CAN1_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_air32f10x.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[20]"></a>CAN1_SCE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_air32f10x.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[15]"></a>DMA1_Channel1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_air32f10x.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>DMA1_Channel2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_air32f10x.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>DMA1_Channel3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_air32f10x.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>DMA1_Channel4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_air32f10x.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>DMA1_Channel5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_air32f10x.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>DMA1_Channel6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_air32f10x.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>DMA1_Channel7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_air32f10x.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[42]"></a>DMA2_Channel1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_air32f10x.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[43]"></a>DMA2_Channel2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_air32f10x.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[44]"></a>DMA2_Channel3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_air32f10x.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[45]"></a>DMA2_Channel4_5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_air32f10x.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[10]"></a>EXTI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_air32f10x.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[32]"></a>EXTI15_10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_air32f10x.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[11]"></a>EXTI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_air32f10x.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[12]"></a>EXTI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_air32f10x.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[13]"></a>EXTI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_air32f10x.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[14]"></a>EXTI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_air32f10x.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[21]"></a>EXTI9_5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_air32f10x.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[e]"></a>FLASH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_air32f10x.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[3a]"></a>FSMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_air32f10x.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[2a]"></a>I2C1_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_air32f10x.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[29]"></a>I2C1_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_air32f10x.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[2c]"></a>I2C2_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_air32f10x.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[2b]"></a>I2C2_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_air32f10x.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[b]"></a>PVD_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_air32f10x.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[f]"></a>RCC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_air32f10x.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[47]"></a>RNG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_air32f10x.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[33]"></a>RTCAlarm_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_air32f10x.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[d]"></a>RTC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_air32f10x.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[3b]"></a>SDIO_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_air32f10x.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[48]"></a>SENSOR_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_air32f10x.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[2d]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_air32f10x.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[2e]"></a>SPI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_air32f10x.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[3d]"></a>SPI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_air32f10x.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[46]"></a>SYMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_air32f10x.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[c]"></a>TAMPER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_air32f10x.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[22]"></a>TIM1_BRK_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_air32f10x.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[25]"></a>TIM1_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_air32f10x.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>TIM1_TRG_COM_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_air32f10x.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[23]"></a>TIM1_UP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_air32f10x.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[26]"></a>TIM2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_air32f10x.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>TIM3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_air32f10x.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[28]"></a>TIM4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_air32f10x.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[3c]"></a>TIM5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_air32f10x.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[40]"></a>TIM6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_air32f10x.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[41]"></a>TIM7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_air32f10x.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[35]"></a>TIM8_BRK_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_air32f10x.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[38]"></a>TIM8_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_air32f10x.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[37]"></a>TIM8_TRG_COM_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_air32f10x.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[36]"></a>TIM8_UP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_air32f10x.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[3e]"></a>UART4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_air32f10x.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[3f]"></a>UART5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_air32f10x.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[30]"></a>USART2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_air32f10x.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[31]"></a>USART3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_air32f10x.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[34]"></a>USBWakeUp_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_air32f10x.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>USB_HP_CAN1_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_air32f10x.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[1e]"></a>USB_LP_CAN1_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_air32f10x.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[a]"></a>WWDG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_air32f10x.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[12d]"></a>__aeabi_memcpy</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, memcpya.o(.text), UNUSED)

<P><STRONG><a name="[b6]"></a>__aeabi_memcpy4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memcpya.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PowerOnStorageOperation
</UL>

<P><STRONG><a name="[12e]"></a>__aeabi_memcpy8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memcpya.o(.text), UNUSED)

<P><STRONG><a name="[67]"></a>__aeabi_memset</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_Draw_a_table_based_on_the_angle_arc
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_Draw_a_table_arc
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset$wrapper
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>

<P><STRONG><a name="[af]"></a>__aeabi_memset4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PixelPageCache2D_OpenWin_customize
</UL>

<P><STRONG><a name="[12f]"></a>__aeabi_memset8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[66]"></a>__aeabi_memclr</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_Draw_a_table_arc
</UL>

<P><STRONG><a name="[b0]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PixelPageCache2D_OpenWin_customize
</UL>

<P><STRONG><a name="[130]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[68]"></a>_memset$wrapper</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, memseta.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>

<P><STRONG><a name="[b5]"></a>memcmp</STRONG> (Thumb, 26 bytes, Stack size 12 bytes, memcmp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = memcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_FontAddressAcquisition
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PowerOnStorageOperation
</UL>

<P><STRONG><a name="[110]"></a>strcpy</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, strcpy.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_statistics_update
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_parameter_update
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_debug_update
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_check_update
</UL>

<P><STRONG><a name="[131]"></a>__aeabi_uidiv</STRONG> (Thumb, 0 bytes, Stack size 12 bytes, uidiv.o(.text), UNUSED)

<P><STRONG><a name="[124]"></a>__aeabi_uidivmod</STRONG> (Thumb, 44 bytes, Stack size 12 bytes, uidiv.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[65]"></a>__scatterload</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, init.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main_after_scatterload
</UL>
<BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_main_scatterload
</UL>

<P><STRONG><a name="[132]"></a>__scatterload_rt2</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, init.o(.text), UNUSED)

<P><STRONG><a name="[133]"></a>__decompress</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __dczerorl2.o(.text), UNUSED)

<P><STRONG><a name="[134]"></a>__decompress1</STRONG> (Thumb, 86 bytes, Stack size unknown bytes, __dczerorl2.o(.text), UNUSED)

<P><STRONG><a name="[c1]"></a>AIR_RCC_PLLConfig</STRONG> (Thumb, 160 bytes, Stack size 32 bytes, air32f10x_rcc_ex.o(.text.AIR_RCC_PLLConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = AIR_RCC_PLLConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_ClkConfiguration
</UL>

<P><STRONG><a name="[6a]"></a>BEEP_InitHard</STRONG> (Thumb, 138 bytes, Stack size 48 bytes, bsp_beep.o(.text.BEEP_InitHard))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = BEEP_InitHard &rArr; TIM_OC1Init
</UL>
<BR>[Calls]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TimeBaseInit
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OCStructInit
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC1PreloadConfig
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC1Init
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Cmd
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ARRPreloadConfig
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB1PeriphClockCmd
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphClockCmd
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_Init
</UL>

<P><STRONG><a name="[fa]"></a>BEEP_Pro10ms</STRONG> (Thumb, 212 bytes, Stack size 8 bytes, bsp_beep.o(.text.BEEP_Pro10ms))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = BEEP_Pro10ms
</UL>
<BR>[Called By]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_RunPer10ms
</UL>

<P><STRONG><a name="[93]"></a>BEEP_Start</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, bsp_beep.o(.text.BEEP_Start))
<BR><BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handling_fifo_Events1ms
</UL>

<P><STRONG><a name="[74]"></a>Backboard_data_receive</STRONG> (Thumb, 302 bytes, Stack size 40 bytes, pro_cus_com.o(.text.Backboard_data_receive))
<BR><BR>[Stack]<UL><LI>Max Depth = 100<LI>Call Chain = Backboard_data_receive &rArr; QR_code_byte_stream_processing &rArr; ReceiveQRcode &rArr; FLASH_ProgramWord
</UL>
<BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QR_code_byte_stream_processing
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CalCrc16_Check
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comGetChar
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comSendBuf
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_GetRunTime
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_CheckRunTime
</UL>
<BR>[Called By]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[7b]"></a>BackupsParameData</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, flash_eeprom.o(.text.BackupsParameData))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = BackupsParameData &rArr; FlashSaveData &rArr; FLASH_ProgramWord
</UL>
<BR>[Calls]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FlashSaveData
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handling_fifo_Events1ms
</UL>

<P><STRONG><a name="[4c]"></a>BootAppReceiveDataProcessing</STRONG> (Thumb, 74 bytes, Stack size 0 bytes, bsp_boot.o(.text.BootAppReceiveDataProcessing))
<BR><BR>[Calls]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_SystemReset
</UL>
<BR>[Address Reference Count : 1]<UL><LI> bsp_uart_fifo.o(.text.bsp_InitUart)
</UL>
<P><STRONG><a name="[4]"></a>BusFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, air32f10x_it.o(.text.BusFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[7e]"></a>Business_EE_half_hour_storage_management</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, pro_workflow.o(.text.Business_EE_half_hour_storage_management))
<BR><BR>[Calls]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_Putfifo
</UL>
<BR>[Called By]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[63]"></a>Business_EE_immediate_storage_and_storage_management</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, pro_workflow.o(.text.Business_EE_immediate_storage_and_storage_management))
<BR><BR>[Calls]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_Putfifo
</UL>
<BR>[Called By]<UL><LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_statistics_view_load
<LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_standby_view_load
</UL>
<BR>[Address Reference Count : 1]<UL><LI> parame.o(.rodata.parames_config_update)
</UL>
<P><STRONG><a name="[79]"></a>CalCrc16_Check</STRONG> (Thumb, 246 bytes, Stack size 8 bytes, bsp_crc.o(.text.CalCrc16_Check))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = CalCrc16_Check
</UL>
<BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FlashSaveData
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PowerOnStorageOperation
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Backboard_data_receive
</UL>

<P><STRONG><a name="[80]"></a>Countdown_processing_for_no_running_operation_on_the_running_interface</STRONG> (Thumb, 126 bytes, Stack size 0 bytes, pro_workflow.o(.text.Countdown_processing_for_no_running_operation_on_the_running_interface))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = Countdown_processing_for_no_running_operation_on_the_running_interface &rArr; page_goto
</UL>
<BR>[Calls]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_goto
</UL>
<BR>[Called By]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[7]"></a>DebugMon_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, air32f10x_it.o(.text.DebugMon_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[109]"></a>Desiccant_output_control</STRONG> (Thumb, 134 bytes, Stack size 0 bytes, pro_workflow.o(.text.Desiccant_output_control))
<BR><BR>[Called By]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[82]"></a>DetectALARM100ms</STRONG> (Thumb, 82 bytes, Stack size 8 bytes, bsp_timer.o(.text.DetectALARM100ms))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DetectALARM100ms
</UL>
<BR>[Calls]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_Putfifo
</UL>
<BR>[Called By]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[108]"></a>Detergent_output_control</STRONG> (Thumb, 134 bytes, Stack size 0 bytes, pro_workflow.o(.text.Detergent_output_control))
<BR><BR>[Called By]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[83]"></a>Dishwasher_usage_statistics</STRONG> (Thumb, 188 bytes, Stack size 8 bytes, pro_workflow.o(.text.Dishwasher_usage_statistics))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = Dishwasher_usage_statistics &rArr; parame_adj
</UL>
<BR>[Calls]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parame_adj
</UL>
<BR>[Called By]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[85]"></a>DishwashingBusinessOperation</STRONG> (Thumb, 228 bytes, Stack size 0 bytes, pro_workflow.o(.text.DishwashingBusinessOperation))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = DishwashingBusinessOperation &rArr; page_goto
</UL>
<BR>[Calls]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_goto
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_Putfifo
</UL>
<BR>[Called By]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[101]"></a>DishwashingCloseBusiness</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, pro_workflow.o(.text.DishwashingCloseBusiness))
<BR><BR>[Called By]<UL><LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_standby_view_load
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;keyHandle
</UL>

<P><STRONG><a name="[87]"></a>DishwashingDeterminationOfOperatingConditions</STRONG> (Thumb, 260 bytes, Stack size 0 bytes, pro_workflow.o(.text.DishwashingDeterminationOfOperatingConditions))
<BR><BR>[Called By]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_run_update
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;From_suspending_business_to_starting
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Stop_operation_management_due_to_water_shortage
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Door_closing_delay_start_function
</UL>

<P><STRONG><a name="[86]"></a>Door_closing_delay_start_function</STRONG> (Thumb, 124 bytes, Stack size 8 bytes, pro_workflow.o(.text.Door_closing_delay_start_function))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Door_closing_delay_start_function
</UL>
<BR>[Calls]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DishwashingDeterminationOfOperatingConditions
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_Putfifo
</UL>
<BR>[Called By]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[88]"></a>Door_from_closed_to_open</STRONG> (Thumb, 76 bytes, Stack size 8 bytes, pro_workflow.o(.text.Door_from_closed_to_open))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Door_from_closed_to_open
</UL>
<BR>[Calls]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_Putfifo
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handling_fifo_Events1ms
</UL>

<P><STRONG><a name="[89]"></a>Door_from_open_to_closed</STRONG> (Thumb, 62 bytes, Stack size 0 bytes, pro_workflow.o(.text.Door_from_open_to_closed))
<BR><BR>[Calls]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_Putfifo
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handling_fifo_Events1ms
</UL>

<P><STRONG><a name="[8a]"></a>Ex_fault_alarm_detection_function</STRONG> (Thumb, 236 bytes, Stack size 8 bytes, pro_error.o(.text.Ex_fault_alarm_detection_function))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Ex_fault_alarm_detection_function
</UL>
<BR>[Calls]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_Putfifo
</UL>
<BR>[Called By]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[8c]"></a>FLASH_ClearFlag</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, air32f10x_flash.o(.text.FLASH_ClearFlag))
<BR><BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FlashSaveData
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReceiveQRcode
</UL>

<P><STRONG><a name="[8d]"></a>FLASH_ErasePage</STRONG> (Thumb, 624 bytes, Stack size 0 bytes, air32f10x_flash.o(.text.FLASH_ErasePage))
<BR><BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FlashSaveData
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReceiveQRcode
</UL>

<P><STRONG><a name="[8f]"></a>FLASH_Lock</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, air32f10x_flash.o(.text.FLASH_Lock))
<BR><BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FlashSaveData
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReceiveQRcode
</UL>

<P><STRONG><a name="[8e]"></a>FLASH_ProgramWord</STRONG> (Thumb, 1062 bytes, Stack size 12 bytes, air32f10x_flash.o(.text.FLASH_ProgramWord))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = FLASH_ProgramWord
</UL>
<BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FlashSaveData
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReceiveQRcode
</UL>

<P><STRONG><a name="[8b]"></a>FLASH_Unlock</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, air32f10x_flash.o(.text.FLASH_Unlock))
<BR><BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FlashSaveData
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReceiveQRcode
</UL>

<P><STRONG><a name="[7c]"></a>FlashSaveData</STRONG> (Thumb, 112 bytes, Stack size 24 bytes, flash_eeprom.o(.text.FlashSaveData))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = FlashSaveData &rArr; FLASH_ProgramWord
</UL>
<BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_Unlock
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_ProgramWord
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_Lock
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_ErasePage
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_ClearFlag
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CalCrc16_Check
</UL>
<BR>[Called By]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SaveParameData
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BackupsParameData
</UL>

<P><STRONG><a name="[90]"></a>From_suspending_business_to_starting</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, pro_workflow.o(.text.From_suspending_business_to_starting))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = From_suspending_business_to_starting
</UL>
<BR>[Calls]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DishwashingDeterminationOfOperatingConditions
</UL>
<BR>[Called By]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;keyHandle
</UL>

<P><STRONG><a name="[11a]"></a>Full_water_check_function</STRONG> (Thumb, 72 bytes, Stack size 0 bytes, pro_workflow.o(.text.Full_water_check_function))
<BR><BR>[Called By]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_run_update
</UL>

<P><STRONG><a name="[6d]"></a>GPIO_Init</STRONG> (Thumb, 1026 bytes, Stack size 16 bytes, air32f10x_gpio.o(.text.GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ILI9340X_Init
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_InitKeyHard
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BEEP_InitHard
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_InitUart
</UL>

<P><STRONG><a name="[a4]"></a>GPIO_PinRemapConfig</STRONG> (Thumb, 126 bytes, Stack size 16 bytes, air32f10x_gpio.o(.text.GPIO_PinRemapConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = GPIO_PinRemapConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ILI9340X_Init
</UL>

<P><STRONG><a name="[ac]"></a>GPIO_ReadInputDataBit</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, air32f10x_gpio.o(.text.GPIO_ReadInputDataBit))
<BR><BR>[Called By]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IsKeyDownFunc
</UL>

<P><STRONG><a name="[9e]"></a>GPIO_ResetBits</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, air32f10x_gpio.o(.text.GPIO_ResetBits))
<BR><BR>[Called By]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ILI9340X_BackLed_Control
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IsKeyDownFunc
</UL>

<P><STRONG><a name="[9f]"></a>GPIO_SetBits</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, air32f10x_gpio.o(.text.GPIO_SetBits))
<BR><BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ILI9340X_Init
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ILI9340X_BackLed_Control
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_InitKeyHard
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IsKeyDownFunc
</UL>

<P><STRONG><a name="[91]"></a>Handling_fifo_Events1ms</STRONG> (Thumb, 300 bytes, Stack size 8 bytes, bsp_fifo.o(.text.Handling_fifo_Events1ms))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = Handling_fifo_Events1ms &rArr; keyHandle &rArr; Init_parame_level
</UL>
<BR>[Calls]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;keyHandle
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Wa_L_from_open_to_closed
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Wa_L_from_closed_to_open
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Wa_H_from_open_to_closed
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Wa_H_from_closed_to_open
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SaveParameData
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RI_L_from_open_to_closed
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RI_L_from_closed_to_open
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RI_H_from_open_to_closed
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RI_H_from_closed_to_open
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Door_from_open_to_closed
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Door_from_closed_to_open
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BackupsParameData
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BEEP_Start
</UL>
<BR>[Called By]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[2]"></a>HardFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, air32f10x_it.o(.text.HardFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[9d]"></a>ILI9340X_BackLed_Control</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, bsp_ili9340x_lcd.o(.text.ILI9340X_BackLed_Control))
<BR><BR>[Calls]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
</UL>
<BR>[Called By]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_Clear_Screen
</UL>

<P><STRONG><a name="[a0]"></a>ILI9340X_Clear</STRONG> (Thumb, 498 bytes, Stack size 32 bytes, bsp_ili9340x_lcd.o(.text.ILI9340X_Clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = ILI9340X_Clear &rArr; ILI9340X_OpenWindow
</UL>
<BR>[Calls]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ILI9340X_OpenWindow
</UL>
<BR>[Called By]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_update_ProgressBar
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_Clear_Screen
<LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_statistics_view_load
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_statistics_update
<LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_run_view_load
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_run_update
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_qr_code_view_load
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_parameter_view_load
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_parameter_update
<LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_developer_view_load
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_debug_view_load
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_debug_update
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_check_view_load
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_Init
</UL>

<P><STRONG><a name="[a2]"></a>ILI9340X_DrawRectangle</STRONG> (Thumb, 2458 bytes, Stack size 72 bytes, bsp_ili9340x_lcd.o(.text.ILI9340X_DrawRectangle))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = ILI9340X_DrawRectangle &rArr; ILI9340X_OpenWindow
</UL>
<BR>[Calls]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ILI9340X_OpenWindow
</UL>
<BR>[Called By]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_run_view_load
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_run_update
</UL>

<P><STRONG><a name="[a3]"></a>ILI9340X_Init</STRONG> (Thumb, 162 bytes, Stack size 24 bytes, bsp_ili9340x_lcd.o(.text.ILI9340X_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = ILI9340X_Init &rArr; ILI9340X_REG_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ILI9340X_REG_Config
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_PinRemapConfig
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphClockCmd
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_Init
</UL>

<P><STRONG><a name="[a1]"></a>ILI9340X_OpenWindow</STRONG> (Thumb, 470 bytes, Stack size 32 bytes, bsp_ili9340x_lcd.o(.text.ILI9340X_OpenWindow))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = ILI9340X_OpenWindow
</UL>
<BR>[Called By]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ILI9340X_TwoColorChart_XYReversed
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ILI9340X_Picture
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ILI9340X_DrawRectangle
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ILI9340X_TwoColorChart
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ILI9340X_Clear
</UL>

<P><STRONG><a name="[a6]"></a>ILI9340X_Picture</STRONG> (Thumb, 602 bytes, Stack size 40 bytes, bsp_ili9340x_lcd.o(.text.ILI9340X_Picture))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = ILI9340X_Picture &rArr; ILI9340X_OpenWindow
</UL>
<BR>[Calls]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ILI9340X_OpenWindow
</UL>
<BR>[Called By]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_draw_arc
</UL>

<P><STRONG><a name="[a5]"></a>ILI9340X_REG_Config</STRONG> (Thumb, 2232 bytes, Stack size 40 bytes, bsp_ili9340x_lcd.o(.text.ILI9340X_REG_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = ILI9340X_REG_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_DelayMS
</UL>
<BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ILI9340X_Init
</UL>

<P><STRONG><a name="[a8]"></a>ILI9340X_TwoColorChart</STRONG> (Thumb, 550 bytes, Stack size 64 bytes, bsp_ili9340x_lcd.o(.text.ILI9340X_TwoColorChart))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = ILI9340X_TwoColorChart &rArr; ILI9340X_OpenWindow
</UL>
<BR>[Calls]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ILI9340X_OpenWindow
</UL>
<BR>[Called By]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_update_Rolling_Album
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_update_Animation
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_update_text_gradient
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_statistics_update
<LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_start_update
<LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_run_view_load
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_run_update
<LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_qr_code_update
<LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_developer_view_load
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_debug_view_load
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_debug_update
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_check_view_load
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_check_update
</UL>

<P><STRONG><a name="[a9]"></a>ILI9340X_TwoColorChart_XYReversed</STRONG> (Thumb, 538 bytes, Stack size 96 bytes, bsp_ili9340x_lcd.o(.text.ILI9340X_TwoColorChart_XYReversed))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = ILI9340X_TwoColorChart_XYReversed &rArr; ILI9340X_OpenWindow
</UL>
<BR>[Calls]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ILI9340X_OpenWindow
</UL>
<BR>[Called By]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_update_menu
</UL>

<P><STRONG><a name="[ff]"></a>Init_parame_level</STRONG> (Thumb, 684 bytes, Stack size 36 bytes, parame.o(.text.Init_parame_level))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = Init_parame_level
</UL>
<BR>[Called By]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;keyHandle
</UL>

<P><STRONG><a name="[10a]"></a>Inlet_valve_output_control</STRONG> (Thumb, 476 bytes, Stack size 16 bytes, pro_workflow.o(.text.Inlet_valve_output_control))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = Inlet_valve_output_control
</UL>
<BR>[Called By]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[aa]"></a>Interface_no_operation_countdown_processing</STRONG> (Thumb, 74 bytes, Stack size 16 bytes, pro_workflow.o(.text.Interface_no_operation_countdown_processing))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = Interface_no_operation_countdown_processing &rArr; page_goto
</UL>
<BR>[Calls]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_goto
</UL>
<BR>[Called By]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[ab]"></a>IsKeyDownFunc</STRONG> (Thumb, 502 bytes, Stack size 56 bytes, bsp_key.o(.text.IsKeyDownFunc))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = IsKeyDownFunc
</UL>
<BR>[Calls]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ReadInputDataBit
</UL>
<BR>[Called By]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_DetectKey
</UL>

<P><STRONG><a name="[ad]"></a>Key_subscript_logic_detection</STRONG> (Thumb, 816 bytes, Stack size 32 bytes, bsp_key.o(.text.Key_subscript_logic_detection))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = Key_subscript_logic_detection
</UL>
<BR>[Calls]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_Putfifo
</UL>
<BR>[Called By]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_DetectKey
</UL>

<P><STRONG><a name="[106]"></a>LockStateManagement</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, pro_workflow.o(.text.LockStateManagement))
<BR><BR>[Called By]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[3]"></a>MemManage_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, air32f10x_it.o(.text.MemManage_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemManage_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemManage_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[1]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, air32f10x_it.o(.text.NMI_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[f7]"></a>NVIC_Init</STRONG> (Thumb, 96 bytes, Stack size 8 bytes, misc.o(.text.NVIC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = NVIC_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_InitUart
</UL>

<P><STRONG><a name="[ed]"></a>NVIC_PriorityGroupConfig</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, misc.o(.text.NVIC_PriorityGroupConfig))
<BR><BR>[Called By]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_Init
</UL>

<P><STRONG><a name="[8]"></a>PendSV_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, air32f10x_it.o(.text.PendSV_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[ae]"></a>PixelPageCache2D_OpenWin_customize</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, ui28.o(.text.PixelPageCache2D_OpenWin_customize))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = PixelPageCache2D_OpenWin_customize
</UL>
<BR>[Calls]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset4
</UL>
<BR>[Called By]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_update_menu
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_update_Rolling_Album
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_update_Animation
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_update_text_gradient
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_debug_update
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_check_update
</UL>

<P><STRONG><a name="[b1]"></a>PixelPageCache2D_Text_customize</STRONG> (Thumb, 126 bytes, Stack size 48 bytes, ui28.o(.text.PixelPageCache2D_Text_customize))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = PixelPageCache2D_Text_customize &rArr; PixelPageCache2D_TwoColorChart_customize
</UL>
<BR>[Calls]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_FontAddressAcquisition
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PixelPageCache2D_TwoColorChart_customize
</UL>
<BR>[Called By]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_update_menu
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_update_text_gradient
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_statistics_update
<LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_start_update
<LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_developer_view_load
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_debug_view_load
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_debug_update
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_check_view_load
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_check_update
</UL>

<P><STRONG><a name="[b2]"></a>PixelPageCache2D_TwoColorChart_customize</STRONG> (Thumb, 578 bytes, Stack size 64 bytes, ui28.o(.text.PixelPageCache2D_TwoColorChart_customize))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = PixelPageCache2D_TwoColorChart_customize
</UL>
<BR>[Called By]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_update_Rolling_Album
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_update_Animation
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PixelPageCache2D_Text_customize
</UL>

<P><STRONG><a name="[b4]"></a>PowerOnStorageOperation</STRONG> (Thumb, 198 bytes, Stack size 8 bytes, flash_eeprom.o(.text.PowerOnStorageOperation))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = PowerOnStorageOperation &rArr; memcmp
</UL>
<BR>[Calls]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CalCrc16_Check
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ms100_alarm_clock
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memcmp
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[b8]"></a>Power_on_screen_dwell_time</STRONG> (Thumb, 46 bytes, Stack size 0 bytes, pro_workflow.o(.text.Power_on_screen_dwell_time))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = Power_on_screen_dwell_time &rArr; page_goto
</UL>
<BR>[Calls]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_goto
</UL>
<BR>[Called By]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[107]"></a>Power_on_water_full_once_detection_function</STRONG> (Thumb, 76 bytes, Stack size 0 bytes, pro_workflow.o(.text.Power_on_water_full_once_detection_function))
<BR><BR>[Called By]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[76]"></a>QR_code_byte_stream_processing</STRONG> (Thumb, 126 bytes, Stack size 8 bytes, pro_cus_com.o(.text.QR_code_byte_stream_processing))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = QR_code_byte_stream_processing &rArr; ReceiveQRcode &rArr; FLASH_ProgramWord
</UL>
<BR>[Calls]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parame_adj
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReceiveQRcode
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comClearTxFifo
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comClearRxFifo
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comSendBuf
</UL>
<BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Backboard_data_receive
</UL>

<P><STRONG><a name="[ef]"></a>RCC_AHBPeriphClockCmd</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, air32f10x_rcc.o(.text.RCC_AHBPeriphClockCmd))
<BR><BR>[Called By]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_Init
</UL>

<P><STRONG><a name="[6c]"></a>RCC_APB1PeriphClockCmd</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, air32f10x_rcc.o(.text.RCC_APB1PeriphClockCmd))
<BR><BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BEEP_InitHard
</UL>

<P><STRONG><a name="[6b]"></a>RCC_APB2PeriphClockCmd</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, air32f10x_rcc.o(.text.RCC_APB2PeriphClockCmd))
<BR><BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ILI9340X_Init
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_InitKeyHard
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BEEP_InitHard
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_InitUart
</UL>

<P><STRONG><a name="[bc]"></a>RCC_ClkConfiguration</STRONG> (Thumb, 128 bytes, Stack size 8 bytes, bsp.o(.text.RCC_ClkConfiguration))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = RCC_ClkConfiguration &rArr; AIR_RCC_PLLConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_SYSCLKConfig
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_PLLCmd
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_PCLK2Config
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_PCLK1Config
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_LSICmd
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_HSICmd
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_HSEConfig
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_HCLKConfig
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_GetFlagStatus
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_DeInit
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AIR_RCC_PLLConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_Init
</UL>

<P><STRONG><a name="[bd]"></a>RCC_DeInit</STRONG> (Thumb, 66 bytes, Stack size 0 bytes, air32f10x_rcc.o(.text.RCC_DeInit))
<BR><BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_ClkConfiguration
</UL>

<P><STRONG><a name="[e9]"></a>RCC_GetClocksFreq</STRONG> (Thumb, 184 bytes, Stack size 8 bytes, air32f10x_rcc.o(.text.RCC_GetClocksFreq))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = RCC_GetClocksFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Init
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_InitTimer
</UL>

<P><STRONG><a name="[bf]"></a>RCC_GetFlagStatus</STRONG> (Thumb, 42 bytes, Stack size 0 bytes, air32f10x_rcc.o(.text.RCC_GetFlagStatus))
<BR><BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_ClkConfiguration
</UL>

<P><STRONG><a name="[c3]"></a>RCC_HCLKConfig</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, air32f10x_rcc.o(.text.RCC_HCLKConfig))
<BR><BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_ClkConfiguration
</UL>

<P><STRONG><a name="[be]"></a>RCC_HSEConfig</STRONG> (Thumb, 64 bytes, Stack size 0 bytes, air32f10x_rcc.o(.text.RCC_HSEConfig))
<BR><BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_ClkConfiguration
</UL>

<P><STRONG><a name="[c7]"></a>RCC_HSICmd</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, air32f10x_rcc.o(.text.RCC_HSICmd))
<BR><BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_ClkConfiguration
</UL>

<P><STRONG><a name="[c6]"></a>RCC_LSICmd</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, air32f10x_rcc.o(.text.RCC_LSICmd))
<BR><BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_ClkConfiguration
</UL>

<P><STRONG><a name="[c4]"></a>RCC_PCLK1Config</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, air32f10x_rcc.o(.text.RCC_PCLK1Config))
<BR><BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_ClkConfiguration
</UL>

<P><STRONG><a name="[c5]"></a>RCC_PCLK2Config</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, air32f10x_rcc.o(.text.RCC_PCLK2Config))
<BR><BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_ClkConfiguration
</UL>

<P><STRONG><a name="[c0]"></a>RCC_PLLCmd</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, air32f10x_rcc.o(.text.RCC_PLLCmd))
<BR><BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_ClkConfiguration
</UL>

<P><STRONG><a name="[c2]"></a>RCC_SYSCLKConfig</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, air32f10x_rcc.o(.text.RCC_SYSCLKConfig))
<BR><BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_ClkConfiguration
</UL>

<P><STRONG><a name="[9a]"></a>RI_H_from_closed_to_open</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, pro_workflow.o(.text.RI_H_from_closed_to_open))
<BR><BR>[Calls]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_Putfifo
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handling_fifo_Events1ms
</UL>

<P><STRONG><a name="[99]"></a>RI_H_from_open_to_closed</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, pro_workflow.o(.text.RI_H_from_open_to_closed))
<BR><BR>[Calls]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_Putfifo
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handling_fifo_Events1ms
</UL>

<P><STRONG><a name="[9c]"></a>RI_L_from_closed_to_open</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, pro_workflow.o(.text.RI_L_from_closed_to_open))
<BR><BR>[Calls]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_Putfifo
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handling_fifo_Events1ms
</UL>

<P><STRONG><a name="[9b]"></a>RI_L_from_open_to_closed</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, pro_workflow.o(.text.RI_L_from_open_to_closed))
<BR><BR>[Calls]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_Putfifo
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handling_fifo_Events1ms
</UL>

<P><STRONG><a name="[bb]"></a>ReceiveQRcode</STRONG> (Thumb, 204 bytes, Stack size 40 bytes, pro_cus_com.o(.text.ReceiveQRcode))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = ReceiveQRcode &rArr; FLASH_ProgramWord
</UL>
<BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_Unlock
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_ProgramWord
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_Lock
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_ErasePage
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_ClearFlag
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comGetChar
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QR_code_byte_stream_processing
</UL>

<P><STRONG><a name="[10b]"></a>Rinse_heating_output_control</STRONG> (Thumb, 490 bytes, Stack size 8 bytes, pro_workflow.o(.text.Rinse_heating_output_control))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Rinse_heating_output_control
</UL>
<BR>[Called By]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[10d]"></a>Rinse_pump_output_control</STRONG> (Thumb, 442 bytes, Stack size 16 bytes, pro_workflow.o(.text.Rinse_pump_output_control))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = Rinse_pump_output_control
</UL>
<BR>[Called By]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[6]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, air32f10x_it.o(.text.SVC_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[94]"></a>SaveParameData</STRONG> (Thumb, 106 bytes, Stack size 8 bytes, flash_eeprom.o(.text.SaveParameData))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = SaveParameData &rArr; FlashSaveData &rArr; FLASH_ProgramWord
</UL>
<BR>[Calls]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FlashSaveData
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ms100_alarm_clock
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handling_fifo_Events1ms
</UL>

<P><STRONG><a name="[102]"></a>ScanDataChanges10ms</STRONG> (Thumb, 202 bytes, Stack size 40 bytes, parame.o(.text.ScanDataChanges10ms))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = ScanDataChanges10ms
</UL>
<BR>[Called By]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[c8]"></a>Stop_operation_management_due_to_water_shortage</STRONG> (Thumb, 176 bytes, Stack size 8 bytes, pro_workflow.o(.text.Stop_operation_management_due_to_water_shortage))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Stop_operation_management_due_to_water_shortage
</UL>
<BR>[Calls]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DishwashingDeterminationOfOperatingConditions
</UL>
<BR>[Called By]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[9]"></a>SysTick_Handler</STRONG> (Thumb, 60 bytes, Stack size 0 bytes, bsp_timer.o(.text.SysTick_Handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = SysTick_Handler &rArr; bsp_RunPer10ms &rArr; bsp_KeyScan10ms &rArr; bsp_DetectKey &rArr; IsKeyDownFunc
</UL>
<BR>[Calls]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_RunPer10ms
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[4a]"></a>SystemInit</STRONG> (Thumb, 334 bytes, Stack size 8 bytes, system_air32f10x.o(.text.SystemInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SystemInit
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(.text)
</UL>
<P><STRONG><a name="[72]"></a>TIM_ARRPreloadConfig</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, air32f10x_tim.o(.text.TIM_ARRPreloadConfig))
<BR><BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BEEP_InitHard
</UL>

<P><STRONG><a name="[73]"></a>TIM_Cmd</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, air32f10x_tim.o(.text.TIM_Cmd))
<BR><BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BEEP_InitHard
</UL>

<P><STRONG><a name="[70]"></a>TIM_OC1Init</STRONG> (Thumb, 108 bytes, Stack size 16 bytes, air32f10x_tim.o(.text.TIM_OC1Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = TIM_OC1Init
</UL>
<BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BEEP_InitHard
</UL>

<P><STRONG><a name="[71]"></a>TIM_OC1PreloadConfig</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, air32f10x_tim.o(.text.TIM_OC1PreloadConfig))
<BR><BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BEEP_InitHard
</UL>

<P><STRONG><a name="[6f]"></a>TIM_OCStructInit</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, air32f10x_tim.o(.text.TIM_OCStructInit))
<BR><BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BEEP_InitHard
</UL>

<P><STRONG><a name="[6e]"></a>TIM_TimeBaseInit</STRONG> (Thumb, 162 bytes, Stack size 8 bytes, air32f10x_tim.o(.text.TIM_TimeBaseInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM_TimeBaseInit
</UL>
<BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BEEP_InitHard
</UL>

<P><STRONG><a name="[ca]"></a>UI28_Clear_Screen</STRONG> (Thumb, 94 bytes, Stack size 16 bytes, ui28.o(.text.UI28_Clear_Screen))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = UI28_Clear_Screen &rArr; ILI9340X_Clear &rArr; ILI9340X_OpenWindow
</UL>
<BR>[Calls]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ILI9340X_BackLed_Control
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ILI9340X_Clear
</UL>
<BR>[Called By]<UL><LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_statistics_view_load
<LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_start_view_load
<LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_standby_view_load
<LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_run_view_load
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_qr_code_view_load
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_parameter_view_load
<LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_developer_view_load
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_debug_view_load
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_check_view_load
</UL>

<P><STRONG><a name="[cb]"></a>UI28_Draw_a_table_arc</STRONG> (Thumb, 626 bytes, Stack size 40 bytes, ui28_arc.o(.text.UI28_Draw_a_table_arc))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = UI28_Draw_a_table_arc &rArr; UI28_Draw_a_table_based_on_the_angle_arc
</UL>
<BR>[Calls]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_Draw_a_table_based_on_the_angle_arc
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
<LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_draw_arc
</UL>

<P><STRONG><a name="[cc]"></a>UI28_Draw_a_table_based_on_the_angle_arc</STRONG> (Thumb, 712 bytes, Stack size 64 bytes, ui28_arc.o(.text.UI28_Draw_a_table_based_on_the_angle_arc))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = UI28_Draw_a_table_based_on_the_angle_arc
</UL>
<BR>[Calls]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_Draw_a_table_arc
</UL>

<P><STRONG><a name="[b3]"></a>UI28_FontAddressAcquisition</STRONG> (Thumb, 804 bytes, Stack size 40 bytes, ui28_matrix.o(.text.UI28_FontAddressAcquisition))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = UI28_FontAddressAcquisition &rArr; memcmp
</UL>
<BR>[Calls]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PixelPageCache2D_Text_customize
</UL>

<P><STRONG><a name="[cd]"></a>UI28_Init</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, ui28.o(.text.UI28_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = UI28_Init &rArr; ILI9340X_Init &rArr; ILI9340X_REG_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui28_Init_Rolling_Album_Engineering
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui28_Init_ProgressBar_Engineering
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui28_Init_Menu_Engineering
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ILI9340X_Init
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ILI9340X_Clear
</UL>
<BR>[Called By]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_Init
</UL>

<P><STRONG><a name="[d1]"></a>UI28_Polling</STRONG> (Thumb, 658 bytes, Stack size 16 bytes, ui28.o(.text.UI28_Polling))
<BR><BR>[Stack]<UL><LI>Max Depth = 240<LI>Call Chain = UI28_Polling &rArr; UI28_update_menu &rArr; ILI9340X_TwoColorChart_XYReversed &rArr; ILI9340X_OpenWindow
</UL>
<BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui28_Init_Rolling_Album
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui28_Init_ProgressBar
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui28_Init_Menu
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Update_the_current_menu_based_on_the_target_menu
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Update_the_current_image_based_on_the_target_image_Album
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Update_current_status_based_on_target_progressBar
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_update_menu
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_update_Rolling_Album
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_update_ProgressBar
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_update_Animation
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_arc_loading
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_update_text_gradient
</UL>
<BR>[Called By]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[ea]"></a>UI28_anima</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, ui28.o(.text.UI28_anima))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = UI28_anima
</UL>
<BR>[Called By]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Update_the_current_menu_based_on_the_target_menu
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Update_the_current_image_based_on_the_target_image_Album
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Update_current_status_based_on_target_progressBar
</UL>

<P><STRONG><a name="[d8]"></a>UI28_arc_loading</STRONG> (Thumb, 230 bytes, Stack size 32 bytes, ui28_arc.o(.text.UI28_arc_loading))
<BR><BR>[Stack]<UL><LI>Max Depth = 216<LI>Call Chain = UI28_arc_loading &rArr; UI28_draw_arc &rArr; UI28_Draw_a_table_arc &rArr; UI28_Draw_a_table_based_on_the_angle_arc
</UL>
<BR>[Calls]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_draw_arc
</UL>
<BR>[Called By]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_Polling
</UL>

<P><STRONG><a name="[119]"></a>UI28_change_color_album</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, ui28_rolling_album.o(.text.UI28_change_color_album))
<BR><BR>[Called By]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_run_update
</UL>

<P><STRONG><a name="[117]"></a>UI28_change_color_album_text_gradient</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, ui28_matrix.o(.text.UI28_change_color_album_text_gradient))
<BR><BR>[Called By]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_run_view_load
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_run_update
</UL>

<P><STRONG><a name="[11b]"></a>UI28_change_cursor_value_ProgressBar</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, ui28_progress_bar.o(.text.UI28_change_cursor_value_ProgressBar))
<BR><BR>[Called By]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_run_update
</UL>

<P><STRONG><a name="[118]"></a>UI28_change_cursor_value_album</STRONG> (Thumb, 86 bytes, Stack size 0 bytes, ui28_rolling_album.o(.text.UI28_change_cursor_value_album))
<BR><BR>[Called By]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_run_update
</UL>

<P><STRONG><a name="[df]"></a>UI28_change_table_menu</STRONG> (Thumb, 182 bytes, Stack size 0 bytes, ui28_menu.o(.text.UI28_change_table_menu))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = UI28_change_table_menu &rArr; Update_the_target_based_on_the_cursor_menu
</UL>
<BR>[Calls]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Update_the_target_based_on_the_cursor_menu
</UL>
<BR>[Called By]<UL><LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_statistics_view_load
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_parameter_view_load
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;keyHandle
</UL>

<P><STRONG><a name="[e2]"></a>UI28_character_pixel_width_calculation</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, ui28.o(.text.UI28_character_pixel_width_calculation))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = UI28_character_pixel_width_calculation
</UL>
<BR>[Called By]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_update_menu
</UL>

<P><STRONG><a name="[11d]"></a>UI28_display_onoff_ProgressBar</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, ui28_progress_bar.o(.text.UI28_display_onoff_ProgressBar))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = UI28_display_onoff_ProgressBar
</UL>
<BR>[Called By]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_run_view_load
</UL>

<P><STRONG><a name="[116]"></a>UI28_display_onoff_album</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, ui28_rolling_album.o(.text.UI28_display_onoff_album))
<BR><BR>[Called By]<UL><LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_standby_view_load
<LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_run_view_load
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_run_update
</UL>

<P><STRONG><a name="[11f]"></a>UI28_display_onoff_arc_loading</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, ui28_arc.o(.text.UI28_display_onoff_arc_loading))
<BR><BR>[Called By]<UL><LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_start_view_load
</UL>

<P><STRONG><a name="[115]"></a>UI28_display_onoff_menu</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, ui28_menu.o(.text.UI28_display_onoff_menu))
<BR><BR>[Called By]<UL><LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_statistics_view_load
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_parameter_view_load
</UL>

<P><STRONG><a name="[112]"></a>UI28_display_onoff_text_gradient</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, ui28_matrix.o(.text.UI28_display_onoff_text_gradient))
<BR><BR>[Called By]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_statistics_update
<LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_start_view_load
<LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_standby_view_load
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_run_update
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_parameter_update
</UL>

<P><STRONG><a name="[de]"></a>UI28_draw_arc</STRONG> (Thumb, 514 bytes, Stack size 80 bytes, ui28_arc.o(.text.UI28_draw_arc))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = UI28_draw_arc &rArr; UI28_Draw_a_table_arc &rArr; UI28_Draw_a_table_based_on_the_angle_arc
</UL>
<BR>[Calls]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ILI9340X_Picture
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_Draw_a_table_arc
</UL>
<BR>[Called By]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_arc_loading
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_debug_view_load
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_debug_update
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_check_view_load
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_check_update
</UL>

<P><STRONG><a name="[100]"></a>UI28_init_text_gradient_alpha</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, ui28_matrix.o(.text.UI28_init_text_gradient_alpha))
<BR><BR>[Called By]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_statistics_update
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_run_update
<LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_qr_code_update
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_parameter_update
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;keyHandle
</UL>

<P><STRONG><a name="[11e]"></a>UI28_is_the_cursor_hidden_menu</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, ui28_menu.o(.text.UI28_is_the_cursor_hidden_menu))
<BR><BR>[Called By]<UL><LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_statistics_view_load
<LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_standby_view_load
</UL>

<P><STRONG><a name="[11c]"></a>UI28_set_synchronous_gradient_text_gradient</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, ui28_matrix.o(.text.UI28_set_synchronous_gradient_text_gradient))
<BR><BR>[Called By]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_run_view_load
</UL>

<P><STRONG><a name="[113]"></a>UI28_set_up_text_gradient_add</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, ui28_matrix.o(.text.UI28_set_up_text_gradient_add))
<BR><BR>[Called By]<UL><LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_statistics_view_load
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_statistics_update
<LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_start_view_load
<LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_standby_update
<LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_run_view_load
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_run_update
<LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_qr_code_update
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_parameter_view_load
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_parameter_update
</UL>

<P><STRONG><a name="[fe]"></a>UI28_single_step_album</STRONG> (Thumb, 134 bytes, Stack size 8 bytes, ui28_rolling_album.o(.text.UI28_single_step_album))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = UI28_single_step_album
</UL>
<BR>[Called By]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;keyHandle
</UL>

<P><STRONG><a name="[e1]"></a>UI28_single_step_menu</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, ui28_menu.o(.text.UI28_single_step_menu))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = UI28_single_step_menu &rArr; Update_the_target_based_on_the_cursor_menu
</UL>
<BR>[Calls]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Update_the_target_based_on_the_cursor_menu
</UL>
<BR>[Called By]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;keyHandle
</UL>

<P><STRONG><a name="[da]"></a>UI28_update_Animation</STRONG> (Thumb, 368 bytes, Stack size 48 bytes, ui28_animation.o(.text.UI28_update_Animation))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = UI28_update_Animation &rArr; ILI9340X_TwoColorChart &rArr; ILI9340X_OpenWindow
</UL>
<BR>[Calls]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PixelPageCache2D_TwoColorChart_customize
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PixelPageCache2D_OpenWin_customize
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ILI9340X_TwoColorChart
</UL>
<BR>[Called By]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_Polling
</UL>

<P><STRONG><a name="[dd]"></a>UI28_update_ProgressBar</STRONG> (Thumb, 102 bytes, Stack size 24 bytes, ui28_progress_bar.o(.text.UI28_update_ProgressBar))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = UI28_update_ProgressBar &rArr; ILI9340X_Clear &rArr; ILI9340X_OpenWindow
</UL>
<BR>[Calls]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ILI9340X_Clear
</UL>
<BR>[Called By]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_Polling
</UL>

<P><STRONG><a name="[d7]"></a>UI28_update_Rolling_Album</STRONG> (Thumb, 508 bytes, Stack size 64 bytes, ui28_rolling_album.o(.text.UI28_update_Rolling_Album))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = UI28_update_Rolling_Album &rArr; ILI9340X_TwoColorChart &rArr; ILI9340X_OpenWindow
</UL>
<BR>[Calls]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PixelPageCache2D_TwoColorChart_customize
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PixelPageCache2D_OpenWin_customize
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ILI9340X_TwoColorChart
</UL>
<BR>[Called By]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_Polling
</UL>

<P><STRONG><a name="[d4]"></a>UI28_update_menu</STRONG> (Thumb, 1678 bytes, Stack size 96 bytes, ui28_menu.o(.text.UI28_update_menu))
<BR><BR>[Stack]<UL><LI>Max Depth = 224<LI>Call Chain = UI28_update_menu &rArr; ILI9340X_TwoColorChart_XYReversed &rArr; ILI9340X_OpenWindow
</UL>
<BR>[Calls]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ILI9340X_TwoColorChart_XYReversed
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_character_pixel_width_calculation
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PixelPageCache2D_Text_customize
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PixelPageCache2D_OpenWin_customize
</UL>
<BR>[Called By]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_Polling
</UL>

<P><STRONG><a name="[d9]"></a>UI28_update_text_gradient</STRONG> (Thumb, 608 bytes, Stack size 40 bytes, ui28_matrix.o(.text.UI28_update_text_gradient))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = UI28_update_text_gradient &rArr; PixelPageCache2D_Text_customize &rArr; PixelPageCache2D_TwoColorChart_customize
</UL>
<BR>[Calls]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;alpha_blend_rgb565
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PixelPageCache2D_Text_customize
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PixelPageCache2D_OpenWin_customize
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ILI9340X_TwoColorChart
</UL>
<BR>[Called By]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_statistics_update
<LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_run_view_load
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_run_update
<LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_qr_code_update
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_parameter_update
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_Polling
</UL>

<P><STRONG><a name="[2f]"></a>USART1_IRQHandler</STRONG> (Thumb, 292 bytes, Stack size 8 bytes, bsp_uart_fifo.o(.text.USART1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = USART1_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_SendData
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ReceiveData
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ITConfig
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetITStatus
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[f6]"></a>USART_ClearFlag</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, air32f10x_usart.o(.text.USART_ClearFlag))
<BR><BR>[Called By]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_InitUart
</UL>

<P><STRONG><a name="[f5]"></a>USART_Cmd</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, air32f10x_usart.o(.text.USART_Cmd))
<BR><BR>[Called By]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_InitUart
</UL>

<P><STRONG><a name="[e4]"></a>USART_GetITStatus</STRONG> (Thumb, 50 bytes, Stack size 0 bytes, air32f10x_usart.o(.text.USART_GetITStatus))
<BR><BR>[Called By]<UL><LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comSendBuf
</UL>

<P><STRONG><a name="[e7]"></a>USART_ITConfig</STRONG> (Thumb, 46 bytes, Stack size 0 bytes, air32f10x_usart.o(.text.USART_ITConfig))
<BR><BR>[Called By]<UL><LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comSendBuf
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_InitUart
</UL>

<P><STRONG><a name="[e8]"></a>USART_Init</STRONG> (Thumb, 194 bytes, Stack size 40 bytes, air32f10x_usart.o(.text.USART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = USART_Init &rArr; RCC_GetClocksFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_GetClocksFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_InitUart
</UL>

<P><STRONG><a name="[e5]"></a>USART_ReceiveData</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, air32f10x_usart.o(.text.USART_ReceiveData))
<BR><BR>[Called By]<UL><LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
</UL>

<P><STRONG><a name="[e6]"></a>USART_SendData</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, air32f10x_usart.o(.text.USART_SendData))
<BR><BR>[Called By]<UL><LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
</UL>

<P><STRONG><a name="[dc]"></a>Update_current_status_based_on_target_progressBar</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, ui28_progress_bar.o(.text.Update_current_status_based_on_target_progressBar))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Update_current_status_based_on_target_progressBar &rArr; UI28_anima
</UL>
<BR>[Calls]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_anima
</UL>
<BR>[Called By]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_Polling
</UL>

<P><STRONG><a name="[d6]"></a>Update_the_current_image_based_on_the_target_image_Album</STRONG> (Thumb, 80 bytes, Stack size 16 bytes, ui28_rolling_album.o(.text.Update_the_current_image_based_on_the_target_image_Album))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = Update_the_current_image_based_on_the_target_image_Album &rArr; UI28_anima
</UL>
<BR>[Calls]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_anima
</UL>
<BR>[Called By]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_Polling
</UL>

<P><STRONG><a name="[d2]"></a>Update_the_current_menu_based_on_the_target_menu</STRONG> (Thumb, 172 bytes, Stack size 8 bytes, ui28_menu.o(.text.Update_the_current_menu_based_on_the_target_menu))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = Update_the_current_menu_based_on_the_target_menu &rArr; UI28_anima
</UL>
<BR>[Calls]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_anima
</UL>
<BR>[Called By]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_Polling
</UL>

<P><STRONG><a name="[e0]"></a>Update_the_target_based_on_the_cursor_menu</STRONG> (Thumb, 244 bytes, Stack size 8 bytes, ui28_menu.o(.text.Update_the_target_based_on_the_cursor_menu))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Update_the_target_based_on_the_cursor_menu
</UL>
<BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_single_step_menu
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_change_table_menu
</UL>

<P><STRONG><a name="[5]"></a>UsageFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, air32f10x_it.o(.text.UsageFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_air32f10x.o(RESET)
</UL>
<P><STRONG><a name="[96]"></a>Wa_H_from_closed_to_open</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, pro_workflow.o(.text.Wa_H_from_closed_to_open))
<BR><BR>[Calls]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_Putfifo
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handling_fifo_Events1ms
</UL>

<P><STRONG><a name="[95]"></a>Wa_H_from_open_to_closed</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, pro_workflow.o(.text.Wa_H_from_open_to_closed))
<BR><BR>[Calls]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_Putfifo
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handling_fifo_Events1ms
</UL>

<P><STRONG><a name="[98]"></a>Wa_L_from_closed_to_open</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, pro_workflow.o(.text.Wa_L_from_closed_to_open))
<BR><BR>[Calls]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_Putfifo
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handling_fifo_Events1ms
</UL>

<P><STRONG><a name="[97]"></a>Wa_L_from_open_to_closed</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, pro_workflow.o(.text.Wa_L_from_open_to_closed))
<BR><BR>[Calls]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_Putfifo
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handling_fifo_Events1ms
</UL>

<P><STRONG><a name="[10c]"></a>Washing_and_heating_output_control</STRONG> (Thumb, 406 bytes, Stack size 8 bytes, pro_workflow.o(.text.Washing_and_heating_output_control))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Washing_and_heating_output_control
</UL>
<BR>[Called By]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[10e]"></a>Washing_pump_output_control</STRONG> (Thumb, 98 bytes, Stack size 0 bytes, pro_workflow.o(.text.Washing_pump_output_control))
<BR><BR>[Called By]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[e3]"></a>alpha_blend_rgb565</STRONG> (Thumb, 90 bytes, Stack size 16 bytes, ui28.o(.text.alpha_blend_rgb565))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = alpha_blend_rgb565
</UL>
<BR>[Called By]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_update_text_gradient
</UL>

<P><STRONG><a name="[77]"></a>bsp_CheckRunTime</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, bsp_timer.o(.text.bsp_CheckRunTime))
<BR><BR>[Called By]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Backboard_data_receive
</UL>

<P><STRONG><a name="[a7]"></a>bsp_DelayMS</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, bsp_timer.o(.text.bsp_DelayMS))
<BR><BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ILI9340X_REG_Config
</UL>

<P><STRONG><a name="[eb]"></a>bsp_DetectKey</STRONG> (Thumb, 310 bytes, Stack size 24 bytes, bsp_key.o(.text.bsp_DetectKey))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = bsp_DetectKey &rArr; IsKeyDownFunc
</UL>
<BR>[Calls]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Key_subscript_logic_detection
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IsKeyDownFunc
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_Putfifo
</UL>
<BR>[Called By]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_KeyScan10ms
</UL>

<P><STRONG><a name="[78]"></a>bsp_GetRunTime</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, bsp_timer.o(.text.bsp_GetRunTime))
<BR><BR>[Called By]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Backboard_data_receive
</UL>

<P><STRONG><a name="[ec]"></a>bsp_Init</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, bsp.o(.text.bsp_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = bsp_Init &rArr; page_mgr_init &rArr; page_start_view_load &rArr; UI28_Clear_Screen &rArr; ILI9340X_Clear &rArr; ILI9340X_OpenWindow
</UL>
<BR>[Calls]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_mgr_init
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_InitTimer
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_InitSignal
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_InitKey
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_Init
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_ClkConfiguration
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHBPeriphClockCmd
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_PriorityGroupConfig
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BEEP_InitHard
</UL>
<BR>[Called By]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[f1]"></a>bsp_InitKey</STRONG> (Thumb, 234 bytes, Stack size 8 bytes, bsp_key.o(.text.bsp_InitKey))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = bsp_InitKey &rArr; bsp_InitKeyHard &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_InitKeyHard
</UL>
<BR>[Called By]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_Init
</UL>

<P><STRONG><a name="[f3]"></a>bsp_InitKeyHard</STRONG> (Thumb, 170 bytes, Stack size 24 bytes, bsp_key.o(.text.bsp_InitKeyHard))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = bsp_InitKeyHard &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphClockCmd
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_InitKey
</UL>

<P><STRONG><a name="[f2]"></a>bsp_InitSignal</STRONG> (Thumb, 90 bytes, Stack size 0 bytes, bsp_signal.o(.text.bsp_InitSignal))
<BR><BR>[Called By]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_Init
</UL>

<P><STRONG><a name="[ee]"></a>bsp_InitTimer</STRONG> (Thumb, 66 bytes, Stack size 32 bytes, bsp_timer.o(.text.bsp_InitTimer))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = bsp_InitTimer &rArr; RCC_GetClocksFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_GetClocksFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_Init
</UL>

<P><STRONG><a name="[f4]"></a>bsp_InitUart</STRONG> (Thumb, 248 bytes, Stack size 40 bytes, bsp_uart_fifo.o(.text.bsp_InitUart))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = bsp_InitUart &rArr; USART_Init &rArr; RCC_GetClocksFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Init
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ITConfig
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Cmd
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ClearFlag
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphClockCmd
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_Init
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[f8]"></a>bsp_KeyScan10ms</STRONG> (Thumb, 112 bytes, Stack size 8 bytes, bsp_key.o(.text.bsp_KeyScan10ms))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = bsp_KeyScan10ms &rArr; bsp_DetectKey &rArr; IsKeyDownFunc
</UL>
<BR>[Calls]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_DetectKey
</UL>
<BR>[Called By]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_RunPer10ms
</UL>

<P><STRONG><a name="[7f]"></a>bsp_Putfifo</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, bsp_fifo.o(.text.bsp_Putfifo))
<BR><BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Business_EE_immediate_storage_and_storage_management
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_DetectKey
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Key_subscript_logic_detection
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;keyHandle
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Wa_L_from_open_to_closed
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Wa_L_from_closed_to_open
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Wa_H_from_open_to_closed
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Wa_H_from_closed_to_open
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RI_L_from_open_to_closed
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RI_L_from_closed_to_open
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RI_H_from_open_to_closed
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RI_H_from_closed_to_open
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Door_from_open_to_closed
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Door_from_closed_to_open
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_SignalScan10ms
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ex_fault_alarm_detection_function
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Door_closing_delay_start_function
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DishwashingBusinessOperation
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DetectALARM100ms
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Business_EE_half_hour_storage_management
</UL>

<P><STRONG><a name="[c9]"></a>bsp_RunPer10ms</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, bsp.o(.text.bsp_RunPer10ms))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = bsp_RunPer10ms &rArr; bsp_KeyScan10ms &rArr; bsp_DetectKey &rArr; IsKeyDownFunc
</UL>
<BR>[Calls]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_SignalScan10ms
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_KeyScan10ms
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BEEP_Pro10ms
</UL>
<BR>[Called By]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[111]"></a>bsp_SetKeyParam</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, bsp_key.o(.text.bsp_SetKeyParam))
<BR><BR>[Called By]<UL><LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_statistics_view_load
<LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_standby_view_load
<LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_run_view_load
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_parameter_view_load
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_parameter_update
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_debug_view_load
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_check_view_load
</UL>

<P><STRONG><a name="[120]"></a>bsp_SetSignalfifotask</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, bsp_signal.o(.text.bsp_SetSignalfifotask))
<BR><BR>[Called By]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pro_signal_input
</UL>

<P><STRONG><a name="[f9]"></a>bsp_SignalScan10ms</STRONG> (Thumb, 230 bytes, Stack size 16 bytes, bsp_signal.o(.text.bsp_SignalScan10ms))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = bsp_SignalScan10ms
</UL>
<BR>[Calls]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_Putfifo
</UL>
<BR>[Called By]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_RunPer10ms
</UL>

<P><STRONG><a name="[fb]"></a>bsp_TempProbeScan1ms</STRONG> (Thumb, 584 bytes, Stack size 16 bytes, bsp_temp.o(.text.bsp_TempProbeScan1ms))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = bsp_TempProbeScan1ms
</UL>
<BR>[Calls]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;filter1Step
</UL>
<BR>[Called By]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[ba]"></a>comClearRxFifo</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, bsp_uart_fifo.o(.text.comClearRxFifo))
<BR><BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QR_code_byte_stream_processing
</UL>

<P><STRONG><a name="[b9]"></a>comClearTxFifo</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, bsp_uart_fifo.o(.text.comClearTxFifo))
<BR><BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QR_code_byte_stream_processing
</UL>

<P><STRONG><a name="[75]"></a>comGetChar</STRONG> (Thumb, 76 bytes, Stack size 0 bytes, bsp_uart_fifo.o(.text.comGetChar))
<BR><BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReceiveQRcode
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Backboard_data_receive
</UL>

<P><STRONG><a name="[7a]"></a>comSendBuf</STRONG> (Thumb, 182 bytes, Stack size 32 bytes, bsp_uart_fifo.o(.text.comSendBuf))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = comSendBuf
</UL>
<BR>[Calls]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ITConfig
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetITStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QR_code_byte_stream_processing
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;encrypt_decrypt
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Backboard_data_receive
</UL>

<P><STRONG><a name="[fd]"></a>encrypt_decrypt</STRONG> (Thumb, 486 bytes, Stack size 24 bytes, main.o(.text.encrypt_decrypt))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = encrypt_decrypt &rArr; comSendBuf
</UL>
<BR>[Calls]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comSendBuf
</UL>
<BR>[Called By]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[fc]"></a>filter1Step</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, bsp_utility.o(.text.filter1Step))
<BR><BR>[Called By]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_TempProbeScan1ms
</UL>

<P><STRONG><a name="[92]"></a>keyHandle</STRONG> (Thumb, 2656 bytes, Stack size 16 bytes, pro_pagekey.o(.text.keyHandle))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = keyHandle &rArr; Init_parame_level
</UL>
<BR>[Calls]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_single_step_menu
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_single_step_album
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_init_text_gradient_alpha
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_change_table_menu
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_parame_level
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_goto
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;From_suspending_business_to_starting
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DishwashingCloseBusiness
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parame_adj
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_Putfifo
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handling_fifo_Events1ms
</UL>

<P><STRONG><a name="[49]"></a>main</STRONG> (Thumb, 216 bytes, Stack size 0 bytes, main.o(.text.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 240<LI>Call Chain = main &rArr; UI28_Polling &rArr; UI28_update_menu &rArr; ILI9340X_TwoColorChart_XYReversed &rArr; ILI9340X_OpenWindow
</UL>
<BR>[Calls]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pro_signal_output
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pro_signal_input
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_current_update
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;encrypt_decrypt
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_TempProbeScan1ms
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_InitUart
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_Init
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_GetRunTime
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_CheckRunTime
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Washing_pump_output_control
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Washing_and_heating_output_control
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_Polling
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Stop_operation_management_due_to_water_shortage
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ScanDataChanges10ms
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rinse_pump_output_control
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rinse_heating_output_control
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Power_on_water_full_once_detection_function
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Power_on_screen_dwell_time
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PowerOnStorageOperation
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LockStateManagement
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Interface_no_operation_countdown_processing
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Inlet_valve_output_control
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handling_fifo_Events1ms
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ex_fault_alarm_detection_function
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Door_closing_delay_start_function
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DishwashingBusinessOperation
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Dishwasher_usage_statistics
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Detergent_output_control
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DetectALARM100ms
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Desiccant_output_control
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Countdown_processing_for_no_running_operation_on_the_running_interface
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Business_EE_half_hour_storage_management
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Backboard_data_receive
</UL>
<BR>[Address Reference Count : 1]<UL><LI> entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P><STRONG><a name="[b7]"></a>ms100_alarm_clock</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, bsp_timer.o(.text.ms100_alarm_clock))
<BR><BR>[Called By]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SaveParameData
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PowerOnStorageOperation
</UL>

<P><STRONG><a name="[60]"></a>page_check_update</STRONG> (Thumb, 850 bytes, Stack size 72 bytes, page_check.o(.text.page_check_update))
<BR><BR>[Stack]<UL><LI>Max Depth = 256<LI>Call Chain = page_check_update &rArr; UI28_draw_arc &rArr; UI28_Draw_a_table_arc &rArr; UI28_Draw_a_table_based_on_the_angle_arc
</UL>
<BR>[Calls]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_draw_arc
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PixelPageCache2D_Text_customize
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PixelPageCache2D_OpenWin_customize
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ILI9340X_TwoColorChart
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> page.o(.rodata.pa_tVar)
</UL>
<P><STRONG><a name="[5f]"></a>page_check_view_load</STRONG> (Thumb, 1484 bytes, Stack size 56 bytes, page_check.o(.text.page_check_view_load))
<BR><BR>[Stack]<UL><LI>Max Depth = 240<LI>Call Chain = page_check_view_load &rArr; UI28_draw_arc &rArr; UI28_Draw_a_table_arc &rArr; UI28_Draw_a_table_based_on_the_angle_arc
</UL>
<BR>[Calls]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_draw_arc
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_Clear_Screen
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PixelPageCache2D_Text_customize
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ILI9340X_TwoColorChart
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ILI9340X_Clear
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_SetKeyParam
</UL>
<BR>[Address Reference Count : 1]<UL><LI> page.o(.rodata.pa_tVar)
</UL>
<P><STRONG><a name="[105]"></a>page_current_update</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, page.o(.text.page_current_update))
<BR><BR>[Called By]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[5a]"></a>page_debug_update</STRONG> (Thumb, 1246 bytes, Stack size 72 bytes, page_debug.o(.text.page_debug_update))
<BR><BR>[Stack]<UL><LI>Max Depth = 256<LI>Call Chain = page_debug_update &rArr; UI28_draw_arc &rArr; UI28_Draw_a_table_arc &rArr; UI28_Draw_a_table_based_on_the_angle_arc
</UL>
<BR>[Calls]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_draw_arc
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PixelPageCache2D_Text_customize
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PixelPageCache2D_OpenWin_customize
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ILI9340X_TwoColorChart
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ILI9340X_Clear
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> page.o(.rodata.pa_tVar)
</UL>
<P><STRONG><a name="[59]"></a>page_debug_view_load</STRONG> (Thumb, 1492 bytes, Stack size 56 bytes, page_debug.o(.text.page_debug_view_load))
<BR><BR>[Stack]<UL><LI>Max Depth = 240<LI>Call Chain = page_debug_view_load &rArr; UI28_draw_arc &rArr; UI28_Draw_a_table_arc &rArr; UI28_Draw_a_table_based_on_the_angle_arc
</UL>
<BR>[Calls]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_draw_arc
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_Clear_Screen
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PixelPageCache2D_Text_customize
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ILI9340X_TwoColorChart
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ILI9340X_Clear
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_SetKeyParam
</UL>
<BR>[Address Reference Count : 1]<UL><LI> page.o(.rodata.pa_tVar)
</UL>
<P><STRONG><a name="[62]"></a>page_developer_update</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, page_developer.o(.text.page_developer_update))
<BR>[Address Reference Count : 1]<UL><LI> page.o(.rodata.pa_tVar)
</UL>
<P><STRONG><a name="[61]"></a>page_developer_view_load</STRONG> (Thumb, 134 bytes, Stack size 40 bytes, page_developer.o(.text.page_developer_view_load))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = page_developer_view_load &rArr; PixelPageCache2D_Text_customize &rArr; PixelPageCache2D_TwoColorChart_customize
</UL>
<BR>[Calls]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_Clear_Screen
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PixelPageCache2D_Text_customize
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ILI9340X_TwoColorChart
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ILI9340X_Clear
</UL>
<BR>[Address Reference Count : 1]<UL><LI> page.o(.rodata.pa_tVar)
</UL>
<P><STRONG><a name="[81]"></a>page_goto</STRONG> (Thumb, 146 bytes, Stack size 16 bytes, page.o(.text.page_goto))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = page_goto
</UL>
<BR>[Called By]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;keyHandle
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Power_on_screen_dwell_time
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Interface_no_operation_countdown_processing
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DishwashingBusinessOperation
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Countdown_processing_for_no_running_operation_on_the_running_interface
</UL>

<P><STRONG><a name="[f0]"></a>page_mgr_init</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, page.o(.text.page_mgr_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = page_mgr_init &rArr; page_start_view_load &rArr; UI28_Clear_Screen &rArr; ILI9340X_Clear &rArr; ILI9340X_OpenWindow
</UL>
<BR>[Calls]<UL><LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_start_view_load
</UL>
<BR>[Called By]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_Init
</UL>

<P><STRONG><a name="[58]"></a>page_parameter_update</STRONG> (Thumb, 3400 bytes, Stack size 40 bytes, page_parameter.o(.text.page_parameter_update))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = page_parameter_update &rArr; UI28_update_text_gradient &rArr; PixelPageCache2D_Text_customize &rArr; PixelPageCache2D_TwoColorChart_customize
</UL>
<BR>[Calls]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_update_text_gradient
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_set_up_text_gradient_add
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_display_onoff_text_gradient
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ILI9340X_Clear
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_init_text_gradient_alpha
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_SetKeyParam
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> page.o(.rodata.pa_tVar)
</UL>
<P><STRONG><a name="[57]"></a>page_parameter_view_load</STRONG> (Thumb, 136 bytes, Stack size 16 bytes, page_parameter.o(.text.page_parameter_view_load))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = page_parameter_view_load &rArr; UI28_Clear_Screen &rArr; ILI9340X_Clear &rArr; ILI9340X_OpenWindow
</UL>
<BR>[Calls]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui28_Play_Count_Animation
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_set_up_text_gradient_add
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_display_onoff_menu
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_Clear_Screen
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ILI9340X_Clear
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_change_table_menu
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_SetKeyParam
</UL>
<BR>[Address Reference Count : 1]<UL><LI> page.o(.rodata.pa_tVar)
</UL>
<P><STRONG><a name="[5c]"></a>page_qr_code_update</STRONG> (Thumb, 100 bytes, Stack size 24 bytes, page_qr_code.o(.text.page_qr_code_update))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = page_qr_code_update &rArr; UI28_update_text_gradient &rArr; PixelPageCache2D_Text_customize &rArr; PixelPageCache2D_TwoColorChart_customize
</UL>
<BR>[Calls]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_update_text_gradient
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_set_up_text_gradient_add
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ILI9340X_TwoColorChart
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_init_text_gradient_alpha
</UL>
<BR>[Address Reference Count : 1]<UL><LI> page.o(.rodata.pa_tVar)
</UL>
<P><STRONG><a name="[5b]"></a>page_qr_code_view_load</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, page_qr_code.o(.text.page_qr_code_view_load))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = page_qr_code_view_load &rArr; UI28_Clear_Screen &rArr; ILI9340X_Clear &rArr; ILI9340X_OpenWindow
</UL>
<BR>[Calls]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui28_Play_Count_Animation
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_Clear_Screen
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ILI9340X_Clear
</UL>
<BR>[Address Reference Count : 1]<UL><LI> page.o(.rodata.pa_tVar)
</UL>
<P><STRONG><a name="[54]"></a>page_run_root_load</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, page_run.o(.text.page_run_root_load))
<BR>[Address Reference Count : 1]<UL><LI> page.o(.rodata.pa_tVar)
</UL>
<P><STRONG><a name="[56]"></a>page_run_update</STRONG> (Thumb, 2304 bytes, Stack size 88 bytes, page_run.o(.text.page_run_update))
<BR><BR>[Stack]<UL><LI>Max Depth = 240<LI>Call Chain = page_run_update &rArr; UI28_update_text_gradient &rArr; PixelPageCache2D_Text_customize &rArr; PixelPageCache2D_TwoColorChart_customize
</UL>
<BR>[Calls]<UL><LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_display_onoff_album
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_change_cursor_value_album
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_change_cursor_value_ProgressBar
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_change_color_album_text_gradient
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_change_color_album
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ILI9340X_DrawRectangle
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui28_Play_Count_Animation
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_update_text_gradient
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_set_up_text_gradient_add
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_display_onoff_text_gradient
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ILI9340X_TwoColorChart
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ILI9340X_Clear
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_init_text_gradient_alpha
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Full_water_check_function
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DishwashingDeterminationOfOperatingConditions
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> page.o(.rodata.pa_tVar)
</UL>
<P><STRONG><a name="[55]"></a>page_run_view_load</STRONG> (Thumb, 692 bytes, Stack size 40 bytes, page_run.o(.text.page_run_view_load))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = page_run_view_load &rArr; UI28_update_text_gradient &rArr; PixelPageCache2D_Text_customize &rArr; PixelPageCache2D_TwoColorChart_customize
</UL>
<BR>[Calls]<UL><LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_set_synchronous_gradient_text_gradient
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_display_onoff_album
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_display_onoff_ProgressBar
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_change_color_album_text_gradient
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ILI9340X_DrawRectangle
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_update_text_gradient
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_set_up_text_gradient_add
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_Clear_Screen
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ILI9340X_TwoColorChart
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ILI9340X_Clear
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_SetKeyParam
</UL>
<BR>[Address Reference Count : 1]<UL><LI> page.o(.rodata.pa_tVar)
</UL>
<P><STRONG><a name="[51]"></a>page_standby_root_load</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, page_standby.o(.text.page_standby_root_load))
<BR>[Address Reference Count : 1]<UL><LI> page.o(.rodata.pa_tVar)
</UL>
<P><STRONG><a name="[53]"></a>page_standby_update</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, page_standby.o(.text.page_standby_update))
<BR><BR>[Calls]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_set_up_text_gradient_add
</UL>
<BR>[Address Reference Count : 1]<UL><LI> page.o(.rodata.pa_tVar)
</UL>
<P><STRONG><a name="[52]"></a>page_standby_view_load</STRONG> (Thumb, 150 bytes, Stack size 8 bytes, page_standby.o(.text.page_standby_view_load))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = page_standby_view_load &rArr; UI28_Clear_Screen &rArr; ILI9340X_Clear &rArr; ILI9340X_OpenWindow
</UL>
<BR>[Calls]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_is_the_cursor_hidden_menu
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_display_onoff_album
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_display_onoff_text_gradient
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_Clear_Screen
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DishwashingCloseBusiness
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Business_EE_immediate_storage_and_storage_management
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_SetKeyParam
</UL>
<BR>[Address Reference Count : 1]<UL><LI> page.o(.rodata.pa_tVar)
</UL>
<P><STRONG><a name="[4e]"></a>page_start_root_load</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, page_start.o(.text.page_start_root_load))
<BR>[Address Reference Count : 1]<UL><LI> page.o(.rodata.pa_tVar)
</UL>
<P><STRONG><a name="[50]"></a>page_start_update</STRONG> (Thumb, 72 bytes, Stack size 24 bytes, page_start.o(.text.page_start_update))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = page_start_update &rArr; PixelPageCache2D_Text_customize &rArr; PixelPageCache2D_TwoColorChart_customize
</UL>
<BR>[Calls]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PixelPageCache2D_Text_customize
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ILI9340X_TwoColorChart
</UL>
<BR>[Address Reference Count : 1]<UL><LI> page.o(.rodata.pa_tVar)
</UL>
<P><STRONG><a name="[4f]"></a>page_start_view_load</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, page_start.o(.text.page_start_view_load))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = page_start_view_load &rArr; UI28_Clear_Screen &rArr; ILI9340X_Clear &rArr; ILI9340X_OpenWindow
</UL>
<BR>[Calls]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_display_onoff_arc_loading
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_set_up_text_gradient_add
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_display_onoff_text_gradient
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_Clear_Screen
</UL>
<BR>[Called By]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_mgr_init
</UL>
<BR>[Address Reference Count : 1]<UL><LI> page.o(.rodata.pa_tVar)
</UL>
<P><STRONG><a name="[5e]"></a>page_statistics_update</STRONG> (Thumb, 1404 bytes, Stack size 40 bytes, page_statistics.o(.text.page_statistics_update))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = page_statistics_update &rArr; UI28_update_text_gradient &rArr; PixelPageCache2D_Text_customize &rArr; PixelPageCache2D_TwoColorChart_customize
</UL>
<BR>[Calls]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_update_text_gradient
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_set_up_text_gradient_add
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_display_onoff_text_gradient
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PixelPageCache2D_Text_customize
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ILI9340X_TwoColorChart
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ILI9340X_Clear
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_init_text_gradient_alpha
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> page.o(.rodata.pa_tVar)
</UL>
<P><STRONG><a name="[5d]"></a>page_statistics_view_load</STRONG> (Thumb, 148 bytes, Stack size 16 bytes, page_statistics.o(.text.page_statistics_view_load))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = page_statistics_view_load &rArr; UI28_Clear_Screen &rArr; ILI9340X_Clear &rArr; ILI9340X_OpenWindow
</UL>
<BR>[Calls]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_is_the_cursor_hidden_menu
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui28_Play_Count_Animation
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_set_up_text_gradient_add
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_display_onoff_menu
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_Clear_Screen
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ILI9340X_Clear
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_change_table_menu
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Business_EE_immediate_storage_and_storage_management
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_SetKeyParam
</UL>
<BR>[Address Reference Count : 1]<UL><LI> page.o(.rodata.pa_tVar)
</UL>
<P><STRONG><a name="[84]"></a>parame_adj</STRONG> (Thumb, 308 bytes, Stack size 20 bytes, parame.o(.text.parame_adj))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = parame_adj
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QR_code_byte_stream_processing
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;keyHandle
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Dishwasher_usage_statistics
</UL>

<P><STRONG><a name="[103]"></a>pro_signal_input</STRONG> (Thumb, 200 bytes, Stack size 16 bytes, pro_hardware.o(.text.pro_signal_input))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = pro_signal_input
</UL>
<BR>[Calls]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_SetSignalfifotask
</UL>
<BR>[Called By]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[104]"></a>pro_signal_output</STRONG> (Thumb, 386 bytes, Stack size 8 bytes, pro_hardware.o(.text.pro_signal_output))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = pro_signal_output
</UL>
<BR>[Called By]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[d3]"></a>ui28_Init_Menu</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, ui28_menu.o(.text.ui28_Init_Menu))
<BR><BR>[Called By]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_Polling
</UL>

<P><STRONG><a name="[ce]"></a>ui28_Init_Menu_Engineering</STRONG> (Thumb, 3348 bytes, Stack size 20 bytes, ui28_menu.o(.text.ui28_Init_Menu_Engineering))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = ui28_Init_Menu_Engineering
</UL>
<BR>[Called By]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_Init
</UL>

<P><STRONG><a name="[db]"></a>ui28_Init_ProgressBar</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, ui28_progress_bar.o(.text.ui28_Init_ProgressBar))
<BR><BR>[Called By]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_Polling
</UL>

<P><STRONG><a name="[d0]"></a>ui28_Init_ProgressBar_Engineering</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, ui28_progress_bar.o(.text.ui28_Init_ProgressBar_Engineering))
<BR><BR>[Called By]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_Init
</UL>

<P><STRONG><a name="[d5]"></a>ui28_Init_Rolling_Album</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, ui28_rolling_album.o(.text.ui28_Init_Rolling_Album))
<BR><BR>[Called By]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_Polling
</UL>

<P><STRONG><a name="[cf]"></a>ui28_Init_Rolling_Album_Engineering</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, ui28_rolling_album.o(.text.ui28_Init_Rolling_Album_Engineering))
<BR><BR>[Called By]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UI28_Init
</UL>

<P><STRONG><a name="[114]"></a>ui28_Play_Count_Animation</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, ui28_animation.o(.text.ui28_Play_Count_Animation))
<BR><BR>[Called By]<UL><LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_statistics_view_load
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_run_update
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_qr_code_view_load
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_parameter_view_load
</UL>

<P><STRONG><a name="[121]"></a>__0sprintf$6</STRONG> (Thumb, 34 bytes, Stack size 24 bytes, printf6.o(i.__0sprintf$6), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sputc
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[135]"></a>__1sprintf$6</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printf6.o(i.__0sprintf$6), UNUSED)

<P><STRONG><a name="[10f]"></a>__2sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printf6.o(i.__0sprintf$6))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_statistics_update
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_run_update
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_parameter_update
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_debug_update
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_check_update
</UL>

<P><STRONG><a name="[136]"></a>__scatterload_copy</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_copy), UNUSED)

<P><STRONG><a name="[137]"></a>__scatterload_null</STRONG> (Thumb, 2 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_null), UNUSED)

<P><STRONG><a name="[138]"></a>__scatterload_zeroinit</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_zeroinit), UNUSED)
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[7d]"></a>__NVIC_SystemReset</STRONG> (Thumb, 42 bytes, Stack size 0 bytes, bsp_boot.o(.text.__NVIC_SystemReset))
<BR><BR>[Called By]<UL><LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BootAppReceiveDataProcessing
</UL>

<P><STRONG><a name="[122]"></a>_printf_core</STRONG> (Thumb, 748 bytes, Stack size 96 bytes, printf6.o(i._printf_core), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidivmod
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
</UL>
<BR>[Called By]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sprintf$6
</UL>

<P><STRONG><a name="[125]"></a>_printf_post_padding</STRONG> (Thumb, 36 bytes, Stack size 24 bytes, printf6.o(i._printf_post_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[123]"></a>_printf_pre_padding</STRONG> (Thumb, 46 bytes, Stack size 24 bytes, printf6.o(i._printf_pre_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[4d]"></a>_sputc</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, printf6.o(i._sputc))
<BR><BR>[Called By]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sprintf$6
</UL>
<BR>[Address Reference Count : 1]<UL><LI> printf6.o(i.__0sprintf$6)
</UL><P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
