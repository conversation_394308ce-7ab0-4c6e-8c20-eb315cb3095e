#include "bsp.h"
#if KEY_SWITCH
/* 使能GPIO时钟 */
#define ALL_KEY_GPIO_CLK_ENABLE()                                               \
	{                                                                           \
		RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA, ENABLE); /*使能GPIOA时钟*/ \
		RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOB, ENABLE); /*使能GPIOA时钟*/ \
	};

/* 依次定义GPIO */
typedef struct
{
	GPIO_TypeDef *gpio;
	uint16_t pin;
	uint8_t position; /* 位置 */
} X_GPIO_T;

/* GPIO和PIN定义 */
const X_GPIO_T row_gpio_list[] =
	{
		/* 行 */
		{GPIOB, GPIO_Pin_14, 0}, /* 行1 */
		{GPIOA, GPIO_Pin_11, 1}, /* 行2 */
		{GPIOA, GPIO_Pin_12, 3}, /* 行3 */
		{GPIOB, GPIO_Pin_9, 4},	 /* 行4 */
		{GPIOB, GPIO_Pin_8, 5},	 /* 行5 */
};
const X_GPIO_T col_gpio_list[] =
	{
		/* 列 */
		{GPIOB, GPIO_Pin_13, 0}, /* 列1 */
		{GPIOB, GPIO_Pin_12, 1}, /* 列2 */
};

#define KEY_ROW_NUM (sizeof(row_gpio_list) / sizeof(X_GPIO_T)) /* 行个数 */
#define KEY_COL_NUM (sizeof(col_gpio_list) / sizeof(X_GPIO_T)) /* 列个数 */

/*
*********************************************************************************************************
*	函 数 名: bsp_InitKeyHard
*	形    参: 无
*	返 回 值: 无
*	功能说明: 配置按键对应的GPIO
*********************************************************************************************************
*/
void bsp_InitKeyHard(void)
{
	GPIO_InitTypeDef gpio_init;
	uint8_t i;

	/* 第1步：打开GPIO时钟 */
	ALL_KEY_GPIO_CLK_ENABLE();

	/* 第2步：配置所有列GPIO为输出模式 */
	gpio_init.GPIO_Mode = GPIO_Mode_Out_PP; /* 设置推挽输出模式 */
	gpio_init.GPIO_Speed = GPIO_Speed_2MHz; /* GPIO速度等级 */

	for (i = 0; i < KEY_COL_NUM; i++)
	{
		gpio_init.GPIO_Pin = col_gpio_list[i].pin;
		GPIO_Init(col_gpio_list[i].gpio, &gpio_init);
		GPIO_SetBits(col_gpio_list[i].gpio, col_gpio_list[i].pin);
	}

	/* 第3步：配置所有行GPIO为输入模式 */
	gpio_init.GPIO_Mode = GPIO_Mode_IPU;	/* 设置上拉输入 */
	gpio_init.GPIO_Speed = GPIO_Speed_2MHz; /* GPIO速度等级 */

	for (i = 0; i < KEY_ROW_NUM; i++)
	{
		gpio_init.GPIO_Pin = row_gpio_list[i].pin;
		GPIO_Init(row_gpio_list[i].gpio, &gpio_init);
	}
}

/*
*********************************************************************************************************
*	函 数 名: IsKeyDownFunc
*	形    参: _id：键值ID
*	返 回 值: 1： 表示按下(导通） 0：表示未按下（释放）
*	功能说明: 判断按键是否按下。单键和组合键区分。单键事件不允许有其他键按下。
*********************************************************************************************************
*/
uint8_t IsKeyDownFunc(uint8_t _id)
{
	uint16_t i, j;
	uint16_t KEY_Cache = 0; /* 按键状态缓存，低位到高位按键递增，1表示按下 */

	/* 根据实际硬件实现所有按键键值的读取，只需要实现读取键值就行 */
	for (i = 0; i < KEY_COL_NUM; i++)
	{ /* 选中列 */
		GPIO_ResetBits(col_gpio_list[i].gpio, col_gpio_list[i].pin);
// {
//     __IO uint32_t aa = 1000;
//     while(aa--);
// }
		for (j = 0; j < KEY_ROW_NUM; j++)
		{ /* 读取行 */
			if (GPIO_ReadInputDataBit(row_gpio_list[j].gpio, row_gpio_list[j].pin) == Bit_RESET)
			{
				KEY_Cache |= (1UL << ((i * KEY_ROW_NUM) + j));
			}
		}
		/* 复位列 */
		GPIO_SetBits(col_gpio_list[i].gpio, col_gpio_list[i].pin);
	}

	switch (_id)
	{
#define KEY_DEBUG_X(name, help, LTime, RSpeed) \
	case KID_##name:                           \
		if (KEY_Cache == help)                 \
		{                                      \
			return 1;                          \
		}                                      \
		break;
		KEY_ITEM_LIST
#undef KEY_DEBUG_X
	default:
		/* 其它值不处理 */
		return 2;
	}
	if (KEY_Cache == 0)
	{
		return 0;
	}
	else
	{
		return 2;
	}
}

volatile key_t s_tBtn[KEY_ITEM_NUM] = {0}; /* 按键结构体 */

/*
*********************************************************************************************************
*	函 数 名: bsp_InitKeyVar
*	形    参: 无
*	返 回 值: 无
*	功能说明: 初始化按键变量
*********************************************************************************************************
*/
void bsp_InitKeyVar(void)
{
	uint8_t i;

	/* 给每个按键结构体成员变量赋一组缺省值 */
	for (i = 0; i < KEY_ITEM_NUM; i++)
	{
		s_tBtn[i].LongCount = 0;			   /* 长按计数器 */
		s_tBtn[i].Count = KEY_FILTER_TIME / 2; /* 计数器设置为滤波时间的一半 */
		s_tBtn[i].State = 0;				   /* 按键缺省状态，0为未按下 */
	}

/* 如果需要单独更改某个按键的参数，可以在此单独重新赋值 */
#define KEY_DEBUG_X(name, help, LTime, RSpeed) bsp_SetKeyParam(KID_##name, LTime, RSpeed);
	KEY_ITEM_LIST
#undef KEY_DEBUG_X
}

/*
*********************************************************************************************************
*	函 数 名: bsp_SetKeyParam
*	形    参: _ucKeyID : 按键ID，从0开始
*	形    参: _LongTime : 长按事件时间
*	形    参: _RepeatSpeed : 连发速度
*	返 回 值: 无
*	功能说明: 设置按键参数
*********************************************************************************************************
*/
void bsp_SetKeyParam(uint8_t _ucKeyID, uint16_t _LongTime, uint8_t _RepeatSpeed)
{
	s_tBtn[_ucKeyID].LongTime = _LongTime;		 /* 长按时间 0 表示不检测长按键事件 */
	s_tBtn[_ucKeyID].RepeatSpeed = _RepeatSpeed; /* 按键连发的速度，0表示不支持连发 */
	s_tBtn[_ucKeyID].RepeatCount = 0;			 /* 连发计数器 */
}

/*
*********************************************************************************************************
*	函 数 名: bsp_GetKeyState
*	形    参: _ucKeyID : 按键ID，从0开始
*	返 回 值: 1 表示按下， 0 表示未按下
*	功能说明: 读取按键的状态
*********************************************************************************************************
*/
uint8_t bsp_GetKeyState(uint8_t _ucKeyID)
{
	return s_tBtn[_ucKeyID].State;
}

#if SEQUENTIAL_KEY_SWITCH
volatile seq_key_t seq_tBtn[KEY_SEQ_ITEM_NUM] = {0}; /* 顺序按键结构体 */

/*
  顺序按键固定参数在此添加
*/
#define KEY_SEQ_DEBUG_X(name, ...) const uint8_t Fixed_sequence_##name[] = __VA_ARGS__;
KEY_SEQ_ITEM_LIST
#undef KEY_SEQ_DEBUG_X

/*
*********************************************************************************************************
*	函 数 名: Sequential_Key_Init
*	形    参: 无
*	返 回 值: 无
*	功能说明: 初始化顺序按键变量
*********************************************************************************************************
*/
void Sequential_Key_Init(void)
{
	uint8_t i;

	/* 给每个按键结构体成员变量赋一组缺省值 */
	for (i = 0; i < KEY_SEQ_ITEM_NUM; i++)
	{
		seq_tBtn[i].count = 0;			  /* 顺序按键时间计数 */
		seq_tBtn[i].subscript_record = 0; /* 顺序按键下标记录 */
	}
	/* 如果需要单独更改某个按键的参数，可以在此单独重新赋值 */
#define KEY_SEQ_DEBUG_X(name, ...) seq_tBtn[KID_SEQ_##name].fixed = &Fixed_sequence_##name[0]; /* 按键顺序固定参数指针 */
	KEY_SEQ_ITEM_LIST
#undef KEY_SEQ_DEBUG_X

#define KEY_SEQ_DEBUG_X(name, ...) seq_tBtn[KID_SEQ_##name].NUM = (sizeof(Fixed_sequence_##name) / sizeof(uint8_t)); /* 顺序按键固定参数的个数 */
	KEY_SEQ_ITEM_LIST
#undef KEY_SEQ_DEBUG_X
}

/*
*********************************************************************************************************
*	函 数 名: Key_subscript_logic_detection
*	形    参: key_：成功触发的键值
*	返 回 值: 0：无顺序键值触发；非0：有顺序键值触发
*	功能说明: 顺序按键下标逻辑检测
*********************************************************************************************************
*/
uint8_t Key_subscript_logic_detection(uint8_t key_)
{
	uint8_t i = 0;
	uint8_t _ucKeyID;
	uint8_t const *p;
	for (_ucKeyID = 0; _ucKeyID < KEY_SEQ_ITEM_NUM; _ucKeyID++)
	{
		p = seq_tBtn[_ucKeyID].fixed;
		if (seq_tBtn[_ucKeyID].count > KEY_INTERVAL_TIME) /* 顺序按键直接间隔超过的话 */
		{
			seq_tBtn[_ucKeyID].count = 0;			 /* 按键计时重新开始 */
			seq_tBtn[_ucKeyID].subscript_record = 0; /* 顺序按键下标回到第一位 */
		}
		if (key_ == p[seq_tBtn[_ucKeyID].subscript_record]) /* 如果此时键值等于固定参数下标所对应值 */
		{
			if (seq_tBtn[_ucKeyID].subscript_record == (seq_tBtn[_ucKeyID].NUM - 1)) /* 如果顺序到了最后一个下标值 */
			{
				seq_tBtn[_ucKeyID].count = 0;					 /* 按键计时重新开始 */
				seq_tBtn[_ucKeyID].subscript_record = 0;		 /* 顺序按键下标回到第一位 */
				bsp_Putfifo((KEY_ITEM_NUM << 2) + 1 + _ucKeyID); /* 键值顺序按键放入按键FIFO */
				i = 1;
			}
			else
			{
				seq_tBtn[_ucKeyID].subscript_record++; /* 顺序正确没到最后一个下标值，下标++ */
			}
		}
		else /* 如果此时键值不等于固定参数下标所对应的值 */
		{
			uint8_t Return_index = seq_tBtn[_ucKeyID].subscript_record; /* 要返回的下标 */
			uint8_t Compare_subscripts_1 = 0;							/* 对比下标1 */
			uint8_t Compare_subscripts_2 = 0;							/* 对比下标2 */

			while (Return_index)
			{
				if (key_ == p[Return_index - 1])
				{
					Compare_subscripts_1 = seq_tBtn[_ucKeyID].subscript_record - 1;
					Compare_subscripts_2 = Return_index - 2;

					while (Compare_subscripts_1 > Compare_subscripts_2)
					{
						if (p[Compare_subscripts_1] == p[Compare_subscripts_2])
						{
							Compare_subscripts_1--;
							Compare_subscripts_2--;
						}
						else
						{
							break;
						}
					}
				}
				if (Compare_subscripts_1 < Compare_subscripts_2)
				{
					break;
				}
				Return_index--;
			}

			seq_tBtn[_ucKeyID].subscript_record = Return_index; /* 顺序按键下标回到 */
		}
	}
	return i;
}

/*
*********************************************************************************************************
*	函 数 名: Sequence_key_interval_Temporal_logic
*	形    参: 无
*	返 回 值: 无
*	功能说明: 顺序按键间隔时间逻辑。非阻塞，被10ms一次周期性的调用
*********************************************************************************************************
*/
void Sequence_key_interval_Temporal_logic(void)
{
	uint8_t _ucKeyID;
	for (_ucKeyID = 0; _ucKeyID < KEY_SEQ_ITEM_NUM; _ucKeyID++)
	{
		if (seq_tBtn[_ucKeyID].count <= KEY_INTERVAL_TIME)
		{
			if (seq_tBtn[_ucKeyID].subscript_record != 0) /* 顺序按键下标记录 */
			{
				seq_tBtn[_ucKeyID].count++;
			}
		}
	}
}
#endif

#if MULTIPLE_KEY_STROKES

/* 多击按键索引表 */
const uint8_t multiple_key_list[KEY_MUL_ITEM_NUM][2] =
	{
#define KEY_MUL_DEBUG_X(name, KID_K_, NUM_) {KID_K_, NUM_},
		KEY_MUL_ITEM_LIST
#undef KEY_MUL_DEBUG_X
};

volatile mul_key_t m_tBtn[KEY_ITEM_NUM] = {0}; /* 多击按键结构体 */

/*
*********************************************************************************************************
*	函 数 名: multiple_Key_Init
*	形    参: 无
*	返 回 值: 无
*	功能说明: 初始化多击按键变量
*********************************************************************************************************
*/
void multiple_Key_Init(void)
{
	uint8_t i;

	/* 关闭所有多击按键 */
	for (i = 0; i < KEY_ITEM_NUM; i++)
	{
		m_tBtn[i].EnableFlag = 0; /* 使能标志 */
		m_tBtn[i].DelayCount = 0; /* 延迟计数器，用于多击检测 */
		m_tBtn[i].ClickCount = 0; /* 单击次数计数 */
	}

	/* 根据索引表使能 */
	for (i = 0; i < KEY_MUL_ITEM_NUM; i++)
	{
		m_tBtn[multiple_key_list[i][0]].EnableFlag = 1; /* 使能标志 */
	}
}

#endif

/*
*********************************************************************************************************
*	函 数 名: bsp_DetectKey
*	形    参: IO的id， 从0开始编码
*	返 回 值: 无
*	功能说明: 检测一个按键。非阻塞状态，必须被周期性的调用
*********************************************************************************************************
*/
void bsp_DetectKey(uint8_t i)
{
	static uint8_t KEYLONGflag = 0; /*该标志用以防止组合按键先后释放与长按连发误触其他按键（全部按键抬起有效），导致再次发送一次单按键的消息,为0执行抬起任务才能执行*/
	volatile key_t *pBtn;
	pBtn = &s_tBtn[i];		   /* 读取相应按键的结构体地址，程序里面每个按键都有自己的结构体 */
	if (1 == IsKeyDownFunc(i)) /* 如果对应按键按下 */
	{
		if (pBtn->Count < KEY_FILTER_TIME) /* 如果滤波器计数器小于设定的滤波时间 */
		{
			pBtn->Count = KEY_FILTER_TIME; /* 将滤波器计数器设置等于设定的滤波时间 */
		}
		else if (pBtn->Count < 2 * KEY_FILTER_TIME) /* 如果滤波器计数器小于两倍设定的滤波时间 */
		{
			pBtn->Count++; /* 滤波器计数器+1 */
		}
		else /* 如果波器计数器大于两倍设定的滤波时间 */
		{
			if (pBtn->State == 0 && KEYLONGflag == 0) /* 如果对应按键原来是弹起并且长按或组合键已经释放 */
			{
				pBtn->State = 1; /* 将对应的按键设定为按下 */
				/* 发送按钮按下的消息 */
				bsp_Putfifo((uint8_t)(4 * i + 1));
			}
			if (pBtn->LongTime > 0 && pBtn->State == 1) /* 如果设定了按键长按持续时间大于0（需要检测长按），并且当前为按下状态（防止上个长按未结束就进入组合按键） */
			{
				if (pBtn->LongCount < pBtn->LongTime) /* 如果长按计数器小于长按持续时间 */
				{
					/* 发送按钮持续按下的消息 */
					if (++pBtn->LongCount == pBtn->LongTime) /* 长按计数器+1之后判断是否到达长按持续时间 */
					{
						/* 如果到达长按持续时间 */
						/* 键值长按放入按键FIFO */
						bsp_Putfifo((uint8_t)(4 * i + 4));
						KEYLONGflag = 1; /* 标志位置1，防止抬起任务执行 */
					}
				}
				else /* 如果长按计数器大于长按持续时间（目前处于长按状态下） */
				{
					if (pBtn->RepeatSpeed > 0) /* 如果设定了连续按键周期 */
					{
						if (++pBtn->RepeatCount >= pBtn->RepeatSpeed) /* 连续按键计数器+1，之后再次判断是否超过了连续按键周期 */
						{
							/* 如果超过设定的连按周期，计数器归零 */
							pBtn->RepeatCount = 0;
							/* 长按键后，每隔10ms*RepeatSpeed发送1个按键 */
							bsp_Putfifo((uint8_t)(4 * i + 3));
						}
					}
				}
			}
		}
	}
	else if (0 == IsKeyDownFunc(i)) /* 如果对应按键未按下 */
	{
		if (KEYLONGflag == 1) /* 如果原先长按或组合键未释放 */
		{
			if (pBtn->State == 1) /* 如果对应按键原来是按下 */
			{
				pBtn->State = 0; /* 将对应的按键设定为抬起 */

				pBtn->LongCount = 0;   /* 长按计数器归零 */
				pBtn->RepeatCount = 0; /* 连续按键计数器归零 */
				KEYLONGflag = 0;	   /* 将现在状态设置成长按与组合键已经全部释放 */
			}
		}
		else /* 原先长按与组合键已经全部释放并且现在按键是未按下状态 */
		{
			if (pBtn->Count > KEY_FILTER_TIME) /* 如果滤波器计数器大于设定的滤波时间 */
			{
				pBtn->Count = KEY_FILTER_TIME; /* 将滤波器计数器设置等于设定的滤波时间 */
			}
			else if (pBtn->Count != 0) /* 如果滤波器计数器不等于0 */
			{
				pBtn->Count--; /* 将滤波器计数器-1 */
			}
			else /* 如果滤波器计数器==0（判断已经完全抬起了） */
			{
				if (pBtn->State == 1) /* 如果对应按键原来是按下并且检测按键是真的抬起 */
				{
					pBtn->State = 0;	   /* 将对应的按键设定为抬起 */
					pBtn->LongCount = 0;   /* 长按计数器归零 */
					pBtn->RepeatCount = 0; /* 连续按键计数器归零 */
#if MULTIPLE_KEY_STROKES
					if (0 == m_tBtn[i].EnableFlag /* 如果未使能 */)
					{
#if SEQUENTIAL_KEY_SWITCH
						/* 顺序按键下标逻辑检测 */
						if (Key_subscript_logic_detection(i) == 0)
#endif
						{
							/* 发送按钮弹起的消息 */
							bsp_Putfifo((uint8_t)(4 * i + 2));
						}
						return;
					}
					else
					{
						m_tBtn[i].ClickCount++;				 /* 单击次数++ */
						m_tBtn[i].DelayCount = KEY_MUL_TIME; /* 多击检测计数器重新开始倒计时 */
					}
#else
#if SEQUENTIAL_KEY_SWITCH
					/* 顺序按键下标逻辑检测 */
					if (Key_subscript_logic_detection(i) == 0)
#endif
					{
						/* 发送按钮弹起的消息 */
						bsp_Putfifo((i << 2) + 2);
					}
#endif
				}
#if MULTIPLE_KEY_STROKES
				if (m_tBtn[i].DelayCount) /* 如果大于0，才能递减，多击检测计数器一定会减为0。 */
				{
					m_tBtn[i].DelayCount--;
					if (m_tBtn[i].DelayCount == 0) /* 如果减到0了，说明按键多击没有按下了 */
					{
						if (m_tBtn[i].ClickCount > 0) /* 如果已经确认产生了单击或多击 */
						{
							if (m_tBtn[i].ClickCount == 1) /* 如果只连击了一下 */
							{
#if SEQUENTIAL_KEY_SWITCH
								/* 顺序按键下标逻辑检测 */
								if (Key_subscript_logic_detection(i) == 0)
#endif
								{
									/* 发送按钮弹起的消息 */
									bsp_Putfifo((uint8_t)(4 * i + 2));
								}
							}
							else if (m_tBtn[i].ClickCount > 1) /* 如果连击了多次以上 */
							{								   /* 查表发送多击任务 */
								uint8_t a;
								for (a = 0; a < KEY_MUL_ITEM_NUM; a++)
								{
									if (multiple_key_list[a][0] == i /* id匹配 */ && multiple_key_list[a][1] == m_tBtn[i].ClickCount /* 连击数匹配 */)
									{
										/* 发送按钮弹起的消息 */
#if SEQUENTIAL_KEY_SWITCH
										bsp_Putfifo((uint8_t)(4 * KEY_ITEM_NUM + KEY_SEQ_ITEM_NUM + a + 1)); /* 键值多击按键放入按键FIFO */
#else
										bsp_Putfifo((uint8_t)(4 * KEY_ITEM_NUM + a + 1)); /* 键值多击按键放入按键FIFO */
#endif
									}
								}
							}
							m_tBtn[i].ClickCount = 0; /* 记录按键次数的变量，必须被清除 */
						}
					}
				}
#endif
			}
		}
	}
	else // 无效值跳过
	{
		;
	}
}

/*
*********************************************************************************************************
*	函 数 名: bsp_InitKey
*	形    参: 无
*	返 回 值: 无
*	功能说明: 初始化按键
*********************************************************************************************************
*/
void bsp_InitKey(void)
{
	bsp_InitKeyHard(); /* 配置按键对应的GPIO */
	bsp_InitKeyVar();  /* 初始化按键变量 */
#if SEQUENTIAL_KEY_SWITCH
	Sequential_Key_Init(); /* 初始化顺序按键变量 */
#endif
#if MULTIPLE_KEY_STROKES
	multiple_Key_Init(); /* 初始化多击按键变量 */
#endif
}

/*
*********************************************************************************************************
*	函 数 名: bsp_KeyScan10ms
*	形    参: 无
*	返 回 值: 无
*	功能说明: 扫描所有按键。非阻塞，被10ms周期性的调用
*********************************************************************************************************
*/
void bsp_KeyScan10ms(void)
{
	uint8_t i;
	for (i = 0; i < KEY_ITEM_NUM; i++)
	{
		bsp_DetectKey(i);
	}
#if SEQUENTIAL_KEY_SWITCH
	Sequence_key_interval_Temporal_logic();
#endif
}
#endif
