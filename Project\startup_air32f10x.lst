


ARM Macro Assembler    Page 1 


    1 00000000         ;******************** (C) COPYRIGHT ********************
                       
    2 00000000         ;* File Name          : startup_air32f10x.s
    3 00000000         ;* Author             : MCD Application Team
    4 00000000         ;* Version            : V3.5.0
    5 00000000         ;* Date               : 
    6 00000000         ;* Description        : air32F10x High Density Devices v
                       ector table for MDK-ARM  
    7 00000000         ;*******************************************************
                       ************************
    8 00000000         ;*******************************************************
                       ************************
    9 00000000         
   10 00000000         ; Amount of memory (in bytes) allocated for Stack
   11 00000000         ; Tailor this value to your application needs
   12 00000000         ; <h> Stack Configuration
   13 00000000         ;   <o> Stack Size (in Bytes) <0x0-0xFFFFFFFF:8>
   14 00000000         ; </h>
   15 00000000         
   16 00000000 00000400 
                       Stack_Size
                               EQU              0x00000400
   17 00000000         
   18 00000000                 AREA             STACK, NOINIT, READWRITE, ALIGN
=3
   19 00000000         Stack_Mem
                               SPACE            Stack_Size
   20 00000400         __initial_sp
   21 00000400         
   22 00000400         ; <h> Heap Configuration
   23 00000400         ;   <o>  Heap Size (in Bytes) <0x0-0xFFFFFFFF:8>
   24 00000400         ; </h>
   25 00000400         
   26 00000400 00001000 
                       Heap_Size
                               EQU              0x00001000
   27 00000400         
   28 00000400                 AREA             HEAP, NOINIT, READWRITE, ALIGN=
3
   29 00000000         __heap_base
   30 00000000         Heap_Mem
                               SPACE            Heap_Size
   31 00001000         __heap_limit
   32 00001000         
   33 00001000                 PRESERVE8
   34 00001000                 THUMB
   35 00001000         
   36 00001000         
   37 00001000         ; Vector Table Mapped to Address 0 at Reset
   38 00001000                 AREA             RESET, DATA, READONLY
   39 00000000                 EXPORT           __Vectors
   40 00000000                 EXPORT           __Vectors_End
   41 00000000                 EXPORT           __Vectors_Size
   42 00000000         
   43 00000000 00000000 
                       __Vectors
                               DCD              __initial_sp ; Top of Stack
   44 00000004 00000000        DCD              Reset_Handler ; Reset Handler
   45 00000008 00000000        DCD              NMI_Handler ; NMI Handler



ARM Macro Assembler    Page 2 


   46 0000000C 00000000        DCD              HardFault_Handler ; Hard Fault 
                                                            Handler
   47 00000010 00000000        DCD              MemManage_Handler 
                                                            ; MPU Fault Handler
                                                            
   48 00000014 00000000        DCD              BusFault_Handler 
                                                            ; Bus Fault Handler
                                                            
   49 00000018 00000000        DCD              UsageFault_Handler ; Usage Faul
                                                            t Handler
   50 0000001C 30313030        DCD              0x30313030  ; Reserved
   51 00000020 30303030        DCD              0x30303030  ; Reserved
   52 00000024 30303531        DCD              0x30303531  ; Reserved
   53 00000028 33303532        DCD              0x33303532  ; Reserved
   54 0000002C 00000000        DCD              SVC_Handler ; SVCall Handler
   55 00000030 00000000        DCD              DebugMon_Handler ; Debug Monito
                                                            r Handler
   56 00000034 00000000        DCD              0           ; Reserved
   57 00000038 00000000        DCD              PendSV_Handler ; PendSV Handler
                                                            
   58 0000003C 00000000        DCD              SysTick_Handler 
                                                            ; SysTick Handler
   59 00000040         
   60 00000040         ; External Interrupts
   61 00000040 00000000        DCD              WWDG_IRQHandler 
                                                            ; Window Watchdog
   62 00000044 00000000        DCD              PVD_IRQHandler ; PVD through EX
                                                            TI Line detect
   63 00000048 00000000        DCD              TAMPER_IRQHandler ; Tamper
   64 0000004C 00000000        DCD              RTC_IRQHandler ; RTC
   65 00000050 00000000        DCD              FLASH_IRQHandler ; Flash
   66 00000054 00000000        DCD              RCC_IRQHandler ; RCC
   67 00000058 00000000        DCD              EXTI0_IRQHandler ; EXTI Line 0
   68 0000005C 00000000        DCD              EXTI1_IRQHandler ; EXTI Line 1
   69 00000060 00000000        DCD              EXTI2_IRQHandler ; EXTI Line 2
   70 00000064 00000000        DCD              EXTI3_IRQHandler ; EXTI Line 3
   71 00000068 00000000        DCD              EXTI4_IRQHandler ; EXTI Line 4
   72 0000006C 00000000        DCD              DMA1_Channel1_IRQHandler 
                                                            ; DMA1 Channel 1
   73 00000070 00000000        DCD              DMA1_Channel2_IRQHandler 
                                                            ; DMA1 Channel 2
   74 00000074 00000000        DCD              DMA1_Channel3_IRQHandler 
                                                            ; DMA1 Channel 3
   75 00000078 00000000        DCD              DMA1_Channel4_IRQHandler 
                                                            ; DMA1 Channel 4
   76 0000007C 00000000        DCD              DMA1_Channel5_IRQHandler 
                                                            ; DMA1 Channel 5
   77 00000080 00000000        DCD              DMA1_Channel6_IRQHandler 
                                                            ; DMA1 Channel 6
   78 00000084 00000000        DCD              DMA1_Channel7_IRQHandler 
                                                            ; DMA1 Channel 7
   79 00000088 00000000        DCD              ADC1_2_IRQHandler ; ADC1 & ADC2
                                                            
   80 0000008C 00000000        DCD              USB_HP_CAN1_TX_IRQHandler ; USB
                                                             High Priority or C
                                                            AN1 TX
   81 00000090 00000000        DCD              USB_LP_CAN1_RX0_IRQHandler ; US
                                                            B Low  Priority or 
                                                            CAN1 RX0



ARM Macro Assembler    Page 3 


   82 00000094 00000000        DCD              CAN1_RX1_IRQHandler ; CAN1 RX1
   83 00000098 00000000        DCD              CAN1_SCE_IRQHandler ; CAN1 SCE
   84 0000009C 00000000        DCD              EXTI9_5_IRQHandler 
                                                            ; EXTI Line 9..5
   85 000000A0 00000000        DCD              TIM1_BRK_IRQHandler 
                                                            ; TIM1 Break
   86 000000A4 00000000        DCD              TIM1_UP_IRQHandler 
                                                            ; TIM1 Update
   87 000000A8 00000000        DCD              TIM1_TRG_COM_IRQHandler ; TIM1 
                                                            Trigger and Commuta
                                                            tion
   88 000000AC 00000000        DCD              TIM1_CC_IRQHandler ; TIM1 Captu
                                                            re Compare
   89 000000B0 00000000        DCD              TIM2_IRQHandler ; TIM2
   90 000000B4 00000000        DCD              TIM3_IRQHandler ; TIM3
   91 000000B8 00000000        DCD              TIM4_IRQHandler ; TIM4
   92 000000BC 00000000        DCD              I2C1_EV_IRQHandler ; I2C1 Event
                                                            
   93 000000C0 00000000        DCD              I2C1_ER_IRQHandler ; I2C1 Error
                                                            
   94 000000C4 00000000        DCD              I2C2_EV_IRQHandler ; I2C2 Event
                                                            
   95 000000C8 00000000        DCD              I2C2_ER_IRQHandler ; I2C2 Error
                                                            
   96 000000CC 00000000        DCD              SPI1_IRQHandler ; SPI1
   97 000000D0 00000000        DCD              SPI2_IRQHandler ; SPI2
   98 000000D4 00000000        DCD              USART1_IRQHandler ; USART1
   99 000000D8 00000000        DCD              USART2_IRQHandler ; USART2
  100 000000DC 00000000        DCD              USART3_IRQHandler ; USART3
  101 000000E0 00000000        DCD              EXTI15_10_IRQHandler 
                                                            ; EXTI Line 15..10
  102 000000E4 00000000        DCD              RTCAlarm_IRQHandler ; RTC Alarm
                                                             through EXTI Line
  103 000000E8 00000000        DCD              USBWakeUp_IRQHandler ; USB Wake
                                                            up from suspend
  104 000000EC 00000000        DCD              TIM8_BRK_IRQHandler 
                                                            ; TIM8 Break
  105 000000F0 00000000        DCD              TIM8_UP_IRQHandler 
                                                            ; TIM8 Update
  106 000000F4 00000000        DCD              TIM8_TRG_COM_IRQHandler ; TIM8 
                                                            Trigger and Commuta
                                                            tion
  107 000000F8 00000000        DCD              TIM8_CC_IRQHandler ; TIM8 Captu
                                                            re Compare
  108 000000FC 00000000        DCD              ADC3_IRQHandler ; ADC3
  109 00000100 00000000        DCD              FSMC_IRQHandler ; FSMC
  110 00000104 00000000        DCD              SDIO_IRQHandler ; SDIO
  111 00000108 00000000        DCD              TIM5_IRQHandler ; TIM5
  112 0000010C 00000000        DCD              SPI3_IRQHandler ; SPI3
  113 00000110 00000000        DCD              UART4_IRQHandler ; UART4
  114 00000114 00000000        DCD              UART5_IRQHandler ; UART5
  115 00000118 00000000        DCD              TIM6_IRQHandler ; TIM6
  116 0000011C 00000000        DCD              TIM7_IRQHandler ; TIM7
  117 00000120 00000000        DCD              DMA2_Channel1_IRQHandler 
                                                            ; DMA2 Channel1
  118 00000124 00000000        DCD              DMA2_Channel2_IRQHandler 
                                                            ; DMA2 Channel2
  119 00000128 00000000        DCD              DMA2_Channel3_IRQHandler 
                                                            ; DMA2 Channel3



ARM Macro Assembler    Page 4 


  120 0000012C 00000000        DCD              DMA2_Channel4_5_IRQHandler ; DM
                                                            A2 Channel4 & Chann
                                                            el5
  121 00000130 00000000        DCD              0           ; Reserved
  122 00000134 00000000        DCD              0           ; Reserved
  123 00000138 00000000        DCD              0           ; Reserved
  124 0000013C 00000000        DCD              0           ; Reserved
  125 00000140 00000000        DCD              0           ; Reserved 
  126 00000144 00000000        DCD              0           ; Reserved
  127 00000148 00000000        DCD              0           ; Reserved
  128 0000014C 00000000        DCD              0           ; Reserved     
  129 00000150 00000000        DCD              SYMC_IRQHandler
  130 00000154 00000000        DCD              RNG_IRQHandler
  131 00000158 00000000        DCD              SENSOR_IRQHandler
  132 0000015C 00000000        DCD              0
  133 00000160 00000000        DCD              0
  134 00000164 00000000        DCD              0
  135 00000168 00000000        DCD              0
  136 0000016C 00000000        DCD              0
  137 00000170 00000000        DCD              0
  138 00000174 00000000        DCD              0
  139 00000178 00000000        DCD              0
  140 0000017C 00000000        DCD              0
  141 00000180 00000000        DCD              0
  142 00000184 00000000        DCD              0
  143 00000188 00000000        DCD              0
  144 0000018C 00000000        DCD              0
  145 00000190 00000000        DCD              0
  146 00000194 00000000        DCD              0
  147 00000198 00000000        DCD              0
  148 0000019C 00000000        DCD              0
  149 000001A0 00000000        DCD              0
  150 000001A4 00000000        DCD              0
  151 000001A8 00000000        DCD              0
  152 000001AC 00000000        DCD              0
  153 000001B0 00000000        DCD              0
  154 000001B4 00000000        DCD              0
  155 000001B8 00000000        DCD              0
  156 000001BC 00000000        DCD              0
  157 000001C0 00000000        DCD              0
  158 000001C4 00000000        DCD              0
  159 000001C8 00000000        DCD              0
  160 000001CC 00000000        DCD              0
  161 000001D0 00000000        DCD              0
  162 000001D4 00000000        DCD              0
  163 000001D8 00000000        DCD              0
  164 000001DC 00000000        DCD              0
  165 000001E0 00000000        DCD              0
  166 000001E4 00000000        DCD              0
  167 000001E8 00000000        DCD              0
  168 000001EC 00000000        DCD              0
  169 000001F0 00000000        DCD              0
  170 000001F4 00000000        DCD              0
  171 000001F8 00000000        DCD              0
  172 000001FC 00000000        DCD              0
  173 00000200 00000000        DCD              0
  174 00000204 00000000        DCD              0
  175 00000208 00000000        DCD              0
  176 0000020C 00000000        DCD              0



ARM Macro Assembler    Page 5 


  177 00000210 00000000        DCD              0
  178 00000214 00000000        DCD              0
  179 00000218 00000000        DCD              0
  180 0000021C 00000000        DCD              0
  181 00000220         ;DCD  0X20005000
  182 00000220         ;DCD     BOOT_RAM
  183 00000220         
  184 00000220         
  185 00000220         __Vectors_End
  186 00000220         
  187 00000220 00000220 
                       __Vectors_Size
                               EQU              __Vectors_End - __Vectors
  188 00000220         
  189 00000220                 AREA             |.text|, CODE, READONLY
  190 00000000         
  191 00000000         ;
  192 00000000         BOOT_RAM
                               PROC
  193 00000000                 EXPORT           BOOT_RAM             [WEAK]
  194 00000000                 IMPORT           __main
  195 00000000                 IMPORT           SystemInit
  196 00000000 4819            LDR              R0, =SystemInit
  197 00000002 4780            BLX              R0
  198 00000004 4819            LDR              R0, =__main
  199 00000006 4700            BX               R0
  200 00000008                 ENDP
  201 00000008         
  202 00000008         ; Reset handler
  203 00000008         Reset_Handler
                               PROC
  204 00000008                 EXPORT           Reset_Handler             [WEAK
]
  205 00000008         ;unlock
  206 00000008 4819            LDR              R0,=0x400210F0
  207 0000000A F04F 0101       MOV              R1,#0x00000001
  208 0000000E 6001            STR              R1,[R0]
  209 00000010 4A18            LDR              R2,=0x40016C00
  210 00000012 4B19            LDR              R3,=0xa7d93a86
  211 00000014 6013            STR              R3,[R2]
  212 00000016 4B19            LDR              R3,=0xab12dfcd
  213 00000018 6013            STR              R3,[R2]
  214 0000001A 4B19            LDR              R3,=0xcded3526
  215 0000001C 6013            STR              R3,[R2]
  216 0000001E 4B19            LDR              R3,=0x200183FF
  217 00000020 6193            STR              R3,[R2,#0x18]
  218 00000022 4C19            LDR              R4,=0x4002228c
  219 00000024 F04F 35A5       LDR              R5,=0xa5a5a5a5
  220 00000028 6025            STR              R5,[R4]
  221 0000002A         ;lock
  222 0000002A 4A11            LDR              R2,=0x400210F0
  223 0000002C F04F 0300       LDR              R3,=0x00000000
  224 00000030 6013            STR              R3,[R2]
  225 00000032 4A10            LDR              R2,=0x40016C00
  226 00000034 4B15            LDR              R3,=0x5826c579
  227 00000036 6013            STR              R3,[R2]
  228 00000038 4B15            LDR              R3,=0x54ed2032
  229 0000003A 6013            STR              R3,[R2]
  230 0000003C 4B15            LDR              R3,=0x3212cad9



ARM Macro Assembler    Page 6 


  231 0000003E 6013            STR              R3,[R2]
  232 00000040 4A11            LDR              R2,=0x4002228c
  233 00000042 F04F 335A       LDR              R3,=0x5a5a5a5a
  234 00000046 6013            STR              R3,[R2]
  235 00000048 F04F 0100       MOV              R1,#0x00000000
  236 0000004C                 IMPORT           __main
  237 0000004C                 IMPORT           SystemInit
  238 0000004C 4806            LDR              R0, =SystemInit
  239 0000004E 4780            BLX              R0
  240 00000050 4806            LDR              R0, =__main
  241 00000052 4700            BX               R0
  242 00000054                 ENDP
  243 00000054         
  244 00000054         ; Dummy Exception Handlers (infinite loops which can be 
                       modified)
  245 00000054         
  246 00000054         NMI_Handler
                               PROC
  247 00000054                 EXPORT           NMI_Handler                [WEA
K]
  248 00000054 E7FE            B                .
  249 00000056                 ENDP
  251 00000056         HardFault_Handler
                               PROC
  252 00000056                 EXPORT           HardFault_Handler          [WEA
K]
  253 00000056 E7FE            B                .
  254 00000058                 ENDP
  256 00000058         MemManage_Handler
                               PROC
  257 00000058                 EXPORT           MemManage_Handler          [WEA
K]
  258 00000058 E7FE            B                .
  259 0000005A                 ENDP
  261 0000005A         BusFault_Handler
                               PROC
  262 0000005A                 EXPORT           BusFault_Handler           [WEA
K]
  263 0000005A E7FE            B                .
  264 0000005C                 ENDP
  266 0000005C         UsageFault_Handler
                               PROC
  267 0000005C                 EXPORT           UsageFault_Handler         [WEA
K]
  268 0000005C E7FE            B                .
  269 0000005E                 ENDP
  270 0000005E         SVC_Handler
                               PROC
  271 0000005E                 EXPORT           SVC_Handler                [WEA
K]
  272 0000005E E7FE            B                .
  273 00000060                 ENDP
  275 00000060         DebugMon_Handler
                               PROC
  276 00000060                 EXPORT           DebugMon_Handler           [WEA
K]
  277 00000060 E7FE            B                .
  278 00000062                 ENDP
  279 00000062         PendSV_Handler



ARM Macro Assembler    Page 7 


                               PROC
  280 00000062                 EXPORT           PendSV_Handler             [WEA
K]
  281 00000062 E7FE            B                .
  282 00000064                 ENDP
  283 00000064         SysTick_Handler
                               PROC
  284 00000064                 EXPORT           SysTick_Handler            [WEA
K]
  285 00000064 E7FE            B                .
  286 00000066                 ENDP
  287 00000066         
  288 00000066         Default_Handler
                               PROC
  289 00000066         
  290 00000066                 EXPORT           WWDG_IRQHandler            [WEA
K]
  291 00000066                 EXPORT           PVD_IRQHandler             [WEA
K]
  292 00000066                 EXPORT           TAMPER_IRQHandler          [WEA
K]
  293 00000066                 EXPORT           RTC_IRQHandler             [WEA
K]
  294 00000066                 EXPORT           FLASH_IRQHandler           [WEA
K]
  295 00000066                 EXPORT           RCC_IRQHandler             [WEA
K]
  296 00000066                 EXPORT           EXTI0_IRQHandler           [WEA
K]
  297 00000066                 EXPORT           EXTI1_IRQHandler           [WEA
K]
  298 00000066                 EXPORT           EXTI2_IRQHandler           [WEA
K]
  299 00000066                 EXPORT           EXTI3_IRQHandler           [WEA
K]
  300 00000066                 EXPORT           EXTI4_IRQHandler           [WEA
K]
  301 00000066                 EXPORT           DMA1_Channel1_IRQHandler   [WEA
K]
  302 00000066                 EXPORT           DMA1_Channel2_IRQHandler   [WEA
K]
  303 00000066                 EXPORT           DMA1_Channel3_IRQHandler   [WEA
K]
  304 00000066                 EXPORT           DMA1_Channel4_IRQHandler   [WEA
K]
  305 00000066                 EXPORT           DMA1_Channel5_IRQHandler   [WEA
K]
  306 00000066                 EXPORT           DMA1_Channel6_IRQHandler   [WEA
K]
  307 00000066                 EXPORT           DMA1_Channel7_IRQHandler   [WEA
K]
  308 00000066                 EXPORT           ADC1_2_IRQHandler          [WEA
K]
  309 00000066                 EXPORT           USB_HP_CAN1_TX_IRQHandler  [WEA
K]
  310 00000066                 EXPORT           USB_LP_CAN1_RX0_IRQHandler [WEA
K]
  311 00000066                 EXPORT           CAN1_RX1_IRQHandler        [WEA
K]



ARM Macro Assembler    Page 8 


  312 00000066                 EXPORT           CAN1_SCE_IRQHandler        [WEA
K]
  313 00000066                 EXPORT           EXTI9_5_IRQHandler         [WEA
K]
  314 00000066                 EXPORT           TIM1_BRK_IRQHandler        [WEA
K]
  315 00000066                 EXPORT           TIM1_UP_IRQHandler         [WEA
K]
  316 00000066                 EXPORT           TIM1_TRG_COM_IRQHandler    [WEA
K]
  317 00000066                 EXPORT           TIM1_CC_IRQHandler         [WEA
K]
  318 00000066                 EXPORT           TIM2_IRQHandler            [WEA
K]
  319 00000066                 EXPORT           TIM3_IRQHandler            [WEA
K]
  320 00000066                 EXPORT           TIM4_IRQHandler            [WEA
K]
  321 00000066                 EXPORT           I2C1_EV_IRQHandler         [WEA
K]
  322 00000066                 EXPORT           I2C1_ER_IRQHandler         [WEA
K]
  323 00000066                 EXPORT           I2C2_EV_IRQHandler         [WEA
K]
  324 00000066                 EXPORT           I2C2_ER_IRQHandler         [WEA
K]
  325 00000066                 EXPORT           SPI1_IRQHandler            [WEA
K]
  326 00000066                 EXPORT           SPI2_IRQHandler            [WEA
K]
  327 00000066                 EXPORT           USART1_IRQHandler          [WEA
K]
  328 00000066                 EXPORT           USART2_IRQHandler          [WEA
K]
  329 00000066                 EXPORT           USART3_IRQHandler          [WEA
K]
  330 00000066                 EXPORT           EXTI15_10_IRQHandler       [WEA
K]
  331 00000066                 EXPORT           RTCAlarm_IRQHandler        [WEA
K]
  332 00000066                 EXPORT           USBWakeUp_IRQHandler       [WEA
K]
  333 00000066                 EXPORT           TIM8_BRK_IRQHandler        [WEA
K]
  334 00000066                 EXPORT           TIM8_UP_IRQHandler         [WEA
K]
  335 00000066                 EXPORT           TIM8_TRG_COM_IRQHandler    [WEA
K]
  336 00000066                 EXPORT           TIM8_CC_IRQHandler         [WEA
K]
  337 00000066                 EXPORT           ADC3_IRQHandler            [WEA
K]
  338 00000066                 EXPORT           FSMC_IRQHandler            [WEA
K]
  339 00000066                 EXPORT           SDIO_IRQHandler            [WEA
K]
  340 00000066                 EXPORT           TIM5_IRQHandler            [WEA
K]
  341 00000066                 EXPORT           SPI3_IRQHandler            [WEA



ARM Macro Assembler    Page 9 


K]
  342 00000066                 EXPORT           UART4_IRQHandler           [WEA
K]
  343 00000066                 EXPORT           UART5_IRQHandler           [WEA
K]
  344 00000066                 EXPORT           TIM6_IRQHandler            [WEA
K]
  345 00000066                 EXPORT           TIM7_IRQHandler            [WEA
K]
  346 00000066                 EXPORT           DMA2_Channel1_IRQHandler   [WEA
K]
  347 00000066                 EXPORT           DMA2_Channel2_IRQHandler   [WEA
K]
  348 00000066                 EXPORT           DMA2_Channel3_IRQHandler   [WEA
K]
  349 00000066                 EXPORT           DMA2_Channel4_5_IRQHandler [WEA
K]
  350 00000066                 EXPORT           SYMC_IRQHandler      [WEAK]
  351 00000066                 EXPORT           RNG_IRQHandler      [WEAK]
  352 00000066                 EXPORT           SENSOR_IRQHandler     [WEAK]
  353 00000066         
  354 00000066         
  355 00000066         WWDG_IRQHandler
  356 00000066         PVD_IRQHandler
  357 00000066         TAMPER_IRQHandler
  358 00000066         RTC_IRQHandler
  359 00000066         FLASH_IRQHandler
  360 00000066         RCC_IRQHandler
  361 00000066         EXTI0_IRQHandler
  362 00000066         EXTI1_IRQHandler
  363 00000066         EXTI2_IRQHandler
  364 00000066         EXTI3_IRQHandler
  365 00000066         EXTI4_IRQHandler
  366 00000066         DMA1_Channel1_IRQHandler
  367 00000066         DMA1_Channel2_IRQHandler
  368 00000066         DMA1_Channel3_IRQHandler
  369 00000066         DMA1_Channel4_IRQHandler
  370 00000066         DMA1_Channel5_IRQHandler
  371 00000066         DMA1_Channel6_IRQHandler
  372 00000066         DMA1_Channel7_IRQHandler
  373 00000066         ADC1_2_IRQHandler
  374 00000066         USB_HP_CAN1_TX_IRQHandler
  375 00000066         USB_LP_CAN1_RX0_IRQHandler
  376 00000066         CAN1_RX1_IRQHandler
  377 00000066         CAN1_SCE_IRQHandler
  378 00000066         EXTI9_5_IRQHandler
  379 00000066         TIM1_BRK_IRQHandler
  380 00000066         TIM1_UP_IRQHandler
  381 00000066         TIM1_TRG_COM_IRQHandler
  382 00000066         TIM1_CC_IRQHandler
  383 00000066         TIM2_IRQHandler
  384 00000066         TIM3_IRQHandler
  385 00000066         TIM4_IRQHandler
  386 00000066         I2C1_EV_IRQHandler
  387 00000066         I2C1_ER_IRQHandler
  388 00000066         I2C2_EV_IRQHandler
  389 00000066         I2C2_ER_IRQHandler
  390 00000066         SPI1_IRQHandler
  391 00000066         SPI2_IRQHandler



ARM Macro Assembler    Page 10 


  392 00000066         USART1_IRQHandler
  393 00000066         USART2_IRQHandler
  394 00000066         USART3_IRQHandler
  395 00000066         EXTI15_10_IRQHandler
  396 00000066         RTCAlarm_IRQHandler
  397 00000066         USBWakeUp_IRQHandler
  398 00000066         TIM8_BRK_IRQHandler
  399 00000066         TIM8_UP_IRQHandler
  400 00000066         TIM8_TRG_COM_IRQHandler
  401 00000066         TIM8_CC_IRQHandler
  402 00000066         ADC3_IRQHandler
  403 00000066         FSMC_IRQHandler
  404 00000066         SDIO_IRQHandler
  405 00000066         TIM5_IRQHandler
  406 00000066         SPI3_IRQHandler
  407 00000066         UART4_IRQHandler
  408 00000066         UART5_IRQHandler
  409 00000066         TIM6_IRQHandler
  410 00000066         TIM7_IRQHandler
  411 00000066         DMA2_Channel1_IRQHandler
  412 00000066         DMA2_Channel2_IRQHandler
  413 00000066         DMA2_Channel3_IRQHandler
  414 00000066         DMA2_Channel4_5_IRQHandler
  415 00000066         SYMC_IRQHandler
  416 00000066         RNG_IRQHandler
  417 00000066         SENSOR_IRQHandler
  418 00000066         
  419 00000066 E7FE            B                .
  420 00000068         
  421 00000068                 ENDP
  422 00000068         
  423 00000068                 ALIGN
  424 00000068         
  425 00000068         ;*******************************************************
                       ************************
  426 00000068         ; User Stack and Heap initialization
  427 00000068         ;*******************************************************
                       ************************
  428 00000068                 IF               :DEF:__MICROLIB
  429 00000068         
  430 00000068                 EXPORT           __initial_sp
  431 00000068                 EXPORT           __heap_base
  432 00000068                 EXPORT           __heap_limit
  433 00000068         
  434 00000068                 ELSE
  449                          ENDIF
  450 00000068         
  451 00000068                 END
              00000000 
              00000000 
              400210F0 
              40016C00 
              A7D93A86 
              AB12DFCD 
              CDED3526 
              200183FF 
              4002228C 
              5826C579 
              54ED2032 



ARM Macro Assembler    Page 11 


              3212CAD9 
Command Line: --debug --xref --diag_suppress=9931 --cpu=Cortex-M3 --depend=.\ob
j\startup_air32f10x.d -o.\obj\startup_air32f10x.o -I.\RTE\_Target_1 -ID:\Users\
mumu\AppData\Local\Arm\Packs\Keil\AIR32F103_DFP\1.1.9\Device\Include -ID:\Keil_
v5\ARM\CMSIS\Include --predefine="__MICROLIB SETA 1" --predefine="__UVISION_VER
SION SETA 525" --predefine="AIR32F10X_MD SETA 1" --list=.\startup_air32f10x.lst
 ..\Libraries\STARTUP\arm\startup_air32f10x.s



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

STACK 00000000

Symbol: STACK
   Definitions
      At line 18 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      None
Comment: STACK unused
Stack_Mem 00000000

Symbol: Stack_Mem
   Definitions
      At line 19 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      None
Comment: Stack_Mem unused
__initial_sp 00000400

Symbol: __initial_sp
   Definitions
      At line 20 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 43 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 430 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

HEAP 00000000

Symbol: HEAP
   Definitions
      At line 28 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      None
Comment: HEAP unused
Heap_Mem 00000000

Symbol: Heap_Mem
   Definitions
      At line 30 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      None
Comment: Heap_Mem unused
__heap_base 00000000

Symbol: __heap_base
   Definitions
      At line 29 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 431 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
Comment: __heap_base used once
__heap_limit 00001000

Symbol: __heap_limit
   Definitions
      At line 31 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 432 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
Comment: __heap_limit used once
4 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

RESET 00000000

Symbol: RESET
   Definitions
      At line 38 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      None
Comment: RESET unused
__Vectors 00000000

Symbol: __Vectors
   Definitions
      At line 43 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 39 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 187 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

__Vectors_End 00000220

Symbol: __Vectors_End
   Definitions
      At line 185 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 40 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 187 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

.text 00000000

Symbol: .text
   Definitions
      At line 189 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      None
Comment: .text unused
ADC1_2_IRQHandler 00000066

Symbol: ADC1_2_IRQHandler
   Definitions
      At line 373 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 79 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 308 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

ADC3_IRQHandler 00000066

Symbol: ADC3_IRQHandler
   Definitions
      At line 402 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 108 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 337 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

BOOT_RAM 00000000

Symbol: BOOT_RAM
   Definitions
      At line 192 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 193 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
Comment: BOOT_RAM used once
BusFault_Handler 0000005A

Symbol: BusFault_Handler
   Definitions
      At line 261 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 48 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 262 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

CAN1_RX1_IRQHandler 00000066

Symbol: CAN1_RX1_IRQHandler
   Definitions
      At line 376 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 82 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 311 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

CAN1_SCE_IRQHandler 00000066

Symbol: CAN1_SCE_IRQHandler
   Definitions
      At line 377 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 83 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s



ARM Macro Assembler    Page 2 Alphabetic symbol ordering
Relocatable symbols

      At line 312 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

DMA1_Channel1_IRQHandler 00000066

Symbol: DMA1_Channel1_IRQHandler
   Definitions
      At line 366 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 72 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 301 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

DMA1_Channel2_IRQHandler 00000066

Symbol: DMA1_Channel2_IRQHandler
   Definitions
      At line 367 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 73 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 302 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

DMA1_Channel3_IRQHandler 00000066

Symbol: DMA1_Channel3_IRQHandler
   Definitions
      At line 368 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 74 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 303 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

DMA1_Channel4_IRQHandler 00000066

Symbol: DMA1_Channel4_IRQHandler
   Definitions
      At line 369 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 75 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 304 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

DMA1_Channel5_IRQHandler 00000066

Symbol: DMA1_Channel5_IRQHandler
   Definitions
      At line 370 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 76 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 305 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

DMA1_Channel6_IRQHandler 00000066

Symbol: DMA1_Channel6_IRQHandler
   Definitions
      At line 371 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 77 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 306 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

DMA1_Channel7_IRQHandler 00000066

Symbol: DMA1_Channel7_IRQHandler



ARM Macro Assembler    Page 3 Alphabetic symbol ordering
Relocatable symbols

   Definitions
      At line 372 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 78 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 307 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

DMA2_Channel1_IRQHandler 00000066

Symbol: DMA2_Channel1_IRQHandler
   Definitions
      At line 411 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 117 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 346 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

DMA2_Channel2_IRQHandler 00000066

Symbol: DMA2_Channel2_IRQHandler
   Definitions
      At line 412 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 118 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 347 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

DMA2_Channel3_IRQHandler 00000066

Symbol: DMA2_Channel3_IRQHandler
   Definitions
      At line 413 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 119 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 348 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

DMA2_Channel4_5_IRQHandler 00000066

Symbol: DMA2_Channel4_5_IRQHandler
   Definitions
      At line 414 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 120 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 349 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

DebugMon_Handler 00000060

Symbol: DebugMon_Handler
   Definitions
      At line 275 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 55 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 276 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

Default_Handler 00000066

Symbol: Default_Handler
   Definitions
      At line 288 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      None
Comment: Default_Handler unused



ARM Macro Assembler    Page 4 Alphabetic symbol ordering
Relocatable symbols

EXTI0_IRQHandler 00000066

Symbol: EXTI0_IRQHandler
   Definitions
      At line 361 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 67 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 296 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

EXTI15_10_IRQHandler 00000066

Symbol: EXTI15_10_IRQHandler
   Definitions
      At line 395 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 101 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 330 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

EXTI1_IRQHandler 00000066

Symbol: EXTI1_IRQHandler
   Definitions
      At line 362 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 68 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 297 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

EXTI2_IRQHandler 00000066

Symbol: EXTI2_IRQHandler
   Definitions
      At line 363 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 69 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 298 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

EXTI3_IRQHandler 00000066

Symbol: EXTI3_IRQHandler
   Definitions
      At line 364 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 70 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 299 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

EXTI4_IRQHandler 00000066

Symbol: EXTI4_IRQHandler
   Definitions
      At line 365 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 71 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 300 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

EXTI9_5_IRQHandler 00000066

Symbol: EXTI9_5_IRQHandler
   Definitions
      At line 378 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s



ARM Macro Assembler    Page 5 Alphabetic symbol ordering
Relocatable symbols

   Uses
      At line 84 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 313 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

FLASH_IRQHandler 00000066

Symbol: FLASH_IRQHandler
   Definitions
      At line 359 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 65 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 294 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

FSMC_IRQHandler 00000066

Symbol: FSMC_IRQHandler
   Definitions
      At line 403 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 109 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 338 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

HardFault_Handler 00000056

Symbol: HardFault_Handler
   Definitions
      At line 251 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 46 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 252 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

I2C1_ER_IRQHandler 00000066

Symbol: I2C1_ER_IRQHandler
   Definitions
      At line 387 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 93 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 322 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

I2C1_EV_IRQHandler 00000066

Symbol: I2C1_EV_IRQHandler
   Definitions
      At line 386 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 92 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 321 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

I2C2_ER_IRQHandler 00000066

Symbol: I2C2_ER_IRQHandler
   Definitions
      At line 389 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 95 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 324 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

I2C2_EV_IRQHandler 00000066



ARM Macro Assembler    Page 6 Alphabetic symbol ordering
Relocatable symbols


Symbol: I2C2_EV_IRQHandler
   Definitions
      At line 388 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 94 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 323 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

MemManage_Handler 00000058

Symbol: MemManage_Handler
   Definitions
      At line 256 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 47 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 257 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

NMI_Handler 00000054

Symbol: NMI_Handler
   Definitions
      At line 246 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 45 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 247 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

PVD_IRQHandler 00000066

Symbol: PVD_IRQHandler
   Definitions
      At line 356 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 62 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 291 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

PendSV_Handler 00000062

Symbol: PendSV_Handler
   Definitions
      At line 279 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 57 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 280 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

RCC_IRQHandler 00000066

Symbol: RCC_IRQHandler
   Definitions
      At line 360 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 66 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 295 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

RNG_IRQHandler 00000066

Symbol: RNG_IRQHandler
   Definitions
      At line 416 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses



ARM Macro Assembler    Page 7 Alphabetic symbol ordering
Relocatable symbols

      At line 130 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 351 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

RTCAlarm_IRQHandler 00000066

Symbol: RTCAlarm_IRQHandler
   Definitions
      At line 396 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 102 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 331 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

RTC_IRQHandler 00000066

Symbol: RTC_IRQHandler
   Definitions
      At line 358 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 64 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 293 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

Reset_Handler 00000008

Symbol: Reset_Handler
   Definitions
      At line 203 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 44 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 204 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

SDIO_IRQHandler 00000066

Symbol: SDIO_IRQHandler
   Definitions
      At line 404 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 110 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 339 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

SENSOR_IRQHandler 00000066

Symbol: SENSOR_IRQHandler
   Definitions
      At line 417 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 131 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 352 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

SPI1_IRQHandler 00000066

Symbol: SPI1_IRQHandler
   Definitions
      At line 390 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 96 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 325 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

SPI2_IRQHandler 00000066




ARM Macro Assembler    Page 8 Alphabetic symbol ordering
Relocatable symbols

Symbol: SPI2_IRQHandler
   Definitions
      At line 391 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 97 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 326 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

SPI3_IRQHandler 00000066

Symbol: SPI3_IRQHandler
   Definitions
      At line 406 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 112 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 341 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

SVC_Handler 0000005E

Symbol: SVC_Handler
   Definitions
      At line 270 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 54 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 271 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

SYMC_IRQHandler 00000066

Symbol: SYMC_IRQHandler
   Definitions
      At line 415 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 129 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 350 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

SysTick_Handler 00000064

Symbol: SysTick_Handler
   Definitions
      At line 283 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 58 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 284 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

TAMPER_IRQHandler 00000066

Symbol: TAMPER_IRQHandler
   Definitions
      At line 357 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 63 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 292 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

TIM1_BRK_IRQHandler 00000066

Symbol: TIM1_BRK_IRQHandler
   Definitions
      At line 379 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 85 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s



ARM Macro Assembler    Page 9 Alphabetic symbol ordering
Relocatable symbols

      At line 314 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

TIM1_CC_IRQHandler 00000066

Symbol: TIM1_CC_IRQHandler
   Definitions
      At line 382 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 88 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 317 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

TIM1_TRG_COM_IRQHandler 00000066

Symbol: TIM1_TRG_COM_IRQHandler
   Definitions
      At line 381 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 87 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 316 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

TIM1_UP_IRQHandler 00000066

Symbol: TIM1_UP_IRQHandler
   Definitions
      At line 380 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 86 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 315 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

TIM2_IRQHandler 00000066

Symbol: TIM2_IRQHandler
   Definitions
      At line 383 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 89 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 318 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

TIM3_IRQHandler 00000066

Symbol: TIM3_IRQHandler
   Definitions
      At line 384 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 90 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 319 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

TIM4_IRQHandler 00000066

Symbol: TIM4_IRQHandler
   Definitions
      At line 385 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 91 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 320 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

TIM5_IRQHandler 00000066

Symbol: TIM5_IRQHandler



ARM Macro Assembler    Page 10 Alphabetic symbol ordering
Relocatable symbols

   Definitions
      At line 405 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 111 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 340 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

TIM6_IRQHandler 00000066

Symbol: TIM6_IRQHandler
   Definitions
      At line 409 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 115 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 344 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

TIM7_IRQHandler 00000066

Symbol: TIM7_IRQHandler
   Definitions
      At line 410 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 116 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 345 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

TIM8_BRK_IRQHandler 00000066

Symbol: TIM8_BRK_IRQHandler
   Definitions
      At line 398 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 104 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 333 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

TIM8_CC_IRQHandler 00000066

Symbol: TIM8_CC_IRQHandler
   Definitions
      At line 401 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 107 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 336 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

TIM8_TRG_COM_IRQHandler 00000066

Symbol: TIM8_TRG_COM_IRQHandler
   Definitions
      At line 400 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 106 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 335 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

TIM8_UP_IRQHandler 00000066

Symbol: TIM8_UP_IRQHandler
   Definitions
      At line 399 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 105 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 334 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s



ARM Macro Assembler    Page 11 Alphabetic symbol ordering
Relocatable symbols


UART4_IRQHandler 00000066

Symbol: UART4_IRQHandler
   Definitions
      At line 407 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 113 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 342 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

UART5_IRQHandler 00000066

Symbol: UART5_IRQHandler
   Definitions
      At line 408 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 114 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 343 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

USART1_IRQHandler 00000066

Symbol: USART1_IRQHandler
   Definitions
      At line 392 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 98 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 327 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

USART2_IRQHandler 00000066

Symbol: USART2_IRQHandler
   Definitions
      At line 393 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 99 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 328 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

USART3_IRQHandler 00000066

Symbol: USART3_IRQHandler
   Definitions
      At line 394 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 100 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 329 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

USBWakeUp_IRQHandler 00000066

Symbol: USBWakeUp_IRQHandler
   Definitions
      At line 397 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 103 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 332 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

USB_HP_CAN1_TX_IRQHandler 00000066

Symbol: USB_HP_CAN1_TX_IRQHandler
   Definitions



ARM Macro Assembler    Page 12 Alphabetic symbol ordering
Relocatable symbols

      At line 374 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 80 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 309 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

USB_LP_CAN1_RX0_IRQHandler 00000066

Symbol: USB_LP_CAN1_RX0_IRQHandler
   Definitions
      At line 375 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 81 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 310 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

UsageFault_Handler 0000005C

Symbol: UsageFault_Handler
   Definitions
      At line 266 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 49 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 267 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

WWDG_IRQHandler 00000066

Symbol: WWDG_IRQHandler
   Definitions
      At line 355 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 61 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 290 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s

76 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Absolute symbols

Heap_Size 00001000

Symbol: Heap_Size
   Definitions
      At line 26 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 30 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
Comment: Heap_Size used once
Stack_Size 00000400

Symbol: Stack_Size
   Definitions
      At line 16 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 19 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
Comment: Stack_Size used once
__Vectors_Size 00000220

Symbol: __Vectors_Size
   Definitions
      At line 187 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 41 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
Comment: __Vectors_Size used once
3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
External symbols

SystemInit 00000000

Symbol: SystemInit
   Definitions
      At line 195 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 237 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 196 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 238 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
Warning: SystemInit multiply defined
__main 00000000

Symbol: __main
   Definitions
      At line 194 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 236 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
   Uses
      At line 198 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
      At line 240 in file ..\Libraries\STARTUP\arm\startup_air32f10x.s
Warning: __main multiply defined
2 symbols
426 symbols in table
