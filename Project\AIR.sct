; *************************************************************
; *** Scatter-Loading Description File generated by uVision ***
; *************************************************************

;LR_IROM2 0x0801F7FC 0x00010000 {
;  ER_IROM2 0x0801F7FC 0x00010000 {
;    *.o(.sram_data +First)  ; 强制保留该段
;  }
;}

;LR_IROM1 0x08000000 0x00040000  {    ; load region size_region
;  ER_IROM1 0x08000000 0x00040000  {  ; load address = execution address
;   *.o (RESET, +First)
;   *(InRoot$$Sections)
;   .ANY (+RO)
;   .ANY (+XO)
;  }
  
  ;RW_IRAM1 0x20000000 UNINIT 0x00000004  {  ; RW data
  ;        *(.bss.NoInit)
  ;}
  
;  RW_IRAM2 0x20000008 0x00017FF8  {  ; RW data
;   .ANY (+RW +ZI)
;  }
;}


LR_IROM1 0x08002000 0x0003C800  {    ; 主Flash区域: 244KB (0x08003000-0x0803F800)
  ER_IROM1 0x08002000 0x0003C800  {  ; 代码和只读数据
   *.o (RESET, +First)               ; 中断向量表
   *(InRoot$$Sections)               ; 库使用的特殊段
   .ANY (+RO)                        ; 所有只读数据
   .ANY (+XO)                        ; 可执行代码
  }
  
  RW_IRAM1 0x20000000 UNINIT 0x00000004  {  ; NoInit区域(4字节)
          *(.bss.NoInit)                    ; 不初始化的变量
  }
  
  RW_IRAM2 0x20000008 0x00017FF8  {  ; 主RAM区(减去前8字节)
   .ANY (+RW +ZI)                    ; 可读写数据和零初始化数据
  }
}

LR_IROM2 0x0803E800 0x00001800  {    ; 
  ER_MY_SECTION 0x0803E800 0x0000800 { ; 指定最后2KB（2048字节）
    *(.my_section)                  ; 匹配C代码中的section名称
  }
  ER_EEDATA1 0x0803F000 0x0000800 { ; 指定最后2KB（2048字节）
    *(.eedata1)                  ; 匹配C代码中的section名称
  }
  ER_EEDATA2 0x0803F800 0x0000800 { ; 指定最后2KB（2048字节）
    *(.eedata2)                  ; 匹配C代码中的section名称
  }
}
