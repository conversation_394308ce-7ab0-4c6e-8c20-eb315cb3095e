#include "lzmUI.h"

// 根据字模ID与所在位置获取 ASCII字符
char *LZM_RetrieveASCIIcharacters(enum LZM_MATRIX_e_ FontSize, unsigned int mid)
{
    switch (FontSize)
    {
#define LZM_MATRIX_DEBUG_X(heigh) \
    case lzm_char_##heigh:      \
        return (char *)Font##heigh##_Ascii[mid].txt;
        LZM_MATRIX_ITEM_LIST
#undef LZM_MATRIX_DEBUG_X
    default:
        return 0;
    }
}
// 根据字模ID与所在位置获取 汉语字符
char *LZM_RetrieveCHINESEcharacters(enum LZM_MATRIX_e_ FontSize, unsigned int mid)
{
    switch (FontSize)
    {
#define LZM_MATRIX_DEBUG_X(heigh) \
    case lzm_char_##heigh:      \
        return (char *)Font##heigh##_Chinese[mid].txt;
        LZM_MATRIX_ITEM_LIST
#undef LZM_MATRIX_DEBUG_X
    default:
        return 0;
    }
}
// 获取字模地址
unsigned char *LZM_FontAddressAcquisition(char *character_, enum LZM_MATRIX_e_ FontSize)
{
    if (character_ == 0)
    {
        /* 字符串地址为空 */
        return 0;
    }

    if (character_[0] == 0)
    {
        /* 字符结尾 */
        return 0;
    }

    if (FontSize >= LZM_MATRIX_ITEM_NUM)
    {
        /* 字高不在范围，返回空地址 */
        return 0;
    }

    unsigned int left = 0;
    unsigned int right = 0;
    char *dat_;
    unsigned int mid = 0;
    int cmp = 0;

    if ((character_[0] & 0x80) == 0) // UTF-8 ASCII判断
    {
        // 在ASCII数组中二分查找
        right = lzm_matrix_ascii_quan_list[FontSize] - 1;

        while (left <= right)
        {
            mid = left + ((right - left) >>1);
            dat_=LZM_RetrieveASCIIcharacters(FontSize, mid);
            cmp = strncmp(dat_, character_, 1);

            if (cmp < 0)
            {
                left = mid + 1;
            }
            else if (cmp > 0)
            {
                right = mid - 1;
            }
            else
            {
                return (unsigned char *)(dat_+1);
            }
        }
    }
    else
    {
        // 在汉字数组中二分查找
        right = lzm_matrix_chinese_quan_list[FontSize] - 1;

        while (left <= right)
        {
            mid = left + ((right - left) >>1);
            dat_=LZM_RetrieveCHINESEcharacters(FontSize, mid);
            cmp = memcmp(dat_, character_, 3);

            if (cmp < 0)
            {
                left = mid + 1;
            }
            else if (cmp > 0)
            {
                right = mid - 1;
            }
            else
            {
                return (unsigned char *)(dat_+3);
            }
        }
    }
    return 0; // 未找到返回空指针
}
