#include "UI28.h"
struct FONT14_ASCII const Font14_Ascii[] =
{
  {
    " ",0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,  /* 0 */ 
  },
  {
    "%",0x00,0xE4,0xA8,0xA8,0xA8,0xB0,0x74,0x1A,0x2A,0x2A,0x2A,0x4A,0x4C,0x00,  /* 0 */ 
  },
  {
    "0",0x00,0x18,0x3C,0x66,0x66,0x66,0x66,0x66,0x66,0x66,0x66,0x6C,0x3C,0x00,  /* 1 */ 
  },
  {
    "1",0x00,0x00,0x38,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x3E,0x00,  /* 2 */ 
  },
  {
    "2",0x00,0x3C,0x66,0x66,0x66,0x06,0x0C,0x0C,0x18,0x30,0x60,0x66,0x7E,0x00,  /* 3 */ 
  },
  {
    "3",0x00,0x38,0x6C,0x66,0x06,0x0C,0x18,0x0C,0x06,0x06,0x66,0x66,0x3C,0x00,  /* 4 */ 
  },
  {
    "4",0x00,0x0C,0x0C,0x1C,0x1C,0x3C,0x6C,0x6C,0xCC,0x7E,0x0C,0x0C,0x1E,0x00,  /* 5 */ 
  },
  {
    "5",0x00,0x7E,0x60,0x60,0x60,0x78,0x7E,0x06,0x06,0x66,0x66,0x66,0x3C,0x00,  /* 6 */ 
  },
  {
    "6",0x00,0x1C,0x36,0x60,0x60,0x78,0x7E,0x66,0x66,0x66,0x66,0x66,0x3C,0x00,  /* 7 */ 
  },
  {
    "7",0x00,0x7E,0x66,0x06,0x0C,0x0C,0x18,0x18,0x18,0x18,0x18,0x38,0x18,0x00,  /* 8 */ 
  },
  {
    "8",0x00,0x3C,0x66,0x66,0x66,0x76,0x3C,0x6C,0x66,0x66,0x66,0x66,0x3C,0x00,  /* 9 */ 
  },
  {
    "9",0x00,0x3C,0x66,0x66,0x66,0x66,0x66,0x6E,0x3E,0x06,0x06,0x6C,0x38,0x00,  /* 10 */ 
  }
};

struct FONT14_CHINESE const Font14_Chinese[] =
{

};

UI28_MATRIX_XX_QUAN_LIST(14)