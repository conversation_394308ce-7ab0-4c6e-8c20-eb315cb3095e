#include "UI28_progress_bar.h"
#if (UI28_PROGRESS_BAR_ENABLED == 1)

volatile uint_progressDis_t ui28_ProgressBar_display = 0; /* 进度条显示标志位 */

/* 进度条背景颜色表 */
const unsigned short ui28_progress_COLOR_0_list[UI28_PROGRESS_ITEM_NUM] =
    {
#define UI28_PROGRESS_DEBUG_X(Lname, COLOR_0_, COLOR_1_, Lx, Ly, Lw, Lh) COLOR_0_,
        UI28_PROGRESS_ITEM_LIST
#undef UI28_PROGRESS_DEBUG_X
};

/* 进度条颜色表 */
const unsigned short ui28_progress_COLOR_1_list[UI28_PROGRESS_ITEM_NUM] =
    {
#define UI28_PROGRESS_DEBUG_X(Lname, COLOR_0_, COLOR_1_, Lx, Ly, Lw, Lh) COLOR_1_,
        UI28_PROGRESS_ITEM_LIST
#undef UI28_PROGRESS_DEBUG_X
};

/* 进度条X坐标表 */
const unsigned short ui28_progress_Ax_list[UI28_PROGRESS_ITEM_NUM] =
    {
#define UI28_PROGRESS_DEBUG_X(Lname, COLOR_0_, COLOR_1_, Lx, Ly, Lw, Lh) Lx,
        UI28_PROGRESS_ITEM_LIST
#undef UI28_PROGRESS_DEBUG_X
};

/* 进度条Y坐标表 */
const unsigned short ui28_progress_Ay_list[UI28_PROGRESS_ITEM_NUM] =
    {
#define UI28_PROGRESS_DEBUG_X(Lname, COLOR_0_, COLOR_1_, Lx, Ly, Lw, Lh) Ly,
        UI28_PROGRESS_ITEM_LIST
#undef UI28_PROGRESS_DEBUG_X
};

/* 进度条宽表 */
const unsigned short ui28_progress_Aw_list[UI28_PROGRESS_ITEM_NUM] =
    {
#define UI28_PROGRESS_DEBUG_X(Lname, COLOR_0_, COLOR_1_, Lx, Ly, Lw, Lh) Lw,
        UI28_PROGRESS_ITEM_LIST
#undef UI28_PROGRESS_DEBUG_X
};

/* 进度条高表 */
const unsigned short ui28_progress_Ah_list[UI28_PROGRESS_ITEM_NUM] =
    {
#define UI28_PROGRESS_DEBUG_X(Lname, COLOR_0_, COLOR_1_, Lx, Ly, Lw, Lh) Lh,
        UI28_PROGRESS_ITEM_LIST
#undef UI28_PROGRESS_DEBUG_X
};

/* 进度条速度表 */
volatile unsigned char ui28_progress_speed_list[UI28_PROGRESS_ITEM_NUM] =
    {
#define UI28_PROGRESS_DEBUG_X(Lname, COLOR_0_, COLOR_1_, Lx, Ly, Lw, Lh) 5,
        UI28_PROGRESS_ITEM_LIST
#undef UI28_PROGRESS_DEBUG_X
};

/* 进度条目标值百分比表(256倍) */
volatile int ui28_progress_target_value_list[UI28_PROGRESS_ITEM_NUM] =
    {
#define UI28_PROGRESS_DEBUG_X(Lname, COLOR_0_, COLOR_1_, Lx, Ly, Lw, Lh) 0,
        UI28_PROGRESS_ITEM_LIST
#undef UI28_PROGRESS_DEBUG_X
};

/* 进度条当前值百分比表(256倍) */
volatile int ui28_progress_current_value_list[UI28_PROGRESS_ITEM_NUM] =
    {
#define UI28_PROGRESS_DEBUG_X(Lname, COLOR_0_, COLOR_1_, Lx, Ly, Lw, Lh) 0,
        UI28_PROGRESS_ITEM_LIST
#undef UI28_PROGRESS_DEBUG_X
};

/* 根据目标进度更新当前 */
void Update_current_status_based_on_target_progressBar(enum UI28_PROGRESS_e_ ALBUM_)
{
    UI28_anima((int *)&ui28_progress_current_value_list[ALBUM_], (int *)&ui28_progress_target_value_list[ALBUM_], (int)ui28_progress_speed_list[ALBUM_]); // 窗口目标中值(256倍)
}
/* 更新进度条 */
void UI28_update_ProgressBar(enum UI28_PROGRESS_e_ ALBUM_)
{
    int Progress_bar_length;

    if (ui28_progress_current_value_list[ALBUM_] == ui28_progress_target_value_list[ALBUM_])
    {
        /* 全部都相等，就不用刷新了 */
        return;
    }

    // 计算进度条长度
    Progress_bar_length = (ui28_progress_Aw_list[ALBUM_] * ui28_progress_current_value_list[ALBUM_] / 100) >> 8;

    ILI9340X_Clear(ui28_progress_Ax_list[ALBUM_], ui28_progress_Ay_list[ALBUM_], (unsigned short)Progress_bar_length, ui28_progress_Ah_list[ALBUM_], ui28_progress_COLOR_1_list[ALBUM_]);                                          /*对ILI9340X显示器的某一窗口以某种颜色进行清屏*/
    ILI9340X_Clear((Progress_bar_length + ui28_progress_Ax_list[ALBUM_]), ui28_progress_Ay_list[ALBUM_], (ui28_progress_Aw_list[ALBUM_] - Progress_bar_length), ui28_progress_Ah_list[ALBUM_], ui28_progress_COLOR_0_list[ALBUM_]); /*对ILI9340X显示器的某一窗口以某种颜色进行清屏*/
}
/* 工程初始化进度条（在系统上电执行一次） */
void ui28_Init_ProgressBar_Engineering(void)
{
}
/* 初始化进度条 */
void ui28_Init_ProgressBar(enum UI28_PROGRESS_e_ ALBUM_)
{
    ui28_progress_current_value_list[ALBUM_] = 0 /* 进度条当前值表(256倍) */;
}

/* 进度条显示开关（关实际只是不刷新了） */
void UI28_display_onoff_ProgressBar(enum UI28_PROGRESS_e_ ALBUM_, unsigned char _onoff_)
{
    if (_onoff_ == 0)
    {
        ui28_ProgressBar_display &= ~(1UL << ALBUM_);
    }
    else
    {
        ui28_ProgressBar_display |= (1UL << ALBUM_);
    }
}
/* 设置进度条位置 */
void UI28_change_cursor_value_ProgressBar(enum UI28_PROGRESS_e_ ALBUM_, unsigned char cur_val, unsigned char speed)
{
    ui28_progress_speed_list[ALBUM_] = speed;
    ui28_progress_target_value_list[ALBUM_] = (int)cur_val << 8;
}

#endif