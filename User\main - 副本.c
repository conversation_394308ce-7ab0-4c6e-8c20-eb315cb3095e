#include <stdlib.h>
#include <string.h>
#include <stdio.h>
#include "bsp.h"				/* 底层硬件驱动 */

#define GPIO_GROUP_TEST GPIOA
#define GPIO_MODE_TEST GPIO_Mode_Out_PP
#define GPIO_SPEED_TEST GPIO_Speed_50MHz
#define GPIO_PIN1_TEST GPIO_Pin_8

//GPIO输出初始化
void GPIO_Configuration(void)
{
	GPIO_InitTypeDef GPIO_InitStructure;

//	RCC_APB2PeriphClockCmd(RCC_APB2Periph_AFIO, ENABLE);
//	GPIO_PinRemapConfig(GPIO_Remap_SWJ_Disable , ENABLE);
	
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA, ENABLE); //使能GPIOA时钟

	GPIO_InitStructure.GPIO_Pin = GPIO_PIN1_TEST;
	GPIO_InitStructure.GPIO_Speed = GPIO_SPEED_TEST; //速度50MHz
	GPIO_InitStructure.GPIO_Mode = GPIO_MODE_TEST;	 //输出模式
	GPIO_Init(GPIO_GROUP_TEST, &GPIO_InitStructure); //初始化GPIOB.13
}

int main(void)
{
	
	int tick1ms,tick10ms,tick1000ms=0;

	
	bsp_Init();		/* 硬件初始化 */
	
	ILI9340X_Init();//ILI9340X初始化函数，如果要用到lcd，一定要调用这个函数
/***************************************** 初始化GPIO *****************************************/
	bsp_InitUart();
	comSendChar(COM1, 0x5A);
	
	uint8_t ReverseAssist=1;	
	GPIO_Configuration();//GPIO输出初始化
	GPIO_SetBits(GPIO_GROUP_TEST, GPIO_PIN1_TEST);
	while (1)
	{
		if (bsp_CheckRunTime(tick1ms)>1)/* 1ms进入一次 */
		{
			tick1ms=bsp_GetRunTime();/* 更新超时计数 */
			
		}
		
		if (bsp_CheckRunTime(tick10ms)>10)/* 10ms进入一次 */
		{
			tick10ms=bsp_GetRunTime();/* 更新超时计数 */
			
		}
		
		if (bsp_CheckRunTime(tick1000ms)>10000)/* 1000ms进入一次 */
		{
			tick1000ms=bsp_GetRunTime();/* 更新超时计数 */
			
			if (ReverseAssist==0)
			{
				ReverseAssist=1;
				GPIO_ResetBits(GPIO_GROUP_TEST, GPIO_PIN1_TEST);
			}
			else
			{
				ReverseAssist=0;
				GPIO_SetBits(GPIO_GROUP_TEST, GPIO_PIN1_TEST);
			}
		}
	}
}
