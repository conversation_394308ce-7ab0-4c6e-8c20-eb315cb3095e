#include "bsp.h"
static volatile bsp_fifo_t b_tfifo; /* FIFO结构体 */

/*
*********************************************************************************************************
*	函 数 名: bsp_Putfifo
*	形    参: _Code : 任务代码
*	返 回 值: 无
*	功能说明: 将1个任务压入FIFO缓冲区。可用于模拟一个按键。
*********************************************************************************************************
*/
void bsp_Putfifo(uint8_t _Code)
{
    b_tfifo.Buf[b_tfifo.Write] = _Code;

    if (++b_tfifo.Write >= FIFO_SIZE)
    {
        b_tfifo.Write = 0;
    }
}

/*
*********************************************************************************************************
*	函 数 名: bsp_Getfifo
*	形    参: 无
*	返 回 值: 任务代码
*	功能说明: 从FIFO缓冲区读取一个任务
*********************************************************************************************************
*/
uint8_t bsp_Getfifo(void)
{
    uint8_t ret;

    if (b_tfifo.Read == b_tfifo.Write)
    {
        return FIFO_NONE;
    }
    else
    {
        ret = b_tfifo.Buf[b_tfifo.Read];

        if (++b_tfifo.Read >= FIFO_SIZE)
        {
            b_tfifo.Read = 0;
        }
        return ret;
    }
}

/*
*********************************************************************************************************
*	函 数 名: bsp_Queryfifo
*	形    参: _Code：要确认的任务
*	返 回 值: 1：任务已经存在 0:不存在
*	功能说明: 确认任务在FIFO缓冲区是否已存在
*********************************************************************************************************
*/
uint8_t bsp_Queryfifo(uint8_t _Code)
{
    uint8_t Read2 = b_tfifo.Read;
    for (;;)
    {
        if (Read2 == b_tfifo.Write)
        {
            return 0;
        }

        if (b_tfifo.Buf[Read2] == _Code)
        {
            return 1;
        }
        else
        {
            if (++Read2 >= FIFO_SIZE)
            {
                Read2 = 0;
            }
        }
    }
}

/*
*********************************************************************************************************
*	函 数 名: bsp_Clearfifo
*	形    参: 无
*	返 回 值: 无
*	功能说明: 清空FIFO缓冲区
*********************************************************************************************************
*/
void bsp_Clearfifo(void)
{
    b_tfifo.Read = 0;
    b_tfifo.Write = 0;
}

/*
*********************************************************************************************************
*	函 数 名: Handling_fifo_Events1ms
*	形    参: 无
*	返 回 值: 无
*	功能说明: 处理任务事件。非阻塞，被s1ms周期性的调用
*********************************************************************************************************
*/
void Handling_fifo_Events1ms(void)
{
    uint8_t ucfifoCode;
    ucfifoCode = bsp_Getfifo();
    if (ucfifoCode > 0)
    {
#if KEY_SWITCH
        if (ucfifoCode <= K3_SEQ /* 所有按键范围 */)
        {
            /* 键值处理:基于页面 */
            keyHandle(ucfifoCode);
        }
#endif
        /* 如果有键按下 */
        switch (ucfifoCode)
        {
#if BEEP_SWITCH
#define BEEP_DEBUG_X(name, Cycle, ...)         \
    case name##_BEEP:                          \
        BEEP_Start(&beep_music_##name, Cycle); \
        break;
            BEEP_ITEM_LIST
#undef BEEP_DEBUG_X
#endif

#define FIFOADD_DEBUG_X(name, ...) \
    case name:                     \
        __VA_ARGS__;               \
        break;
            FIFOADD_ITEM_LIST
#undef FIFOADD_DEBUG_X

        default:
            /* 其它的不处理 */
            break;
        }
    }
}
