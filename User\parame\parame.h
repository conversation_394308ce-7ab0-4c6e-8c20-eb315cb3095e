#ifndef _PARAME_H
#define _PARAME_H
#include <stdint.h>
#include <stdio.h>
#include "parame_tongyong.h"

#ifndef TRUE
#define TRUE 1
#endif

#ifndef FALSE
#define FALSE 0
#endif

/* 单位文本 */
extern volatile char Unit_Text1[];
extern volatile char Unit_Text2[];
extern volatile char Unit_Text3[];
extern volatile char Unit_Text4[];/* ℃ */
extern volatile char Unit_Text5[];/* ℉ */
extern volatile char Unit_Text6[];
extern volatile char Unit_Text7[];
extern volatile char Unit_Text8[];
extern volatile char Unit_Text9[];

typedef enum
{
	UNIT_SEC = 0, /* 秒 */
	UNIT_MIN,	  /* 分 */
	UNIT_HOR,	  /* 时 */
	UNIT_C,		  /* 摄氏度℃ */
	UNIT_01C,	  /* 0.1摄氏度℃ */
	UNIT_F,		  /* 华氏度℉ */
	UNIT_01F,	  /* 0.1华氏度℉ */
	UNIT_CNT,	  /* 次 */
	UNIT_PER,	  /* 百分% */
	UNIT_THOUSAN, /* 千分‰ */
	UNIT_WELL,	  /* # */
	UNIT_NONE,	  /* 无单位 */
} PARAME_UNIT;

/* 参数调节模式类型 */
typedef enum
{
	ADJ_MODE_CIRC = 0, /* 循环调节 */
	ADJ_MODE_AMP,	   /* 限幅调节 */
} PARAME_ADJ_MADE;

/* 初始化等级类型 */
typedef enum
{
	RST_LEVEL_USER = 0, /* 可以被系统和用户初始化，普通参数都适用 */
	RST_LEVEL_USER_1,
	RST_LEVEL_USER_2,
	RST_LEVEL_USER_3,
	RST_LEVEL_USER_4,
	RST_LEVEL_SYS,		/* 只能被系统初始化，用户无法初始化，适用与锁机标志，锁机时间，工作模式等参数 */
	RST_LEVEL_NONE,		/* 不能被初始化 无法被系统和用户初始化，适用与传感器校准值电阻触摸校准值等硬件相关参数 */
} PARAME_RET_LEVEL;

/* 参数调整方向类型 */
typedef enum
{
	PARAME_INC = 0, /* 增加 */
	PARAME_DEC,		/* 减少 */
} PARAME_ADJ_DIRECTION;

/* 参数布进类型 */
typedef enum
{
	PARAME_S_STEP = 0, /* 单步 */
	PARAME_D_STEP,	   /* 连步 */
} PARAME_S_OR_D_STEP;

/*参数结构体*/
typedef struct
{
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) type name[num];
	PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
} parame_t;

/*参数存储结构体*/
typedef struct
{
	parame_t sys;
	char VERSION[14];
	char DATE[6];
} parame_f_t;

/*参数类型结构体*/
typedef struct
{
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) type name;
	PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
} parame_l_t;

/*参数查询结构体*/
typedef struct
{
	void *now;		  // 当前
	void *last;		  // 上一次
	void *init;		  // 初始值
	void *min;		  // 下限
	void *max;		  // 上限
	void *adj_S_step; // 单次调节步进
	void *adj_C_step; // 连续调节步进
} parame_query_t;

/* 初始值 */
extern const parame_t parames_config_init;
/* 下限 */
extern const parame_l_t parames_config_min;
/* 上限 */
extern const parame_l_t parames_config_max;
/* 单次调节步进 */
extern const parame_l_t parames_config_S_step;
/* 连续调节步进 */
extern const parame_l_t parames_config_C_step;
/* 个数 */
extern const uint8_t parames_config_num[];
/* 单位 */
extern const uint8_t parames_config_unit[];
/* 调节模式 */
extern const uint8_t parames_config_mode[];
/* 初始化等级 */
extern const uint8_t parames_config_level[];
/* 限制上限的参数ID */
extern const uint8_t parames_config_max_id[];
/* 限制下限的参数ID */
extern const uint8_t parames_config_min_id[];
/* 参数更新回调，调用期间已经是更新后的数据 */
extern void (*const parames_config_update[])(void);
/*用户参数数值*/
extern volatile parame_f_t _sys_parames;
/*用户上一次参数数值*/
extern volatile parame_t _sys_last_parames;
/*参数查询结构体*/
extern const parame_query_t PQuery_t[];

/*获取参数值地址*/
#define PVp(idx) (PQuery_t[idx].now)
/*获取上一次参数值地址*/
#define PLVp(idx) (PQuery_t[idx].last)
/*获取参数下限地址*/
#define PMINp(idx) (PQuery_t[idx].min)
/*获取参数上限地址*/
#define PMAXp(idx) (PQuery_t[idx].max)

/*获取参数值*/
#define PV(type, idx, idd) (((type *)PVp(idx))[idd])
/*获取上一次参数值*/
#define PLV(type, idx, idd) (((type *)PLVp(idx))[idd])
/*获取参数下限*/
#define PMIN(type, idx) (*(type *)PMINp(idx))
/*获取参数上限*/
#define PMAX(type, idx) (*(type *)PMAXp(idx))
/*获取参数单位*/
#define PUNIT(idx) (parames_config_unit[idx])

/*用户可调参数调整*/
void parame_adj(uint8_t idx, uint8_t idd, PARAME_ADJ_DIRECTION op, PARAME_S_OR_D_STEP step);
/*修改参数*/
uint8_t parame_set_pv(uint8_t idx, uint8_t idd, void *value);
/*扫描数据变化。非阻塞，被10ms周期性的调用*/
void ScanDataChanges10ms(void);
/*按等级初始化参数*/
void Init_parame_level(PARAME_RET_LEVEL level_, uint8_t idd_);
#endif
