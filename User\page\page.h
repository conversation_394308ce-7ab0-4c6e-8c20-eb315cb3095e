#ifndef _PAGE_H
#define _PAGE_H
#include <stdint.h>
#include <stdio.h>
#include "bsp.h"
#include "page_start.h"
#include "page_standby.h"
#include "page_run.h"
#include "page_parameter.h"
#include "page_debug.h"
#include "page_statistics.h"
#include "page_check.h"
#include "page_developer.h"
#include "page_qr_code.h"

#define PAGE_ID_ITEM_LIST                                      \
	PAGE_ID_DEBUG_X(start, ROOT_PAGE)	  /* 开机画面 */       \
	PAGE_ID_DEBUG_X(standby, ROOT_PAGE)	  /* 待机菜单 */       \
	PAGE_ID_DEBUG_X(run, ROOT_PAGE)		  /* 运行 */           \
	PAGE_ID_DEBUG_X(parameter, SUB_PAGE)  /* 参数设置 */       \
	PAGE_ID_DEBUG_X(debug, SUB_PAGE)	  /* 调试 */           \
	PAGE_ID_DEBUG_X(qr_code, SUB_PAGE)	  /* 二维码(物联网) */ \
	PAGE_ID_DEBUG_X(statistics, SUB_PAGE) /* 统计查看 */       \
	PAGE_ID_DEBUG_X(check, SUB_PAGE)	  /* 状态查看 */       \
	PAGE_ID_DEBUG_X(developer, SUB_PAGE)  /* 开发者 */

enum page_id
{
#define PAGE_ID_DEBUG_X(name, CLASS) page_##name,
	PAGE_ID_ITEM_LIST
#undef PAGE_ID_DEBUG_X
		PAGE_ID_ITEM_NUM, /* 页面个数 */
	page_home = 0xFF,	  /* page_home非真实界面，类似手机home键，只有在子界面返回根界面时调用 */
};

/* 页面类型 */
typedef enum
{
	ROOT_PAGE = 0, /* 根页面 */
	SUB_PAGE,	   /* 子页面 */
} PAGE_CLASS;

/* 页面 */
typedef struct
{
	PAGE_CLASS page_class;		  /* 界面类型 */
	void (*page_root_load)(void); /* 根界面切换回调，只有根类型界面切换时会调用，可以在此函数中添加业务规则 */
	void (*page_view_load)(void); /* 界面加载回调，调用期间页面管理器数据为之前界面，回调结束后会自动更新页面管理器，回调中不能访问page_goto函数，避免界面管理器错乱 */
	void (*page_update)(void);	  /* 界面更新回调 */
} page_t;

/* 页面管理器 */
typedef struct
{
	uint8_t current_page; // 当前交互的界面
	uint8_t root_page;	  // 当前根的界面
} page_mgr_t;

extern const page_t pa_tVar[];		  /* 界面结构体 */
extern volatile page_mgr_t pa_m_tVar; /* 界面管理器 */

/*页面管理器 初始化*/
void page_mgr_init(void);
/*切换页面*/
void page_goto(uint8_t page);
/*当前交互界面更新，需要非阻塞轮询*/
void page_current_update(void);
/*获取当前交互界面*/
uint8_t page_mgr_curpage(void);
/*获取当前根界面*/
uint8_t page_mgr_root(void);
#endif
