#ifndef __BSP_TIMER_H
#define __BSP_TIMER_H

/*
	每个闹钟对应1个全局的结构体变量。
*/
typedef struct _ALARM_CLOCK_T
{
	uint16_t usTime;//时间，单位 100ms
	uint8_t message;//用于结束后发送的消息（在按键FIFO中）（不可以为0xFF，0xFF为闹钟失能）
}ALARM_CLOCK_T;

#define ALARM_CLOCK_ITEM_LIST          \
	ALARM_CLOCK_DEBUG_X(C1 /* 闹钟名称 */)\
	ALARM_CLOCK_DEBUG_X(C2 /* 闹钟名称 */)  

/* 闹钟ID*/
typedef enum
{
#define ALARM_CLOCK_DEBUG_X(name) ALARM_##name,
	ALARM_CLOCK_ITEM_LIST
#undef ALARM_CLOCK_DEBUG_X
		FALARM_CLOCK_ITEM_NUM,
}ALARM_CLOCK_ID_E;

void bsp_InitTimer(void);/* 配置systick中断，并初始化软件定时器变量*/
int32_t bsp_GetRunTime(void);/* 取CPU运行时间，单位1ms。*/
int32_t bsp_CheckRunTime(int32_t _LastTime);/* 计算当前运行时间和给定时刻之间的差值。*/
void bsp_DelayMS(uint32_t n);/* ms级延迟，延迟精度为正负1ms*/
void ms100_alarm_clock(ALARM_CLOCK_ID_E sto_, uint16_t time_100ms, uint8_t End_Message);//Timer0任务闹钟
void DetectALARM100ms(void);//扫描所有闹钟。非阻塞，被systick中断周期性的调用，100ms一次
void SysTick_Handler(void);/* 系统嘀嗒定时器中断服务程序*/
#endif

