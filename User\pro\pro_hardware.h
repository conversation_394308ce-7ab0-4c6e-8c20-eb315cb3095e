#ifndef _PRO_HARDWARE_H
#define _PRO_HARDWARE_H

/* 业务用 继电器枚举 */
enum PRO_REL_Type
{
	PRELT1,/* 洗涤剂 */
	PRELT2,/* 干燥机 */
	PRELT3,/* 漂加热 */
	PRELT4,/* 洗加热 */
	PRELT5,/* 进水阀 */
    PRELT6,/* 漂洗泵 */
	PRELT7,/* 洗涤泵 */
};

/* 业务用 pwm枚举 */
enum PRO_PWM_Type
{
	PPWMT1,/* 洗涤剂 */
	PPWMT2,/* 干燥机 */
};

/* 业务用 开关信号枚举 */
enum PRO_SS_Type
{
	PSST1,/* 洗高 */
	PSST2,/* 洗低 */
	PSST3,/* 漂高 */
	PSST4,/* 漂低 */
	PSST5,/* 机门 */
};

/* 业务用 温度枚举 */
enum PRO_TEMP_Type
{
	PTT1,/* 洗涤温度 */
	PTT2,/* 漂洗温度 */
};

extern __IO uint32_t pro_relay;//业务用继电器输出
extern __IO uint8_t pro_pwm[2];//业务用pwm输出
extern __IO uint32_t pro_switch_signal;//业务用开关信号
extern __IO int pro_temp_use[TEMPERATURE_ITEM_NUM];//业务用温度信号(使用)
extern __IO int pro_temp_see[TEMPERATURE_ITEM_NUM];//业务用温度信号(显示)
extern __IO uint8_t pro_temp_abn[TEMPERATURE_ITEM_NUM]; // 业务用温度探头异常标志位(大于0为异常) 

/* 业务用 继电器控制 */
#define PRO_RELAY_CONTROL(RELAY_, ON_OFF_) (pro_relay = (pro_relay & ~(1 << RELAY_)) | ((!!(ON_OFF_)) << RELAY_))
/* 业务用 pwm控制 */
#define PRO_PWM_CONTROL(PWM_, percentage) (pro_pwm[PWM_]=percentage)
/* 业务用 继电器控制状态获取 */
#define PRO_RELAY_OBTAIN(RELAY_) ((pro_relay >> RELAY_) & 1)
/* 业务用 开关信号获取 */
#define PRO_SW_SIG_OBTAIN(SW_SIG_) ((pro_switch_signal >> SW_SIG_) & 1)
/* 业务用 温度信号(使用)获取 */
#define PRO_TEMP_USE_OBTAIN(TEMP_) (pro_temp_use[TEMP_])
/* 业务用 温度信号(显示)获取 */
#define PRO_TEMP_SEE_OBTAIN(TEMP_) (pro_temp_see[TEMP_])
/* 业务用 温度探头异常获取 */
#define PRO_TEMP_ANO_OBTAIN(TEMP_) (pro_temp_abn[TEMP_])

/* 业务信号传入 */
void pro_signal_input(void);
/* 业务信号输出 */
void pro_signal_output(void);
#endif
