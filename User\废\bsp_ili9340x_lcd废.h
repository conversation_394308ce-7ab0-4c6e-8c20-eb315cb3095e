#ifndef _BSP_ILI9340X_LCD_H_
#define _BSP_ILI9340X_LCD_H_
/******************************* ILI9340X 显示屏8位8080通讯引脚定义 ***************************/
/******控制信号线******/
/* 片选 */
#define _ILI_CS_PORT_ A
#define _ILI_CS_PIN_ 4
/* RS(D/I)引脚 */
#define _ILI_RS_PORT_ A
#define _ILI_RS_PIN_ 5
/* 写使能 */
#define _ILI_WR_PORT_ A
#define _ILI_WR_PIN_ 6
/* 读使能 */
#define _ILI_RD_PORT_ A
#define _ILI_RD_PIN_ 7
/* 复位 */
#define _ILI_RST_PORT_ A
#define _ILI_RST_PIN_ 15
/* 背光引脚 */
#define _ILI_BK_PORT_ A
#define _ILI_BK_PIN_ 8
/* 数据信号线 */
#define _ILI_DATA_PORT_ B

/********信号线控制相关的宏***************/
// 中间宏：强制展开参数
#define _EXPAND_CLK(port) RCC_APB2Periph_GPIO##port
#define _EXPAND_PORT(port) GPIO##port
#define _EXPAND_PIN(pin) GPIO_Pin_##pin

// 最终宏：调用中间宏
#define EXPAND_CLK(port) _EXPAND_CLK(port)
#define EXPAND_PORT(port) _EXPAND_PORT(port)
#define EXPAND_PIN(pin) _EXPAND_PIN(pin)

#define ILI9340X_CS_SET EXPAND_PORT(_ILI_CS_PORT_)->BSRR = EXPAND_PIN(_ILI_CS_PIN_) // 片选端口
#define ILI9340X_RS_SET EXPAND_PORT(_ILI_RS_PORT_)->BSRR = EXPAND_PIN(_ILI_RS_PIN_) // 数据/命令
#define ILI9340X_WR_SET EXPAND_PORT(_ILI_WR_PORT_)->BSRR = EXPAND_PIN(_ILI_WR_PIN_) // 写数据
#define ILI9340X_RD_SET EXPAND_PORT(_ILI_RD_PORT_)->BSRR = EXPAND_PIN(_ILI_RD_PIN_) // 读数据

#define ILI9340X_CS_CLR EXPAND_PORT(_ILI_CS_PORT_)->BRR = EXPAND_PIN(_ILI_CS_PIN_) // 片选端口
#define ILI9340X_RS_CLR EXPAND_PORT(_ILI_RS_PORT_)->BRR = EXPAND_PIN(_ILI_RS_PIN_) // 数据/命令
#define ILI9340X_WR_CLR EXPAND_PORT(_ILI_WR_PORT_)->BRR = EXPAND_PIN(_ILI_WR_PIN_) // 写数据
#define ILI9340X_RD_CLR EXPAND_PORT(_ILI_RD_PORT_)->BRR = EXPAND_PIN(_ILI_RD_PIN_) // 读数据

// 数据线输入输出
#define ILI9340X_DATAOUT(x)                          \
    do                                               \
    {                                                \
        EXPAND_PORT(_ILI_DATA_PORT_)->ODR &= 0xFF00; \
        EXPAND_PORT(_ILI_DATA_PORT_)->ODR += x;      \
    } while (0) // 数据输出
#define ILI9340X_DATAIN ((EXPAND_PORT(_ILI_DATA_PORT_)->IDR) & 0x00FF) // 数据输入

/*************************************** 调试预用 ******************************************/
#define ILI9340X_DEBUG_DELAY() // bsp_DelayMS(10)
/***************************** ILI934 显示区域的起始坐标和总行列数 ***************************/
#define ILI9340X_DispWindow_X_Star 0 // 起始点的X坐标
#define ILI9340X_DispWindow_Y_Star 0 // 起始点的Y坐标

#define ILI9340X_LESS_PIXEL 240 // 液晶屏较短方向的像素宽度
#define ILI9340X_MORE_PIXEL 320 // 液晶屏较长方向的像素宽度

/******************************* 定义 ILI9340X 显示屏常用颜色 ********************************/
#define BACKGROUND BLACK // 默认背景颜色

#define WHITE 0xFFFF   // 白色
#define BLACK 0x0000   // 黑色
#define GREY 0xF7DE    // 灰色
#define BLUE 0x001F    // 蓝色
#define BLUE2 0x051F   // 浅蓝色
#define RED 0xF800     // 红色
#define MAGENTA 0xF81F // 红紫色，洋红色
#define GREEN 0x07E0   // 绿色
#define CYAN 0x7FFF    // 蓝绿色，青色
#define YELLOW 0xFFE0  // 黄色
#define BRED 0xF81F
#define GRED 0xFFE0
#define GBLUE 0x07FF

/******************************* 定义 ILI9340X 常用命令 ********************************/
#define CMD_SetCoordinateX 0x2A // 设置X坐标
#define CMD_SetCoordinateY 0x2B // 设置Y坐标
#define CMD_SetPixel 0x2C       // 填充像素

/* 像素R角表 */
#define LZM_PIXEL_R_LIST                                                                                                                                                                                                                                                                                                                                                                                                                                                                 \
	LZM_PIXEL_R_DEBUG_X(4 /* R角 */, {2, 1, 0, 0})                                                                                                                                                                                                                                                                                                                                                                                                                                       \
	LZM_PIXEL_R_DEBUG_X(5 /* R角 */, {3, 1, 1, 0, 0})                                                                                                                                                                                                                                                                                                                                                                                                                                    \
	LZM_PIXEL_R_DEBUG_X(6 /* R角 */, {4, 2, 1, 1, 0, 0})                                                                                                                                                                                                                                                                                                                                                                                                                                 \
	LZM_PIXEL_R_DEBUG_X(7 /* R角 */, {5, 3, 2, 1, 1, 0, 0})                                                                                                                                                                                                                                                                                                                                                                                                                              \
	LZM_PIXEL_R_DEBUG_X(8 /* R角 */, {5, 3, 2, 1, 1, 0, 0, 0})                                                                                                                                                                                                                                                                                                                                                                                                                           \
	LZM_PIXEL_R_DEBUG_X(9 /* R角 */, {6, 4, 3, 2, 1, 1, 0, 0, 0})                                                                                                                                                                                                                                                                                                                                                                                                                        \
	LZM_PIXEL_R_DEBUG_X(10 /* R角 */, {7, 5, 3, 2, 2, 1, 1, 0, 0, 0})                                                                                                                                                                                                                                                                                                                                                                                                                    \
	LZM_PIXEL_R_DEBUG_X(11 /* R角 */, {8, 5, 4, 3, 2, 1, 1, 1, 0, 0, 0})                                                                                                                                                                                                                                                                                                                                                                                                                 \
	LZM_PIXEL_R_DEBUG_X(12 /* R角 */, {9, 7, 5, 4, 3, 2, 2, 1, 1, 0, 0, 0})                                                                                                                                                                                                                                                                                                                                                                                                              \
	LZM_PIXEL_R_DEBUG_X(13 /* R角 */, {9, 7, 5, 4, 3, 2, 2, 1, 1, 0, 0, 0, 0})                                                                                                                                                                                                                                                                                                                                                                                                           \
	LZM_PIXEL_R_DEBUG_X(14 /* R角 */, {10, 8, 6, 5, 4, 3, 2, 2, 1, 1, 0, 0, 0, 0})                                                                                                                                                                                                                                                                                                                                                                                                       \
	LZM_PIXEL_R_DEBUG_X(15 /* R角 */, {11, 8, 6, 5, 4, 3, 2, 2, 1, 1, 1, 0, 0, 0, 0})                                                                                                                                                                                                                                                                                                                                                                                                    \
	LZM_PIXEL_R_DEBUG_X(16 /* R角 */, {12, 9, 7, 6, 5, 4, 3, 2, 2, 1, 1, 1, 0, 0, 0, 0})                                                                                                                                                                                                                                                                                                                                                                                                 \
	LZM_PIXEL_R_DEBUG_X(17 /* R角 */, {13, 10, 8, 7, 5, 4, 4, 3, 2, 2, 1, 1, 1, 0, 0, 0, 0})                                                                                                                                                                                                                                                                                                                                                                                             \
	LZM_PIXEL_R_DEBUG_X(18 /* R角 */, {14, 11, 9, 7, 6, 5, 4, 3, 3, 2, 2, 1, 1, 1, 0, 0, 0, 0})                                                                                                                                                                                                                                                                                                                                                                                          \
	LZM_PIXEL_R_DEBUG_X(19 /* R角 */, {15, 12, 10, 8, 7, 6, 5, 4, 3, 3, 2, 2, 1, 1, 1, 0, 0, 0, 0})                                                                                                                                                                                                                                                                                                                                                                                      \
	LZM_PIXEL_R_DEBUG_X(20 /* R角 */, {16, 12, 10, 9, 7, 6, 5, 4, 4, 3, 2, 2, 1, 1, 1, 1, 0, 0, 0, 0})                                                                                                                                                                                                                                                                                                                                                                                   \
	LZM_PIXEL_R_DEBUG_X(21 /* R角 */, {16, 13, 11, 9, 8, 7, 6, 5, 4, 3, 3, 2, 2, 1, 1, 1, 0, 0, 0, 0, 0})                                                                                                                                                                                                                                                                                                                                                                                \
	LZM_PIXEL_R_DEBUG_X(22 /* R角 */, {17, 14, 12, 10, 9, 7, 6, 5, 5, 4, 3, 3, 2, 2, 1, 1, 1, 0, 0, 0, 0, 0})                                                                                                                                                                                                                                                                                                                                                                            \
	LZM_PIXEL_R_DEBUG_X(23 /* R角 */, {18, 15, 13, 11, 9, 8, 7, 6, 5, 4, 4, 3, 3, 2, 2, 1, 1, 1, 0, 0, 0, 0, 0})                                                                                                                                                                                                                                                                                                                                                                         \
	LZM_PIXEL_R_DEBUG_X(24 /* R角 */, {19, 16, 13, 12, 10, 9, 8, 7, 6, 5, 4, 4, 3, 2, 2, 2, 1, 1, 1, 0, 0, 0, 0, 0})                                                                                                                                                                                                                                                                                                                                                                     \
	LZM_PIXEL_R_DEBUG_X(25 /* R角 */, {20, 16, 14, 12, 11, 9, 8, 7, 6, 5, 5, 4, 3, 3, 2, 2, 1, 1, 1, 1, 0, 0, 0, 0, 0})                                                                                                                                                                                                                                                                                                                                                                  \
	LZM_PIXEL_R_DEBUG_X(26 /* R角 */, {21, 17, 15, 13, 11, 10, 9, 8, 7, 6, 5, 4, 4, 3, 3, 2, 2, 1, 1, 1, 1, 0, 0, 0, 0, 0})                                                                                                                                                                                                                                                                                                                                                              \
	LZM_PIXEL_R_DEBUG_X(27 /* R角 */, {22, 18, 16, 14, 12, 11, 9, 8, 7, 6, 6, 5, 4, 4, 3, 3, 2, 2, 1, 1, 1, 1, 0, 0, 0, 0, 0})                                                                                                                                                                                                                                                                                                                                                           \
	LZM_PIXEL_R_DEBUG_X(28 /* R角 */, {23, 19, 16, 14, 13, 11, 10, 9, 8, 7, 6, 5, 5, 4, 3, 3, 2, 2, 2, 1, 1, 1, 1, 0, 0, 0, 0, 0})                                                                                                                                                                                                                                                                                                                                                       \
	LZM_PIXEL_R_DEBUG_X(29 /* R角 */, {24, 20, 17, 15, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 4, 3, 3, 2, 2, 2, 1, 1, 1, 1, 0, 0, 0, 0, 0})                                                                                                                                                                                                                                                                                                                                                   \
	LZM_PIXEL_R_DEBUG_X(30 /* R角 */, {25, 21, 18, 16, 14, 13, 11, 10, 9, 8, 7, 6, 6, 5, 4, 4, 3, 3, 2, 2, 2, 1, 1, 1, 1, 0, 0, 0, 0, 0})                                                                                                                                                                                                                                                                                                                                                \
	LZM_PIXEL_R_DEBUG_X(31 /* R角 */, {25, 21, 19, 17, 15, 13, 12, 11, 10, 9, 8, 7, 6, 5, 5, 4, 4, 3, 3, 2, 2, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0})                                                                                                                                                                                                                                                                                                                                            \
	LZM_PIXEL_R_DEBUG_X(32 /* R角 */, {26, 22, 20, 17, 16, 14, 13, 11, 10, 9, 8, 7, 7, 6, 5, 5, 4, 3, 3, 3, 2, 2, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0})                                                                                                                                                                                                                                                                                                                                         \
	LZM_PIXEL_R_DEBUG_X(33 /* R角 */, {27, 23, 20, 18, 16, 15, 13, 12, 11, 10, 9, 8, 7, 6, 6, 5, 4, 4, 3, 3, 2, 2, 2, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0})                                                                                                                                                                                                                                                                                                                                     \
	LZM_PIXEL_R_DEBUG_X(34 /* R角 */, {28, 24, 21, 19, 17, 15, 14, 13, 12, 10, 9, 9, 8, 7, 6, 5, 5, 4, 4, 3, 3, 2, 2, 2, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0})                                                                                                                                                                                                                                                                                                                                  \
	LZM_PIXEL_R_DEBUG_X(35 /* R角 */, {29, 25, 22, 20, 18, 16, 15, 13, 12, 11, 10, 9, 8, 7, 7, 6, 5, 5, 4, 4, 3, 3, 2, 2, 2, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0})                                                                                                                                                                                                                                                                                                                              \
	LZM_PIXEL_R_DEBUG_X(36 /* R角 */, {30, 26, 23, 21, 19, 17, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 6, 5, 5, 4, 4, 3, 3, 2, 2, 2, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0})                                                                                                                                                                                                                                                                                                                          \
	LZM_PIXEL_R_DEBUG_X(37 /* R角 */, {31, 27, 24, 21, 19, 18, 16, 15, 13, 12, 11, 10, 9, 8, 8, 7, 6, 6, 5, 4, 4, 3, 3, 3, 2, 2, 2, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0})                                                                                                                                                                                                                                                                                                                       \
	LZM_PIXEL_R_DEBUG_X(38 /* R角 */, {32, 27, 24, 22, 20, 18, 17, 15, 14, 13, 12, 11, 10, 9, 8, 7, 7, 6, 5, 5, 4, 4, 3, 3, 2, 2, 2, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0})                                                                                                                                                                                                                                                                                                                   \
	LZM_PIXEL_R_DEBUG_X(39 /* R角 */, {33, 28, 25, 23, 21, 19, 17, 16, 15, 13, 12, 11, 10, 9, 9, 8, 7, 6, 6, 5, 5, 4, 4, 3, 3, 2, 2, 2, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0})                                                                                                                                                                                                                                                                                                                \
	LZM_PIXEL_R_DEBUG_X(40 /* R角 */, {34, 29, 26, 24, 22, 20, 18, 17, 15, 14, 13, 12, 11, 10, 9, 8, 8, 7, 6, 6, 5, 5, 4, 4, 3, 3, 2, 2, 2, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0})                                                                                                                                                                                                                                                                                                            \
	LZM_PIXEL_R_DEBUG_X(41 /* R角 */, {35, 30, 27, 24, 22, 20, 19, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 7, 6, 5, 5, 4, 4, 3, 3, 3, 2, 2, 2, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0})                                                                                                                                                                                                                                                                                                        \
	LZM_PIXEL_R_DEBUG_X(42 /* R角 */, {36, 31, 28, 25, 23, 21, 20, 18, 17, 15, 14, 13, 12, 11, 10, 9, 9, 8, 7, 7, 6, 5, 5, 4, 4, 3, 3, 3, 2, 2, 2, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0})                                                                                                                                                                                                                                                                                                     \
	LZM_PIXEL_R_DEBUG_X(43 /* R角 */, {36, 32, 29, 26, 24, 22, 20, 19, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 8, 7, 6, 6, 5, 5, 4, 4, 3, 3, 3, 2, 2, 2, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0})                                                                                                                                                                                                                                                                                                 \
	LZM_PIXEL_R_DEBUG_X(44 /* R角 */, {37, 33, 29, 27, 25, 23, 21, 19, 18, 17, 15, 14, 13, 12, 11, 10, 10, 9, 8, 7, 7, 6, 6, 5, 5, 4, 4, 3, 3, 2, 2, 2, 2, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0})                                                                                                                                                                                                                                                                                             \
	LZM_PIXEL_R_DEBUG_X(45 /* R角 */, {38, 33, 30, 28, 25, 23, 22, 20, 19, 17, 16, 15, 14, 13, 12, 11, 10, 9, 9, 8, 7, 7, 6, 5, 5, 4, 4, 4, 3, 3, 2, 2, 2, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0})                                                                                                                                                                                                                                                                                          \
	LZM_PIXEL_R_DEBUG_X(46 /* R角 */, {39, 34, 31, 28, 26, 24, 22, 21, 19, 18, 17, 16, 14, 13, 12, 12, 11, 10, 9, 8, 8, 7, 6, 6, 5, 5, 4, 4, 3, 3, 3, 2, 2, 2, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0})                                                                                                                                                                                                                                                                                      \
	LZM_PIXEL_R_DEBUG_X(47 /* R角 */, {40, 35, 32, 29, 27, 25, 23, 22, 20, 19, 17, 16, 15, 14, 13, 12, 11, 10, 10, 9, 8, 8, 7, 6, 6, 5, 5, 4, 4, 3, 3, 3, 2, 2, 2, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0})                                                                                                                                                                                                                                                                                  \
	LZM_PIXEL_R_DEBUG_X(48 /* R角 */, {41, 36, 33, 30, 28, 26, 24, 22, 21, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 9, 8, 7, 7, 6, 6, 5, 5, 4, 4, 3, 3, 3, 2, 2, 2, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0})                                                                                                                                                                                                                                                                               \
	LZM_PIXEL_R_DEBUG_X(49 /* R角 */, {42, 37, 34, 31, 28, 26, 25, 23, 21, 20, 19, 17, 16, 15, 14, 13, 12, 11, 11, 10, 9, 8, 8, 7, 7, 6, 5, 5, 4, 4, 4, 3, 3, 3, 2, 2, 2, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0})                                                                                                                                                                                                                                                                           \
	LZM_PIXEL_R_DEBUG_X(50 /* R角 */, {43, 38, 34, 32, 29, 27, 25, 24, 22, 21, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 10, 9, 8, 8, 7, 6, 6, 5, 5, 4, 4, 4, 3, 3, 2, 2, 2, 2, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0})                                                                                                                                                                                                                                                                       \
	LZM_PIXEL_R_DEBUG_X(51 /* R角 */, {44, 39, 35, 32, 30, 28, 26, 24, 23, 21, 20, 19, 18, 16, 15, 14, 13, 13, 12, 11, 10, 9, 9, 8, 7, 7, 6, 6, 5, 5, 4, 4, 3, 3, 3, 2, 2, 2, 2, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0})                                                                                                                                                                                                                                                                    \
	LZM_PIXEL_R_DEBUG_X(52 /* R角 */, {45, 40, 36, 33, 31, 29, 27, 25, 24, 22, 21, 19, 18, 17, 16, 15, 14, 13, 12, 11, 11, 10, 9, 9, 8, 7, 7, 6, 6, 5, 5, 4, 4, 3, 3, 3, 2, 2, 2, 2, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0})                                                                                                                                                                                                                                                                \
	LZM_PIXEL_R_DEBUG_X(53 /* R角 */, {46, 41, 37, 35, 32, 30, 28, 26, 25, 23, 22, 21, 19, 18, 17, 16, 15, 14, 13, 12, 12, 11, 10, 9, 9, 8, 7, 7, 6, 6, 5, 5, 4, 4, 4, 3, 3, 2, 2, 2, 2, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0})                                                                                                                                                                                                                                                            \
	LZM_PIXEL_R_DEBUG_X(54 /* R角 */, {47, 42, 38, 35, 33, 31, 29, 27, 25, 24, 23, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 11, 10, 9, 8, 8, 7, 7, 6, 6, 5, 5, 4, 4, 3, 3, 3, 2, 2, 2, 2, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0})                                                                                                                                                                                                                                                        \
	LZM_PIXEL_R_DEBUG_X(55 /* R角 */, {48, 43, 39, 36, 34, 32, 30, 28, 26, 25, 23, 22, 21, 19, 18, 17, 16, 15, 14, 13, 13, 12, 11, 10, 10, 9, 8, 8, 7, 7, 6, 6, 5, 5, 4, 4, 3, 3, 3, 2, 2, 2, 2, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0})                                                                                                                                                                                                                                                    \
	LZM_PIXEL_R_DEBUG_X(56 /* R角 */, {49, 44, 40, 37, 35, 32, 30, 29, 27, 25, 24, 23, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 12, 11, 10, 9, 9, 8, 8, 7, 6, 6, 5, 5, 5, 4, 4, 3, 3, 3, 2, 2, 2, 2, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0})                                                                                                                                                                                                                                                 \
	LZM_PIXEL_R_DEBUG_X(57 /* R角 */, {50, 45, 41, 38, 35, 33, 31, 29, 28, 26, 25, 23, 22, 21, 20, 18, 17, 16, 15, 15, 14, 13, 12, 11, 11, 10, 9, 9, 8, 7, 7, 6, 6, 5, 5, 4, 4, 4, 3, 3, 3, 2, 2, 2, 2, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0})                                                                                                                                                                                                                                             \
	LZM_PIXEL_R_DEBUG_X(58 /* R角 */, {51, 45, 42, 39, 36, 34, 32, 30, 28, 27, 25, 24, 23, 21, 20, 19, 18, 17, 16, 15, 14, 13, 13, 12, 11, 10, 10, 9, 8, 8, 7, 7, 6, 6, 5, 5, 4, 4, 4, 3, 3, 3, 2, 2, 2, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0})                                                                                                                                                                                                                                         \
	LZM_PIXEL_R_DEBUG_X(59 /* R角 */, {52, 46, 43, 39, 37, 35, 33, 31, 29, 27, 26, 25, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 12, 11, 10, 9, 9, 8, 8, 7, 7, 6, 6, 5, 5, 4, 4, 3, 3, 3, 3, 2, 2, 2, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0})                                                                                                                                                                                                                                      \
	LZM_PIXEL_R_DEBUG_X(60 /* R角 */, {53, 47, 43, 40, 38, 35, 33, 31, 30, 28, 27, 25, 24, 23, 21, 20, 19, 18, 17, 16, 15, 14, 14, 13, 12, 11, 11, 10, 9, 9, 8, 7, 7, 6, 6, 5, 5, 5, 4, 4, 3, 3, 3, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0})                                                                                                                                                                                                                                  \
	LZM_PIXEL_R_DEBUG_X(61 /* R角 */, {54, 48, 44, 41, 39, 36, 34, 32, 30, 29, 27, 26, 25, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 13, 12, 11, 10, 10, 9, 8, 8, 7, 7, 6, 6, 5, 5, 5, 4, 4, 3, 3, 3, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0})                                                                                                                                                                                                                              \
	LZM_PIXEL_R_DEBUG_X(62 /* R角 */, {55, 49, 45, 42, 39, 37, 35, 33, 31, 30, 28, 27, 25, 24, 23, 21, 20, 19, 18, 17, 16, 15, 15, 14, 13, 12, 12, 11, 10, 10, 9, 8, 8, 7, 7, 6, 6, 5, 5, 4, 4, 4, 3, 3, 3, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0})                                                                                                                                                                                                                          \
	LZM_PIXEL_R_DEBUG_X(63 /* R角 */, {56, 50, 46, 43, 40, 38, 36, 34, 32, 30, 29, 27, 26, 25, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 14, 13, 12, 11, 11, 10, 9, 9, 8, 8, 7, 7, 6, 6, 5, 5, 4, 4, 4, 3, 3, 3, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0})                                                                                                                                                                                                                       \
	LZM_PIXEL_R_DEBUG_X(64 /* R角 */, {57, 51, 47, 44, 41, 39, 36, 34, 33, 31, 29, 28, 27, 25, 24, 23, 22, 21, 19, 18, 18, 17, 16, 15, 14, 13, 13, 12, 11, 10, 10, 9, 9, 8, 7, 7, 6, 6, 6, 5, 5, 4, 4, 4, 3, 3, 3, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0})                                                                                                                                                                                                                   \
	LZM_PIXEL_R_DEBUG_X(65 /* R角 */, {57, 52, 48, 44, 42, 39, 37, 35, 33, 32, 30, 29, 27, 26, 25, 23, 22, 21, 20, 19, 18, 17, 16, 15, 15, 14, 13, 12, 12, 11, 10, 10, 9, 8, 8, 7, 7, 6, 6, 5, 5, 5, 4, 4, 3, 3, 3, 3, 2, 2, 2, 2, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0})                                                                                                                                                                                                               \
	LZM_PIXEL_R_DEBUG_X(66 /* R角 */, {58, 53, 49, 45, 43, 40, 38, 36, 34, 32, 31, 29, 28, 27, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 14, 13, 12, 11, 11, 10, 9, 9, 8, 8, 7, 7, 6, 6, 5, 5, 5, 4, 4, 3, 3, 3, 3, 2, 2, 2, 2, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0})                                                                                                                                                                                                            \
	LZM_PIXEL_R_DEBUG_X(67 /* R角 */, {59, 53, 49, 46, 43, 41, 39, 37, 35, 33, 31, 30, 29, 27, 26, 25, 23, 22, 21, 20, 19, 18, 17, 16, 16, 15, 14, 13, 13, 12, 11, 10, 10, 9, 9, 8, 8, 7, 7, 6, 6, 5, 5, 4, 4, 4, 3, 3, 3, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0})                                                                                                                                                                                                        \
	LZM_PIXEL_R_DEBUG_X(68 /* R角 */, {60, 54, 50, 47, 44, 42, 39, 37, 36, 34, 32, 31, 29, 28, 27, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 15, 14, 13, 12, 12, 11, 10, 10, 9, 9, 8, 7, 7, 6, 6, 6, 5, 5, 4, 4, 4, 3, 3, 3, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0})                                                                                                                                                                                                    \
	LZM_PIXEL_R_DEBUG_X(69 /* R角 */, {61, 55, 51, 48, 45, 43, 40, 38, 36, 35, 33, 31, 30, 29, 27, 26, 25, 24, 22, 21, 20, 19, 18, 18, 17, 16, 15, 14, 14, 13, 12, 11, 11, 10, 10, 9, 8, 8, 7, 7, 6, 6, 6, 5, 5, 4, 4, 4, 3, 3, 3, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0})                                                                                                                                                                                                \
	LZM_PIXEL_R_DEBUG_X(70 /* R角 */, {62, 56, 52, 49, 46, 43, 41, 39, 37, 35, 34, 32, 31, 29, 28, 27, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 16, 15, 14, 13, 13, 12, 11, 11, 10, 9, 9, 8, 8, 7, 7, 6, 6, 5, 5, 5, 4, 4, 4, 3, 3, 3, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0})                                                                                                                                                                                             \
	LZM_PIXEL_R_DEBUG_X(71 /* R角 */, {63, 57, 53, 49, 47, 44, 42, 40, 38, 36, 34, 33, 31, 30, 29, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 15, 14, 13, 12, 12, 11, 10, 10, 9, 9, 8, 8, 7, 7, 6, 6, 5, 5, 5, 4, 4, 3, 3, 3, 3, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0})                                                                                                                                                                                         \
	LZM_PIXEL_R_DEBUG_X(72 /* R角 */, {64, 58, 54, 50, 47, 45, 43, 41, 39, 37, 35, 33, 32, 31, 29, 28, 27, 25, 24, 23, 22, 21, 20, 19, 18, 17, 17, 16, 15, 14, 14, 13, 12, 11, 11, 10, 10, 9, 9, 8, 8, 7, 7, 6, 6, 5, 5, 4, 4, 4, 3, 3, 3, 3, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0})                                                                                                                                                                                     \
	LZM_PIXEL_R_DEBUG_X(73 /* R角 */, {65, 59, 55, 51, 48, 46, 43, 41, 39, 37, 36, 34, 33, 31, 30, 29, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 16, 15, 14, 13, 13, 12, 11, 11, 10, 9, 9, 8, 8, 7, 7, 6, 6, 6, 5, 5, 4, 4, 4, 3, 3, 3, 3, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0})                                                                                                                                                                                  \
	LZM_PIXEL_R_DEBUG_X(74 /* R角 */, {66, 60, 55, 52, 49, 47, 44, 42, 40, 38, 37, 35, 33, 32, 31, 29, 28, 27, 26, 24, 23, 22, 21, 20, 19, 19, 18, 17, 16, 15, 15, 14, 13, 12, 12, 11, 11, 10, 9, 9, 8, 8, 7, 7, 6, 6, 6, 5, 5, 4, 4, 4, 3, 3, 3, 2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0})                                                                                                                                                                              \
	LZM_PIXEL_R_DEBUG_X(75 /* R角 */, {67, 61, 56, 53, 50, 47, 45, 43, 41, 39, 37, 36, 34, 33, 31, 30, 29, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 17, 16, 15, 14, 14, 13, 12, 12, 11, 10, 10, 9, 9, 8, 8, 7, 7, 6, 6, 5, 5, 5, 4, 4, 4, 3, 3, 3, 2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0})                                                                                                                                                                          \
	LZM_PIXEL_R_DEBUG_X(76 /* R角 */, {68, 61, 57, 54, 51, 48, 46, 44, 42, 40, 38, 36, 35, 33, 32, 31, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 16, 15, 14, 13, 13, 12, 11, 11, 10, 10, 9, 9, 8, 8, 7, 7, 6, 6, 5, 5, 5, 4, 4, 4, 3, 3, 3, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0})                                                                                                                                                                      \
	LZM_PIXEL_R_DEBUG_X(77 /* R角 */, {69, 62, 58, 55, 52, 49, 47, 44, 42, 40, 39, 37, 35, 34, 33, 31, 30, 29, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 18, 17, 16, 15, 15, 14, 13, 12, 12, 11, 11, 10, 9, 9, 8, 8, 7, 7, 7, 6, 6, 5, 5, 5, 4, 4, 4, 3, 3, 3, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0})                                                                                                                                                                   \
	LZM_PIXEL_R_DEBUG_X(78 /* R角 */, {70, 63, 59, 55, 52, 50, 47, 45, 43, 41, 39, 38, 36, 35, 33, 32, 31, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 17, 16, 15, 14, 14, 13, 12, 12, 11, 10, 10, 9, 9, 8, 8, 7, 7, 6, 6, 6, 5, 5, 4, 4, 4, 3, 3, 3, 3, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0})                                                                                                                                                               \
	LZM_PIXEL_R_DEBUG_X(79 /* R角 */, {71, 64, 60, 56, 53, 51, 48, 46, 44, 42, 40, 38, 37, 35, 34, 33, 31, 30, 29, 28, 26, 25, 24, 23, 22, 21, 20, 20, 19, 18, 17, 16, 16, 15, 14, 13, 13, 12, 11, 11, 10, 10, 9, 9, 8, 8, 7, 7, 6, 6, 6, 5, 5, 4, 4, 4, 3, 3, 3, 3, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0})                                                                                                                                                           \
	LZM_PIXEL_R_DEBUG_X(80 /* R角 */, {72, 65, 61, 57, 54, 51, 49, 47, 45, 43, 41, 39, 38, 36, 35, 33, 32, 31, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 18, 17, 16, 15, 15, 14, 13, 13, 12, 11, 11, 10, 10, 9, 9, 8, 8, 7, 7, 6, 6, 5, 5, 5, 4, 4, 4, 3, 3, 3, 3, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0})                                                                                                                                                       \
	LZM_PIXEL_R_DEBUG_X(81 /* R角 */, {73, 66, 62, 58, 55, 52, 50, 47, 45, 43, 42, 40, 38, 37, 35, 34, 33, 31, 30, 29, 28, 27, 25, 24, 23, 22, 22, 21, 20, 19, 18, 17, 17, 16, 15, 14, 14, 13, 12, 12, 11, 11, 10, 9, 9, 8, 8, 7, 7, 7, 6, 6, 5, 5, 5, 4, 4, 4, 3, 3, 3, 3, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0})                                                                                                                                                    \
	LZM_PIXEL_R_DEBUG_X(82 /* R角 */, {73, 67, 62, 59, 56, 53, 51, 48, 46, 44, 42, 41, 39, 37, 36, 35, 33, 32, 31, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 19, 18, 17, 16, 16, 15, 14, 13, 13, 12, 12, 11, 10, 10, 9, 9, 8, 8, 7, 7, 7, 6, 6, 5, 5, 5, 4, 4, 4, 3, 3, 3, 2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0})                                                                                                                                                \
	LZM_PIXEL_R_DEBUG_X(83 /* R角 */, {74, 68, 63, 60, 57, 54, 51, 49, 47, 45, 43, 41, 40, 38, 37, 35, 34, 33, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 18, 17, 16, 15, 15, 14, 13, 13, 12, 11, 11, 10, 10, 9, 9, 8, 8, 7, 7, 6, 6, 6, 5, 5, 5, 4, 4, 4, 3, 3, 3, 2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0})                                                                                                                                            \
	LZM_PIXEL_R_DEBUG_X(84 /* R角 */, {75, 69, 64, 61, 57, 55, 52, 50, 48, 46, 44, 42, 40, 39, 37, 36, 35, 33, 32, 31, 30, 28, 27, 26, 25, 24, 23, 22, 21, 21, 20, 19, 18, 17, 17, 16, 15, 14, 14, 13, 12, 12, 11, 11, 10, 10, 9, 9, 8, 8, 7, 7, 6, 6, 6, 5, 5, 4, 4, 4, 4, 3, 3, 3, 2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0})                                                                                                                                        \
	LZM_PIXEL_R_DEBUG_X(85 /* R角 */, {76, 70, 65, 61, 58, 55, 53, 51, 48, 46, 45, 43, 41, 40, 38, 37, 35, 34, 33, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 19, 18, 17, 16, 16, 15, 14, 14, 13, 12, 12, 11, 11, 10, 9, 9, 8, 8, 8, 7, 7, 6, 6, 5, 5, 5, 4, 4, 4, 3, 3, 3, 3, 2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0})                                                                                                                                     \
	LZM_PIXEL_R_DEBUG_X(86 /* R角 */, {77, 71, 66, 62, 59, 56, 54, 51, 49, 47, 45, 44, 42, 40, 39, 37, 36, 35, 33, 32, 31, 30, 29, 27, 26, 25, 24, 23, 23, 22, 21, 20, 19, 18, 18, 17, 16, 15, 15, 14, 13, 13, 12, 12, 11, 10, 10, 9, 9, 8, 8, 7, 7, 7, 6, 6, 5, 5, 5, 4, 4, 4, 3, 3, 3, 3, 2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0})                                                                                                                                 \
	LZM_PIXEL_R_DEBUG_X(87 /* R角 */, {78, 71, 67, 63, 60, 57, 55, 52, 50, 48, 46, 44, 43, 41, 39, 38, 37, 35, 34, 33, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 20, 19, 18, 17, 17, 16, 15, 14, 14, 13, 13, 12, 11, 11, 10, 10, 9, 9, 8, 8, 7, 7, 7, 6, 6, 5, 5, 5, 4, 4, 4, 3, 3, 3, 3, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0})                                                                                                                             \
	LZM_PIXEL_R_DEBUG_X(88 /* R角 */, {79, 72, 68, 64, 61, 58, 55, 53, 51, 49, 47, 45, 43, 42, 40, 39, 37, 36, 35, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 19, 18, 17, 16, 16, 15, 14, 14, 13, 12, 12, 11, 11, 10, 10, 9, 9, 8, 8, 7, 7, 6, 6, 6, 5, 5, 5, 4, 4, 4, 3, 3, 3, 3, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0})                                                                                                                         \
	LZM_PIXEL_R_DEBUG_X(89 /* R角 */, {80, 73, 69, 65, 62, 59, 56, 54, 52, 49, 48, 46, 44, 42, 41, 39, 38, 37, 35, 34, 33, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 21, 20, 19, 18, 18, 17, 16, 15, 15, 14, 13, 13, 12, 12, 11, 11, 10, 9, 9, 9, 8, 8, 7, 7, 6, 6, 6, 5, 5, 5, 4, 4, 4, 3, 3, 3, 3, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0})                                                                                                                      \
	LZM_PIXEL_R_DEBUG_X(90 /* R角 */, {81, 74, 69, 66, 62, 60, 57, 55, 52, 50, 48, 46, 45, 43, 42, 40, 39, 37, 36, 35, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 20, 19, 18, 17, 17, 16, 15, 15, 14, 13, 13, 12, 11, 11, 10, 10, 9, 9, 8, 8, 8, 7, 7, 6, 6, 6, 5, 5, 4, 4, 4, 4, 3, 3, 3, 2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0})                                                                                                                  \
	LZM_PIXEL_R_DEBUG_X(91 /* R角 */, {82, 75, 70, 67, 63, 60, 58, 55, 53, 51, 49, 47, 45, 44, 42, 41, 39, 38, 37, 35, 34, 33, 32, 30, 29, 28, 27, 26, 25, 24, 23, 23, 22, 21, 20, 19, 19, 18, 17, 16, 16, 15, 14, 14, 13, 12, 12, 11, 11, 10, 10, 9, 9, 8, 8, 7, 7, 7, 6, 6, 5, 5, 5, 4, 4, 4, 4, 3, 3, 3, 2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0})                                                                                                              \
	LZM_PIXEL_R_DEBUG_X(92 /* R角 */, {83, 76, 71, 67, 64, 61, 59, 56, 54, 52, 50, 48, 46, 45, 43, 41, 40, 39, 37, 36, 35, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 21, 20, 19, 18, 18, 17, 16, 15, 15, 14, 14, 13, 12, 12, 11, 11, 10, 10, 9, 9, 8, 8, 7, 7, 7, 6, 6, 5, 5, 5, 4, 4, 4, 3, 3, 3, 3, 2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0})                                                                                                          \
	LZM_PIXEL_R_DEBUG_X(93 /* R角 */, {84, 77, 72, 68, 65, 62, 59, 57, 55, 53, 51, 49, 47, 45, 44, 42, 41, 39, 38, 37, 35, 34, 33, 32, 31, 30, 28, 27, 26, 26, 25, 24, 23, 22, 21, 20, 20, 19, 18, 17, 17, 16, 15, 15, 14, 13, 13, 12, 12, 11, 11, 10, 10, 9, 9, 8, 8, 7, 7, 6, 6, 6, 5, 5, 5, 4, 4, 4, 3, 3, 3, 3, 2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0})                                                                                                      \
	LZM_PIXEL_R_DEBUG_X(94 /* R角 */, {85, 78, 73, 69, 66, 63, 60, 58, 55, 53, 51, 49, 48, 46, 44, 43, 41, 40, 39, 37, 36, 35, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 22, 21, 20, 19, 19, 18, 17, 16, 16, 15, 14, 14, 13, 13, 12, 11, 11, 10, 10, 9, 9, 8, 8, 8, 7, 7, 6, 6, 6, 5, 5, 5, 4, 4, 4, 3, 3, 3, 3, 2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0})                                                                                                   \
	LZM_PIXEL_R_DEBUG_X(95 /* R角 */, {86, 79, 74, 70, 67, 64, 61, 59, 56, 54, 52, 50, 48, 47, 45, 43, 42, 41, 39, 38, 37, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 21, 20, 19, 18, 18, 17, 16, 15, 15, 14, 14, 13, 12, 12, 11, 11, 10, 10, 9, 9, 8, 8, 8, 7, 7, 6, 6, 6, 5, 5, 5, 4, 4, 4, 3, 3, 3, 3, 2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0})                                                                                               \
	LZM_PIXEL_R_DEBUG_X(96 /* R角 */, {87, 80, 75, 71, 67, 64, 62, 59, 57, 55, 53, 51, 49, 47, 46, 44, 43, 41, 40, 39, 37, 36, 35, 34, 32, 31, 30, 29, 28, 27, 26, 25, 24, 24, 23, 22, 21, 20, 20, 19, 18, 17, 17, 16, 15, 15, 14, 13, 13, 12, 12, 11, 11, 10, 10, 9, 9, 8, 8, 7, 7, 7, 6, 6, 5, 5, 5, 4, 4, 4, 4, 3, 3, 3, 3, 2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0})                                                                                           \
	LZM_PIXEL_R_DEBUG_X(97 /* R角 */, {88, 81, 76, 72, 68, 65, 63, 60, 58, 56, 54, 52, 50, 48, 46, 45, 43, 42, 41, 39, 38, 37, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 22, 21, 20, 19, 19, 18, 17, 16, 16, 15, 14, 14, 13, 13, 12, 12, 11, 11, 10, 10, 9, 9, 8, 8, 7, 7, 7, 6, 6, 5, 5, 5, 4, 4, 4, 4, 3, 3, 3, 3, 2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0})                                                                                       \
	LZM_PIXEL_R_DEBUG_X(98 /* R角 */, {89, 81, 77, 73, 69, 66, 63, 61, 59, 56, 54, 52, 51, 49, 47, 46, 44, 43, 41, 40, 39, 37, 36, 35, 34, 33, 31, 30, 29, 28, 27, 26, 26, 25, 24, 23, 22, 21, 21, 20, 19, 18, 18, 17, 16, 16, 15, 14, 14, 13, 13, 12, 11, 11, 10, 10, 9, 9, 9, 8, 8, 7, 7, 6, 6, 6, 5, 5, 5, 4, 4, 4, 4, 3, 3, 3, 3, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0})                                                                                    \
	LZM_PIXEL_R_DEBUG_X(99 /* R角 */, {90, 82, 77, 73, 70, 67, 64, 62, 59, 57, 55, 53, 51, 50, 48, 46, 45, 43, 42, 41, 39, 38, 37, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 23, 22, 21, 20, 20, 19, 18, 17, 17, 16, 15, 15, 14, 14, 13, 12, 12, 11, 11, 10, 10, 9, 9, 8, 8, 8, 7, 7, 6, 6, 6, 5, 5, 5, 4, 4, 4, 3, 3, 3, 3, 2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0})                                                                                \
	LZM_PIXEL_R_DEBUG_X(100 /* R角 */, {91, 83, 78, 74, 71, 68, 65, 63, 60, 58, 56, 54, 52, 50, 49, 47, 45, 44, 43, 41, 40, 39, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 22, 21, 20, 19, 19, 18, 17, 16, 16, 15, 15, 14, 13, 13, 12, 12, 11, 11, 10, 10, 9, 9, 8, 8, 8, 7, 7, 6, 6, 6, 5, 5, 5, 4, 4, 4, 3, 3, 3, 3, 2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0})                                                                           \
	LZM_PIXEL_R_DEBUG_X(101 /* R角 */, {91, 84, 79, 75, 72, 69, 66, 63, 61, 59, 57, 55, 53, 51, 49, 48, 46, 45, 43, 42, 41, 39, 38, 37, 36, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 25, 24, 23, 22, 21, 21, 20, 19, 18, 18, 17, 16, 16, 15, 14, 14, 13, 13, 12, 12, 11, 11, 10, 10, 9, 9, 8, 8, 7, 7, 7, 6, 6, 6, 5, 5, 5, 4, 4, 4, 3, 3, 3, 3, 2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0})                                                                       \
	LZM_PIXEL_R_DEBUG_X(102 /* R角 */, {92, 85, 80, 76, 73, 69, 67, 64, 62, 60, 57, 55, 54, 52, 50, 48, 47, 45, 44, 43, 41, 40, 39, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 23, 22, 21, 20, 20, 19, 18, 17, 17, 16, 15, 15, 14, 14, 13, 13, 12, 11, 11, 10, 10, 10, 9, 9, 8, 8, 7, 7, 7, 6, 6, 5, 5, 5, 5, 4, 4, 4, 3, 3, 3, 3, 2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0})                                                                   \
	LZM_PIXEL_R_DEBUG_X(103 /* R角 */, {93, 86, 81, 77, 73, 70, 67, 65, 63, 60, 58, 56, 54, 53, 51, 49, 48, 46, 45, 43, 42, 41, 39, 38, 37, 36, 35, 33, 32, 31, 30, 29, 28, 27, 27, 26, 25, 24, 23, 22, 22, 21, 20, 19, 19, 18, 17, 17, 16, 15, 15, 14, 14, 13, 12, 12, 11, 11, 10, 10, 9, 9, 9, 8, 8, 7, 7, 6, 6, 6, 5, 5, 5, 4, 4, 4, 4, 3, 3, 3, 3, 2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0})                                                                \
	LZM_PIXEL_R_DEBUG_X(104 /* R角 */, {94, 87, 82, 78, 74, 71, 68, 66, 63, 61, 59, 57, 55, 53, 52, 50, 48, 47, 45, 44, 43, 41, 40, 39, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 24, 23, 22, 21, 21, 20, 19, 18, 18, 17, 16, 16, 15, 15, 14, 13, 13, 12, 12, 11, 11, 10, 10, 9, 9, 8, 8, 8, 7, 7, 6, 6, 6, 5, 5, 5, 4, 4, 4, 4, 3, 3, 3, 3, 2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0})                                                            \
	LZM_PIXEL_R_DEBUG_X(105 /* R角 */, {95, 88, 83, 79, 75, 72, 69, 67, 64, 62, 60, 58, 56, 54, 52, 51, 49, 47, 46, 45, 43, 42, 41, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 23, 22, 21, 20, 20, 19, 18, 17, 17, 16, 16, 15, 14, 14, 13, 13, 12, 12, 11, 11, 10, 10, 9, 9, 8, 8, 8, 7, 7, 6, 6, 6, 5, 5, 5, 4, 4, 4, 4, 3, 3, 3, 3, 2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0})                                                        \
	LZM_PIXEL_R_DEBUG_X(106 /* R角 */, {96, 89, 84, 79, 76, 73, 70, 67, 65, 63, 61, 58, 57, 55, 53, 51, 50, 48, 47, 45, 44, 43, 41, 40, 39, 38, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 26, 25, 24, 23, 22, 22, 21, 20, 19, 19, 18, 17, 17, 16, 15, 15, 14, 14, 13, 13, 12, 11, 11, 11, 10, 10, 9, 9, 8, 8, 7, 7, 7, 6, 6, 6, 5, 5, 5, 4, 4, 4, 3, 3, 3, 3, 3, 2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0})                                                    \
	LZM_PIXEL_R_DEBUG_X(107 /* R角 */, {97, 90, 85, 80, 77, 74, 71, 68, 66, 63, 61, 59, 57, 55, 54, 52, 50, 49, 47, 46, 45, 43, 42, 41, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 24, 23, 22, 21, 21, 20, 19, 18, 18, 17, 16, 16, 15, 15, 14, 13, 13, 12, 12, 11, 11, 10, 10, 9, 9, 9, 8, 8, 7, 7, 7, 6, 6, 6, 5, 5, 5, 4, 4, 4, 3, 3, 3, 3, 3, 2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0})                                                 \
	LZM_PIXEL_R_DEBUG_X(108 /* R角 */, {98, 91, 85, 81, 78, 74, 72, 69, 67, 64, 62, 60, 58, 56, 54, 53, 51, 50, 48, 47, 45, 44, 43, 41, 40, 39, 38, 37, 35, 34, 33, 32, 31, 30, 29, 28, 28, 27, 26, 25, 24, 23, 23, 22, 21, 20, 20, 19, 18, 18, 17, 16, 16, 15, 14, 14, 13, 13, 12, 12, 11, 11, 10, 10, 9, 9, 9, 8, 8, 7, 7, 7, 6, 6, 5, 5, 5, 5, 4, 4, 4, 3, 3, 3, 3, 2, 2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0})                                             \
	LZM_PIXEL_R_DEBUG_X(109 /* R角 */, {99, 91, 86, 82, 79, 75, 72, 70, 67, 65, 63, 61, 59, 57, 55, 53, 52, 50, 49, 47, 46, 45, 43, 42, 41, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 25, 24, 23, 22, 22, 21, 20, 19, 19, 18, 17, 17, 16, 15, 15, 14, 14, 13, 13, 12, 12, 11, 11, 10, 10, 9, 9, 8, 8, 8, 7, 7, 6, 6, 6, 5, 5, 5, 5, 4, 4, 4, 3, 3, 3, 3, 2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0})                                         \
	LZM_PIXEL_R_DEBUG_X(110 /* R角 */, {100, 92, 87, 83, 79, 76, 73, 71, 68, 66, 64, 62, 60, 58, 56, 54, 53, 51, 49, 48, 47, 45, 44, 43, 41, 40, 39, 38, 37, 36, 34, 33, 32, 31, 30, 30, 29, 28, 27, 26, 25, 24, 24, 23, 22, 21, 21, 20, 19, 18, 18, 17, 17, 16, 15, 15, 14, 14, 13, 13, 12, 12, 11, 11, 10, 10, 9, 9, 8, 8, 8, 7, 7, 6, 6, 6, 5, 5, 5, 4, 4, 4, 4, 3, 3, 3, 3, 2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0})                                    \
	LZM_PIXEL_R_DEBUG_X(111 /* R角 */, {101, 93, 88, 84, 80, 77, 74, 71, 69, 67, 64, 62, 60, 58, 57, 55, 53, 52, 50, 49, 47, 46, 45, 43, 42, 41, 40, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 27, 26, 25, 24, 23, 23, 22, 21, 20, 20, 19, 18, 18, 17, 16, 16, 15, 15, 14, 13, 13, 12, 12, 11, 11, 10, 10, 10, 9, 9, 8, 8, 7, 7, 7, 6, 6, 6, 5, 5, 5, 4, 4, 4, 4, 3, 3, 3, 3, 2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0})                                \
	LZM_PIXEL_R_DEBUG_X(112 /* R角 */, {102, 94, 89, 85, 81, 78, 75, 72, 70, 67, 65, 63, 61, 59, 57, 56, 54, 52, 51, 49, 48, 47, 45, 44, 43, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 25, 24, 23, 22, 22, 21, 20, 19, 19, 18, 17, 17, 16, 16, 15, 14, 14, 13, 13, 12, 12, 11, 11, 10, 10, 9, 9, 9, 8, 8, 7, 7, 7, 6, 6, 6, 5, 5, 5, 4, 4, 4, 4, 3, 3, 3, 3, 2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0})                             \
	LZM_PIXEL_R_DEBUG_X(113 /* R角 */, {103, 95, 90, 86, 82, 79, 76, 73, 71, 68, 66, 64, 62, 60, 58, 56, 55, 53, 52, 50, 49, 47, 46, 45, 43, 42, 41, 40, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 28, 27, 26, 25, 24, 24, 23, 22, 21, 21, 20, 19, 19, 18, 17, 17, 16, 15, 15, 14, 14, 13, 13, 12, 12, 11, 11, 10, 10, 9, 9, 9, 8, 8, 7, 7, 7, 6, 6, 6, 5, 5, 5, 4, 4, 4, 4, 3, 3, 3, 3, 2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0})                         \
	LZM_PIXEL_R_DEBUG_X(114 /* R角 */, {104, 96, 91, 86, 83, 80, 77, 74, 71, 69, 67, 65, 63, 61, 59, 57, 55, 54, 52, 51, 49, 48, 47, 45, 44, 43, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 26, 25, 24, 23, 23, 22, 21, 20, 20, 19, 18, 18, 17, 16, 16, 15, 15, 14, 14, 13, 13, 12, 12, 11, 11, 10, 10, 9, 9, 8, 8, 8, 7, 7, 7, 6, 6, 6, 5, 5, 5, 4, 4, 4, 3, 3, 3, 3, 3, 2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0})                     \
	LZM_PIXEL_R_DEBUG_X(115 /* R角 */, {105, 97, 92, 87, 84, 80, 77, 75, 72, 70, 67, 65, 63, 61, 60, 58, 56, 55, 53, 51, 50, 49, 47, 46, 45, 43, 42, 41, 40, 39, 37, 36, 35, 34, 33, 32, 31, 30, 30, 29, 28, 27, 26, 25, 25, 24, 23, 22, 22, 21, 20, 19, 19, 18, 18, 17, 16, 16, 15, 15, 14, 13, 13, 12, 12, 11, 11, 10, 10, 10, 9, 9, 8, 8, 8, 7, 7, 6, 6, 6, 5, 5, 5, 5, 4, 4, 4, 3, 3, 3, 3, 3, 2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0})                 \
	LZM_PIXEL_R_DEBUG_X(116 /* R角 */, {106, 98, 93, 88, 85, 81, 78, 75, 73, 71, 68, 66, 64, 62, 60, 59, 57, 55, 54, 52, 51, 49, 48, 47, 45, 44, 43, 42, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 28, 27, 26, 25, 24, 24, 23, 22, 21, 21, 20, 19, 19, 18, 17, 17, 16, 16, 15, 14, 14, 13, 13, 12, 12, 11, 11, 10, 10, 10, 9, 9, 8, 8, 7, 7, 7, 6, 6, 6, 5, 5, 5, 5, 4, 4, 4, 3, 3, 3, 3, 3, 2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0})             \
	LZM_PIXEL_R_DEBUG_X(117 /* R角 */, {107, 99, 93, 89, 85, 82, 79, 76, 74, 71, 69, 67, 65, 63, 61, 59, 58, 56, 54, 53, 51, 50, 49, 47, 46, 45, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 39, 29, 28, 27, 26, 26, 25, 24, 23, 23, 22, 21, 20, 20, 19, 18, 18, 17, 17, 16, 15, 15, 14, 14, 13, 13, 12, 12, 11, 11, 10, 10, 9, 9, 9, 8, 8, 7, 7, 7, 6, 6, 6, 5, 5, 5, 4, 4, 4, 4, 3, 3, 3, 3, 2, 2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0})          \
	LZM_PIXEL_R_DEBUG_X(118 /* R角 */, {108, 100, 94, 90, 86, 83, 80, 77, 75, 72, 70, 68, 66, 64, 62, 60, 58, 57, 55, 54, 52, 51, 49, 48, 47, 45, 44, 43, 42, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 29, 28, 27, 26, 25, 25, 24, 23, 22, 22, 21, 20, 20, 19, 18, 18, 17, 16, 16, 15, 15, 14, 14, 13, 13, 12, 12, 11, 11, 10, 10, 9, 9, 9, 8, 8, 7, 7, 7, 6, 6, 6, 5, 5, 5, 4, 4, 4, 4, 3, 3, 3, 3, 2, 2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0})     \
	LZM_PIXEL_R_DEBUG_X(119 /* R角 */, {109, 101, 95, 91, 87, 84, 81, 78, 75, 73, 71, 68, 66, 64, 63, 61, 59, 57, 56, 54, 53, 51, 50, 49, 47, 46, 45, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 27, 26, 25, 24, 24, 23, 22, 21, 21, 20, 19, 19, 18, 17, 17, 16, 16, 15, 15, 14, 13, 13, 12, 12, 11, 11, 11, 10, 10, 9, 9, 8, 8, 8, 7, 7, 7, 6, 6, 6, 5, 5, 5, 4, 4, 4, 4, 3, 3, 3, 3, 2, 2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0}) \
	LZM_PIXEL_R_DEBUG_X(120 /* R角 */, {110, 102, 96, 92, 88, 85, 82, 79, 76, 74, 71, 69, 67, 65, 63, 62, 60, 58, 56, 55, 53, 52, 51, 49, 48, 47, 45, 44, 43, 42, 41, 39, 38, 37, 36, 35, 34, 33, 32, 31, 31, 30, 29, 28, 27, 26, 26, 25, 24, 23, 23, 22, 21, 20, 20, 19, 18, 18, 17, 17, 16, 16, 15, 14, 14, 13, 13, 12, 12, 11, 11, 10, 10, 10, 9, 9, 8, 8, 8, 7, 7, 7, 6, 6, 6, 5, 5, 5, 4, 4, 4, 4, 3, 3, 3, 3, 2, 2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0})

#define LZM_PIXEL_R_DEBUG_X(size, ...)      \
	typedef struct PIXEL_R##size##_TYPEDEF_ \
	{                                       \
		const unsigned char R;              \
		const unsigned char dat[size];      \
	} PIXEL_R##size##_TYPEDEF;
LZM_PIXEL_R_LIST
#undef LZM_PIXEL_R_DEBUG_X

#define LZM_PIXEL_R_DEBUG_X(size, ...) extern const PIXEL_R##size##_TYPEDEF pixel_R##size;
LZM_PIXEL_R_LIST
#undef LZM_PIXEL_R_DEBUG_X

#define GET_R_ANGLE_BY_ROW(size) ((unsigned char*)&pixel_R##size)

void ILI9340X_Init(void);                                                                                                                                                                            // ILI9340X初始化函数，如果要用到lcd，一定要调用这个函数
void ILI9340X_BackLed_Control(FunctionalState enumState);                                                                                                                                            // ILI9340X背光LED控制
void ILI9340X_Clear(uint16_t usX, uint16_t usY, uint16_t usWidth, uint16_t usHeight, uint16_t usColors);                                                                                             // 对ILI9340X显示器的某一窗口以某种颜色进行清屏
void ILI9340X_Picture(uint16_t usX, uint16_t usY, uint16_t usWidth, uint16_t usHeight, uint16_t *usPicture);                                                                                         // 对ILI9340X显示器的某一窗口刷图
void ILI9340X_TwoColorChart(uint16_t usX, uint16_t usY, uint16_t usWidth, uint16_t usHeight, uint16_t usColor_0, uint16_t usColor_1, uint8_t *usTwoColor);                                           // 对ILI9340X显示器的某一窗口刷双色图
void ILI9340X_TwoColorChart_XYReversed(uint16_t usX, uint16_t usY, uint16_t usWidth, uint16_t usHeight, uint16_t usX_R, uint16_t usW_R, uint16_t usY_R, uint16_t usH_R, uint8_t *ucR_angle, uint16_t usColor_0, uint16_t usColor_1, uint8_t *usTwoColor);
#endif
