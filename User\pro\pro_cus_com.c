#include "bsp.h"
const uint8_t CusFrameHeaderLength = 0x5A; // 固定帧头

#define APP_CO_ID 1 /*_ID*/

#define APP_TX_SW_NUM 4									   /*发送开关量字节数*/
#define APP_TX_AN_NUM 8									   /*发送模拟量字节数*/
#define APP_TX_AN_PLACE (4 + APP_TX_SW_NUM)				   /*发送模拟报数位置*/
#define APP_TX_CRC_NUM (5 + APP_TX_SW_NUM + APP_TX_AN_NUM) /*发送需要CRC校验个数*/
#define APP_TX_MAX_DATA (APP_TX_CRC_NUM + 2)			   /*发送最大长度个数*/

#define APP_RX_SW_NUM 3									   /*接收开关量字节数*/
#define APP_RX_AN_NUM 8									   /*接收模拟量字节数*/
#define APP_RX_AN_PLACE (4 + APP_RX_SW_NUM)				   /*接收模拟报数位置*/
#define APP_RX_CRC_NUM (5 + APP_RX_SW_NUM + APP_RX_AN_NUM) /*接收需要CRC校验个数*/
#define APP_RX_MAX_DATA (APP_RX_CRC_NUM + 2)			   /*接收最大长度个数*/

// 发送协议（校验只校验数据）:
// 0x5A（帧头）  _ID(低)   _ID(高)   开关量字节数   开关量数据    模拟量字节数   模拟量数据     校验（crc16低） 校验（crc16高）
// 1字节        1字节      1字节     1字节          n字节        1字节          n字节         1字节           1字节

// 开关量数据：
// 信号输入（32位低8）  信号输入（32位偏低8）  信号输入（16位偏高8）  信号输入（32位高8）
// 1字节               1字节                 1字节               1字节

// 模拟量数据：
// 洗涤剂输出百分比   无用     干燥机输出百分比   无用        预留
// 1字节             1字节    1字节             1字节       4字节

volatile uint8_t GearOutput[2] = {0, 0}; // 模拟量输出// 0:洗涤剂 1:干燥剂
volatile uint32_t InformationOutput = 0; // 开关量输出

// 串口发送给后板的数据//接收一次后板的数据就发送一次输出给后板
void Backboard_data_transmission(void)
{
	uint8_t buff[APP_TX_MAX_DATA];

	// 0x5A（帧头）
	buff[0] = CusFrameHeaderLength;
	// _ID
	*(uint16_t *)(&buff[1]) = APP_CO_ID;
	// 开关量字节数
	buff[3] = APP_TX_SW_NUM;
	// 开关量数据
	*(uint32_t *)(&buff[4]) = InformationOutput;
	// 模拟量字节数
	buff[APP_TX_AN_PLACE] = APP_TX_AN_NUM;
	// 模拟量数据
	buff[APP_TX_AN_PLACE + 1] = GearOutput[0];
	buff[APP_TX_AN_PLACE + 3] = GearOutput[1];
	// 生成校验码
	*(uint16_t *)(&buff[APP_TX_CRC_NUM]) = CalCrc16_Check((uint8_t *)&buff[0], 0xFFFF, APP_TX_CRC_NUM);

	// 将数据发送出去
	comSendBuf(COM1, (uint8_t *)&buff[0], APP_TX_MAX_DATA);
}

// 接收协议（校验只校验数据）:
// 0x5A（帧头）  eeID(低)   eeID(高)   开关量字节数   开关量数据    模拟量字节数   模拟量数据     校验（crc16低） 校验（crc16高）
// 1字节         1字节      1字节      1字节          n字节        1字节          n字节         1字节           1字节

// 开关量数据：
// 洗碗机后板类型      信号输入（16位低8）   信号输入（16位高8）
// 1字节              1字节                1字节

// 模拟量数据：
// 洗涤温度int16  漂洗温度int16  预留
// 2字节          2字节         4字节
volatile uint8_t Dis_type = 0;				  // 洗碗机类型
volatile int16_t InformationTemp[2] = {0, 0}; // 0:洗涤温度 1:漂洗温度
volatile uint16_t InformationSignal = 0;	  // 开关量信号

volatile uint8_t g_rfid_recv_lst_len = 0;				 // 队列数据长度
volatile uint8_t g_rfid_recv_lst[APP_RX_MAX_DATA] = {0}; // 缓存区

volatile int g_rfid_recv_tick100ms = 0; // 0.1秒轮询时机
#define MAX_NO_DATA_LENGTH 100			// 确认通讯异常时间(0.1s)
volatile uint8_t Not_receiving_correct_data_count = MAX_NO_DATA_LENGTH;
// 后板信息数据流解析，非阻塞
void Backboard_data_receive(void)
{
	uint16_t data_buf;
	uint8_t ret = 1;
	uint8_t _data = 0;
	while (ret == 1)
	{
		ret = comGetChar(COM1, &_data); // 从接收缓存区读取1字节，非阻塞。无论有无数据均立刻返回
		if (ret == 1)
		{
			if (g_rfid_recv_lst_len == 0)
			{
				if (_data != CusFrameHeaderLength /*帧头*/)
				{
					QR_code_byte_stream_processing(_data); /*二维码字节流处理*/
					return;
				}
			}
			else if (g_rfid_recv_lst_len == 2)
			{
				data_buf = g_rfid_recv_lst[1] + ((uint16_t)_data << 8);
				if (data_buf != APP_CO_ID /*_ID*/)
				{
					QR_code_byte_stream_processing(_data); /*二维码字节流处理*/
					return;
				}
			}
			else if (g_rfid_recv_lst_len == 3)
			{
				if (_data != APP_RX_SW_NUM /*接收开关量字节数*/)
				{
					QR_code_byte_stream_processing(_data); /*二维码字节流处理*/
					return;
				}
			}
			else if (g_rfid_recv_lst_len == APP_RX_AN_PLACE /*接收模拟报数位置*/)
			{
				if (_data != APP_RX_AN_NUM /*接收模拟量字节数*/)
				{
					QR_code_byte_stream_processing(_data); /*二维码字节流处理*/
					return;
				}
			}

			g_rfid_recv_lst[g_rfid_recv_lst_len++] = _data;

			if (g_rfid_recv_lst_len >= APP_RX_MAX_DATA)
			{
				g_rfid_recv_lst_len = 0;

				// 确定为一帧
				// 解析接收到的信息
				if (*(uint16_t *)&g_rfid_recv_lst[APP_RX_CRC_NUM] != CalCrc16_Check((uint8_t *)&g_rfid_recv_lst[0], 0xFFFF, APP_RX_CRC_NUM))
				{
					return;
				}

				// CRC校验通过
				// 这里处理信息
				InformationTemp[0] = *(int16_t *)&g_rfid_recv_lst[APP_RX_AN_PLACE + 1]; // 洗涤温度
				InformationTemp[1] = *(int16_t *)&g_rfid_recv_lst[APP_RX_AN_PLACE + 3]; // 漂洗温度
				Dis_type = g_rfid_recv_lst[4];											// 洗碗机类型
				InformationSignal = *(uint16_t *)&g_rfid_recv_lst[5];					// 开关量信号

				// 更新无接收计数
				Not_receiving_correct_data_count = MAX_NO_DATA_LENGTH;

				// 串口发送给后板的数据//接收一次后板的数据就发送一次输出给后板
				Backboard_data_transmission();
				return;
			}
		}
	}

	if (bsp_CheckRunTime(g_rfid_recv_tick100ms) > 100) /* 100ms进入一次 */
	{
		g_rfid_recv_tick100ms = bsp_GetRunTime(); /* 更新超时计数 */
		if (Not_receiving_correct_data_count > 0)
		{
			Not_receiving_correct_data_count--;
		}
	}
}

/*
*********************************************************************************************************
*	函 数 名: ReceiveQRcode
*	功能说明: 接收二维码
*	形    参: 无
*	返 回 值: TRUE 成功 FALSE 失败
*********************************************************************************************************
*/
uint8_t ReceiveQRcode(void)
{
	uint32_t usTemp;
	uint32_t Address = QR_CODE_ADDRESS;											 /*最后扇区首地址*/
	uint32_t timeout_count = 0;													 /* 超时计数 */
	uint16_t Queur_Leng = 0;													 // 队列长度
	uint16_t checksum = 0;														 // 校验和
	uint8_t *DataBuff = (uint8_t *)&PixelPageCache2D[0][0]; /* 数据接收缓存区 */ /* 借用二维像素页缓存 */
	while (timeout_count < 2000000)
	{
		if (comGetChar(COM1, &DataBuff[Queur_Leng]) /* 从接收缓存区读取1字节 */)
		{
			timeout_count = 0;
			if (Queur_Leng < 2048)
			{
				/* 累加校验合 */
				checksum += DataBuff[Queur_Leng++];
			}
			else
			{
				if (Queur_Leng == 2049)
				{
					if (checksum == *(uint16_t *)&DataBuff[2048])
					{
						/* 校验和成功 */ /* 将数据烧录到芯片flash */

						/* 关中断 */
						__set_PRIMASK(1);

						/* FLASH 解锁 */
						FLASH_Unlock();

						/* Clear pending flags (if any) */
						FLASH_ClearFlag(FLASH_FLAG_BSY | FLASH_FLAG_EOP | FLASH_FLAG_PGERR | FLASH_FLAG_WRPRTERR);

						/* 擦除最后扇区 */
						timeout_count = FLASH_ErasePage(Address);

						/* 按字节模式编程（为提高效率，可以按字编程，一次写入4字节） */
						for (Queur_Leng = 0; Queur_Leng < 2048; Queur_Leng += 4)
						{
							usTemp = *((uint32_t *)&DataBuff[Queur_Leng]);
							timeout_count = FLASH_ProgramWord(Address, usTemp);
							if (timeout_count != FLASH_COMPLETE)
							{
								break;
							}

							Address += 4;
						}

						/* Flash 加锁，禁止写Flash控制寄存器 */
						FLASH_Lock();

						__set_PRIMASK(0); /* 开中断 */

						if (timeout_count == FLASH_COMPLETE)
						{
							return TRUE;
						}
						else
						{
							return FALSE;
						}
					}
					else
					{
						/* 校验和失败 */
						return FALSE;
					}
				}
				Queur_Leng++;
			}
		}
		else
		{
			timeout_count++;
		}
	}
	return FALSE;
}

/* 二维码握手表 */
__I uint8_t QR_code_Handshake[4] = {0xB5, 0x4A, 0xA4, 0x5B};
/* 二维码结束表 */
__I uint8_t QR_code_End[4] = {0x39, 0x93, 0x38, 0x83};
__IO uint8_t QR_code_Leng = 0; // 队列长度
/*二维码字节流处理*/
void QR_code_byte_stream_processing(uint8_t _data)
{
	g_rfid_recv_lst_len = 0; // 队列数据长度
	if (_data == QR_code_Handshake[QR_code_Leng])
	{
		QR_code_Leng++;
		if (QR_code_Leng == 4)
		{
			QR_code_Leng = 0; // 队列长度
			// 握手成功
			/* 1:清空接收缓存区的所有数据 */
			comClearTxFifo(COM1 /* 清零串口发送缓冲区 */);
			comClearRxFifo(COM1 /* 清零串口接收缓冲区 */);
			/* 2:发送握手成功应答 */
			comSendBuf(COM1, (uint8_t *)&QR_code_Handshake[0], 4);
			/* 3:接收二维码数据 */
			if (ReceiveQRcode() /* 接收二维码 */ == FALSE /* 失败 */)
			{
				return;
			}
			/* 4:如果接收成功发送结束吗，不成功就不发送 */
			comSendBuf(COM1, (uint8_t *)&QR_code_End[0], 4);

			/*用户可调参数调整*/
			parame_adj(USR_qr_code, 0, PARAME_INC /* 增加 */, PARAME_S_STEP /* 单步 */);
		}
	}
	else
	{
		QR_code_Leng = 0; // 队列长度
	}
}
