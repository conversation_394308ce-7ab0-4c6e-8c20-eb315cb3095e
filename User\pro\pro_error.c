#include "bsp.h"
volatile uint8_t Alarm_Flag = warn_empty; // 报警标志

// E1故障报警检测函数（非阻塞轮询1S）
void E1_fault_alarm_detection_function(void)
{
    if (PV(int, USR_mill_24, PV(int, USR_set_tad, 0)) /*洗涤探头故障能否运行 */ == NO /* 不能 */)
    {
        if (PRO_TEMP_ANO_OBTAIN(PTT1 /* 洗涤温度 */) /* 业务用 温度探头异常获取 */ > 0)
        {
            Alarm_Flag = warn_E1; // 报警标志
        }
    }
}
// E2故障报警检测函数（非阻塞轮询1S）
void E2_fault_alarm_detection_function(void)
{
    if (PV(int, USR_mill_27, PV(int, USR_set_tad, 0)) /*漂洗探头故障能否运行 */ == NO /* 不能 */)
    {
        if (PRO_TEMP_ANO_OBTAIN(PTT2 /* 漂洗温度 */) /* 业务用 温度探头异常获取 */ > 0)
        {
            Alarm_Flag = warn_E2; // 报警标志
        }
    }
}

// E3故障报警检测函数（非阻塞轮询1S）
void E3_fault_alarm_detection_function(void)
{
    static uint8_t Auxiliary_Counting = 0;    /*辅助计数*/
    static int Water_inlet_timeout_count = 0; // 进水超时计数（0.1秒）

    if (PV(int, USR_user_15, 0) /*进水超时 */ == PMIN(int, USR_user_15) /* 达到下限显示“不检测” */)
    {
        Auxiliary_Counting = 0;        /*辅助计数*/
        Water_inlet_timeout_count = 0; // 进水超时计数（0.1秒）
        return;
    }

    if (pa_m_tVar.current_page /* 当前交互界面 */ == page_debug /* 调试 */)
    {
        Auxiliary_Counting = 0;        /*辅助计数*/
        Water_inlet_timeout_count = 0; // 进水超时计数（0.1秒）
        return;
    }

    if (PRO_RELAY_OBTAIN(PRELT5 /* 进水阀 */) /* 业务用 继电器控制状态获取 */ == OFF /* 关 */)
    {
        Auxiliary_Counting = 0;        /*辅助计数*/
        Water_inlet_timeout_count = 0; // 进水超时计数（0.1秒）
        return;
    }
    else
    {
        if (++Auxiliary_Counting /*辅助计数*/ > 60)
        {
            Auxiliary_Counting = 0;
            if (Water_inlet_timeout_count < PV(int, USR_user_15, 0) /*进水超时 */)
            {
                Water_inlet_timeout_count++; // 进水超时计数（1秒）
            }
            else
            {
                Water_inlet_timeout_count = 0; // 进水超时计数（1秒）
                Alarm_Flag = warn_E3;          // 报警标志
            }
        }
    }
}

// E4故障报警检测函数（非阻塞轮询1S）
void E4_fault_alarm_detection_function(void)
{
    if (Not_receiving_correct_data_count == 0) // 正常是大于0的，等于0表示通讯异常但前板有电
    {
        Alarm_Flag = warn_E4; // 报警标志
    }
}

// Ex故障报警检测函数（非阻塞轮询1S）
void Ex_fault_alarm_detection_function(void)
{
    static uint8_t Last_warn = warn_empty;

    if (pa_m_tVar.root_page /* 当前根的界面 */ != page_run /* 运行 */)
    {
        Alarm_Flag = warn_empty;
        Last_warn = warn_empty;
        return;
    }

    E1_fault_alarm_detection_function(); // E1故障报警检测函数（非阻塞轮询1S）
    E2_fault_alarm_detection_function(); // E2故障报警检测函数（非阻塞轮询1S）
    E3_fault_alarm_detection_function(); // E3故障报警检测函数（非阻塞轮询1S）
    E4_fault_alarm_detection_function(); // E4故障报警检测函数（非阻塞轮询1S）

    if (Alarm_Flag != warn_empty && Last_warn != Alarm_Flag)
    {
        bsp_Putfifo(B5_BEEP); /* 压入任务：报警30S */
    }
    Last_warn = Alarm_Flag;
}
