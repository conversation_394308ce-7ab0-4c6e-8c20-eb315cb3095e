#include "bsp.h"
/* ADC阻值温度映射表 */
/* 分压电阻2K */
/* NTC热敏电阻 10K-3950 阻值温度对应表 1℃ , 阻值单位：欧姆 */
const uint16_t ntc10k_b3950_div2k[] =
    {
        64804, /* -30 */
        64758, /* -29 */
        64709, /* -28 */
        64657, /* -27 */
        64603, /* -26 */
        64546, /* -25 */
        64485, /* -24 */
        64422, /* -23 */
        64355, /* -22 */
        64285, /* -21 */
        64211, /* -20 */
        64134, /* -19 */
        64053, /* -18 */
        63967, /* -17 */
        63878, /* -16 */
        63784, /* -15 */
        63686, /* -14 */
        63583, /* -13 */
        63476, /* -12 */
        63363, /* -11 */
        63246, /* -10 */
        63123, /* -9 */
        62994, /* -8 */
        62860, /* -7 */
        62720, /* -6 */
        62574, /* -5 */
        62422, /* -4 */
        62264, /* -3 */
        62099, /* -2 */
        61927, /* -1 */
        61748, /* 0 */
        61563, /* 1 */
        61370, /* 2 */
        61170, /* 3 */
        60962, /* 4 */
        60746, /* 5 */
        60523, /* 6 */
        60291, /* 7 */
        60052, /* 8 */
        59803, /* 9 */
        59547, /* 10 */
        59282, /* 11 */
        59008, /* 12 */
        58725, /* 13 */
        58433, /* 14 */
        58132, /* 15 */
        57823, /* 16 */
        57503, /* 17 */
        57175, /* 18 */
        56837, /* 19 */
        56490, /* 20 */
        56133, /* 21 */
        55767, /* 22 */
        55392, /* 23 */
        55007, /* 24 */
        54613, /* 25 */
        54209, /* 26 */
        53796, /* 27 */
        53374, /* 28 */
        52943, /* 29 */
        52503, /* 30 */
        52054, /* 31 */
        51596, /* 32 */
        51130, /* 33 */
        50656, /* 34 */
        50173, /* 35 */
        49682, /* 36 */
        49184, /* 37 */
        48678, /* 38 */
        48165, /* 39 */
        47645, /* 40 */
        47119, /* 41 */
        46586, /* 42 */
        46047, /* 43 */
        45502, /* 44 */
        44952, /* 45 */
        44397, /* 46 */
        43838, /* 47 */
        43274, /* 48 */
        42706, /* 49 */
        42135, /* 50 */
        41560, /* 51 */
        40983, /* 52 */
        40404, /* 53 */
        39822, /* 54 */
        39239, /* 55 */
        38655, /* 56 */
        38070, /* 57 */
        37485, /* 58 */
        36900, /* 59 */
        36315, /* 60 */
        35731, /* 61 */
        35148, /* 62 */
        34567, /* 63 */
        33988, /* 64 */
        33411, /* 65 */
        32836, /* 66 */
        32265, /* 67 */
        31697, /* 68 */
        31132, /* 69 */
        30571, /* 70 */
        30015, /* 71 */
        29462, /* 72 */
        28915, /* 73 */
        28372, /* 74 */
        27835, /* 75 */
        27303, /* 76 */
        26778, /* 77 */
        26256, /* 78 */
        25742, /* 79 */
        25234, /* 80 */
        24732, /* 81 */
        24236, /* 82 */
        23748, /* 83 */
        23265, /* 84 */
        22790, /* 85 */
        22322, /* 86 */
        21861, /* 87 */
        21407, /* 88 */
        20959, /* 89 */
        20519, /* 90 */
        20086, /* 91 */
        19661, /* 92 */
        19243, /* 93 */
        18831, /* 94 */
        18428, /* 95 */
        18032, /* 96 */
        17642, /* 97 */
        17261, /* 98 */
        16886, /* 99 */
        16517, /* 100 */
        16157, /* 101 */
        15803, /* 102 */
        15457, /* 103 */
        15118, /* 104 */
        14784, /* 105 */
        14457, /* 106 */
        14139, /* 107 */
        13827, /* 108 */
        13521, /* 109 */
        13220, /* 110 */
        12928, /* 111 */
        12642, /* 112 */
        12360, /* 113 */
        12085, /* 114 */
        11816, /* 115 */
        11555, /* 116 */
        11298, /* 117 */
        11045, /* 118 */
        10802, /* 119 */
        10561, /* 120 */
        10327, /* 121 */
        10098, /* 122 */
        9872,  /* 123 */
        9654,  /* 124 */
        9441,  /* 125 */
        9231,  /* 126 */
        9027,  /* 127 */
        8829,  /* 128 */
        8632,  /* 129 */
        8444,  /* 130 */
        8257,  /* 131 */
        8076,  /* 132 */
        7899,  /* 133 */
        7726,  /* 134 */
        7557,  /* 135 */
        7393,  /* 136 */
        7230,  /* 137 */
        7074,  /* 138 */
        6920,  /* 139 */
        6770,  /* 140 */
        6624,  /* 141 */
        6481,  /* 142 */
        6342,  /* 143 */
        6206,  /* 144 */
        6074,  /* 145 */
        5944,  /* 146 */
        5817,  /* 147 */
        5694,  /* 148 */
        5573,  /* 149 */
        5455,  /* 150 */
        5339,  /* 151 */
        5226,  /* 152 */
        5117,  /* 153 */
        5011,  /* 154 */
        4905,  /* 155 */
        4804,  /* 156 */
        4705,  /* 157 */
        4606,  /* 158 */
        4513,  /* 159 */
        4419,  /* 160 */
        4327,  /* 161 */
        4239,  /* 162 */
        4153,  /* 163 */
        4069,  /* 164 */
        3985,  /* 165 */
        3907,  /* 166 */
        3829,  /* 167 */
        3750,  /* 168 */
        3675,  /* 169 */
        3601,  /* 170 */
        3531,  /* 171 */
        3461,  /* 172 */
        3393,  /* 173 */
        3325,  /* 174 */
        3260,  /* 175 */
        3198,  /* 176 */
        3136,  /* 177 */
        3073,  /* 178 */
        3014,  /* 179 */
        2957,  /* 180 */
        2900,  /* 181 */
        2846,  /* 182 */
        2792,  /* 183 */
        2738,  /* 184 */
        2687,  /* 185 */
        2635,  /* 186 */
        2587,  /* 187 */
        2539,  /* 188 */
        2490,  /* 189 */
        2445,  /* 190 */
        2399,  /* 191 */
        2354,  /* 192 */
        2311,  /* 193 */
        2268,  /* 194 */
        2228,  /* 195 */
        2189,  /* 196 */
        2149,  /* 197 */
        2109,  /* 198 */
        2072,  /* 199 */
        2035,  /* 200 */
        1998,  /* 201 */
        1964,  /* 202 */
        1927,  /* 203 */
        1893,  /* 204 */
        1862,  /* 205 */
        1828,  /* 206 */
        1797,  /* 207 */
        1766,  /* 208 */
        1735,  /* 209 */
        1707,  /* 210 */
        1676,  /* 211 */
        1648,  /* 212 */
        1620,  /* 213 */
        1592,  /* 214 */
        1567,  /* 215 */
        1539,  /* 216 */
        1514,  /* 217 */
        1489,  /* 218 */
        1464,  /* 219 */
        1439,  /* 220 */
        1417,  /* 221 */
        1395,  /* 222 */
        1370,  /* 223 */
        1348,  /* 224 */
        1326,  /* 225 */
        1307,  /* 226 */
        1285,  /* 227 */
        1263,  /* 228 */
        1244,  /* 229 */
        1225,  /* 230 */
        1206,  /* 231 */
        1187,  /* 232 */
        1168,  /* 233 */
        1149,  /* 234 */
        1133,  /* 235 */
        1114,  /* 236 */
        1099,  /* 237 */
        1080,  /* 238 */
        1064,  /* 230 */
        1048   /* 240 */
};

/*
*********************************************************************************************************
*	函 数 名: CalcTempWithRes3950K
*	功能说明: 计算NTC温度（带分辨率）10倍
*	形    参: 输入res 为adc，阻值单位为欧姆  3950K
*	返 回 值: 温度10倍
*********************************************************************************************************
*/
int16_t CalcTempWithRes3950K(uint16_t res)
{
    int16_t result;
    int16_t mid;
    int16_t start = 0;
    int16_t end = (sizeof(ntc10k_b3950_div2k) / sizeof(uint16_t)) - 1;

    if (ntc10k_b3950_div2k[end] >= res) /* 如果超过最大返回10000 */
    {
        return 10000;
    }
    else if (ntc10k_b3950_div2k[0] <= res) /* 如果小于最小返回-10000 */
    {
        return -10000;
    }

    while (start <= end) /* 二分查找 */
    {
        mid = start + (end - start) / 2; /* 防止整数溢出 */
        if (ntc10k_b3950_div2k[mid] < res)
        {
            end = mid - 1; /* 目标值在mid的左侧，因为数组是降序的 */
        }
        else if (ntc10k_b3950_div2k[mid] > res)
        {
            start = mid + 1; /* 目标值在mid的右侧或就是mid的后一个位置，因为数组是降序的 */
            result = mid;    /* 更新插入点的前一个下标 */
        }
        else
        {
            return ((mid * 10) - 300); /* 如果找到，返回(mid * 100) */
        }
    }
    /* 循环结束时，start > end，表示未找到目标值 */
    /* 此时result保存的是应该插入目标值的前一个下标 */
    result = ((result * 10 + (ntc10k_b3950_div2k[result] - res) * 10 / (ntc10k_b3950_div2k[result] - ntc10k_b3950_div2k[result + 1])) - 300); /* 计算分辨率 */
    return result;
}
