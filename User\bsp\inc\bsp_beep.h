#ifndef __BSP_BEEP_H
#define __BSP_BEEP_H
#define BEEP_SWITCH 1 /* 按键开关 */
#if BEEP_SWITCH
/* 配置IO */
#define BEEPIO_ITEM_LIST \
	BEEPIO_DEBUG_X(GPIOA /* GPIO */, GPIO_Pin_0 /* pin */,TIM5 /* TIM时钟 */,1 /* 通道 */)

// #define BEEP_HAVE_POWER		/* 定义此行表示有源蜂鸣器，直接通过GPIO驱动, 无需PWM */
/* 如果使用无源蜂鸣器，IO配置必须支持硬件PWM */

#ifdef	BEEP_HAVE_POWER		/* 有源蜂鸣器 */
#else		/* 无源蜂鸣器 */
/* _ulFreq : PWM信号频率，单位Hz (实际测试，可以输出100MHz），0 表示禁止输出 */
/* _EnableFlag : 使能标志，1表示使能输出，0 表示禁止输出 */
// #define BEEP_OPERATE(_ulFreq, _EnableFlag) bsp_SetTIMOutPWM(GPIOA, GPIO_Pin_0, TIM5, 1, _ulFreq, (5000 * _EnableFlag))
#endif

/* 音阶索引 */
/* p:休止符，L:低音，M:中音，H:高音，HH:超高音，HHH:倍高音，下划线：升半音符号 */
/* 1:哆，2:瑞，3:咪，4:发，5:嗦，6:啦，7:西 */
enum mus_note
{
	note_P = 0, /* 休止符，没有频率 */

	/* 第一个八度 */
	note_L1,  /* 低音哆 (C1) */
	note_L1_, /* 低音哆升半音 (C#1/Db1) */
	note_L2,  /* 低音瑞 (D1) */
	note_L2_, /* 低音瑞升半音 (D#1/Eb1) */
	note_L3,  /* 低音咪 (E1) */
	note_L4,  /* 低音发 (F1) */
	note_L4_, /* 低音发升半音 (F#1/Gb1) */
	note_L5,  /* 低音嗦 (G1) */
	note_L5_, /* 低音嗦升半音 (G#1/Ab1) */
	note_L6,  /* 低音啦 (A1) */
	note_L6_, /* 低音啦升半音 (A#1/Bb1) */
	note_L7,  /* 低音西 (B1) */

	/* 第二个八度 */
	note_M1,  /* 中音哆 (C2) */
	note_M1_, /* 中音哆升半音 (C#2/Db2) */
	note_M2,  /* 中音瑞 (D2) */
	note_M2_, /* 中音瑞升半音 (D#2/Eb2) */
	note_M3,  /* 中音咪 (E2) */
	note_M4,  /* 中音发 (F2) */
	note_M4_, /* 中音发升半音 (F#2/Gb2) */
	note_M5,  /* 中音嗦 (G2) */
	note_M5_, /* 中音嗦升半音 (G#2/Ab2) */
	note_M6,  /* 中音啦 (A2) */
	note_M6_, /* 中音啦升半音 (A#2/Bb2) */
	note_M7,  /* 中音西 (B2) */

	/* 第三个八度 */
	note_H1,  /* 高音哆 (C3) */
	note_H1_, /* 高音哆升半音 (C#3/Db3) */
	note_H2,  /* 高音瑞 (D3) */
	note_H2_, /* 高音瑞升半音 (D#3/Eb3) */
	note_H3,  /* 高音咪 (E3) */
	note_H4,  /* 高音发 (F3) */
	note_H4_, /* 高音发升半音 (F#3/Gb3) */
	note_H5,  /* 高音嗦 (G3) */
	note_H5_, /* 高音嗦升半音 (G#3/Ab3) */
	note_H6,  /* 高音啦 (A3) */
	note_H6_, /* 高音啦升半音 (A#3/Bb3) */
	note_H7,  /* 高音西 (B3) */

	/* 第四个八度 */
	note_HH1,  /* 超高音哆 (C4) */
	note_HH1_, /* 超高音哆升半音 (C#4/Db4) */
	note_HH2,  /* 超高音瑞 (D4) */
	note_HH2_, /* 超高音瑞升半音 (D#4/Eb4) */
	note_HH3,  /* 超高音咪 (E4) */
	note_HH4,  /* 超高音发 (F4) */
	note_HH4_, /* 超高音发升半音 (F#4/Gb4) */
	note_HH5,  /* 超高音嗦 (G4) */
	note_HH5_, /* 超高音嗦升半音 (G#4/Ab4) */
	note_HH6,  /* 超高音啦 (A4) */
	note_HH6_, /* 超高音啦升半音 (A#4/Bb4) */
	note_HH7,  /* 超高音西 (B4) */

	/* 第五个八度 */
	note_HHH1,	/* 倍高音哆 (C5) */
	note_HHH1_, /* 倍高音哆升半音 (C#5/Db5) */
	note_HHH2,	/* 倍高音瑞 (D5) */
	note_HHH2_, /* 倍高音瑞升半音 (D#5/Eb5) */
	note_HHH3,	/* 倍高音咪 (E5) */
	note_HHH4,	/* 倍高音发 (F5) */
	note_HHH4_, /* 倍高音发升半音 (F#5/Gb5) */
	note_HHH5,	/* 倍高音嗦 (G5) */
	note_HHH5_, /* 倍高音嗦升半音 (G#5/Ab5) */
	note_HHH6,	/* 倍高音啦 (A5) */
	note_HHH6_, /* 倍高音啦升半音 (A#5/Bb5) */
	note_HHH7,	/* 倍高音西 (B5) */
	/* ... (可以继续添加更高音的音符，但通常音乐应用中较少用到) */
};

#define TKZC_BEAT 17
#define MUSIC_TKZC                 \
	{                              \
		{note_P, 4 * TKZC_BEAT},   \
		{note_P, 4 * TKZC_BEAT},   \
		{note_P, 4 * TKZC_BEAT},   \
		{note_M6, 2 * TKZC_BEAT},  \
		{note_M7, 2 * TKZC_BEAT},  \
                                   \
		{note_H1, 6 * TKZC_BEAT},  \
		{note_M7, 2 * TKZC_BEAT},  \
		{note_H1, 4 * TKZC_BEAT},  \
		{note_H3, 4 * TKZC_BEAT},  \
                                   \
		{note_M7, 12 * TKZC_BEAT}, \
		{note_M3, 2 * TKZC_BEAT},  \
		{note_M3, 2 * TKZC_BEAT},  \
                                   \
		{note_M6, 6 * TKZC_BEAT},  \
		{note_M5, 2 * TKZC_BEAT},  \
		{note_M6, 4 * TKZC_BEAT},  \
		{note_H1, 4 * TKZC_BEAT},  \
                                   \
		{note_M5, 12 * TKZC_BEAT}, \
		{note_M3, 4 * TKZC_BEAT},  \
                                   \
		{note_M4, 6 * TKZC_BEAT},  \
		{note_M3, 2 * TKZC_BEAT},  \
		{note_M4, 4 * TKZC_BEAT},  \
		{note_H1, 4 * TKZC_BEAT},  \
                                   \
		{note_M3, 8 * TKZC_BEAT},  \
		{note_P, 2 * TKZC_BEAT},   \
		{note_H1, 6 * TKZC_BEAT},  \
                                   \
		{note_M7, 6 * TKZC_BEAT},  \
		{note_M4, 2 * TKZC_BEAT},  \
		{note_M4_, 4 * TKZC_BEAT}, \
		{note_M7, 4 * TKZC_BEAT},  \
                                   \
		{note_M7, 8 * TKZC_BEAT},  \
		{note_P, 4 * TKZC_BEAT},   \
		{note_M6, 2 * TKZC_BEAT},  \
		{note_M7, 2 * TKZC_BEAT},  \
                                   \
		{note_H1, 6 * TKZC_BEAT},  \
		{note_M7, 2 * TKZC_BEAT},  \
		{note_H1, 4 * TKZC_BEAT},  \
		{note_H3, 4 * TKZC_BEAT},  \
                                   \
		{note_M7, 12 * TKZC_BEAT}, \
		{note_M3, 4 * TKZC_BEAT},  \
                                   \
		{note_M6, 6 * TKZC_BEAT},  \
		{note_M5, 2 * TKZC_BEAT},  \
		{note_M6, 4 * TKZC_BEAT},  \
		{note_H1, 4 * TKZC_BEAT},  \
                                   \
		{note_M5, 12 * TKZC_BEAT}, \
		{note_M2, 2 * TKZC_BEAT},  \
		{note_M3, 2 * TKZC_BEAT},  \
                                   \
		{note_M4, 4 * TKZC_BEAT},  \
		{note_H1, 2 * TKZC_BEAT},  \
		{note_M7, 4 * TKZC_BEAT},  \
		{note_H1, 6 * TKZC_BEAT},  \
                                   \
		{note_H2, 4 * TKZC_BEAT},  \
		{note_H3, 2 * TKZC_BEAT},  \
		{note_H1, 10 * TKZC_BEAT}, \
                                   \
		{note_H1, 2 * TKZC_BEAT},  \
		{note_M7, 2 * TKZC_BEAT},  \
		{note_M6, 4 * TKZC_BEAT},  \
		{note_M7, 4 * TKZC_BEAT},  \
		{note_M5_, 4 * TKZC_BEAT}, \
                                   \
		{note_M6, 12 * TKZC_BEAT}, \
		{note_H1, 2 * TKZC_BEAT},  \
		{note_H2, 2 * TKZC_BEAT},  \
                                   \
		{note_H3, 6 * TKZC_BEAT},  \
		{note_H2, 2 * TKZC_BEAT},  \
		{note_H3, 4 * TKZC_BEAT},  \
		{note_H5, 4 * TKZC_BEAT},  \
                                   \
		{note_H2, 12 * TKZC_BEAT}, \
		{note_M5, 4 * TKZC_BEAT},  \
                                   \
		{note_H1, 6 * TKZC_BEAT},  \
		{note_M7, 2 * TKZC_BEAT},  \
		{note_H1, 4 * TKZC_BEAT},  \
		{note_H3, 4 * TKZC_BEAT},  \
                                   \
		{note_H3, 16 * TKZC_BEAT}, \
                                   \
		{note_M6, 2 * TKZC_BEAT},  \
		{note_M7, 2 * TKZC_BEAT},  \
		{note_H1, 4 * TKZC_BEAT},  \
		{note_M7, 4 * TKZC_BEAT},  \
		{note_H2, 2 * TKZC_BEAT},  \
                                   \
		{note_H1, 6 * TKZC_BEAT},  \
		{note_M5, 10 * TKZC_BEAT}, \
                                   \
		{note_H4, 4 * TKZC_BEAT},  \
		{note_H3, 8 * TKZC_BEAT},  \
		{note_H1, 4 * TKZC_BEAT},  \
                                   \
		{note_H3, 16 * TKZC_BEAT}, \
                                   \
		{note_H6, 8 * TKZC_BEAT},  \
		{note_H5, 8 * TKZC_BEAT},  \
                                   \
		{note_H3, 2 * TKZC_BEAT},  \
		{note_H2, 2 * TKZC_BEAT},  \
		{note_H1, 8 * TKZC_BEAT},  \
		{note_P, 2 * TKZC_BEAT},   \
		{note_H1, 2 * TKZC_BEAT},  \
                                   \
		{note_H2, 4 * TKZC_BEAT},  \
		{note_H1, 2 * TKZC_BEAT},  \
		{note_H2, 6 * TKZC_BEAT},  \
		{note_H5, 4 * TKZC_BEAT},  \
                                   \
		{note_H3, 16 * TKZC_BEAT}, \
                                   \
		{note_H5, 8 * TKZC_BEAT},  \
		{note_H6, 8 * TKZC_BEAT},  \
                                   \
		{note_H3, 2 * TKZC_BEAT},  \
		{note_H2, 2 * TKZC_BEAT},  \
		{note_H1, 8 * TKZC_BEAT},  \
		{note_P, 2 * TKZC_BEAT},   \
		{note_H1, 2 * TKZC_BEAT},  \
                                   \
		{note_H2, 4 * TKZC_BEAT},  \
		{note_H1, 2 * TKZC_BEAT},  \
		{note_H2, 6 * TKZC_BEAT},  \
		{note_M7, 4 * TKZC_BEAT},  \
                                   \
		{note_M6, 12 * TKZC_BEAT}, \
		{note_P, 4 * TKZC_BEAT},   \
	}

#define BEEP_ITEM_LIST                                                                                                         \
	BEEP_DEBUG_X(B1 /* 名称 */, 1 /* 循环次数 */, {{note_H7, 20}} /* 乐谱 */)					/* 短按键音 */             \
	BEEP_DEBUG_X(B2 /* 名称 */, 1 /* 循环次数 */, {{note_H7, 40}} /* 乐谱 */)					/* 长按键音 */             \
	BEEP_DEBUG_X(B3 /* 名称 */, 3 /* 循环次数 */, {{note_H7, 10}, {note_P, 5}} /* 乐谱 */)		/* 急促3声(无效提示) */ \
	BEEP_DEBUG_X(B4 /* 名称 */, 3 /* 循环次数 */, {{note_H7, 80}, {note_P, 60}} /* 乐谱 */)		/* 慢响3声 */               \
	BEEP_DEBUG_X(B5 /* 名称 */, 30 /* 循环次数 */, {{note_H7, 40}, {note_P, 60}} /* 乐谱 */)	/* 报警30S */                \
	BEEP_DEBUG_X(B6 /* 名称 */, 65535 /* 循环次数 */, {{note_H7, 40}, {note_P, 60}} /* 乐谱 */) /* 一直报警 */             \
	BEEP_DEBUG_X(Bn /* 名称 */, 1 /* 循环次数 */, MUSIC_TKZC /* 乐谱 */)						/* 天空之城 */

/* 蜂鸣器音乐ID */
enum beep_e_id
{
#define BEEP_DEBUG_X(name, Cycle, ...) BEEP_##name,
	BEEP_ITEM_LIST
#undef BEEP_DEBUG_X
		BEEP_TEM_NUM /* 蜂鸣器音乐个数 */,
};

/* 节拍 */
typedef struct
{
	uint8_t note;	/* 音阶 */
	uint16_t durat; /* 时长 单位10ms  */
} beats_t;

/* 音乐 */
typedef struct
{
	beats_t const *bea; /* 乐谱指针 */
	uint16_t len;		/* 节拍个数 */
} music_t;

/* 蜂鸣器管理器 */
typedef struct _beet_t
{
	music_t const *mus;	   /* 播放的音乐 */
	uint16_t usCycle;	   /* 音乐的循环次数 */
	uint16_t usDuratCount; /* 用于单节拍时长计数 */
	uint16_t usBeatsCount; /* 用于单节拍计数 */
	uint16_t usCycleCount; /* 用于循环次数计数 */
} beet_t;

#define BEEP_DEBUG_X(name, Cycle, ...) extern const music_t beep_music_##name;
BEEP_ITEM_LIST
#undef BEEP_DEBUG_X

#define  TIM1_MARK_ 1
#define  TIM8_MARK_ 8
#define  TIM9_MARK_ 9
#define  TIM10_MARK_ 10
#define  TIM2_MARK_ 2
#define  TIM3_MARK_ 3
#define  TIM4_MARK_ 4
#define  TIM5_MARK_ 5
#define  TIM6_MARK_ 6
#define  TIM7_MARK_ 7
#define  TIM12_MARK_ 12
#define  TIM13_MARK_ 13
#define  TIM14_MARK_ 14

#define  BEEP_TIM_DIV 100 //定时器分频比

#define BEEPIO_DEBUG_X(gpio_, pin_, tim_, ch_) tim_##_MARK_
#if (BEEPIO_ITEM_LIST == TIM1_MARK_)
#define  BEEP_TIM_SCALER (BEEP_TIM_DIV-1)
#define  BEEP_TIM_IOD (216000000/BEEP_TIM_DIV)
#elif (BEEPIO_ITEM_LIST ==TIM8_MARK_)
#define  BEEP_TIM_SCALER (BEEP_TIM_DIV-1)
#define  BEEP_TIM_IOD (216000000/BEEP_TIM_DIV)
#elif (BEEPIO_ITEM_LIST ==TIM9_MARK_)
#define  BEEP_TIM_SCALER (BEEP_TIM_DIV-1)
#define  BEEP_TIM_IOD (216000000/BEEP_TIM_DIV)
#elif (BEEPIO_ITEM_LIST ==TIM10_MARK_)
#define  BEEP_TIM_SCALER (BEEP_TIM_DIV-1)
#define  BEEP_TIM_IOD (216000000/BEEP_TIM_DIV)
#elif (BEEPIO_ITEM_LIST ==TIM11_MARK_)
#define  BEEP_TIM_SCALER (BEEP_TIM_DIV-1)
#define  BEEP_TIM_IOD (216000000/BEEP_TIM_DIV)
#elif (BEEPIO_ITEM_LIST ==TIM2_MARK_)
#define  BEEP_TIM_SCALER (BEEP_TIM_DIV-1)
#define  BEEP_TIM_IOD (216000000/BEEP_TIM_DIV)
#elif (BEEPIO_ITEM_LIST ==TIM3_MARK_)
#define  BEEP_TIM_SCALER (BEEP_TIM_DIV-1)
#define  BEEP_TIM_IOD (216000000/BEEP_TIM_DIV)
#elif (BEEPIO_ITEM_LIST ==TIM4_MARK_)
#define  BEEP_TIM_SCALER (BEEP_TIM_DIV-1)
#define  BEEP_TIM_IOD (216000000/BEEP_TIM_DIV)
#elif (BEEPIO_ITEM_LIST ==TIM5_MARK_)
#define  BEEP_TIM_SCALER (BEEP_TIM_DIV-1)
#define  BEEP_TIM_IOD (108000000/BEEP_TIM_DIV)
#elif (BEEPIO_ITEM_LIST ==TIM6_MARK_)
#define  BEEP_TIM_SCALER (BEEP_TIM_DIV-1)
#define  BEEP_TIM_IOD (108000000/BEEP_TIM_DIV)
#elif (BEEPIO_ITEM_LIST ==TIM7_MARK_)
#define  BEEP_TIM_SCALER (BEEP_TIM_DIV-1)
#define  BEEP_TIM_IOD (108000000/BEEP_TIM_DIV)
#elif (BEEPIO_ITEM_LIST ==TIM12_MARK_)
#define  BEEP_TIM_SCALER (BEEP_TIM_DIV-1)
#define  BEEP_TIM_IOD (108000000/BEEP_TIM_DIV)
#elif (BEEPIO_ITEM_LIST ==TIM13_MARK_)
#define  BEEP_TIM_SCALER (BEEP_TIM_DIV-1)
#define  BEEP_TIM_IOD (108000000/BEEP_TIM_DIV)
#elif (BEEPIO_ITEM_LIST ==TIM14_MARK_)
#define  BEEP_TIM_SCALER (BEEP_TIM_DIV-1)
#define  BEEP_TIM_IOD (108000000/BEEP_TIM_DIV)
#else
#endif
#undef BEEPIO_DEBUG_X



/* 初始化蜂鸣器硬件 */
void BEEP_InitHard(void);
/* 启动蜂鸣音 */
void BEEP_Start(music_t const *mus, uint16_t _usCycle);
/* 停止蜂鸣音 */
void BEEP_Stop(void);
/* 每隔10ms调用1次该函数，用于控制蜂鸣器发声 */
void BEEP_Pro10ms(void);
#endif
#endif