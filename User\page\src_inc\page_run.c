#include "page_run.h"

/*
*********************************************************************************************************
*	函 数 名: page_root_load
*	形    参: 无
*	返 回 值: 无
*	功能说明: 根界面切换时调用
*********************************************************************************************************
*/
void page_run_root_load(void)
{
}
/*
*********************************************************************************************************
*	函 数 名: page_view_load
*	形    参: 无
*	返 回 值: 无
*	功能说明: 交互界面切换时调用
*********************************************************************************************************
*/
void page_run_view_load(void)
{
    bsp_SetKeyParam(KID_K1, 100, 0); // 支持长按1秒，无连发
    bsp_SetKeyParam(KID_K2, 100, 0); // 支持长按1秒，无连发
    bsp_SetKeyParam(KID_K3, 100, 0); // 支持长按1秒，无连发
    bsp_SetKeyParam(KID_K4, 100, 0); // 支持长按1秒，无连发
    bsp_SetKeyParam(KID_K5, 100, 0); // 支持长按1秒，无连发

    UI28_Clear_Screen();                                         /*2.8寸屏幕清屏，并关闭所有模块，一般切换界面时调用*/
    UI28_set_synchronous_gradient_text_gradient(ui28_T3_tg, 0);  /* 设置文本同步渐变 */
    UI28_set_synchronous_gradient_text_gradient(ui28_T4_tg, 0);  /* 设置文本同步渐变 */
    UI28_set_synchronous_gradient_text_gradient(ui28_T5_tg, 0);  /* 设置文本同步渐变 */
    UI28_set_synchronous_gradient_text_gradient(ui28_T6_tg, 0);  /* 设置文本同步渐变 */
    UI28_set_synchronous_gradient_text_gradient(ui28_T11_tg, 0); /* 设置文本同步渐变 */
    UI28_set_synchronous_gradient_text_gradient(ui28_T12_tg, 0); /* 设置文本同步渐变 */
    ILI9340X_Clear(0, 0, 320, 4, WHITE);                         /*对ILI9340X显示器的某一窗口以某种颜色进行清屏*/
    ILI9340X_Clear(0, 236, 320, 4, WHITE);                       /*对ILI9340X显示器的某一窗口以某种颜色进行清屏*/
    ILI9340X_Clear(0, 113, 270, 14, WHITE);                      /*对ILI9340X显示器的某一窗口以某种颜色进行清屏*/
    ILI9340X_Clear(277, 113, 43, 14, WHITE);                     /*对ILI9340X显示器的某一窗口以某种颜色进行清屏*/
    ILI9340X_Clear(224, 50, 90, 2, WHITE);                       /*对ILI9340X显示器的某一窗口以某种颜色进行清屏*/

    UI28_display_onoff_ProgressBar(ui28_1_progress, 1); /* 进度条显示开关（关实际只是不刷新了） */

    switch (PV(int, USR_mode, 0) /* 模式 */)
    {
    case Wash_Mode_1 /* 节能 */:
        ILI9340X_DrawRectangle(226, 60, 86, 44, BLACK, GREEN3, GET_R_ANGLE_BY_ROW(5)); /*显示器上画一个矩形*/
        UI28_change_color_album_text_gradient(ui28_T7_tg, GREEN3, WHITE);              /* 更改颜色 */
        UI28_set_up_text_gradient_add(ui28_T7_tg, "节能");                             /* 设置文本地址 */
        break;
    case Wash_Mode_2 /* 标准 */:
        ILI9340X_DrawRectangle(226, 60, 86, 44, BLACK, BLUE5, GET_R_ANGLE_BY_ROW(5)); /*显示器上画一个矩形*/
        UI28_set_up_text_gradient_add(ui28_T7_tg, "标准");                            /* 设置文本地址 */
        UI28_change_color_album_text_gradient(ui28_T7_tg, BLUE5, WHITE);              /* 更改颜色 */
        break;
    case Wash_Mode_3 /* 强力 */:
        ILI9340X_DrawRectangle(226, 60, 86, 44, BLACK, RED2, GET_R_ANGLE_BY_ROW(5)); /*显示器上画一个矩形*/
        UI28_set_up_text_gradient_add(ui28_T7_tg, "强力");                           /* 设置文本地址 */
        UI28_change_color_album_text_gradient(ui28_T7_tg, RED2, WHITE);              /* 更改颜色 */
        break;
    default:
        break;
    }
    UI28_update_text_gradient(ui28_T7_tg); /* 更新文本渐变 */

    UI28_display_onoff_album(ui28_2_album, 1); /* 菜单显示开关（关实际只是不刷新了） */
    UI28_display_onoff_album(ui28_3_album, 1); /* 菜单显示开关（关实际只是不刷新了） */
    UI28_display_onoff_album(ui28_4_album, 1); /* 菜单显示开关（关实际只是不刷新了） */
    UI28_display_onoff_album(ui28_5_album, 1); /* 菜单显示开关（关实际只是不刷新了） */

    UI28_set_up_text_gradient_add(ui28_T3_tg, "洗"); /* 设置文本地址 */
    UI28_set_up_text_gradient_add(ui28_T4_tg, "涤"); /* 设置文本地址 */
    UI28_set_up_text_gradient_add(ui28_T5_tg, "漂"); /* 设置文本地址 */
    UI28_set_up_text_gradient_add(ui28_T6_tg, "洗"); /* 设置文本地址 */
    UI28_update_text_gradient(ui28_T3_tg);           /* 更新文本渐变 */
    UI28_update_text_gradient(ui28_T4_tg);           /* 更新文本渐变 */
    UI28_update_text_gradient(ui28_T5_tg);           /* 更新文本渐变 */
    UI28_update_text_gradient(ui28_T6_tg);           /* 更新文本渐变 */

    ILI9340X_TwoColorChart(252, 137, 48, 88, BLACK, GREY, (uint8_t *)&Pic_48x88[PIC_48_88_machine].dat[0]); /*对ILI9340X显示器的某一窗口刷双色图*/
    ILI9340X_TwoColorChart(258, 157, 36, 36, BLACK, GREY, (uint8_t *)&Pic_36x36[PIC_36_36_water1].dat[0]);  /*对ILI9340X显示器的某一窗口刷双色图*/

    ILI9340X_DrawRectangle(6, 78, 60, 26, BLACK, BLUE3, GET_R_ANGLE_BY_ROW(5));                         /*显示器上画一个矩形*/
    ILI9340X_TwoColorChart(10, 82, 14, 18, BLUE3, BLACK, (uint8_t *)&Pic_14x18[PIC_14_18_drip].dat[0]); /*对ILI9340X显示器的某一窗口刷双色图*/
    if (PV(int, USR_mill_20 /* 漂洗水位状态是否显示 */, PV(int, USR_set_tad, 0)) /*获取参数值*/ == YES)
    {
        if (PV(int, USR_mill_1, PV(int, USR_set_tad, 0)) /* 水满判断 */ == water_full_2 /* 洗高与漂高 */ || PV(int, USR_mill_2, PV(int, USR_set_tad, 0)) /* 漂洗控温开启条件 */ >= R_t_c_o_c_3 /* 仅漂高 */)
        {
            ILI9340X_DrawRectangle(6, 137, 60, 26, BLACK, YELLOW2, GET_R_ANGLE_BY_ROW(5));                         /*显示器上画一个矩形*/
            ILI9340X_TwoColorChart(10, 141, 14, 18, YELLOW2, BLACK, (uint8_t *)&Pic_14x18[PIC_14_18_drip].dat[0]); /*对ILI9340X显示器的某一窗口刷双色图*/
        }
    }
}

/*
*********************************************************************************************************
*	函  数 名: page_update
*	形    参: 无
*	返 回 值: 无
*	功能说明: 为交互界面时，会周期调用
*********************************************************************************************************
*/
void page_run_update(void)
{
    static int USR_mode_ = 0;
    unsigned char cur_val;

    if (Alarm_Flag != warn_empty)
    {
        /* 发生了报警 */
        UI28_display_onoff_album(ui28_2_album, 0);        /* 菜单显示开关（关实际只是不刷新了） */
        UI28_display_onoff_album(ui28_3_album, 0);        /* 菜单显示开关（关实际只是不刷新了） */
        UI28_display_onoff_album(ui28_4_album, 0);        /* 菜单显示开关（关实际只是不刷新了） */
        UI28_display_onoff_album(ui28_5_album, 0);        /* 菜单显示开关（关实际只是不刷新了） */
        ui28_Play_Count_Animation(ui28_1_animation, 0);   /* 设置动画次数 */
        UI28_display_onoff_text_gradient(ui28_T3_tg, 0);  /* 文本渐变显示开关（关实际只是不刷新了） */
        UI28_display_onoff_text_gradient(ui28_T4_tg, 0);  /* 文本渐变显示开关（关实际只是不刷新了） */
        UI28_display_onoff_text_gradient(ui28_T5_tg, 0);  /* 文本渐变显示开关（关实际只是不刷新了） */
        UI28_display_onoff_text_gradient(ui28_T6_tg, 0);  /* 文本渐变显示开关（关实际只是不刷新了） */
        UI28_display_onoff_text_gradient(ui28_T11_tg, 0); /* 文本渐变显示开关（关实际只是不刷新了） */
        UI28_display_onoff_text_gradient(ui28_T12_tg, 0); /* 文本渐变显示开关（关实际只是不刷新了） */

        /* 刷新报警图片 */
        ILI9340X_Clear(0, 50, 320, 35, RED);   /*对ILI9340X显示器的某一窗口以某种颜色进行清屏*/
        ILI9340X_Clear(0, 155, 320, 35, RED);  /*对ILI9340X显示器的某一窗口以某种颜色进行清屏*/
        ILI9340X_Clear(0, 85, 10, 70, RED);    /*对ILI9340X显示器的某一窗口以某种颜色进行清屏*/
        ILI9340X_Clear(80, 85, 240, 17, RED);  /*对ILI9340X显示器的某一窗口以某种颜色进行清屏*/
        ILI9340X_Clear(80, 138, 240, 17, RED); /*对ILI9340X显示器的某一窗口以某种颜色进行清屏*/
        ILI9340X_Clear(80, 102, 15, 36, RED);  /*对ILI9340X显示器的某一窗口以某种颜色进行清屏*/
        {
            uint16_t x = 0; /* 警告条 */
            for (x = 0; x < 320; x += 40)
            {
                ILI9340X_TwoColorChart(x, 30, 40, 20, ORANGE, RED, (uint8_t *)&Pic_40x20[PIC_40_20_warning_tape].dat[0]);  /*对ILI9340X显示器的某一窗口刷双色图*/
                ILI9340X_TwoColorChart(x, 190, 40, 20, ORANGE, RED, (uint8_t *)&Pic_40x20[PIC_40_20_warning_tape].dat[0]); /*对ILI9340X显示器的某一窗口刷双色图*/
            }
        }
        ILI9340X_TwoColorChart(10, 85, 70, 70, RED, YELLOW, (uint8_t *)&Pic_70x70[PIC_70_70_warning].dat[0]); /*对ILI9340X显示器的某一窗口刷双色图*/
        /* 刷新报警文字 */
        switch (Alarm_Flag /* 报警标志 */)
        {
        case warn_E1 /* E1:洗涤探头异常 */:
            UI28_set_up_text_gradient_add(ui28_T15_tg, "洗涤探头异常"); /* 设置文本地址 */
            break;
        case warn_E2 /* E2:漂洗探头异常 */:
            UI28_set_up_text_gradient_add(ui28_T15_tg, "漂洗探头异常"); /* 设置文本地址 */
            break;
        case warn_E3 /* E3:加水超时报警 */:
            UI28_set_up_text_gradient_add(ui28_T15_tg, "加水超时报警"); /* 设置文本地址 */
            break;
        case warn_E4 /* E4:前后板通讯异常 */:
            UI28_set_up_text_gradient_add(ui28_T15_tg, "后板通讯异常"); /* 设置文本地址 */
            break;
        default:
            break;
        }
        UI28_display_onoff_text_gradient(ui28_T15_tg, 1); /* 文本渐变显示开关（关实际只是不刷新了） */
        return;
    }

    if (USR_mode_ != PV(int, USR_mode, 0) /* 模式 */)
    {
        switch (PV(int, USR_mode, 0) /* 模式 */)
        {
        case Wash_Mode_1 /* 节能 */:
            ILI9340X_DrawRectangle(226, 60, 86, 44, BLACK, GREEN3, GET_R_ANGLE_BY_ROW(5)); /*显示器上画一个矩形*/
            UI28_change_color_album_text_gradient(ui28_T7_tg, GREEN3, WHITE);              /* 更改颜色 */
            UI28_set_up_text_gradient_add(ui28_T7_tg, "节能");                             /* 设置文本地址 */
            break;
        case Wash_Mode_2 /* 标准 */:
            ILI9340X_DrawRectangle(226, 60, 86, 44, BLACK, BLUE5, GET_R_ANGLE_BY_ROW(5)); /*显示器上画一个矩形*/
            UI28_set_up_text_gradient_add(ui28_T7_tg, "标准");                            /* 设置文本地址 */
            UI28_change_color_album_text_gradient(ui28_T7_tg, BLUE5, WHITE);              /* 更改颜色 */
            break;
        case Wash_Mode_3 /* 强力 */:
            ILI9340X_DrawRectangle(226, 60, 86, 44, BLACK, RED2, GET_R_ANGLE_BY_ROW(5)); /*显示器上画一个矩形*/
            UI28_set_up_text_gradient_add(ui28_T7_tg, "强力");                           /* 设置文本地址 */
            UI28_change_color_album_text_gradient(ui28_T7_tg, RED2, WHITE);              /* 更改颜色 */
            break;
        default:
            break;
        }
        UI28_update_text_gradient(ui28_T7_tg); /* 更新文本渐变 */
        USR_mode_ = PV(int, USR_mode, 0) /* 模式 */;
    }

    if (PRO_TEMP_SEE_OBTAIN(PTT1 /* 洗涤温度 */) >= 0)
    {
        cur_val = (unsigned char)(PRO_TEMP_SEE_OBTAIN(PTT1 /* 洗涤温度 */) / 100 % 10 /* 业务用 温度信号(显示)获取 */) + 1;
        UI28_change_cursor_value_album(ui28_2_album, cur_val) /* 设置光标位置 */;
        cur_val = (unsigned char)(PRO_TEMP_SEE_OBTAIN(PTT1 /* 洗涤温度 */) / 10 % 10 /* 业务用 温度信号(显示)获取 */) + 1;
        UI28_change_cursor_value_album(ui28_3_album, cur_val) /* 设置光标位置 */;
    }
    else
    {
        UI28_change_cursor_value_album(ui28_2_album, 0 /* "-" */) /* 设置光标位置 */;
        cur_val = (unsigned char)((-PRO_TEMP_SEE_OBTAIN(PTT1 /* 洗涤温度 */)) / 10 % 10 /* 业务用 温度信号(显示)获取 */) + 1;
        UI28_change_cursor_value_album(ui28_3_album, cur_val) /* 设置光标位置 */;
    }

    if (PRO_TEMP_SEE_OBTAIN(PTT2 /* 漂洗温度 */) >= 0)
    {
        cur_val = (unsigned char)(PRO_TEMP_SEE_OBTAIN(PTT2 /* 漂洗温度 */) / 100 % 10 /* 业务用 温度信号(显示)获取 */) + 1;
        UI28_change_cursor_value_album(ui28_4_album, cur_val) /* 设置光标位置 */;
        cur_val = (unsigned char)(PRO_TEMP_SEE_OBTAIN(PTT2 /* 漂洗温度 */) / 10 % 10 /* 业务用 温度信号(显示)获取 */) + 1;
        UI28_change_cursor_value_album(ui28_5_album, cur_val) /* 设置光标位置 */;
    }
    else
    {
        UI28_change_cursor_value_album(ui28_4_album, 0 /* "-" */) /* 设置光标位置 */;
        cur_val = (unsigned char)((-PRO_TEMP_SEE_OBTAIN(PTT2 /* 漂洗温度 */)) / 10 % 10 /* 业务用 温度信号(显示)获取 */) + 1;
        UI28_change_cursor_value_album(ui28_5_album, cur_val) /* 设置光标位置 */;
    }

    if (PRO_RELAY_OBTAIN(PRELT4 /* 洗加热 */) /* 业务用 继电器控制状态获取 */ != ON /* 开 */)
    {
        UI28_change_color_album(ui28_2_album, WHITE);                                                       /* 相册更改文字颜色 */
        UI28_change_color_album(ui28_3_album, WHITE);                                                       /* 相册更改文字颜色 */
        ILI9340X_TwoColorChart(175, 52, 32, 32, BLACK, WHITE, (uint8_t *)&Pic_32x32[PIC_32_32_ssd].dat[0]); /*对ILI9340X显示器的某一窗口刷双色图*/
    }
    else
    {
        UI28_change_color_album(ui28_2_album, RED);                                                       /* 相册更改文字颜色 */
        UI28_change_color_album(ui28_3_album, RED);                                                       /* 相册更改文字颜色 */
        ILI9340X_TwoColorChart(175, 52, 32, 32, BLACK, RED, (uint8_t *)&Pic_32x32[PIC_32_32_ssd].dat[0]); /*对ILI9340X显示器的某一窗口刷双色图*/
    }
    if (PRO_RELAY_OBTAIN(PRELT3 /* 漂加热 */) /* 业务用 继电器控制状态获取 */ != ON /* 开 */)
    {
        UI28_change_color_album(ui28_4_album, WHITE);                                                        /* 相册更改文字颜色 */
        UI28_change_color_album(ui28_5_album, WHITE);                                                        /* 相册更改文字颜色 */
        ILI9340X_TwoColorChart(175, 194, 32, 32, BLACK, WHITE, (uint8_t *)&Pic_32x32[PIC_32_32_ssd].dat[0]); /*对ILI9340X显示器的某一窗口刷双色图*/
    }
    else
    {
        UI28_change_color_album(ui28_4_album, RED);                                                        /* 相册更改文字颜色 */
        UI28_change_color_album(ui28_5_album, RED);                                                        /* 相册更改文字颜色 */
        ILI9340X_TwoColorChart(175, 194, 32, 32, BLACK, RED, (uint8_t *)&Pic_32x32[PIC_32_32_ssd].dat[0]); /*对ILI9340X显示器的某一窗口刷双色图*/
    }

    if (PRO_SW_SIG_OBTAIN(PSST5 /* 机门 */) /* 业务用 开关信号获取 */ == SIGNAL_OPEN /* 开路 */)
    {
        ILI9340X_TwoColorChart(230, 12, 22, 30, BLACK, GREY, (uint8_t *)&Pic_22x30[PIC_22_30_open_door /* 【开门】 */].dat[0]); /*对ILI9340X显示器的某一窗口刷双色图*/
        UI28_set_up_text_gradient_add(ui28_T8_tg, "门开");                                                                      /* 设置文本地址 */
    }
    else
    {
        ILI9340X_TwoColorChart(230, 12, 22, 30, BLACK, GREY, (uint8_t *)&Pic_22x30[PIC_22_30_close_door /* 【关门】 */].dat[0]); /*对ILI9340X显示器的某一窗口刷双色图*/
        UI28_set_up_text_gradient_add(ui28_T8_tg, "门关");                                                                       /* 设置文本地址 */
    }
    UI28_init_text_gradient_alpha(ui28_T8_tg); /* 初始化渐变值 */
    UI28_update_text_gradient(ui28_T8_tg);     /* 更新文本渐变 */

    if (DIS_P_T.DIS_S_ /* 洗碗状态 */ == DIS_S_RUN /* 动 */ && (DIS_P_T.DIS_P_ /* 洗碗进程 */ == DIS_P_WASH /* 洗涤 */ || DIS_P_T.DIS_P_ /* 洗碗进程 */ == DIS_P_RINSE /* 漂洗 */))
    {
        ui28_Play_Count_Animation(ui28_1_animation, 0XFF); /* 设置动画次数 */
    }
    else
    {
        ui28_Play_Count_Animation(ui28_1_animation, 0); /* 设置动画次数 */
    }

    if (DIS_P_T.DIS_S_ /* 洗碗状态 */ == DIS_S_RUN /* 动 */)
    {
        if (DIS_P_T.DIS_P_ /* 洗碗进程 */ == DIS_P_WASH /* 洗涤 */)
        {
            UI28_display_onoff_text_gradient(ui28_T3_tg, 1); /* 文本渐变显示开关（关实际只是不刷新了） */
            UI28_display_onoff_text_gradient(ui28_T4_tg, 1); /* 文本渐变显示开关（关实际只是不刷新了） */

            UI28_display_onoff_text_gradient(ui28_T5_tg, 0); /* 文本渐变显示开关（关实际只是不刷新了） */
            UI28_display_onoff_text_gradient(ui28_T6_tg, 0); /* 文本渐变显示开关（关实际只是不刷新了） */
            UI28_init_text_gradient_alpha(ui28_T5_tg);       /* 初始化渐变值 */
            UI28_init_text_gradient_alpha(ui28_T6_tg);       /* 初始化渐变值 */
            UI28_update_text_gradient(ui28_T5_tg);           /* 更新文本渐变 */
            UI28_update_text_gradient(ui28_T6_tg);           /* 更新文本渐变 */
        }
        else if (DIS_P_T.DIS_P_ /* 洗碗进程 */ == DIS_P_RINSE /* 漂洗 */)
        {
            UI28_display_onoff_text_gradient(ui28_T5_tg, 1); /* 文本渐变显示开关（关实际只是不刷新了） */
            UI28_display_onoff_text_gradient(ui28_T6_tg, 1); /* 文本渐变显示开关（关实际只是不刷新了） */

            UI28_display_onoff_text_gradient(ui28_T3_tg, 0); /* 文本渐变显示开关（关实际只是不刷新了） */
            UI28_display_onoff_text_gradient(ui28_T4_tg, 0); /* 文本渐变显示开关（关实际只是不刷新了） */
            UI28_init_text_gradient_alpha(ui28_T3_tg);       /* 初始化渐变值 */
            UI28_init_text_gradient_alpha(ui28_T4_tg);       /* 初始化渐变值 */
            UI28_update_text_gradient(ui28_T3_tg);           /* 更新文本渐变 */
            UI28_update_text_gradient(ui28_T4_tg);           /* 更新文本渐变 */
        }
    }
    else
    {
        UI28_display_onoff_text_gradient(ui28_T3_tg, 0); /* 文本渐变显示开关（关实际只是不刷新了） */
        UI28_display_onoff_text_gradient(ui28_T4_tg, 0); /* 文本渐变显示开关（关实际只是不刷新了） */
        UI28_display_onoff_text_gradient(ui28_T5_tg, 0); /* 文本渐变显示开关（关实际只是不刷新了） */
        UI28_display_onoff_text_gradient(ui28_T6_tg, 0); /* 文本渐变显示开关（关实际只是不刷新了） */
        UI28_init_text_gradient_alpha(ui28_T3_tg);       /* 初始化渐变值 */
        UI28_init_text_gradient_alpha(ui28_T4_tg);       /* 初始化渐变值 */
        UI28_init_text_gradient_alpha(ui28_T5_tg);       /* 初始化渐变值 */
        UI28_init_text_gradient_alpha(ui28_T6_tg);       /* 初始化渐变值 */
        UI28_update_text_gradient(ui28_T3_tg);           /* 更新文本渐变 */
        UI28_update_text_gradient(ui28_T4_tg);           /* 更新文本渐变 */
        UI28_update_text_gradient(ui28_T5_tg);           /* 更新文本渐变 */
        UI28_update_text_gradient(ui28_T6_tg);           /* 更新文本渐变 */
    }

    if (PV(int, USR_mill_2, PV(int, USR_set_tad, 0)) /* 漂洗控温开启条件 */ == R_t_c_o_c_2 /* 仅洗低 */ || PV(int, USR_mill_3, PV(int, USR_set_tad, 0)) /* 洗涤控温开启条件 */ == W_t_c_o_c_2 /* 仅洗低 */)
    {
        if (PRO_SW_SIG_OBTAIN(PSST1 /* 洗高 */) /* 业务用 开关信号获取 */ == SIGNAL_OPEN /* 开路 */)
        {
            if (PRO_SW_SIG_OBTAIN(PSST2 /* 洗低 */) /* 业务用 开关信号获取 */ == SIGNAL_OPEN /* 开路 */)
            {
                UI28_set_up_text_gradient_add(ui28_T9_tg, "未满"); /* 设置文本地址 */
            }
            else
            {
                UI28_set_up_text_gradient_add(ui28_T9_tg, "正常"); /* 设置文本地址 */
            }
        }
        else
        {
            UI28_set_up_text_gradient_add(ui28_T9_tg, "水满"); /* 设置文本地址 */
        }
    }
    else
    {
        if (PRO_SW_SIG_OBTAIN(PSST1 /* 洗高 */) /* 业务用 开关信号获取 */ == SIGNAL_OPEN /* 开路 */)
        {
            UI28_set_up_text_gradient_add(ui28_T9_tg, "未满"); /* 设置文本地址 */
        }
        else
        {
            UI28_set_up_text_gradient_add(ui28_T9_tg, "水满"); /* 设置文本地址 */
        }
    }
    UI28_init_text_gradient_alpha(ui28_T9_tg); /* 初始化渐变值 */
    UI28_update_text_gradient(ui28_T9_tg);     /* 更新文本渐变 */

    if (PV(int, USR_mill_20 /* 漂洗水位状态是否显示 */, PV(int, USR_set_tad, 0)) /*获取参数值*/ == YES)
    {
        if (PV(int, USR_mill_1, PV(int, USR_set_tad, 0)) /* 水满判断 */ == water_full_2 /* 洗高与漂高 */ || PV(int, USR_mill_2, PV(int, USR_set_tad, 0)) /* 漂洗控温开启条件 */ >= R_t_c_o_c_3 /* 仅漂高 */)
        {
            if (PV(int, USR_mill_2, PV(int, USR_set_tad, 0)) /* 漂洗控温开启条件 */ == R_t_c_o_c_4 /* 仅漂低 */)
            {
                if (PRO_SW_SIG_OBTAIN(PSST3 /* 漂高 */) /* 业务用 开关信号获取 */ == SIGNAL_OPEN /* 开路 */)
                {
                    if (PRO_SW_SIG_OBTAIN(PSST4 /* 漂低 */) /* 业务用 开关信号获取 */ == SIGNAL_OPEN /* 开路 */)
                    {
                        UI28_set_up_text_gradient_add(ui28_T10_tg, "未满"); /* 设置文本地址 */
                    }
                    else
                    {
                        UI28_set_up_text_gradient_add(ui28_T10_tg, "正常"); /* 设置文本地址 */
                    }
                }
                else
                {
                    UI28_set_up_text_gradient_add(ui28_T10_tg, "水满"); /* 设置文本地址 */
                }
            }
            else
            {
                if (PRO_SW_SIG_OBTAIN(PSST3 /* 漂高 */) /* 业务用 开关信号获取 */ == SIGNAL_OPEN /* 开路 */)
                {
                    UI28_set_up_text_gradient_add(ui28_T10_tg, "缺水"); /* 设置文本地址 */
                }
                else
                {
                    UI28_set_up_text_gradient_add(ui28_T10_tg, "未满"); /* 设置文本地址 */
                }
            }
            UI28_init_text_gradient_alpha(ui28_T10_tg); /* 初始化渐变值 */
            UI28_update_text_gradient(ui28_T10_tg);     /* 更新文本渐变 */
        }
    }

    if ((Water_inlet_delay_count > 0) /* 进水延时中 */ && (Full_water_check_function() == TRUE) /* 水满检查为真 */)
    {
        UI28_display_onoff_text_gradient(ui28_T11_tg, 1);            /* 文本渐变显示开关（关实际只是不刷新了） */
        UI28_set_up_text_gradient_add(ui28_T11_tg, "延时进水中..."); /* 设置文本地址 */
    }
    else
    {
        UI28_display_onoff_text_gradient(ui28_T11_tg, 0); /* 文本渐变显示开关（关实际只是不刷新了） */
        UI28_set_up_text_gradient_add(ui28_T11_tg, " ");  /* 设置文本地址 */
        UI28_init_text_gradient_alpha(ui28_T11_tg);       /* 初始化渐变值 */
        UI28_update_text_gradient(ui28_T11_tg);           /* 更新文本渐变 */
    }

    if (DIS_P_T.DIS_S_ /* 洗碗状态 */ == DIS_S_RUN /* 动 */)
    {
        uint16_t progress_bar = 0;      /* 进度条 */
        uint8_t progress_bar_speed = 1; /* 进度条速度 */
        int percentage = 0;             /* 百分比 */
        int remaining_time = 0;         /* 总时间 */
        char text_b[6] = "    %";
        char text[23];
        switch (DIS_P_T.DIS_P_ /* 洗碗进程 */)
        {
        case DIS_P_WASH /* 洗涤 */:
            switch (PV(int, USR_mode, 0)) /* 洗碗模式(节能、标准、强力) */
            {
            case Wash_Mode_1:                                                                   /* 节能 */
                remaining_time = PV(int, USR_mill_17, PV(int, USR_set_tad, 0)) - DIS_P_T.count; /* 剩余时间=节能时间-计数 */
                break;
            case Wash_Mode_2:                                                                   /* 标准 */
                remaining_time = PV(int, USR_mill_18, PV(int, USR_set_tad, 0)) - DIS_P_T.count; /* 剩余时间=标准时间-计数 */
                break;
            case Wash_Mode_3:                                                                   /* 强力 */
                remaining_time = PV(int, USR_mill_19, PV(int, USR_set_tad, 0)) - DIS_P_T.count; /* 剩余时间=强力时间-计数 */
                break;
            default:
                break;
            }
            sprintf(&text[0], "%s", "洗涤中:剩余");
            progress_bar_speed = 6; /* 进度条速度 */
            break;
        case DIS_P_STOP /* 停顿 */:
            remaining_time = PV(int, USR_mill_23, PV(int, USR_set_tad, 0)) - DIS_P_T.count; /* 剩余时间=停顿时间-计数 */
            sprintf(&text[0], "%s", "停顿中:剩余");
            progress_bar_speed = 5; /* 进度条速度 */
            break;
        case DIS_P_RINSE /* 漂洗 */:
            remaining_time = PV(int, USR_user_13, 0) - DIS_P_T.count; /* 剩余时间=漂洗时间-计数 */
            sprintf(&text[0], "%s", "漂洗中:剩余");
            progress_bar_speed = 6; /* 进度条速度 */
            break;
        case DIS_P_FINISH /* 完成 */:
            UI28_set_up_text_gradient_add(ui28_T12_tg, " "); /* 设置文本地址 */
            break;
        default:
            break;
        }

        if (DIS_P_T.DIS_P_ /* 洗碗进程 */ != DIS_P_FINISH /* 完成 */)
        {
            percentage = DIS_P_T.count * 100 / (DIS_P_T.count + remaining_time); /* 百分比 */
            if (percentage < 10 /*个*/)
            {
                text_b[2] = 0x30 + (uint8_t)percentage;
            }
            else if (percentage < 100 /*十*/)
            {
                text_b[2] = 0x30 + (uint8_t)(percentage / 10);
                text_b[3] = 0x30 + (uint8_t)(percentage % 10);
            }
            else if (percentage == 100 /*百*/)
            {
                text_b[1] = 0x31;
                text_b[2] = 0x30;
                text_b[3] = 0x30;
            }
            UI28_set_up_text_gradient_add(ui28_T13_tg, &text_b[0]); /* 设置文本地址 */
            UI28_init_text_gradient_alpha(ui28_T13_tg);             /* 初始化渐变值 */
            UI28_update_text_gradient(ui28_T13_tg);                 /* 更新文本渐变 */

            progress_bar = DIS_P_T.count * 100 / (DIS_P_T.count + remaining_time); /* 进度条 */
            UI28_change_cursor_value_ProgressBar(ui28_1_progress, (uint8_t)progress_bar, progress_bar_speed) /* 设置进度条位置 */;

            if (remaining_time == 0)
            {
                remaining_time = 1;
            }

            sprintf(&text[16], "%d", remaining_time);
            if (remaining_time < 10 /*个*/)
            {
                memcpy(&text[17], "秒", 4);
            }
            else if (remaining_time < 100 /*十*/)
            {
                memcpy(&text[18], "秒", 4);
            }
            else if (remaining_time < 1000 /*百*/)
            {
                memcpy(&text[19], "秒", 4);
            }
            UI28_set_up_text_gradient_add(ui28_T12_tg, (volatile char *)&text[0]); /* 设置文本地址 */
        }

        UI28_display_onoff_text_gradient(ui28_T12_tg, 0); /* 文本渐变显示开关（关实际只是不刷新了） */
        UI28_init_text_gradient_alpha(ui28_T12_tg);       /* 初始化渐变值 */
        UI28_update_text_gradient(ui28_T12_tg);           /* 更新文本渐变 */
    }
    else
    {
        UI28_display_onoff_text_gradient(ui28_T12_tg, 1); /* 文本渐变显示开关（关实际只是不刷新了） */
        switch (DishwashingDeterminationOfOperatingConditions() /* 运行条件判断 */)
        {
        case DIS_C_SATISFY /* 满足 */:
            if (DIS_P_T.DIS_P_ /* 洗碗进程 */ == DIS_P_FINISH /* 完成 */)
            {
                UI28_set_up_text_gradient_add(ui28_T12_tg, "完成,满足洗碗条件"); /* 设置文本地址 */
            }
            else
            {
                UI28_set_up_text_gradient_add(ui28_T12_tg, "满足洗碗条件"); /* 设置文本地址 */
            }
            break;
        case DIS_C_DOOR /* 门 */:
            UI28_set_up_text_gradient_add(ui28_T12_tg, "机门未关,请稍后..."); /* 设置文本地址 */
            break;
        case DIS_C_WATER /* 水 */:
            UI28_set_up_text_gradient_add(ui28_T12_tg, "水未满,请稍后..."); /* 设置文本地址 */
            break;
        case DIS_C_TEMP /* 温度 */:
            UI28_set_up_text_gradient_add(ui28_T12_tg, "未到门槛温度,等待"); /* 设置文本地址 */
            break;
        default:
            break;
        }
    }
}
