#ifndef _BSP_TEMP_H_
#define _BSP_TEMP_H_
#define KALMAN_SWITCH 0
#if (KALMAN_SWITCH == 1)      /* 卡尔曼滤波器开关 */
#define KALMAN_SHARE_SWITCH 0 /* 卡尔曼滤波器共用开关，1表示共用，0表示独立，如果有缩短滤波周期的需求可以禁用共用 */
#define KALMAN_NUMBER 4       /* 卡尔曼滤波个数（1表示2个，2表示4个，3表示8个，4表示16个） */
#define KALMAN_E_mea 1.0f     /* 初始化测量误差（传感器误差+AD误差） */
#define KALMAN_E_est 2.5f     /* 初始化估计误差（初始化不可为 0 ） */
#endif

#define WINDOW_FILTERING_SWITCH 0 /* 窗口滤波启用开关 */

#define REDUCE_WEIGHT_FILTERING_SWITCH 0  /* 降权重滤波启用开关 */
#if (REDUCE_WEIGHT_FILTERING_SWITCH == 1) /* 降权重滤波启用开关 */
#define REDUCE_WEIGHT_PERIOD 500          /* 降权重周期ms */
#endif

#define DISPLAY_REFRESH_CYCLE 1000 /* 显示刷新周期ms */
#define DISPLAY_FILTERING_WEIGHT 3 /* 显示滤波权重，越大越缓慢 */

// #define NTC_TEMP 0   // ntc探头
// #define K_TEMP 1     // K型探头
#define OTHER_TEMP 2 // 其他探头

#ifdef NTC_TEMP
#error "将NTC温度获取宏定义后，删除该行"
#define NTC_DRI bsp_GetAdcValues();                                                               /* 获取一次ADC的值，NTC温度转换用 */
#define NTC_ODT_TEMP(a) temp_p_t[a].True_temp = CalcTempWithRes3950K(AdcValues[temp_probe_pa[a]]) // 获取NTC温度
#define NTC_FAULT_CONDITIONS()                                                                    /* NTC探头故障条件 */ \
    do                                                                                                                  \
    {                                                                                                                   \
    } while (0)
#endif
#ifdef K_TEMP
#error "将K型温度获取宏定义后，删除该行"
#define K_DRI bsp_GetAdcValues();             /* 获取一次ADC的值 */
#define K_ODT_TEMP(a) temp_p_t[a].True_temp = // 获取K型温度
#define K_FAULT_CONDITIONS()                  /* K型探头故障条件 */ \
    do                                                              \
    {                                                               \
    } while (0)
#endif
#ifdef OTHER_TEMP
// #error "将其他温度获取宏定义后，删除该行"
#define OTHER_DRI ;                                                  /* 从后板获取温度，不需要驱动传感器 */
#define OTHER_ODT_TEMP(a) temp_p_t[a].True_temp = InformationTemp[a] // 获取其他温度
#define OTHER_FAULT_CONDITIONS()                                     /* 其他探头故障条件 */                                                                 \
    do                                                                                                                                                      \
    {                                                                                                                                                       \
        if (temp_p_t[i].Control_temp < -250 /* 小于-250，可能探头未插开路 */ || temp_p_t[i].Control_temp > 2400 /* 大于2400，可能探头短路或者电路板连锡 */) \
        {                                                                                                                                                   \
            if (0 == temp_p_t[i].abnormal)                                                                                                                  \
            {                                                                                                                                               \
                temp_p_t[i].abnormal = 5;                                                                                                                   \
            }                                                                                                                                               \
        }                                                                                                                                                   \
        else                                                                                                                                                \
        {                                                                                                                                                   \
            if (0 != temp_p_t[i].abnormal)                                                                                                                  \
            {                                                                                                                                               \
                temp_p_t[i].abnormal--;                                                                                                                     \
                temp_p_t[i].Control_temp = temp_p_t[i].True_temp; /* NTC探头恢复时，直接使用原始值 */                                                       \
            }                                                                                                                                               \
        }                                                                                                                                                   \
    } while (0)
#endif

#define TEMP_PROBE_ITEM_LIST                                                                                                                                        \
    TEMP_PROBE_DEBUG_X(TP1 /* 名称 */, OTHER_TEMP /* 探头类型 */, 0 /* 获取通道 */, 0 /* 窗口滑动滤波个数，0表示关闭 */, 3 /* 降权值，曲线越缓，小于2表示不滤波 */) \
    TEMP_PROBE_DEBUG_X(TP2 /* 名称 */, OTHER_TEMP /* 探头类型 */, 1 /* 获取通道 */, 0 /* 窗口滑动滤波个数，0表示关闭 */, 3 /* 降权值，曲线越缓，小于2表示不滤波 */)

#define TEMPERATURE_ITEM_LIST                                                                                                                        \
    TEMPERATURE_DEBUG_X(T1 /* 名称 */, TPID_TP1 /* 探头ID */, NULL /* 目标温度地址，不为空时开启磁吸滤波 */, 0 /* 显示偏移值 0.1℃ */) /* 洗涤温度 */ \
    TEMPERATURE_DEBUG_X(T2 /* 名称 */, TPID_TP2 /* 探头ID */, NULL /* 目标温度地址，不为空时开启磁吸滤波 */, 0 /* 显示偏移值 0.1℃ */) /* 漂洗温度 */

/* 温度探头ID */
enum temp_probe_id
{
#define TEMP_PROBE_DEBUG_X(name, class, pass, Win, weight) TPID_##name,
    TEMP_PROBE_ITEM_LIST
#undef TEMP_PROBE_DEBUG_X
        TEMP_PROBE_ITEM_NUM,
};

/* 温度ID */
enum temperature_id
{
#define TEMPERATURE_DEBUG_X(name, tpid, desired, drift) TID_##name,
    TEMPERATURE_ITEM_LIST
#undef TEMPERATURE_DEBUG_X
        TEMPERATURE_ITEM_NUM,
};

/* 温度探头结构体 */
typedef struct
{
    int16_t True_temp;    /* 原始温度(0.1°C),从传感器获取未经过处理的温度 */
    int16_t Control_temp; /* 使用温度(0.1°C)，经过滤波处理后的温度，给业务用如控温 */
    uint8_t abnormal;     /* 探头异常标志(大于0为异常) */
} temp_probe_t;

/* 温度 */
typedef struct
{
    temp_probe_t *probe;     /* 探头指针 */
    int16_t *Desired_temp;   /* 目标温度(0.1°C) */
    int16_t Displaytemp;     /* 显示温度(0.1°C) */
    int16_t LastDisplaytemp; /* 上一次显示温度(0.1°C) */
    int16_t NextDisplaytemp; /* 下一次显示温度(0.1°C) */
} temperature_t;

extern const uint8_t temp_probe_c[TEMP_PROBE_ITEM_NUM];       /* 温度探头类型表 */
extern volatile temp_probe_t temp_p_t[TEMP_PROBE_ITEM_NUM];   /* 温度探头结构体 */
extern volatile temperature_t temp_e_t[TEMPERATURE_ITEM_NUM]; /* 温度结构体 */

/* 扫描所有探头，做滤波处理，被1ms周期性的调用 */
void bsp_TempProbeScan1ms(void);

#endif
