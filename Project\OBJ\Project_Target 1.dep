Dependencies for Project 'Project', Target 'Target 1': (DO NOT MODIFY !)
F (..\User\main.c)(0x6857B0E5)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/main.o -MD)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdlib.h)(0x603507D0)
I (D:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (..\User\bsp\bsp.h)(0x6826A21F)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
I (..\User\bsp\inc\bsp_timer.h)(0x680C5798)
I (..\User\bsp\inc\bsp_uart_fifo.h)(0x682426F2)
I (..\User\bsp\inc\bsp_boot.h)(0x6832B3D5)
I (..\User\bsp\inc\bsp_key.h)(0x68576AFA)
I (..\User\bsp\inc\bsp_crc.h)(0x682C3545)
I (..\User\bsp\inc\bsp_utility.h)(0x68107F3A)
I (..\User\bsp\inc\bsp_temp.h)(0x681322C8)
I (..\User\bsp\inc\bsp_ntc.h)(0x678DDDCC)
I (..\User\bsp\inc\bsp_signal.h)(0x682ACFBF)
I (..\User\bsp\inc\bsp_beep.h)(0x6827E666)
I (..\User\bsp\inc\bsp_fifo.h)(0x682C143B)
I (..\User\bsp\inc\bsp_tim_pwm.h)(0x68143C04)
I (..\User\ui2.8\UI28.h)(0x6856620B)
I (..\User\ui2.8\UI28_auxiliary_table.h)(0x6801A019)
I (..\User\ui2.8\bsp_ili9340x_lcd.h)(0x6828483F)
I (..\User\ui2.8\UI28_font_16.h)(0x681EB98A)
I (..\User\ui2.8\UI28_font_20.h)(0x67F69774)
I (..\User\ui2.8\UI28_font_32.h)(0x681AFF0D)
I (..\User\ui2.8\UI28_font_72.h)(0x681D7617)
I (..\User\ui2.8\UI28_font_28.h)(0x681DBC62)
I (..\User\ui2.8\UI28_font_36.h)(0x681EACC0)
I (..\User\ui2.8\UI28_font_24.h)(0x682E7B21)
I (..\User\ui2.8\UI28_font_18.h)(0x681EF7D2)
I (..\User\ui2.8\UI28_font_14.h)(0x681ED798)
I (..\User\ui2.8\UI28_pic_120x120.h)(0x6857B1FC)
I (..\User\ui2.8\UI28_pic_32x32.h)(0x681D853E)
I (..\User\ui2.8\UI28_pic_22x30.h)(0x681D8CDE)
I (..\User\ui2.8\UI28_pic_14x18.h)(0x681DB278)
I (..\User\ui2.8\UI28_pic_48x88.h)(0x681DA6D8)
I (..\User\ui2.8\UI28_pic_36x36.h)(0x681DB27C)
I (..\User\ui2.8\UI28_pic_45x45.h)(0x68204BEA)
I (..\User\ui2.8\UI28_pic_40x20.h)(0x68283E6F)
I (..\User\ui2.8\UI28_pic_70x70.h)(0x682840DF)
I (..\User\ui2.8\UI28_matrix.h)(0x6857730C)
I (..\User\ui2.8\UI28_menu.h)(0x68579EAC)
I (..\User\ui2.8\UI28_rolling_album.h)(0x6854C854)
I (..\User\ui2.8\UI28_arc.h)(0x685772E6)
I (..\User\ui2.8\UI28_animation.h)(0x6829BB09)
I (..\User\ui2.8\UI28_progress_bar.h)(0x68567976)
I (..\User\pro\pro_cus_com.h)(0x685605BA)
I (..\User\pro\pro_hardware.h)(0x6829DA80)
I (..\User\pro\pro_workflow.h)(0x682D8D63)
I (..\User\pro\pro_error.h)(0x682C215A)
I (..\User\pro\pro_pagekey.h)(0x68297893)
I (..\User\page\page.h)(0x68555953)
I (..\User\page\src_inc\page_start.h)(0x67966D00)
I (..\User\page\src_inc\page_standby.h)(0x67966D02)
I (..\User\page\src_inc\page_run.h)(0x67966D08)
I (..\User\page\src_inc\page_parameter.h)(0x68579AE2)
I (..\User\page\src_inc\page_debug.h)(0x67966D0C)
I (..\User\page\src_inc\page_statistics.h)(0x682AF7F2)
I (..\User\page\src_inc\page_check.h)(0x67966D0E)
I (..\User\page\src_inc\page_developer.h)(0x67966D0C)
I (..\User\page\src_inc\page_qr_code.h)(0x681F13C4)
I (..\User\parame\parame.h)(0x68294931)
I (..\User\parame\parame_tongyong.h)(0x6857B6B2)
I (..\User\parame\flash_eeprom.h)(0x68560C9B)
F (..\User\air32f10x_it.c)(0x67C58CA8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/air32f10x_it.o -MD)
I (..\User\air32f10x_it.h)(0x67E69F20)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
F (..\Libraries\AIR32F10xLib\src\system_air32f10x.c)(0x68560593)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/system_air32f10x.o -MD)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
F (..\User\bsp\bsp.c)(0x6857BE7D)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/bsp.o -MD)
I (..\User\bsp\bsp.h)(0x6826A21F)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (D:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdlib.h)(0x603507D0)
I (..\User\bsp\inc\bsp_timer.h)(0x680C5798)
I (..\User\bsp\inc\bsp_uart_fifo.h)(0x682426F2)
I (..\User\bsp\inc\bsp_boot.h)(0x6832B3D5)
I (..\User\bsp\inc\bsp_key.h)(0x68576AFA)
I (..\User\bsp\inc\bsp_crc.h)(0x682C3545)
I (..\User\bsp\inc\bsp_utility.h)(0x68107F3A)
I (..\User\bsp\inc\bsp_temp.h)(0x681322C8)
I (..\User\bsp\inc\bsp_ntc.h)(0x678DDDCC)
I (..\User\bsp\inc\bsp_signal.h)(0x682ACFBF)
I (..\User\bsp\inc\bsp_beep.h)(0x6827E666)
I (..\User\bsp\inc\bsp_fifo.h)(0x682C143B)
I (..\User\bsp\inc\bsp_tim_pwm.h)(0x68143C04)
I (..\User\ui2.8\UI28.h)(0x6856620B)
I (..\User\ui2.8\UI28_auxiliary_table.h)(0x6801A019)
I (..\User\ui2.8\bsp_ili9340x_lcd.h)(0x6828483F)
I (..\User\ui2.8\UI28_font_16.h)(0x681EB98A)
I (..\User\ui2.8\UI28_font_20.h)(0x67F69774)
I (..\User\ui2.8\UI28_font_32.h)(0x681AFF0D)
I (..\User\ui2.8\UI28_font_72.h)(0x681D7617)
I (..\User\ui2.8\UI28_font_28.h)(0x681DBC62)
I (..\User\ui2.8\UI28_font_36.h)(0x681EACC0)
I (..\User\ui2.8\UI28_font_24.h)(0x682E7B21)
I (..\User\ui2.8\UI28_font_18.h)(0x681EF7D2)
I (..\User\ui2.8\UI28_font_14.h)(0x681ED798)
I (..\User\ui2.8\UI28_pic_120x120.h)(0x6857B1FC)
I (..\User\ui2.8\UI28_pic_32x32.h)(0x681D853E)
I (..\User\ui2.8\UI28_pic_22x30.h)(0x681D8CDE)
I (..\User\ui2.8\UI28_pic_14x18.h)(0x681DB278)
I (..\User\ui2.8\UI28_pic_48x88.h)(0x681DA6D8)
I (..\User\ui2.8\UI28_pic_36x36.h)(0x681DB27C)
I (..\User\ui2.8\UI28_pic_45x45.h)(0x68204BEA)
I (..\User\ui2.8\UI28_pic_40x20.h)(0x68283E6F)
I (..\User\ui2.8\UI28_pic_70x70.h)(0x682840DF)
I (..\User\ui2.8\UI28_matrix.h)(0x6857730C)
I (..\User\ui2.8\UI28_menu.h)(0x68579EAC)
I (..\User\ui2.8\UI28_rolling_album.h)(0x6854C854)
I (..\User\ui2.8\UI28_arc.h)(0x685772E6)
I (..\User\ui2.8\UI28_animation.h)(0x6829BB09)
I (..\User\ui2.8\UI28_progress_bar.h)(0x68567976)
I (..\User\pro\pro_cus_com.h)(0x685605BA)
I (..\User\pro\pro_hardware.h)(0x6829DA80)
I (..\User\pro\pro_workflow.h)(0x682D8D63)
I (..\User\pro\pro_error.h)(0x682C215A)
I (..\User\pro\pro_pagekey.h)(0x68297893)
I (..\User\page\page.h)(0x68555953)
I (..\User\page\src_inc\page_start.h)(0x67966D00)
I (..\User\page\src_inc\page_standby.h)(0x67966D02)
I (..\User\page\src_inc\page_run.h)(0x67966D08)
I (..\User\page\src_inc\page_parameter.h)(0x68579AE2)
I (..\User\page\src_inc\page_debug.h)(0x67966D0C)
I (..\User\page\src_inc\page_statistics.h)(0x682AF7F2)
I (..\User\page\src_inc\page_check.h)(0x67966D0E)
I (..\User\page\src_inc\page_developer.h)(0x67966D0C)
I (..\User\page\src_inc\page_qr_code.h)(0x681F13C4)
I (..\User\parame\parame.h)(0x68294931)
I (..\User\parame\parame_tongyong.h)(0x6857B6B2)
I (..\User\parame\flash_eeprom.h)(0x68560C9B)
F (..\User\bsp\bsp.h)(0x6826A21F)()
F (..\User\bsp\src\bsp_timer.c)(0x682C35B2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/bsp_timer.o -MD)
I (..\User\bsp\bsp.h)(0x6826A21F)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (D:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdlib.h)(0x603507D0)
I (..\User\bsp\inc\bsp_timer.h)(0x680C5798)
I (..\User\bsp\inc\bsp_uart_fifo.h)(0x682426F2)
I (..\User\bsp\inc\bsp_boot.h)(0x6832B3D5)
I (..\User\bsp\inc\bsp_key.h)(0x68576AFA)
I (..\User\bsp\inc\bsp_crc.h)(0x682C3545)
I (..\User\bsp\inc\bsp_utility.h)(0x68107F3A)
I (..\User\bsp\inc\bsp_temp.h)(0x681322C8)
I (..\User\bsp\inc\bsp_ntc.h)(0x678DDDCC)
I (..\User\bsp\inc\bsp_signal.h)(0x682ACFBF)
I (..\User\bsp\inc\bsp_beep.h)(0x6827E666)
I (..\User\bsp\inc\bsp_fifo.h)(0x682C143B)
I (..\User\bsp\inc\bsp_tim_pwm.h)(0x68143C04)
I (..\User\ui2.8\UI28.h)(0x6856620B)
I (..\User\ui2.8\UI28_auxiliary_table.h)(0x6801A019)
I (..\User\ui2.8\bsp_ili9340x_lcd.h)(0x6828483F)
I (..\User\ui2.8\UI28_font_16.h)(0x681EB98A)
I (..\User\ui2.8\UI28_font_20.h)(0x67F69774)
I (..\User\ui2.8\UI28_font_32.h)(0x681AFF0D)
I (..\User\ui2.8\UI28_font_72.h)(0x681D7617)
I (..\User\ui2.8\UI28_font_28.h)(0x681DBC62)
I (..\User\ui2.8\UI28_font_36.h)(0x681EACC0)
I (..\User\ui2.8\UI28_font_24.h)(0x682E7B21)
I (..\User\ui2.8\UI28_font_18.h)(0x681EF7D2)
I (..\User\ui2.8\UI28_font_14.h)(0x681ED798)
I (..\User\ui2.8\UI28_pic_120x120.h)(0x6857B1FC)
I (..\User\ui2.8\UI28_pic_32x32.h)(0x681D853E)
I (..\User\ui2.8\UI28_pic_22x30.h)(0x681D8CDE)
I (..\User\ui2.8\UI28_pic_14x18.h)(0x681DB278)
I (..\User\ui2.8\UI28_pic_48x88.h)(0x681DA6D8)
I (..\User\ui2.8\UI28_pic_36x36.h)(0x681DB27C)
I (..\User\ui2.8\UI28_pic_45x45.h)(0x68204BEA)
I (..\User\ui2.8\UI28_pic_40x20.h)(0x68283E6F)
I (..\User\ui2.8\UI28_pic_70x70.h)(0x682840DF)
I (..\User\ui2.8\UI28_matrix.h)(0x6857730C)
I (..\User\ui2.8\UI28_menu.h)(0x68579EAC)
I (..\User\ui2.8\UI28_rolling_album.h)(0x6854C854)
I (..\User\ui2.8\UI28_arc.h)(0x685772E6)
I (..\User\ui2.8\UI28_animation.h)(0x6829BB09)
I (..\User\ui2.8\UI28_progress_bar.h)(0x68567976)
I (..\User\pro\pro_cus_com.h)(0x685605BA)
I (..\User\pro\pro_hardware.h)(0x6829DA80)
I (..\User\pro\pro_workflow.h)(0x682D8D63)
I (..\User\pro\pro_error.h)(0x682C215A)
I (..\User\pro\pro_pagekey.h)(0x68297893)
I (..\User\page\page.h)(0x68555953)
I (..\User\page\src_inc\page_start.h)(0x67966D00)
I (..\User\page\src_inc\page_standby.h)(0x67966D02)
I (..\User\page\src_inc\page_run.h)(0x67966D08)
I (..\User\page\src_inc\page_parameter.h)(0x68579AE2)
I (..\User\page\src_inc\page_debug.h)(0x67966D0C)
I (..\User\page\src_inc\page_statistics.h)(0x682AF7F2)
I (..\User\page\src_inc\page_check.h)(0x67966D0E)
I (..\User\page\src_inc\page_developer.h)(0x67966D0C)
I (..\User\page\src_inc\page_qr_code.h)(0x681F13C4)
I (..\User\parame\parame.h)(0x68294931)
I (..\User\parame\parame_tongyong.h)(0x6857B6B2)
I (..\User\parame\flash_eeprom.h)(0x68560C9B)
F (..\User\bsp\src\bsp_uart_fifo.c)(0x682359CC)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/bsp_uart_fifo.o -MD)
I (..\User\bsp\inc\bsp_uart_fifo.h)(0x682426F2)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdlib.h)(0x603507D0)
I (D:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
I (..\User\bsp\inc\bsp_boot.h)(0x6832B3D5)
I (..\User\bsp\bsp.h)(0x6826A21F)
I (..\User\bsp\inc\bsp_timer.h)(0x680C5798)
I (..\User\bsp\inc\bsp_key.h)(0x68576AFA)
I (..\User\bsp\inc\bsp_crc.h)(0x682C3545)
I (..\User\bsp\inc\bsp_utility.h)(0x68107F3A)
I (..\User\bsp\inc\bsp_temp.h)(0x681322C8)
I (..\User\bsp\inc\bsp_ntc.h)(0x678DDDCC)
I (..\User\bsp\inc\bsp_signal.h)(0x682ACFBF)
I (..\User\bsp\inc\bsp_beep.h)(0x6827E666)
I (..\User\bsp\inc\bsp_fifo.h)(0x682C143B)
I (..\User\bsp\inc\bsp_tim_pwm.h)(0x68143C04)
I (..\User\ui2.8\UI28.h)(0x6856620B)
I (..\User\ui2.8\UI28_auxiliary_table.h)(0x6801A019)
I (..\User\ui2.8\bsp_ili9340x_lcd.h)(0x6828483F)
I (..\User\ui2.8\UI28_font_16.h)(0x681EB98A)
I (..\User\ui2.8\UI28_font_20.h)(0x67F69774)
I (..\User\ui2.8\UI28_font_32.h)(0x681AFF0D)
I (..\User\ui2.8\UI28_font_72.h)(0x681D7617)
I (..\User\ui2.8\UI28_font_28.h)(0x681DBC62)
I (..\User\ui2.8\UI28_font_36.h)(0x681EACC0)
I (..\User\ui2.8\UI28_font_24.h)(0x682E7B21)
I (..\User\ui2.8\UI28_font_18.h)(0x681EF7D2)
I (..\User\ui2.8\UI28_font_14.h)(0x681ED798)
I (..\User\ui2.8\UI28_pic_120x120.h)(0x6857B1FC)
I (..\User\ui2.8\UI28_pic_32x32.h)(0x681D853E)
I (..\User\ui2.8\UI28_pic_22x30.h)(0x681D8CDE)
I (..\User\ui2.8\UI28_pic_14x18.h)(0x681DB278)
I (..\User\ui2.8\UI28_pic_48x88.h)(0x681DA6D8)
I (..\User\ui2.8\UI28_pic_36x36.h)(0x681DB27C)
I (..\User\ui2.8\UI28_pic_45x45.h)(0x68204BEA)
I (..\User\ui2.8\UI28_pic_40x20.h)(0x68283E6F)
I (..\User\ui2.8\UI28_pic_70x70.h)(0x682840DF)
I (..\User\ui2.8\UI28_matrix.h)(0x6857730C)
I (..\User\ui2.8\UI28_menu.h)(0x68579EAC)
I (..\User\ui2.8\UI28_rolling_album.h)(0x6854C854)
I (..\User\ui2.8\UI28_arc.h)(0x685772E6)
I (..\User\ui2.8\UI28_animation.h)(0x6829BB09)
I (..\User\ui2.8\UI28_progress_bar.h)(0x68567976)
I (..\User\pro\pro_cus_com.h)(0x685605BA)
I (..\User\pro\pro_hardware.h)(0x6829DA80)
I (..\User\pro\pro_workflow.h)(0x682D8D63)
I (..\User\pro\pro_error.h)(0x682C215A)
I (..\User\pro\pro_pagekey.h)(0x68297893)
I (..\User\page\page.h)(0x68555953)
I (..\User\page\src_inc\page_start.h)(0x67966D00)
I (..\User\page\src_inc\page_standby.h)(0x67966D02)
I (..\User\page\src_inc\page_run.h)(0x67966D08)
I (..\User\page\src_inc\page_parameter.h)(0x68579AE2)
I (..\User\page\src_inc\page_debug.h)(0x67966D0C)
I (..\User\page\src_inc\page_statistics.h)(0x682AF7F2)
I (..\User\page\src_inc\page_check.h)(0x67966D0E)
I (..\User\page\src_inc\page_developer.h)(0x67966D0C)
I (..\User\page\src_inc\page_qr_code.h)(0x681F13C4)
I (..\User\parame\parame.h)(0x68294931)
I (..\User\parame\parame_tongyong.h)(0x6857B6B2)
I (..\User\parame\flash_eeprom.h)(0x68560C9B)
F (..\User\bsp\src\bsp_fifo.c)(0x682C1617)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/bsp_fifo.o -MD)
I (..\User\bsp\bsp.h)(0x6826A21F)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (D:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdlib.h)(0x603507D0)
I (..\User\bsp\inc\bsp_timer.h)(0x680C5798)
I (..\User\bsp\inc\bsp_uart_fifo.h)(0x682426F2)
I (..\User\bsp\inc\bsp_boot.h)(0x6832B3D5)
I (..\User\bsp\inc\bsp_key.h)(0x68576AFA)
I (..\User\bsp\inc\bsp_crc.h)(0x682C3545)
I (..\User\bsp\inc\bsp_utility.h)(0x68107F3A)
I (..\User\bsp\inc\bsp_temp.h)(0x681322C8)
I (..\User\bsp\inc\bsp_ntc.h)(0x678DDDCC)
I (..\User\bsp\inc\bsp_signal.h)(0x682ACFBF)
I (..\User\bsp\inc\bsp_beep.h)(0x6827E666)
I (..\User\bsp\inc\bsp_fifo.h)(0x682C143B)
I (..\User\bsp\inc\bsp_tim_pwm.h)(0x68143C04)
I (..\User\ui2.8\UI28.h)(0x6856620B)
I (..\User\ui2.8\UI28_auxiliary_table.h)(0x6801A019)
I (..\User\ui2.8\bsp_ili9340x_lcd.h)(0x6828483F)
I (..\User\ui2.8\UI28_font_16.h)(0x681EB98A)
I (..\User\ui2.8\UI28_font_20.h)(0x67F69774)
I (..\User\ui2.8\UI28_font_32.h)(0x681AFF0D)
I (..\User\ui2.8\UI28_font_72.h)(0x681D7617)
I (..\User\ui2.8\UI28_font_28.h)(0x681DBC62)
I (..\User\ui2.8\UI28_font_36.h)(0x681EACC0)
I (..\User\ui2.8\UI28_font_24.h)(0x682E7B21)
I (..\User\ui2.8\UI28_font_18.h)(0x681EF7D2)
I (..\User\ui2.8\UI28_font_14.h)(0x681ED798)
I (..\User\ui2.8\UI28_pic_120x120.h)(0x6857B1FC)
I (..\User\ui2.8\UI28_pic_32x32.h)(0x681D853E)
I (..\User\ui2.8\UI28_pic_22x30.h)(0x681D8CDE)
I (..\User\ui2.8\UI28_pic_14x18.h)(0x681DB278)
I (..\User\ui2.8\UI28_pic_48x88.h)(0x681DA6D8)
I (..\User\ui2.8\UI28_pic_36x36.h)(0x681DB27C)
I (..\User\ui2.8\UI28_pic_45x45.h)(0x68204BEA)
I (..\User\ui2.8\UI28_pic_40x20.h)(0x68283E6F)
I (..\User\ui2.8\UI28_pic_70x70.h)(0x682840DF)
I (..\User\ui2.8\UI28_matrix.h)(0x6857730C)
I (..\User\ui2.8\UI28_menu.h)(0x68579EAC)
I (..\User\ui2.8\UI28_rolling_album.h)(0x6854C854)
I (..\User\ui2.8\UI28_arc.h)(0x685772E6)
I (..\User\ui2.8\UI28_animation.h)(0x6829BB09)
I (..\User\ui2.8\UI28_progress_bar.h)(0x68567976)
I (..\User\pro\pro_cus_com.h)(0x685605BA)
I (..\User\pro\pro_hardware.h)(0x6829DA80)
I (..\User\pro\pro_workflow.h)(0x682D8D63)
I (..\User\pro\pro_error.h)(0x682C215A)
I (..\User\pro\pro_pagekey.h)(0x68297893)
I (..\User\page\page.h)(0x68555953)
I (..\User\page\src_inc\page_start.h)(0x67966D00)
I (..\User\page\src_inc\page_standby.h)(0x67966D02)
I (..\User\page\src_inc\page_run.h)(0x67966D08)
I (..\User\page\src_inc\page_parameter.h)(0x68579AE2)
I (..\User\page\src_inc\page_debug.h)(0x67966D0C)
I (..\User\page\src_inc\page_statistics.h)(0x682AF7F2)
I (..\User\page\src_inc\page_check.h)(0x67966D0E)
I (..\User\page\src_inc\page_developer.h)(0x67966D0C)
I (..\User\page\src_inc\page_qr_code.h)(0x681F13C4)
I (..\User\parame\parame.h)(0x68294931)
I (..\User\parame\parame_tongyong.h)(0x6857B6B2)
I (..\User\parame\flash_eeprom.h)(0x68560C9B)
F (..\User\bsp\src\bsp_key.c)(0x6834126B)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/bsp_key.o -MD)
I (..\User\bsp\bsp.h)(0x6826A21F)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (D:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdlib.h)(0x603507D0)
I (..\User\bsp\inc\bsp_timer.h)(0x680C5798)
I (..\User\bsp\inc\bsp_uart_fifo.h)(0x682426F2)
I (..\User\bsp\inc\bsp_boot.h)(0x6832B3D5)
I (..\User\bsp\inc\bsp_key.h)(0x68576AFA)
I (..\User\bsp\inc\bsp_crc.h)(0x682C3545)
I (..\User\bsp\inc\bsp_utility.h)(0x68107F3A)
I (..\User\bsp\inc\bsp_temp.h)(0x681322C8)
I (..\User\bsp\inc\bsp_ntc.h)(0x678DDDCC)
I (..\User\bsp\inc\bsp_signal.h)(0x682ACFBF)
I (..\User\bsp\inc\bsp_beep.h)(0x6827E666)
I (..\User\bsp\inc\bsp_fifo.h)(0x682C143B)
I (..\User\bsp\inc\bsp_tim_pwm.h)(0x68143C04)
I (..\User\ui2.8\UI28.h)(0x6856620B)
I (..\User\ui2.8\UI28_auxiliary_table.h)(0x6801A019)
I (..\User\ui2.8\bsp_ili9340x_lcd.h)(0x6828483F)
I (..\User\ui2.8\UI28_font_16.h)(0x681EB98A)
I (..\User\ui2.8\UI28_font_20.h)(0x67F69774)
I (..\User\ui2.8\UI28_font_32.h)(0x681AFF0D)
I (..\User\ui2.8\UI28_font_72.h)(0x681D7617)
I (..\User\ui2.8\UI28_font_28.h)(0x681DBC62)
I (..\User\ui2.8\UI28_font_36.h)(0x681EACC0)
I (..\User\ui2.8\UI28_font_24.h)(0x682E7B21)
I (..\User\ui2.8\UI28_font_18.h)(0x681EF7D2)
I (..\User\ui2.8\UI28_font_14.h)(0x681ED798)
I (..\User\ui2.8\UI28_pic_120x120.h)(0x6857B1FC)
I (..\User\ui2.8\UI28_pic_32x32.h)(0x681D853E)
I (..\User\ui2.8\UI28_pic_22x30.h)(0x681D8CDE)
I (..\User\ui2.8\UI28_pic_14x18.h)(0x681DB278)
I (..\User\ui2.8\UI28_pic_48x88.h)(0x681DA6D8)
I (..\User\ui2.8\UI28_pic_36x36.h)(0x681DB27C)
I (..\User\ui2.8\UI28_pic_45x45.h)(0x68204BEA)
I (..\User\ui2.8\UI28_pic_40x20.h)(0x68283E6F)
I (..\User\ui2.8\UI28_pic_70x70.h)(0x682840DF)
I (..\User\ui2.8\UI28_matrix.h)(0x6857730C)
I (..\User\ui2.8\UI28_menu.h)(0x68579EAC)
I (..\User\ui2.8\UI28_rolling_album.h)(0x6854C854)
I (..\User\ui2.8\UI28_arc.h)(0x685772E6)
I (..\User\ui2.8\UI28_animation.h)(0x6829BB09)
I (..\User\ui2.8\UI28_progress_bar.h)(0x68567976)
I (..\User\pro\pro_cus_com.h)(0x685605BA)
I (..\User\pro\pro_hardware.h)(0x6829DA80)
I (..\User\pro\pro_workflow.h)(0x682D8D63)
I (..\User\pro\pro_error.h)(0x682C215A)
I (..\User\pro\pro_pagekey.h)(0x68297893)
I (..\User\page\page.h)(0x68555953)
I (..\User\page\src_inc\page_start.h)(0x67966D00)
I (..\User\page\src_inc\page_standby.h)(0x67966D02)
I (..\User\page\src_inc\page_run.h)(0x67966D08)
I (..\User\page\src_inc\page_parameter.h)(0x68579AE2)
I (..\User\page\src_inc\page_debug.h)(0x67966D0C)
I (..\User\page\src_inc\page_statistics.h)(0x682AF7F2)
I (..\User\page\src_inc\page_check.h)(0x67966D0E)
I (..\User\page\src_inc\page_developer.h)(0x67966D0C)
I (..\User\page\src_inc\page_qr_code.h)(0x681F13C4)
I (..\User\parame\parame.h)(0x68294931)
I (..\User\parame\parame_tongyong.h)(0x6857B6B2)
I (..\User\parame\flash_eeprom.h)(0x68560C9B)
F (..\User\bsp\src\bsp_crc.c)(0x68107F20)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/bsp_crc.o -MD)
I (..\User\bsp\inc\bsp_crc.h)(0x682C3545)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdlib.h)(0x603507D0)
I (D:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
F (..\User\bsp\src\bsp_utility.c)(0x68107FC8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/bsp_utility.o -MD)
I (..\User\bsp\bsp.h)(0x6826A21F)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (D:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdlib.h)(0x603507D0)
I (..\User\bsp\inc\bsp_timer.h)(0x680C5798)
I (..\User\bsp\inc\bsp_uart_fifo.h)(0x682426F2)
I (..\User\bsp\inc\bsp_boot.h)(0x6832B3D5)
I (..\User\bsp\inc\bsp_key.h)(0x68576AFA)
I (..\User\bsp\inc\bsp_crc.h)(0x682C3545)
I (..\User\bsp\inc\bsp_utility.h)(0x68107F3A)
I (..\User\bsp\inc\bsp_temp.h)(0x681322C8)
I (..\User\bsp\inc\bsp_ntc.h)(0x678DDDCC)
I (..\User\bsp\inc\bsp_signal.h)(0x682ACFBF)
I (..\User\bsp\inc\bsp_beep.h)(0x6827E666)
I (..\User\bsp\inc\bsp_fifo.h)(0x682C143B)
I (..\User\bsp\inc\bsp_tim_pwm.h)(0x68143C04)
I (..\User\ui2.8\UI28.h)(0x6856620B)
I (..\User\ui2.8\UI28_auxiliary_table.h)(0x6801A019)
I (..\User\ui2.8\bsp_ili9340x_lcd.h)(0x6828483F)
I (..\User\ui2.8\UI28_font_16.h)(0x681EB98A)
I (..\User\ui2.8\UI28_font_20.h)(0x67F69774)
I (..\User\ui2.8\UI28_font_32.h)(0x681AFF0D)
I (..\User\ui2.8\UI28_font_72.h)(0x681D7617)
I (..\User\ui2.8\UI28_font_28.h)(0x681DBC62)
I (..\User\ui2.8\UI28_font_36.h)(0x681EACC0)
I (..\User\ui2.8\UI28_font_24.h)(0x682E7B21)
I (..\User\ui2.8\UI28_font_18.h)(0x681EF7D2)
I (..\User\ui2.8\UI28_font_14.h)(0x681ED798)
I (..\User\ui2.8\UI28_pic_120x120.h)(0x6857B1FC)
I (..\User\ui2.8\UI28_pic_32x32.h)(0x681D853E)
I (..\User\ui2.8\UI28_pic_22x30.h)(0x681D8CDE)
I (..\User\ui2.8\UI28_pic_14x18.h)(0x681DB278)
I (..\User\ui2.8\UI28_pic_48x88.h)(0x681DA6D8)
I (..\User\ui2.8\UI28_pic_36x36.h)(0x681DB27C)
I (..\User\ui2.8\UI28_pic_45x45.h)(0x68204BEA)
I (..\User\ui2.8\UI28_pic_40x20.h)(0x68283E6F)
I (..\User\ui2.8\UI28_pic_70x70.h)(0x682840DF)
I (..\User\ui2.8\UI28_matrix.h)(0x6857730C)
I (..\User\ui2.8\UI28_menu.h)(0x68579EAC)
I (..\User\ui2.8\UI28_rolling_album.h)(0x6854C854)
I (..\User\ui2.8\UI28_arc.h)(0x685772E6)
I (..\User\ui2.8\UI28_animation.h)(0x6829BB09)
I (..\User\ui2.8\UI28_progress_bar.h)(0x68567976)
I (..\User\pro\pro_cus_com.h)(0x685605BA)
I (..\User\pro\pro_hardware.h)(0x6829DA80)
I (..\User\pro\pro_workflow.h)(0x682D8D63)
I (..\User\pro\pro_error.h)(0x682C215A)
I (..\User\pro\pro_pagekey.h)(0x68297893)
I (..\User\page\page.h)(0x68555953)
I (..\User\page\src_inc\page_start.h)(0x67966D00)
I (..\User\page\src_inc\page_standby.h)(0x67966D02)
I (..\User\page\src_inc\page_run.h)(0x67966D08)
I (..\User\page\src_inc\page_parameter.h)(0x68579AE2)
I (..\User\page\src_inc\page_debug.h)(0x67966D0C)
I (..\User\page\src_inc\page_statistics.h)(0x682AF7F2)
I (..\User\page\src_inc\page_check.h)(0x67966D0E)
I (..\User\page\src_inc\page_developer.h)(0x67966D0C)
I (..\User\page\src_inc\page_qr_code.h)(0x681F13C4)
I (..\User\parame\parame.h)(0x68294931)
I (..\User\parame\parame_tongyong.h)(0x6857B6B2)
I (..\User\parame\flash_eeprom.h)(0x68560C9B)
F (..\User\bsp\src\bsp_temp.c)(0x682C73E3)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/bsp_temp.o -MD)
I (..\User\bsp\bsp.h)(0x6826A21F)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (D:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdlib.h)(0x603507D0)
I (..\User\bsp\inc\bsp_timer.h)(0x680C5798)
I (..\User\bsp\inc\bsp_uart_fifo.h)(0x682426F2)
I (..\User\bsp\inc\bsp_boot.h)(0x6832B3D5)
I (..\User\bsp\inc\bsp_key.h)(0x68576AFA)
I (..\User\bsp\inc\bsp_crc.h)(0x682C3545)
I (..\User\bsp\inc\bsp_utility.h)(0x68107F3A)
I (..\User\bsp\inc\bsp_temp.h)(0x681322C8)
I (..\User\bsp\inc\bsp_ntc.h)(0x678DDDCC)
I (..\User\bsp\inc\bsp_signal.h)(0x682ACFBF)
I (..\User\bsp\inc\bsp_beep.h)(0x6827E666)
I (..\User\bsp\inc\bsp_fifo.h)(0x682C143B)
I (..\User\bsp\inc\bsp_tim_pwm.h)(0x68143C04)
I (..\User\ui2.8\UI28.h)(0x6856620B)
I (..\User\ui2.8\UI28_auxiliary_table.h)(0x6801A019)
I (..\User\ui2.8\bsp_ili9340x_lcd.h)(0x6828483F)
I (..\User\ui2.8\UI28_font_16.h)(0x681EB98A)
I (..\User\ui2.8\UI28_font_20.h)(0x67F69774)
I (..\User\ui2.8\UI28_font_32.h)(0x681AFF0D)
I (..\User\ui2.8\UI28_font_72.h)(0x681D7617)
I (..\User\ui2.8\UI28_font_28.h)(0x681DBC62)
I (..\User\ui2.8\UI28_font_36.h)(0x681EACC0)
I (..\User\ui2.8\UI28_font_24.h)(0x682E7B21)
I (..\User\ui2.8\UI28_font_18.h)(0x681EF7D2)
I (..\User\ui2.8\UI28_font_14.h)(0x681ED798)
I (..\User\ui2.8\UI28_pic_120x120.h)(0x6857B1FC)
I (..\User\ui2.8\UI28_pic_32x32.h)(0x681D853E)
I (..\User\ui2.8\UI28_pic_22x30.h)(0x681D8CDE)
I (..\User\ui2.8\UI28_pic_14x18.h)(0x681DB278)
I (..\User\ui2.8\UI28_pic_48x88.h)(0x681DA6D8)
I (..\User\ui2.8\UI28_pic_36x36.h)(0x681DB27C)
I (..\User\ui2.8\UI28_pic_45x45.h)(0x68204BEA)
I (..\User\ui2.8\UI28_pic_40x20.h)(0x68283E6F)
I (..\User\ui2.8\UI28_pic_70x70.h)(0x682840DF)
I (..\User\ui2.8\UI28_matrix.h)(0x6857730C)
I (..\User\ui2.8\UI28_menu.h)(0x68579EAC)
I (..\User\ui2.8\UI28_rolling_album.h)(0x6854C854)
I (..\User\ui2.8\UI28_arc.h)(0x685772E6)
I (..\User\ui2.8\UI28_animation.h)(0x6829BB09)
I (..\User\ui2.8\UI28_progress_bar.h)(0x68567976)
I (..\User\pro\pro_cus_com.h)(0x685605BA)
I (..\User\pro\pro_hardware.h)(0x6829DA80)
I (..\User\pro\pro_workflow.h)(0x682D8D63)
I (..\User\pro\pro_error.h)(0x682C215A)
I (..\User\pro\pro_pagekey.h)(0x68297893)
I (..\User\page\page.h)(0x68555953)
I (..\User\page\src_inc\page_start.h)(0x67966D00)
I (..\User\page\src_inc\page_standby.h)(0x67966D02)
I (..\User\page\src_inc\page_run.h)(0x67966D08)
I (..\User\page\src_inc\page_parameter.h)(0x68579AE2)
I (..\User\page\src_inc\page_debug.h)(0x67966D0C)
I (..\User\page\src_inc\page_statistics.h)(0x682AF7F2)
I (..\User\page\src_inc\page_check.h)(0x67966D0E)
I (..\User\page\src_inc\page_developer.h)(0x67966D0C)
I (..\User\page\src_inc\page_qr_code.h)(0x681F13C4)
I (..\User\parame\parame.h)(0x68294931)
I (..\User\parame\parame_tongyong.h)(0x6857B6B2)
I (..\User\parame\flash_eeprom.h)(0x68560C9B)
F (..\User\bsp\src\bsp_ntc.c)(0x6810EE75)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/bsp_ntc.o -MD)
I (..\User\bsp\bsp.h)(0x6826A21F)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (D:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdlib.h)(0x603507D0)
I (..\User\bsp\inc\bsp_timer.h)(0x680C5798)
I (..\User\bsp\inc\bsp_uart_fifo.h)(0x682426F2)
I (..\User\bsp\inc\bsp_boot.h)(0x6832B3D5)
I (..\User\bsp\inc\bsp_key.h)(0x68576AFA)
I (..\User\bsp\inc\bsp_crc.h)(0x682C3545)
I (..\User\bsp\inc\bsp_utility.h)(0x68107F3A)
I (..\User\bsp\inc\bsp_temp.h)(0x681322C8)
I (..\User\bsp\inc\bsp_ntc.h)(0x678DDDCC)
I (..\User\bsp\inc\bsp_signal.h)(0x682ACFBF)
I (..\User\bsp\inc\bsp_beep.h)(0x6827E666)
I (..\User\bsp\inc\bsp_fifo.h)(0x682C143B)
I (..\User\bsp\inc\bsp_tim_pwm.h)(0x68143C04)
I (..\User\ui2.8\UI28.h)(0x6856620B)
I (..\User\ui2.8\UI28_auxiliary_table.h)(0x6801A019)
I (..\User\ui2.8\bsp_ili9340x_lcd.h)(0x6828483F)
I (..\User\ui2.8\UI28_font_16.h)(0x681EB98A)
I (..\User\ui2.8\UI28_font_20.h)(0x67F69774)
I (..\User\ui2.8\UI28_font_32.h)(0x681AFF0D)
I (..\User\ui2.8\UI28_font_72.h)(0x681D7617)
I (..\User\ui2.8\UI28_font_28.h)(0x681DBC62)
I (..\User\ui2.8\UI28_font_36.h)(0x681EACC0)
I (..\User\ui2.8\UI28_font_24.h)(0x682E7B21)
I (..\User\ui2.8\UI28_font_18.h)(0x681EF7D2)
I (..\User\ui2.8\UI28_font_14.h)(0x681ED798)
I (..\User\ui2.8\UI28_pic_120x120.h)(0x6857B1FC)
I (..\User\ui2.8\UI28_pic_32x32.h)(0x681D853E)
I (..\User\ui2.8\UI28_pic_22x30.h)(0x681D8CDE)
I (..\User\ui2.8\UI28_pic_14x18.h)(0x681DB278)
I (..\User\ui2.8\UI28_pic_48x88.h)(0x681DA6D8)
I (..\User\ui2.8\UI28_pic_36x36.h)(0x681DB27C)
I (..\User\ui2.8\UI28_pic_45x45.h)(0x68204BEA)
I (..\User\ui2.8\UI28_pic_40x20.h)(0x68283E6F)
I (..\User\ui2.8\UI28_pic_70x70.h)(0x682840DF)
I (..\User\ui2.8\UI28_matrix.h)(0x6857730C)
I (..\User\ui2.8\UI28_menu.h)(0x68579EAC)
I (..\User\ui2.8\UI28_rolling_album.h)(0x6854C854)
I (..\User\ui2.8\UI28_arc.h)(0x685772E6)
I (..\User\ui2.8\UI28_animation.h)(0x6829BB09)
I (..\User\ui2.8\UI28_progress_bar.h)(0x68567976)
I (..\User\pro\pro_cus_com.h)(0x685605BA)
I (..\User\pro\pro_hardware.h)(0x6829DA80)
I (..\User\pro\pro_workflow.h)(0x682D8D63)
I (..\User\pro\pro_error.h)(0x682C215A)
I (..\User\pro\pro_pagekey.h)(0x68297893)
I (..\User\page\page.h)(0x68555953)
I (..\User\page\src_inc\page_start.h)(0x67966D00)
I (..\User\page\src_inc\page_standby.h)(0x67966D02)
I (..\User\page\src_inc\page_run.h)(0x67966D08)
I (..\User\page\src_inc\page_parameter.h)(0x68579AE2)
I (..\User\page\src_inc\page_debug.h)(0x67966D0C)
I (..\User\page\src_inc\page_statistics.h)(0x682AF7F2)
I (..\User\page\src_inc\page_check.h)(0x67966D0E)
I (..\User\page\src_inc\page_developer.h)(0x67966D0C)
I (..\User\page\src_inc\page_qr_code.h)(0x681F13C4)
I (..\User\parame\parame.h)(0x68294931)
I (..\User\parame\parame_tongyong.h)(0x6857B6B2)
I (..\User\parame\flash_eeprom.h)(0x68560C9B)
F (..\User\bsp\src\bsp_signal.c)(0x681428F7)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/bsp_signal.o -MD)
I (..\User\bsp\bsp.h)(0x6826A21F)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (D:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdlib.h)(0x603507D0)
I (..\User\bsp\inc\bsp_timer.h)(0x680C5798)
I (..\User\bsp\inc\bsp_uart_fifo.h)(0x682426F2)
I (..\User\bsp\inc\bsp_boot.h)(0x6832B3D5)
I (..\User\bsp\inc\bsp_key.h)(0x68576AFA)
I (..\User\bsp\inc\bsp_crc.h)(0x682C3545)
I (..\User\bsp\inc\bsp_utility.h)(0x68107F3A)
I (..\User\bsp\inc\bsp_temp.h)(0x681322C8)
I (..\User\bsp\inc\bsp_ntc.h)(0x678DDDCC)
I (..\User\bsp\inc\bsp_signal.h)(0x682ACFBF)
I (..\User\bsp\inc\bsp_beep.h)(0x6827E666)
I (..\User\bsp\inc\bsp_fifo.h)(0x682C143B)
I (..\User\bsp\inc\bsp_tim_pwm.h)(0x68143C04)
I (..\User\ui2.8\UI28.h)(0x6856620B)
I (..\User\ui2.8\UI28_auxiliary_table.h)(0x6801A019)
I (..\User\ui2.8\bsp_ili9340x_lcd.h)(0x6828483F)
I (..\User\ui2.8\UI28_font_16.h)(0x681EB98A)
I (..\User\ui2.8\UI28_font_20.h)(0x67F69774)
I (..\User\ui2.8\UI28_font_32.h)(0x681AFF0D)
I (..\User\ui2.8\UI28_font_72.h)(0x681D7617)
I (..\User\ui2.8\UI28_font_28.h)(0x681DBC62)
I (..\User\ui2.8\UI28_font_36.h)(0x681EACC0)
I (..\User\ui2.8\UI28_font_24.h)(0x682E7B21)
I (..\User\ui2.8\UI28_font_18.h)(0x681EF7D2)
I (..\User\ui2.8\UI28_font_14.h)(0x681ED798)
I (..\User\ui2.8\UI28_pic_120x120.h)(0x6857B1FC)
I (..\User\ui2.8\UI28_pic_32x32.h)(0x681D853E)
I (..\User\ui2.8\UI28_pic_22x30.h)(0x681D8CDE)
I (..\User\ui2.8\UI28_pic_14x18.h)(0x681DB278)
I (..\User\ui2.8\UI28_pic_48x88.h)(0x681DA6D8)
I (..\User\ui2.8\UI28_pic_36x36.h)(0x681DB27C)
I (..\User\ui2.8\UI28_pic_45x45.h)(0x68204BEA)
I (..\User\ui2.8\UI28_pic_40x20.h)(0x68283E6F)
I (..\User\ui2.8\UI28_pic_70x70.h)(0x682840DF)
I (..\User\ui2.8\UI28_matrix.h)(0x6857730C)
I (..\User\ui2.8\UI28_menu.h)(0x68579EAC)
I (..\User\ui2.8\UI28_rolling_album.h)(0x6854C854)
I (..\User\ui2.8\UI28_arc.h)(0x685772E6)
I (..\User\ui2.8\UI28_animation.h)(0x6829BB09)
I (..\User\ui2.8\UI28_progress_bar.h)(0x68567976)
I (..\User\pro\pro_cus_com.h)(0x685605BA)
I (..\User\pro\pro_hardware.h)(0x6829DA80)
I (..\User\pro\pro_workflow.h)(0x682D8D63)
I (..\User\pro\pro_error.h)(0x682C215A)
I (..\User\pro\pro_pagekey.h)(0x68297893)
I (..\User\page\page.h)(0x68555953)
I (..\User\page\src_inc\page_start.h)(0x67966D00)
I (..\User\page\src_inc\page_standby.h)(0x67966D02)
I (..\User\page\src_inc\page_run.h)(0x67966D08)
I (..\User\page\src_inc\page_parameter.h)(0x68579AE2)
I (..\User\page\src_inc\page_debug.h)(0x67966D0C)
I (..\User\page\src_inc\page_statistics.h)(0x682AF7F2)
I (..\User\page\src_inc\page_check.h)(0x67966D0E)
I (..\User\page\src_inc\page_developer.h)(0x67966D0C)
I (..\User\page\src_inc\page_qr_code.h)(0x681F13C4)
I (..\User\parame\parame.h)(0x68294931)
I (..\User\parame\parame_tongyong.h)(0x6857B6B2)
I (..\User\parame\flash_eeprom.h)(0x68560C9B)
F (..\User\bsp\src\bsp_beep.c)(0x6815D4AD)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/bsp_beep.o -MD)
I (..\User\bsp\bsp.h)(0x6826A21F)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (D:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdlib.h)(0x603507D0)
I (..\User\bsp\inc\bsp_timer.h)(0x680C5798)
I (..\User\bsp\inc\bsp_uart_fifo.h)(0x682426F2)
I (..\User\bsp\inc\bsp_boot.h)(0x6832B3D5)
I (..\User\bsp\inc\bsp_key.h)(0x68576AFA)
I (..\User\bsp\inc\bsp_crc.h)(0x682C3545)
I (..\User\bsp\inc\bsp_utility.h)(0x68107F3A)
I (..\User\bsp\inc\bsp_temp.h)(0x681322C8)
I (..\User\bsp\inc\bsp_ntc.h)(0x678DDDCC)
I (..\User\bsp\inc\bsp_signal.h)(0x682ACFBF)
I (..\User\bsp\inc\bsp_beep.h)(0x6827E666)
I (..\User\bsp\inc\bsp_fifo.h)(0x682C143B)
I (..\User\bsp\inc\bsp_tim_pwm.h)(0x68143C04)
I (..\User\ui2.8\UI28.h)(0x6856620B)
I (..\User\ui2.8\UI28_auxiliary_table.h)(0x6801A019)
I (..\User\ui2.8\bsp_ili9340x_lcd.h)(0x6828483F)
I (..\User\ui2.8\UI28_font_16.h)(0x681EB98A)
I (..\User\ui2.8\UI28_font_20.h)(0x67F69774)
I (..\User\ui2.8\UI28_font_32.h)(0x681AFF0D)
I (..\User\ui2.8\UI28_font_72.h)(0x681D7617)
I (..\User\ui2.8\UI28_font_28.h)(0x681DBC62)
I (..\User\ui2.8\UI28_font_36.h)(0x681EACC0)
I (..\User\ui2.8\UI28_font_24.h)(0x682E7B21)
I (..\User\ui2.8\UI28_font_18.h)(0x681EF7D2)
I (..\User\ui2.8\UI28_font_14.h)(0x681ED798)
I (..\User\ui2.8\UI28_pic_120x120.h)(0x6857B1FC)
I (..\User\ui2.8\UI28_pic_32x32.h)(0x681D853E)
I (..\User\ui2.8\UI28_pic_22x30.h)(0x681D8CDE)
I (..\User\ui2.8\UI28_pic_14x18.h)(0x681DB278)
I (..\User\ui2.8\UI28_pic_48x88.h)(0x681DA6D8)
I (..\User\ui2.8\UI28_pic_36x36.h)(0x681DB27C)
I (..\User\ui2.8\UI28_pic_45x45.h)(0x68204BEA)
I (..\User\ui2.8\UI28_pic_40x20.h)(0x68283E6F)
I (..\User\ui2.8\UI28_pic_70x70.h)(0x682840DF)
I (..\User\ui2.8\UI28_matrix.h)(0x6857730C)
I (..\User\ui2.8\UI28_menu.h)(0x68579EAC)
I (..\User\ui2.8\UI28_rolling_album.h)(0x6854C854)
I (..\User\ui2.8\UI28_arc.h)(0x685772E6)
I (..\User\ui2.8\UI28_animation.h)(0x6829BB09)
I (..\User\ui2.8\UI28_progress_bar.h)(0x68567976)
I (..\User\pro\pro_cus_com.h)(0x685605BA)
I (..\User\pro\pro_hardware.h)(0x6829DA80)
I (..\User\pro\pro_workflow.h)(0x682D8D63)
I (..\User\pro\pro_error.h)(0x682C215A)
I (..\User\pro\pro_pagekey.h)(0x68297893)
I (..\User\page\page.h)(0x68555953)
I (..\User\page\src_inc\page_start.h)(0x67966D00)
I (..\User\page\src_inc\page_standby.h)(0x67966D02)
I (..\User\page\src_inc\page_run.h)(0x67966D08)
I (..\User\page\src_inc\page_parameter.h)(0x68579AE2)
I (..\User\page\src_inc\page_debug.h)(0x67966D0C)
I (..\User\page\src_inc\page_statistics.h)(0x682AF7F2)
I (..\User\page\src_inc\page_check.h)(0x67966D0E)
I (..\User\page\src_inc\page_developer.h)(0x67966D0C)
I (..\User\page\src_inc\page_qr_code.h)(0x681F13C4)
I (..\User\parame\parame.h)(0x68294931)
I (..\User\parame\parame_tongyong.h)(0x6857B6B2)
I (..\User\parame\flash_eeprom.h)(0x68560C9B)
F (..\User\bsp\src\bsp_tim_pwm.c)(0x6815D599)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/bsp_tim_pwm.o -MD)
I (..\User\bsp\bsp.h)(0x6826A21F)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (D:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdlib.h)(0x603507D0)
I (..\User\bsp\inc\bsp_timer.h)(0x680C5798)
I (..\User\bsp\inc\bsp_uart_fifo.h)(0x682426F2)
I (..\User\bsp\inc\bsp_boot.h)(0x6832B3D5)
I (..\User\bsp\inc\bsp_key.h)(0x68576AFA)
I (..\User\bsp\inc\bsp_crc.h)(0x682C3545)
I (..\User\bsp\inc\bsp_utility.h)(0x68107F3A)
I (..\User\bsp\inc\bsp_temp.h)(0x681322C8)
I (..\User\bsp\inc\bsp_ntc.h)(0x678DDDCC)
I (..\User\bsp\inc\bsp_signal.h)(0x682ACFBF)
I (..\User\bsp\inc\bsp_beep.h)(0x6827E666)
I (..\User\bsp\inc\bsp_fifo.h)(0x682C143B)
I (..\User\bsp\inc\bsp_tim_pwm.h)(0x68143C04)
I (..\User\ui2.8\UI28.h)(0x6856620B)
I (..\User\ui2.8\UI28_auxiliary_table.h)(0x6801A019)
I (..\User\ui2.8\bsp_ili9340x_lcd.h)(0x6828483F)
I (..\User\ui2.8\UI28_font_16.h)(0x681EB98A)
I (..\User\ui2.8\UI28_font_20.h)(0x67F69774)
I (..\User\ui2.8\UI28_font_32.h)(0x681AFF0D)
I (..\User\ui2.8\UI28_font_72.h)(0x681D7617)
I (..\User\ui2.8\UI28_font_28.h)(0x681DBC62)
I (..\User\ui2.8\UI28_font_36.h)(0x681EACC0)
I (..\User\ui2.8\UI28_font_24.h)(0x682E7B21)
I (..\User\ui2.8\UI28_font_18.h)(0x681EF7D2)
I (..\User\ui2.8\UI28_font_14.h)(0x681ED798)
I (..\User\ui2.8\UI28_pic_120x120.h)(0x6857B1FC)
I (..\User\ui2.8\UI28_pic_32x32.h)(0x681D853E)
I (..\User\ui2.8\UI28_pic_22x30.h)(0x681D8CDE)
I (..\User\ui2.8\UI28_pic_14x18.h)(0x681DB278)
I (..\User\ui2.8\UI28_pic_48x88.h)(0x681DA6D8)
I (..\User\ui2.8\UI28_pic_36x36.h)(0x681DB27C)
I (..\User\ui2.8\UI28_pic_45x45.h)(0x68204BEA)
I (..\User\ui2.8\UI28_pic_40x20.h)(0x68283E6F)
I (..\User\ui2.8\UI28_pic_70x70.h)(0x682840DF)
I (..\User\ui2.8\UI28_matrix.h)(0x6857730C)
I (..\User\ui2.8\UI28_menu.h)(0x68579EAC)
I (..\User\ui2.8\UI28_rolling_album.h)(0x6854C854)
I (..\User\ui2.8\UI28_arc.h)(0x685772E6)
I (..\User\ui2.8\UI28_animation.h)(0x6829BB09)
I (..\User\ui2.8\UI28_progress_bar.h)(0x68567976)
I (..\User\pro\pro_cus_com.h)(0x685605BA)
I (..\User\pro\pro_hardware.h)(0x6829DA80)
I (..\User\pro\pro_workflow.h)(0x682D8D63)
I (..\User\pro\pro_error.h)(0x682C215A)
I (..\User\pro\pro_pagekey.h)(0x68297893)
I (..\User\page\page.h)(0x68555953)
I (..\User\page\src_inc\page_start.h)(0x67966D00)
I (..\User\page\src_inc\page_standby.h)(0x67966D02)
I (..\User\page\src_inc\page_run.h)(0x67966D08)
I (..\User\page\src_inc\page_parameter.h)(0x68579AE2)
I (..\User\page\src_inc\page_debug.h)(0x67966D0C)
I (..\User\page\src_inc\page_statistics.h)(0x682AF7F2)
I (..\User\page\src_inc\page_check.h)(0x67966D0E)
I (..\User\page\src_inc\page_developer.h)(0x67966D0C)
I (..\User\page\src_inc\page_qr_code.h)(0x681F13C4)
I (..\User\parame\parame.h)(0x68294931)
I (..\User\parame\parame_tongyong.h)(0x6857B6B2)
I (..\User\parame\flash_eeprom.h)(0x68560C9B)
F (..\User\bsp\src\bsp_boot.c)(0x68556C53)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/bsp_boot.o -MD)
I (..\User\bsp\inc\bsp_boot.h)(0x6832B3D5)
I (..\User\bsp\bsp.h)(0x6826A21F)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (D:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdlib.h)(0x603507D0)
I (..\User\bsp\inc\bsp_timer.h)(0x680C5798)
I (..\User\bsp\inc\bsp_uart_fifo.h)(0x682426F2)
I (..\User\bsp\inc\bsp_key.h)(0x68576AFA)
I (..\User\bsp\inc\bsp_crc.h)(0x682C3545)
I (..\User\bsp\inc\bsp_utility.h)(0x68107F3A)
I (..\User\bsp\inc\bsp_temp.h)(0x681322C8)
I (..\User\bsp\inc\bsp_ntc.h)(0x678DDDCC)
I (..\User\bsp\inc\bsp_signal.h)(0x682ACFBF)
I (..\User\bsp\inc\bsp_beep.h)(0x6827E666)
I (..\User\bsp\inc\bsp_fifo.h)(0x682C143B)
I (..\User\bsp\inc\bsp_tim_pwm.h)(0x68143C04)
I (..\User\ui2.8\UI28.h)(0x6856620B)
I (..\User\ui2.8\UI28_auxiliary_table.h)(0x6801A019)
I (..\User\ui2.8\bsp_ili9340x_lcd.h)(0x6828483F)
I (..\User\ui2.8\UI28_font_16.h)(0x681EB98A)
I (..\User\ui2.8\UI28_font_20.h)(0x67F69774)
I (..\User\ui2.8\UI28_font_32.h)(0x681AFF0D)
I (..\User\ui2.8\UI28_font_72.h)(0x681D7617)
I (..\User\ui2.8\UI28_font_28.h)(0x681DBC62)
I (..\User\ui2.8\UI28_font_36.h)(0x681EACC0)
I (..\User\ui2.8\UI28_font_24.h)(0x682E7B21)
I (..\User\ui2.8\UI28_font_18.h)(0x681EF7D2)
I (..\User\ui2.8\UI28_font_14.h)(0x681ED798)
I (..\User\ui2.8\UI28_pic_120x120.h)(0x6857B1FC)
I (..\User\ui2.8\UI28_pic_32x32.h)(0x681D853E)
I (..\User\ui2.8\UI28_pic_22x30.h)(0x681D8CDE)
I (..\User\ui2.8\UI28_pic_14x18.h)(0x681DB278)
I (..\User\ui2.8\UI28_pic_48x88.h)(0x681DA6D8)
I (..\User\ui2.8\UI28_pic_36x36.h)(0x681DB27C)
I (..\User\ui2.8\UI28_pic_45x45.h)(0x68204BEA)
I (..\User\ui2.8\UI28_pic_40x20.h)(0x68283E6F)
I (..\User\ui2.8\UI28_pic_70x70.h)(0x682840DF)
I (..\User\ui2.8\UI28_matrix.h)(0x6857730C)
I (..\User\ui2.8\UI28_menu.h)(0x68579EAC)
I (..\User\ui2.8\UI28_rolling_album.h)(0x6854C854)
I (..\User\ui2.8\UI28_arc.h)(0x685772E6)
I (..\User\ui2.8\UI28_animation.h)(0x6829BB09)
I (..\User\ui2.8\UI28_progress_bar.h)(0x68567976)
I (..\User\pro\pro_cus_com.h)(0x685605BA)
I (..\User\pro\pro_hardware.h)(0x6829DA80)
I (..\User\pro\pro_workflow.h)(0x682D8D63)
I (..\User\pro\pro_error.h)(0x682C215A)
I (..\User\pro\pro_pagekey.h)(0x68297893)
I (..\User\page\page.h)(0x68555953)
I (..\User\page\src_inc\page_start.h)(0x67966D00)
I (..\User\page\src_inc\page_standby.h)(0x67966D02)
I (..\User\page\src_inc\page_run.h)(0x67966D08)
I (..\User\page\src_inc\page_parameter.h)(0x68579AE2)
I (..\User\page\src_inc\page_debug.h)(0x67966D0C)
I (..\User\page\src_inc\page_statistics.h)(0x682AF7F2)
I (..\User\page\src_inc\page_check.h)(0x67966D0E)
I (..\User\page\src_inc\page_developer.h)(0x67966D0C)
I (..\User\page\src_inc\page_qr_code.h)(0x681F13C4)
I (..\User\parame\parame.h)(0x68294931)
I (..\User\parame\parame_tongyong.h)(0x6857B6B2)
I (..\User\parame\flash_eeprom.h)(0x68560C9B)
F (..\User\pro\pro_cus_com.c)(0x68561082)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/pro_cus_com.o -MD)
I (..\User\bsp\bsp.h)(0x6826A21F)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (D:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdlib.h)(0x603507D0)
I (..\User\bsp\inc\bsp_timer.h)(0x680C5798)
I (..\User\bsp\inc\bsp_uart_fifo.h)(0x682426F2)
I (..\User\bsp\inc\bsp_boot.h)(0x6832B3D5)
I (..\User\bsp\inc\bsp_key.h)(0x68576AFA)
I (..\User\bsp\inc\bsp_crc.h)(0x682C3545)
I (..\User\bsp\inc\bsp_utility.h)(0x68107F3A)
I (..\User\bsp\inc\bsp_temp.h)(0x681322C8)
I (..\User\bsp\inc\bsp_ntc.h)(0x678DDDCC)
I (..\User\bsp\inc\bsp_signal.h)(0x682ACFBF)
I (..\User\bsp\inc\bsp_beep.h)(0x6827E666)
I (..\User\bsp\inc\bsp_fifo.h)(0x682C143B)
I (..\User\bsp\inc\bsp_tim_pwm.h)(0x68143C04)
I (..\User\ui2.8\UI28.h)(0x6856620B)
I (..\User\ui2.8\UI28_auxiliary_table.h)(0x6801A019)
I (..\User\ui2.8\bsp_ili9340x_lcd.h)(0x6828483F)
I (..\User\ui2.8\UI28_font_16.h)(0x681EB98A)
I (..\User\ui2.8\UI28_font_20.h)(0x67F69774)
I (..\User\ui2.8\UI28_font_32.h)(0x681AFF0D)
I (..\User\ui2.8\UI28_font_72.h)(0x681D7617)
I (..\User\ui2.8\UI28_font_28.h)(0x681DBC62)
I (..\User\ui2.8\UI28_font_36.h)(0x681EACC0)
I (..\User\ui2.8\UI28_font_24.h)(0x682E7B21)
I (..\User\ui2.8\UI28_font_18.h)(0x681EF7D2)
I (..\User\ui2.8\UI28_font_14.h)(0x681ED798)
I (..\User\ui2.8\UI28_pic_120x120.h)(0x6857B1FC)
I (..\User\ui2.8\UI28_pic_32x32.h)(0x681D853E)
I (..\User\ui2.8\UI28_pic_22x30.h)(0x681D8CDE)
I (..\User\ui2.8\UI28_pic_14x18.h)(0x681DB278)
I (..\User\ui2.8\UI28_pic_48x88.h)(0x681DA6D8)
I (..\User\ui2.8\UI28_pic_36x36.h)(0x681DB27C)
I (..\User\ui2.8\UI28_pic_45x45.h)(0x68204BEA)
I (..\User\ui2.8\UI28_pic_40x20.h)(0x68283E6F)
I (..\User\ui2.8\UI28_pic_70x70.h)(0x682840DF)
I (..\User\ui2.8\UI28_matrix.h)(0x6857730C)
I (..\User\ui2.8\UI28_menu.h)(0x68579EAC)
I (..\User\ui2.8\UI28_rolling_album.h)(0x6854C854)
I (..\User\ui2.8\UI28_arc.h)(0x685772E6)
I (..\User\ui2.8\UI28_animation.h)(0x6829BB09)
I (..\User\ui2.8\UI28_progress_bar.h)(0x68567976)
I (..\User\pro\pro_cus_com.h)(0x685605BA)
I (..\User\pro\pro_hardware.h)(0x6829DA80)
I (..\User\pro\pro_workflow.h)(0x682D8D63)
I (..\User\pro\pro_error.h)(0x682C215A)
I (..\User\pro\pro_pagekey.h)(0x68297893)
I (..\User\page\page.h)(0x68555953)
I (..\User\page\src_inc\page_start.h)(0x67966D00)
I (..\User\page\src_inc\page_standby.h)(0x67966D02)
I (..\User\page\src_inc\page_run.h)(0x67966D08)
I (..\User\page\src_inc\page_parameter.h)(0x68579AE2)
I (..\User\page\src_inc\page_debug.h)(0x67966D0C)
I (..\User\page\src_inc\page_statistics.h)(0x682AF7F2)
I (..\User\page\src_inc\page_check.h)(0x67966D0E)
I (..\User\page\src_inc\page_developer.h)(0x67966D0C)
I (..\User\page\src_inc\page_qr_code.h)(0x681F13C4)
I (..\User\parame\parame.h)(0x68294931)
I (..\User\parame\parame_tongyong.h)(0x6857B6B2)
I (..\User\parame\flash_eeprom.h)(0x68560C9B)
F (..\User\pro\pro_cus_com.h)(0x685605BA)()
F (..\User\pro\pro_hardware.c)(0x68539FC7)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/pro_hardware.o -MD)
I (..\User\bsp\bsp.h)(0x6826A21F)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (D:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdlib.h)(0x603507D0)
I (..\User\bsp\inc\bsp_timer.h)(0x680C5798)
I (..\User\bsp\inc\bsp_uart_fifo.h)(0x682426F2)
I (..\User\bsp\inc\bsp_boot.h)(0x6832B3D5)
I (..\User\bsp\inc\bsp_key.h)(0x68576AFA)
I (..\User\bsp\inc\bsp_crc.h)(0x682C3545)
I (..\User\bsp\inc\bsp_utility.h)(0x68107F3A)
I (..\User\bsp\inc\bsp_temp.h)(0x681322C8)
I (..\User\bsp\inc\bsp_ntc.h)(0x678DDDCC)
I (..\User\bsp\inc\bsp_signal.h)(0x682ACFBF)
I (..\User\bsp\inc\bsp_beep.h)(0x6827E666)
I (..\User\bsp\inc\bsp_fifo.h)(0x682C143B)
I (..\User\bsp\inc\bsp_tim_pwm.h)(0x68143C04)
I (..\User\ui2.8\UI28.h)(0x6856620B)
I (..\User\ui2.8\UI28_auxiliary_table.h)(0x6801A019)
I (..\User\ui2.8\bsp_ili9340x_lcd.h)(0x6828483F)
I (..\User\ui2.8\UI28_font_16.h)(0x681EB98A)
I (..\User\ui2.8\UI28_font_20.h)(0x67F69774)
I (..\User\ui2.8\UI28_font_32.h)(0x681AFF0D)
I (..\User\ui2.8\UI28_font_72.h)(0x681D7617)
I (..\User\ui2.8\UI28_font_28.h)(0x681DBC62)
I (..\User\ui2.8\UI28_font_36.h)(0x681EACC0)
I (..\User\ui2.8\UI28_font_24.h)(0x682E7B21)
I (..\User\ui2.8\UI28_font_18.h)(0x681EF7D2)
I (..\User\ui2.8\UI28_font_14.h)(0x681ED798)
I (..\User\ui2.8\UI28_pic_120x120.h)(0x6857B1FC)
I (..\User\ui2.8\UI28_pic_32x32.h)(0x681D853E)
I (..\User\ui2.8\UI28_pic_22x30.h)(0x681D8CDE)
I (..\User\ui2.8\UI28_pic_14x18.h)(0x681DB278)
I (..\User\ui2.8\UI28_pic_48x88.h)(0x681DA6D8)
I (..\User\ui2.8\UI28_pic_36x36.h)(0x681DB27C)
I (..\User\ui2.8\UI28_pic_45x45.h)(0x68204BEA)
I (..\User\ui2.8\UI28_pic_40x20.h)(0x68283E6F)
I (..\User\ui2.8\UI28_pic_70x70.h)(0x682840DF)
I (..\User\ui2.8\UI28_matrix.h)(0x6857730C)
I (..\User\ui2.8\UI28_menu.h)(0x68579EAC)
I (..\User\ui2.8\UI28_rolling_album.h)(0x6854C854)
I (..\User\ui2.8\UI28_arc.h)(0x685772E6)
I (..\User\ui2.8\UI28_animation.h)(0x6829BB09)
I (..\User\ui2.8\UI28_progress_bar.h)(0x68567976)
I (..\User\pro\pro_cus_com.h)(0x685605BA)
I (..\User\pro\pro_hardware.h)(0x6829DA80)
I (..\User\pro\pro_workflow.h)(0x682D8D63)
I (..\User\pro\pro_error.h)(0x682C215A)
I (..\User\pro\pro_pagekey.h)(0x68297893)
I (..\User\page\page.h)(0x68555953)
I (..\User\page\src_inc\page_start.h)(0x67966D00)
I (..\User\page\src_inc\page_standby.h)(0x67966D02)
I (..\User\page\src_inc\page_run.h)(0x67966D08)
I (..\User\page\src_inc\page_parameter.h)(0x68579AE2)
I (..\User\page\src_inc\page_debug.h)(0x67966D0C)
I (..\User\page\src_inc\page_statistics.h)(0x682AF7F2)
I (..\User\page\src_inc\page_check.h)(0x67966D0E)
I (..\User\page\src_inc\page_developer.h)(0x67966D0C)
I (..\User\page\src_inc\page_qr_code.h)(0x681F13C4)
I (..\User\parame\parame.h)(0x68294931)
I (..\User\parame\parame_tongyong.h)(0x6857B6B2)
I (..\User\parame\flash_eeprom.h)(0x68560C9B)
F (..\User\pro\pro_hardware.h)(0x6829DA80)()
F (..\User\pro\pro_workflow.c)(0x6857B114)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/pro_workflow.o -MD)
I (..\User\bsp\bsp.h)(0x6826A21F)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (D:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdlib.h)(0x603507D0)
I (..\User\bsp\inc\bsp_timer.h)(0x680C5798)
I (..\User\bsp\inc\bsp_uart_fifo.h)(0x682426F2)
I (..\User\bsp\inc\bsp_boot.h)(0x6832B3D5)
I (..\User\bsp\inc\bsp_key.h)(0x68576AFA)
I (..\User\bsp\inc\bsp_crc.h)(0x682C3545)
I (..\User\bsp\inc\bsp_utility.h)(0x68107F3A)
I (..\User\bsp\inc\bsp_temp.h)(0x681322C8)
I (..\User\bsp\inc\bsp_ntc.h)(0x678DDDCC)
I (..\User\bsp\inc\bsp_signal.h)(0x682ACFBF)
I (..\User\bsp\inc\bsp_beep.h)(0x6827E666)
I (..\User\bsp\inc\bsp_fifo.h)(0x682C143B)
I (..\User\bsp\inc\bsp_tim_pwm.h)(0x68143C04)
I (..\User\ui2.8\UI28.h)(0x6856620B)
I (..\User\ui2.8\UI28_auxiliary_table.h)(0x6801A019)
I (..\User\ui2.8\bsp_ili9340x_lcd.h)(0x6828483F)
I (..\User\ui2.8\UI28_font_16.h)(0x681EB98A)
I (..\User\ui2.8\UI28_font_20.h)(0x67F69774)
I (..\User\ui2.8\UI28_font_32.h)(0x681AFF0D)
I (..\User\ui2.8\UI28_font_72.h)(0x681D7617)
I (..\User\ui2.8\UI28_font_28.h)(0x681DBC62)
I (..\User\ui2.8\UI28_font_36.h)(0x681EACC0)
I (..\User\ui2.8\UI28_font_24.h)(0x682E7B21)
I (..\User\ui2.8\UI28_font_18.h)(0x681EF7D2)
I (..\User\ui2.8\UI28_font_14.h)(0x681ED798)
I (..\User\ui2.8\UI28_pic_120x120.h)(0x6857B1FC)
I (..\User\ui2.8\UI28_pic_32x32.h)(0x681D853E)
I (..\User\ui2.8\UI28_pic_22x30.h)(0x681D8CDE)
I (..\User\ui2.8\UI28_pic_14x18.h)(0x681DB278)
I (..\User\ui2.8\UI28_pic_48x88.h)(0x681DA6D8)
I (..\User\ui2.8\UI28_pic_36x36.h)(0x681DB27C)
I (..\User\ui2.8\UI28_pic_45x45.h)(0x68204BEA)
I (..\User\ui2.8\UI28_pic_40x20.h)(0x68283E6F)
I (..\User\ui2.8\UI28_pic_70x70.h)(0x682840DF)
I (..\User\ui2.8\UI28_matrix.h)(0x6857730C)
I (..\User\ui2.8\UI28_menu.h)(0x68579EAC)
I (..\User\ui2.8\UI28_rolling_album.h)(0x6854C854)
I (..\User\ui2.8\UI28_arc.h)(0x685772E6)
I (..\User\ui2.8\UI28_animation.h)(0x6829BB09)
I (..\User\ui2.8\UI28_progress_bar.h)(0x68567976)
I (..\User\pro\pro_cus_com.h)(0x685605BA)
I (..\User\pro\pro_hardware.h)(0x6829DA80)
I (..\User\pro\pro_workflow.h)(0x682D8D63)
I (..\User\pro\pro_error.h)(0x682C215A)
I (..\User\pro\pro_pagekey.h)(0x68297893)
I (..\User\page\page.h)(0x68555953)
I (..\User\page\src_inc\page_start.h)(0x67966D00)
I (..\User\page\src_inc\page_standby.h)(0x67966D02)
I (..\User\page\src_inc\page_run.h)(0x67966D08)
I (..\User\page\src_inc\page_parameter.h)(0x68579AE2)
I (..\User\page\src_inc\page_debug.h)(0x67966D0C)
I (..\User\page\src_inc\page_statistics.h)(0x682AF7F2)
I (..\User\page\src_inc\page_check.h)(0x67966D0E)
I (..\User\page\src_inc\page_developer.h)(0x67966D0C)
I (..\User\page\src_inc\page_qr_code.h)(0x681F13C4)
I (..\User\parame\parame.h)(0x68294931)
I (..\User\parame\parame_tongyong.h)(0x6857B6B2)
I (..\User\parame\flash_eeprom.h)(0x68560C9B)
F (..\User\pro\pro_workflow.h)(0x682D8D63)()
F (..\User\pro\pro_error.c)(0x6857984F)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/pro_error.o -MD)
I (..\User\bsp\bsp.h)(0x6826A21F)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (D:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdlib.h)(0x603507D0)
I (..\User\bsp\inc\bsp_timer.h)(0x680C5798)
I (..\User\bsp\inc\bsp_uart_fifo.h)(0x682426F2)
I (..\User\bsp\inc\bsp_boot.h)(0x6832B3D5)
I (..\User\bsp\inc\bsp_key.h)(0x68576AFA)
I (..\User\bsp\inc\bsp_crc.h)(0x682C3545)
I (..\User\bsp\inc\bsp_utility.h)(0x68107F3A)
I (..\User\bsp\inc\bsp_temp.h)(0x681322C8)
I (..\User\bsp\inc\bsp_ntc.h)(0x678DDDCC)
I (..\User\bsp\inc\bsp_signal.h)(0x682ACFBF)
I (..\User\bsp\inc\bsp_beep.h)(0x6827E666)
I (..\User\bsp\inc\bsp_fifo.h)(0x682C143B)
I (..\User\bsp\inc\bsp_tim_pwm.h)(0x68143C04)
I (..\User\ui2.8\UI28.h)(0x6856620B)
I (..\User\ui2.8\UI28_auxiliary_table.h)(0x6801A019)
I (..\User\ui2.8\bsp_ili9340x_lcd.h)(0x6828483F)
I (..\User\ui2.8\UI28_font_16.h)(0x681EB98A)
I (..\User\ui2.8\UI28_font_20.h)(0x67F69774)
I (..\User\ui2.8\UI28_font_32.h)(0x681AFF0D)
I (..\User\ui2.8\UI28_font_72.h)(0x681D7617)
I (..\User\ui2.8\UI28_font_28.h)(0x681DBC62)
I (..\User\ui2.8\UI28_font_36.h)(0x681EACC0)
I (..\User\ui2.8\UI28_font_24.h)(0x682E7B21)
I (..\User\ui2.8\UI28_font_18.h)(0x681EF7D2)
I (..\User\ui2.8\UI28_font_14.h)(0x681ED798)
I (..\User\ui2.8\UI28_pic_120x120.h)(0x6857B1FC)
I (..\User\ui2.8\UI28_pic_32x32.h)(0x681D853E)
I (..\User\ui2.8\UI28_pic_22x30.h)(0x681D8CDE)
I (..\User\ui2.8\UI28_pic_14x18.h)(0x681DB278)
I (..\User\ui2.8\UI28_pic_48x88.h)(0x681DA6D8)
I (..\User\ui2.8\UI28_pic_36x36.h)(0x681DB27C)
I (..\User\ui2.8\UI28_pic_45x45.h)(0x68204BEA)
I (..\User\ui2.8\UI28_pic_40x20.h)(0x68283E6F)
I (..\User\ui2.8\UI28_pic_70x70.h)(0x682840DF)
I (..\User\ui2.8\UI28_matrix.h)(0x6857730C)
I (..\User\ui2.8\UI28_menu.h)(0x68579EAC)
I (..\User\ui2.8\UI28_rolling_album.h)(0x6854C854)
I (..\User\ui2.8\UI28_arc.h)(0x685772E6)
I (..\User\ui2.8\UI28_animation.h)(0x6829BB09)
I (..\User\ui2.8\UI28_progress_bar.h)(0x68567976)
I (..\User\pro\pro_cus_com.h)(0x685605BA)
I (..\User\pro\pro_hardware.h)(0x6829DA80)
I (..\User\pro\pro_workflow.h)(0x682D8D63)
I (..\User\pro\pro_error.h)(0x682C215A)
I (..\User\pro\pro_pagekey.h)(0x68297893)
I (..\User\page\page.h)(0x68555953)
I (..\User\page\src_inc\page_start.h)(0x67966D00)
I (..\User\page\src_inc\page_standby.h)(0x67966D02)
I (..\User\page\src_inc\page_run.h)(0x67966D08)
I (..\User\page\src_inc\page_parameter.h)(0x68579AE2)
I (..\User\page\src_inc\page_debug.h)(0x67966D0C)
I (..\User\page\src_inc\page_statistics.h)(0x682AF7F2)
I (..\User\page\src_inc\page_check.h)(0x67966D0E)
I (..\User\page\src_inc\page_developer.h)(0x67966D0C)
I (..\User\page\src_inc\page_qr_code.h)(0x681F13C4)
I (..\User\parame\parame.h)(0x68294931)
I (..\User\parame\parame_tongyong.h)(0x6857B6B2)
I (..\User\parame\flash_eeprom.h)(0x68560C9B)
F (..\User\pro\pro_error.h)(0x682C215A)()
F (..\User\pro\pro_pagekey.c)(0x6856405D)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/pro_pagekey.o -MD)
I (..\User\bsp\bsp.h)(0x6826A21F)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (D:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdlib.h)(0x603507D0)
I (..\User\bsp\inc\bsp_timer.h)(0x680C5798)
I (..\User\bsp\inc\bsp_uart_fifo.h)(0x682426F2)
I (..\User\bsp\inc\bsp_boot.h)(0x6832B3D5)
I (..\User\bsp\inc\bsp_key.h)(0x68576AFA)
I (..\User\bsp\inc\bsp_crc.h)(0x682C3545)
I (..\User\bsp\inc\bsp_utility.h)(0x68107F3A)
I (..\User\bsp\inc\bsp_temp.h)(0x681322C8)
I (..\User\bsp\inc\bsp_ntc.h)(0x678DDDCC)
I (..\User\bsp\inc\bsp_signal.h)(0x682ACFBF)
I (..\User\bsp\inc\bsp_beep.h)(0x6827E666)
I (..\User\bsp\inc\bsp_fifo.h)(0x682C143B)
I (..\User\bsp\inc\bsp_tim_pwm.h)(0x68143C04)
I (..\User\ui2.8\UI28.h)(0x6856620B)
I (..\User\ui2.8\UI28_auxiliary_table.h)(0x6801A019)
I (..\User\ui2.8\bsp_ili9340x_lcd.h)(0x6828483F)
I (..\User\ui2.8\UI28_font_16.h)(0x681EB98A)
I (..\User\ui2.8\UI28_font_20.h)(0x67F69774)
I (..\User\ui2.8\UI28_font_32.h)(0x681AFF0D)
I (..\User\ui2.8\UI28_font_72.h)(0x681D7617)
I (..\User\ui2.8\UI28_font_28.h)(0x681DBC62)
I (..\User\ui2.8\UI28_font_36.h)(0x681EACC0)
I (..\User\ui2.8\UI28_font_24.h)(0x682E7B21)
I (..\User\ui2.8\UI28_font_18.h)(0x681EF7D2)
I (..\User\ui2.8\UI28_font_14.h)(0x681ED798)
I (..\User\ui2.8\UI28_pic_120x120.h)(0x6857B1FC)
I (..\User\ui2.8\UI28_pic_32x32.h)(0x681D853E)
I (..\User\ui2.8\UI28_pic_22x30.h)(0x681D8CDE)
I (..\User\ui2.8\UI28_pic_14x18.h)(0x681DB278)
I (..\User\ui2.8\UI28_pic_48x88.h)(0x681DA6D8)
I (..\User\ui2.8\UI28_pic_36x36.h)(0x681DB27C)
I (..\User\ui2.8\UI28_pic_45x45.h)(0x68204BEA)
I (..\User\ui2.8\UI28_pic_40x20.h)(0x68283E6F)
I (..\User\ui2.8\UI28_pic_70x70.h)(0x682840DF)
I (..\User\ui2.8\UI28_matrix.h)(0x6857730C)
I (..\User\ui2.8\UI28_menu.h)(0x68579EAC)
I (..\User\ui2.8\UI28_rolling_album.h)(0x6854C854)
I (..\User\ui2.8\UI28_arc.h)(0x685772E6)
I (..\User\ui2.8\UI28_animation.h)(0x6829BB09)
I (..\User\ui2.8\UI28_progress_bar.h)(0x68567976)
I (..\User\pro\pro_cus_com.h)(0x685605BA)
I (..\User\pro\pro_hardware.h)(0x6829DA80)
I (..\User\pro\pro_workflow.h)(0x682D8D63)
I (..\User\pro\pro_error.h)(0x682C215A)
I (..\User\pro\pro_pagekey.h)(0x68297893)
I (..\User\page\page.h)(0x68555953)
I (..\User\page\src_inc\page_start.h)(0x67966D00)
I (..\User\page\src_inc\page_standby.h)(0x67966D02)
I (..\User\page\src_inc\page_run.h)(0x67966D08)
I (..\User\page\src_inc\page_parameter.h)(0x68579AE2)
I (..\User\page\src_inc\page_debug.h)(0x67966D0C)
I (..\User\page\src_inc\page_statistics.h)(0x682AF7F2)
I (..\User\page\src_inc\page_check.h)(0x67966D0E)
I (..\User\page\src_inc\page_developer.h)(0x67966D0C)
I (..\User\page\src_inc\page_qr_code.h)(0x681F13C4)
I (..\User\parame\parame.h)(0x68294931)
I (..\User\parame\parame_tongyong.h)(0x6857B6B2)
I (..\User\parame\flash_eeprom.h)(0x68560C9B)
F (..\User\pro\pro_pagekey.h)(0x68297893)()
F (..\User\page\page.c)(0x68197F66)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/page.o -MD)
I (..\User\page\page.h)(0x68555953)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (..\User\bsp\bsp.h)(0x6826A21F)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
I (D:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdlib.h)(0x603507D0)
I (..\User\bsp\inc\bsp_timer.h)(0x680C5798)
I (..\User\bsp\inc\bsp_uart_fifo.h)(0x682426F2)
I (..\User\bsp\inc\bsp_boot.h)(0x6832B3D5)
I (..\User\bsp\inc\bsp_key.h)(0x68576AFA)
I (..\User\bsp\inc\bsp_crc.h)(0x682C3545)
I (..\User\bsp\inc\bsp_utility.h)(0x68107F3A)
I (..\User\bsp\inc\bsp_temp.h)(0x681322C8)
I (..\User\bsp\inc\bsp_ntc.h)(0x678DDDCC)
I (..\User\bsp\inc\bsp_signal.h)(0x682ACFBF)
I (..\User\bsp\inc\bsp_beep.h)(0x6827E666)
I (..\User\bsp\inc\bsp_fifo.h)(0x682C143B)
I (..\User\bsp\inc\bsp_tim_pwm.h)(0x68143C04)
I (..\User\ui2.8\UI28.h)(0x6856620B)
I (..\User\ui2.8\UI28_auxiliary_table.h)(0x6801A019)
I (..\User\ui2.8\bsp_ili9340x_lcd.h)(0x6828483F)
I (..\User\ui2.8\UI28_font_16.h)(0x681EB98A)
I (..\User\ui2.8\UI28_font_20.h)(0x67F69774)
I (..\User\ui2.8\UI28_font_32.h)(0x681AFF0D)
I (..\User\ui2.8\UI28_font_72.h)(0x681D7617)
I (..\User\ui2.8\UI28_font_28.h)(0x681DBC62)
I (..\User\ui2.8\UI28_font_36.h)(0x681EACC0)
I (..\User\ui2.8\UI28_font_24.h)(0x682E7B21)
I (..\User\ui2.8\UI28_font_18.h)(0x681EF7D2)
I (..\User\ui2.8\UI28_font_14.h)(0x681ED798)
I (..\User\ui2.8\UI28_pic_120x120.h)(0x6857B1FC)
I (..\User\ui2.8\UI28_pic_32x32.h)(0x681D853E)
I (..\User\ui2.8\UI28_pic_22x30.h)(0x681D8CDE)
I (..\User\ui2.8\UI28_pic_14x18.h)(0x681DB278)
I (..\User\ui2.8\UI28_pic_48x88.h)(0x681DA6D8)
I (..\User\ui2.8\UI28_pic_36x36.h)(0x681DB27C)
I (..\User\ui2.8\UI28_pic_45x45.h)(0x68204BEA)
I (..\User\ui2.8\UI28_pic_40x20.h)(0x68283E6F)
I (..\User\ui2.8\UI28_pic_70x70.h)(0x682840DF)
I (..\User\ui2.8\UI28_matrix.h)(0x6857730C)
I (..\User\ui2.8\UI28_menu.h)(0x68579EAC)
I (..\User\ui2.8\UI28_rolling_album.h)(0x6854C854)
I (..\User\ui2.8\UI28_arc.h)(0x685772E6)
I (..\User\ui2.8\UI28_animation.h)(0x6829BB09)
I (..\User\ui2.8\UI28_progress_bar.h)(0x68567976)
I (..\User\pro\pro_cus_com.h)(0x685605BA)
I (..\User\pro\pro_hardware.h)(0x6829DA80)
I (..\User\pro\pro_workflow.h)(0x682D8D63)
I (..\User\pro\pro_error.h)(0x682C215A)
I (..\User\pro\pro_pagekey.h)(0x68297893)
I (..\User\parame\parame.h)(0x68294931)
I (..\User\parame\parame_tongyong.h)(0x6857B6B2)
I (..\User\parame\flash_eeprom.h)(0x68560C9B)
I (..\User\page\src_inc\page_start.h)(0x67966D00)
I (..\User\page\src_inc\page_standby.h)(0x67966D02)
I (..\User\page\src_inc\page_run.h)(0x67966D08)
I (..\User\page\src_inc\page_parameter.h)(0x68579AE2)
I (..\User\page\src_inc\page_debug.h)(0x67966D0C)
I (..\User\page\src_inc\page_statistics.h)(0x682AF7F2)
I (..\User\page\src_inc\page_check.h)(0x67966D0E)
I (..\User\page\src_inc\page_developer.h)(0x67966D0C)
I (..\User\page\src_inc\page_qr_code.h)(0x681F13C4)
F (..\User\page\page.h)(0x68555953)()
F (..\User\page\src_inc\page_check.c)(0x68554F41)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/page_check.o -MD)
I (..\User\page\src_inc\page_check.h)(0x67966D0E)
I (..\User\bsp\bsp.h)(0x6826A21F)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (D:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdlib.h)(0x603507D0)
I (..\User\bsp\inc\bsp_timer.h)(0x680C5798)
I (..\User\bsp\inc\bsp_uart_fifo.h)(0x682426F2)
I (..\User\bsp\inc\bsp_boot.h)(0x6832B3D5)
I (..\User\bsp\inc\bsp_key.h)(0x68576AFA)
I (..\User\bsp\inc\bsp_crc.h)(0x682C3545)
I (..\User\bsp\inc\bsp_utility.h)(0x68107F3A)
I (..\User\bsp\inc\bsp_temp.h)(0x681322C8)
I (..\User\bsp\inc\bsp_ntc.h)(0x678DDDCC)
I (..\User\bsp\inc\bsp_signal.h)(0x682ACFBF)
I (..\User\bsp\inc\bsp_beep.h)(0x6827E666)
I (..\User\bsp\inc\bsp_fifo.h)(0x682C143B)
I (..\User\bsp\inc\bsp_tim_pwm.h)(0x68143C04)
I (..\User\ui2.8\UI28.h)(0x6856620B)
I (..\User\ui2.8\UI28_auxiliary_table.h)(0x6801A019)
I (..\User\ui2.8\bsp_ili9340x_lcd.h)(0x6828483F)
I (..\User\ui2.8\UI28_font_16.h)(0x681EB98A)
I (..\User\ui2.8\UI28_font_20.h)(0x67F69774)
I (..\User\ui2.8\UI28_font_32.h)(0x681AFF0D)
I (..\User\ui2.8\UI28_font_72.h)(0x681D7617)
I (..\User\ui2.8\UI28_font_28.h)(0x681DBC62)
I (..\User\ui2.8\UI28_font_36.h)(0x681EACC0)
I (..\User\ui2.8\UI28_font_24.h)(0x682E7B21)
I (..\User\ui2.8\UI28_font_18.h)(0x681EF7D2)
I (..\User\ui2.8\UI28_font_14.h)(0x681ED798)
I (..\User\ui2.8\UI28_pic_120x120.h)(0x6857B1FC)
I (..\User\ui2.8\UI28_pic_32x32.h)(0x681D853E)
I (..\User\ui2.8\UI28_pic_22x30.h)(0x681D8CDE)
I (..\User\ui2.8\UI28_pic_14x18.h)(0x681DB278)
I (..\User\ui2.8\UI28_pic_48x88.h)(0x681DA6D8)
I (..\User\ui2.8\UI28_pic_36x36.h)(0x681DB27C)
I (..\User\ui2.8\UI28_pic_45x45.h)(0x68204BEA)
I (..\User\ui2.8\UI28_pic_40x20.h)(0x68283E6F)
I (..\User\ui2.8\UI28_pic_70x70.h)(0x682840DF)
I (..\User\ui2.8\UI28_matrix.h)(0x6857730C)
I (..\User\ui2.8\UI28_menu.h)(0x68579EAC)
I (..\User\ui2.8\UI28_rolling_album.h)(0x6854C854)
I (..\User\ui2.8\UI28_arc.h)(0x685772E6)
I (..\User\ui2.8\UI28_animation.h)(0x6829BB09)
I (..\User\ui2.8\UI28_progress_bar.h)(0x68567976)
I (..\User\pro\pro_cus_com.h)(0x685605BA)
I (..\User\pro\pro_hardware.h)(0x6829DA80)
I (..\User\pro\pro_workflow.h)(0x682D8D63)
I (..\User\pro\pro_error.h)(0x682C215A)
I (..\User\pro\pro_pagekey.h)(0x68297893)
I (..\User\page\page.h)(0x68555953)
I (..\User\page\src_inc\page_start.h)(0x67966D00)
I (..\User\page\src_inc\page_standby.h)(0x67966D02)
I (..\User\page\src_inc\page_run.h)(0x67966D08)
I (..\User\page\src_inc\page_parameter.h)(0x68579AE2)
I (..\User\page\src_inc\page_debug.h)(0x67966D0C)
I (..\User\page\src_inc\page_statistics.h)(0x682AF7F2)
I (..\User\page\src_inc\page_developer.h)(0x67966D0C)
I (..\User\page\src_inc\page_qr_code.h)(0x681F13C4)
I (..\User\parame\parame.h)(0x68294931)
I (..\User\parame\parame_tongyong.h)(0x6857B6B2)
I (..\User\parame\flash_eeprom.h)(0x68560C9B)
F (..\User\page\src_inc\page_check.h)(0x67966D0E)()
F (..\User\page\src_inc\page_debug.c)(0x68554ECD)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/page_debug.o -MD)
I (..\User\page\src_inc\page_debug.h)(0x67966D0C)
I (..\User\bsp\bsp.h)(0x6826A21F)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (D:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdlib.h)(0x603507D0)
I (..\User\bsp\inc\bsp_timer.h)(0x680C5798)
I (..\User\bsp\inc\bsp_uart_fifo.h)(0x682426F2)
I (..\User\bsp\inc\bsp_boot.h)(0x6832B3D5)
I (..\User\bsp\inc\bsp_key.h)(0x68576AFA)
I (..\User\bsp\inc\bsp_crc.h)(0x682C3545)
I (..\User\bsp\inc\bsp_utility.h)(0x68107F3A)
I (..\User\bsp\inc\bsp_temp.h)(0x681322C8)
I (..\User\bsp\inc\bsp_ntc.h)(0x678DDDCC)
I (..\User\bsp\inc\bsp_signal.h)(0x682ACFBF)
I (..\User\bsp\inc\bsp_beep.h)(0x6827E666)
I (..\User\bsp\inc\bsp_fifo.h)(0x682C143B)
I (..\User\bsp\inc\bsp_tim_pwm.h)(0x68143C04)
I (..\User\ui2.8\UI28.h)(0x6856620B)
I (..\User\ui2.8\UI28_auxiliary_table.h)(0x6801A019)
I (..\User\ui2.8\bsp_ili9340x_lcd.h)(0x6828483F)
I (..\User\ui2.8\UI28_font_16.h)(0x681EB98A)
I (..\User\ui2.8\UI28_font_20.h)(0x67F69774)
I (..\User\ui2.8\UI28_font_32.h)(0x681AFF0D)
I (..\User\ui2.8\UI28_font_72.h)(0x681D7617)
I (..\User\ui2.8\UI28_font_28.h)(0x681DBC62)
I (..\User\ui2.8\UI28_font_36.h)(0x681EACC0)
I (..\User\ui2.8\UI28_font_24.h)(0x682E7B21)
I (..\User\ui2.8\UI28_font_18.h)(0x681EF7D2)
I (..\User\ui2.8\UI28_font_14.h)(0x681ED798)
I (..\User\ui2.8\UI28_pic_120x120.h)(0x6857B1FC)
I (..\User\ui2.8\UI28_pic_32x32.h)(0x681D853E)
I (..\User\ui2.8\UI28_pic_22x30.h)(0x681D8CDE)
I (..\User\ui2.8\UI28_pic_14x18.h)(0x681DB278)
I (..\User\ui2.8\UI28_pic_48x88.h)(0x681DA6D8)
I (..\User\ui2.8\UI28_pic_36x36.h)(0x681DB27C)
I (..\User\ui2.8\UI28_pic_45x45.h)(0x68204BEA)
I (..\User\ui2.8\UI28_pic_40x20.h)(0x68283E6F)
I (..\User\ui2.8\UI28_pic_70x70.h)(0x682840DF)
I (..\User\ui2.8\UI28_matrix.h)(0x6857730C)
I (..\User\ui2.8\UI28_menu.h)(0x68579EAC)
I (..\User\ui2.8\UI28_rolling_album.h)(0x6854C854)
I (..\User\ui2.8\UI28_arc.h)(0x685772E6)
I (..\User\ui2.8\UI28_animation.h)(0x6829BB09)
I (..\User\ui2.8\UI28_progress_bar.h)(0x68567976)
I (..\User\pro\pro_cus_com.h)(0x685605BA)
I (..\User\pro\pro_hardware.h)(0x6829DA80)
I (..\User\pro\pro_workflow.h)(0x682D8D63)
I (..\User\pro\pro_error.h)(0x682C215A)
I (..\User\pro\pro_pagekey.h)(0x68297893)
I (..\User\page\page.h)(0x68555953)
I (..\User\page\src_inc\page_start.h)(0x67966D00)
I (..\User\page\src_inc\page_standby.h)(0x67966D02)
I (..\User\page\src_inc\page_run.h)(0x67966D08)
I (..\User\page\src_inc\page_parameter.h)(0x68579AE2)
I (..\User\page\src_inc\page_statistics.h)(0x682AF7F2)
I (..\User\page\src_inc\page_check.h)(0x67966D0E)
I (..\User\page\src_inc\page_developer.h)(0x67966D0C)
I (..\User\page\src_inc\page_qr_code.h)(0x681F13C4)
I (..\User\parame\parame.h)(0x68294931)
I (..\User\parame\parame_tongyong.h)(0x6857B6B2)
I (..\User\parame\flash_eeprom.h)(0x68560C9B)
F (..\User\page\src_inc\page_debug.h)(0x67966D0C)()
F (..\User\page\src_inc\page_developer.c)(0x68576EE3)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/page_developer.o -MD)
I (..\User\page\src_inc\page_developer.h)(0x67966D0C)
I (..\User\bsp\bsp.h)(0x6826A21F)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (D:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdlib.h)(0x603507D0)
I (..\User\bsp\inc\bsp_timer.h)(0x680C5798)
I (..\User\bsp\inc\bsp_uart_fifo.h)(0x682426F2)
I (..\User\bsp\inc\bsp_boot.h)(0x6832B3D5)
I (..\User\bsp\inc\bsp_key.h)(0x68576AFA)
I (..\User\bsp\inc\bsp_crc.h)(0x682C3545)
I (..\User\bsp\inc\bsp_utility.h)(0x68107F3A)
I (..\User\bsp\inc\bsp_temp.h)(0x681322C8)
I (..\User\bsp\inc\bsp_ntc.h)(0x678DDDCC)
I (..\User\bsp\inc\bsp_signal.h)(0x682ACFBF)
I (..\User\bsp\inc\bsp_beep.h)(0x6827E666)
I (..\User\bsp\inc\bsp_fifo.h)(0x682C143B)
I (..\User\bsp\inc\bsp_tim_pwm.h)(0x68143C04)
I (..\User\ui2.8\UI28.h)(0x6856620B)
I (..\User\ui2.8\UI28_auxiliary_table.h)(0x6801A019)
I (..\User\ui2.8\bsp_ili9340x_lcd.h)(0x6828483F)
I (..\User\ui2.8\UI28_font_16.h)(0x681EB98A)
I (..\User\ui2.8\UI28_font_20.h)(0x67F69774)
I (..\User\ui2.8\UI28_font_32.h)(0x681AFF0D)
I (..\User\ui2.8\UI28_font_72.h)(0x681D7617)
I (..\User\ui2.8\UI28_font_28.h)(0x681DBC62)
I (..\User\ui2.8\UI28_font_36.h)(0x681EACC0)
I (..\User\ui2.8\UI28_font_24.h)(0x682E7B21)
I (..\User\ui2.8\UI28_font_18.h)(0x681EF7D2)
I (..\User\ui2.8\UI28_font_14.h)(0x681ED798)
I (..\User\ui2.8\UI28_pic_120x120.h)(0x6857B1FC)
I (..\User\ui2.8\UI28_pic_32x32.h)(0x681D853E)
I (..\User\ui2.8\UI28_pic_22x30.h)(0x681D8CDE)
I (..\User\ui2.8\UI28_pic_14x18.h)(0x681DB278)
I (..\User\ui2.8\UI28_pic_48x88.h)(0x681DA6D8)
I (..\User\ui2.8\UI28_pic_36x36.h)(0x681DB27C)
I (..\User\ui2.8\UI28_pic_45x45.h)(0x68204BEA)
I (..\User\ui2.8\UI28_pic_40x20.h)(0x68283E6F)
I (..\User\ui2.8\UI28_pic_70x70.h)(0x682840DF)
I (..\User\ui2.8\UI28_matrix.h)(0x6857730C)
I (..\User\ui2.8\UI28_menu.h)(0x68579EAC)
I (..\User\ui2.8\UI28_rolling_album.h)(0x6854C854)
I (..\User\ui2.8\UI28_arc.h)(0x685772E6)
I (..\User\ui2.8\UI28_animation.h)(0x6829BB09)
I (..\User\ui2.8\UI28_progress_bar.h)(0x68567976)
I (..\User\pro\pro_cus_com.h)(0x685605BA)
I (..\User\pro\pro_hardware.h)(0x6829DA80)
I (..\User\pro\pro_workflow.h)(0x682D8D63)
I (..\User\pro\pro_error.h)(0x682C215A)
I (..\User\pro\pro_pagekey.h)(0x68297893)
I (..\User\page\page.h)(0x68555953)
I (..\User\page\src_inc\page_start.h)(0x67966D00)
I (..\User\page\src_inc\page_standby.h)(0x67966D02)
I (..\User\page\src_inc\page_run.h)(0x67966D08)
I (..\User\page\src_inc\page_parameter.h)(0x68579AE2)
I (..\User\page\src_inc\page_debug.h)(0x67966D0C)
I (..\User\page\src_inc\page_statistics.h)(0x682AF7F2)
I (..\User\page\src_inc\page_check.h)(0x67966D0E)
I (..\User\page\src_inc\page_qr_code.h)(0x681F13C4)
I (..\User\parame\parame.h)(0x68294931)
I (..\User\parame\parame_tongyong.h)(0x6857B6B2)
I (..\User\parame\flash_eeprom.h)(0x68560C9B)
F (..\User\page\src_inc\page_developer.h)(0x67966D0C)()
F (..\User\page\src_inc\page_parameter.c)(0x6857B7F1)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/page_parameter.o -MD)
I (..\User\page\src_inc\page_parameter.h)(0x68579AE2)
I (..\User\bsp\bsp.h)(0x6826A21F)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (D:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdlib.h)(0x603507D0)
I (..\User\bsp\inc\bsp_timer.h)(0x680C5798)
I (..\User\bsp\inc\bsp_uart_fifo.h)(0x682426F2)
I (..\User\bsp\inc\bsp_boot.h)(0x6832B3D5)
I (..\User\bsp\inc\bsp_key.h)(0x68576AFA)
I (..\User\bsp\inc\bsp_crc.h)(0x682C3545)
I (..\User\bsp\inc\bsp_utility.h)(0x68107F3A)
I (..\User\bsp\inc\bsp_temp.h)(0x681322C8)
I (..\User\bsp\inc\bsp_ntc.h)(0x678DDDCC)
I (..\User\bsp\inc\bsp_signal.h)(0x682ACFBF)
I (..\User\bsp\inc\bsp_beep.h)(0x6827E666)
I (..\User\bsp\inc\bsp_fifo.h)(0x682C143B)
I (..\User\bsp\inc\bsp_tim_pwm.h)(0x68143C04)
I (..\User\ui2.8\UI28.h)(0x6856620B)
I (..\User\ui2.8\UI28_auxiliary_table.h)(0x6801A019)
I (..\User\ui2.8\bsp_ili9340x_lcd.h)(0x6828483F)
I (..\User\ui2.8\UI28_font_16.h)(0x681EB98A)
I (..\User\ui2.8\UI28_font_20.h)(0x67F69774)
I (..\User\ui2.8\UI28_font_32.h)(0x681AFF0D)
I (..\User\ui2.8\UI28_font_72.h)(0x681D7617)
I (..\User\ui2.8\UI28_font_28.h)(0x681DBC62)
I (..\User\ui2.8\UI28_font_36.h)(0x681EACC0)
I (..\User\ui2.8\UI28_font_24.h)(0x682E7B21)
I (..\User\ui2.8\UI28_font_18.h)(0x681EF7D2)
I (..\User\ui2.8\UI28_font_14.h)(0x681ED798)
I (..\User\ui2.8\UI28_pic_120x120.h)(0x6857B1FC)
I (..\User\ui2.8\UI28_pic_32x32.h)(0x681D853E)
I (..\User\ui2.8\UI28_pic_22x30.h)(0x681D8CDE)
I (..\User\ui2.8\UI28_pic_14x18.h)(0x681DB278)
I (..\User\ui2.8\UI28_pic_48x88.h)(0x681DA6D8)
I (..\User\ui2.8\UI28_pic_36x36.h)(0x681DB27C)
I (..\User\ui2.8\UI28_pic_45x45.h)(0x68204BEA)
I (..\User\ui2.8\UI28_pic_40x20.h)(0x68283E6F)
I (..\User\ui2.8\UI28_pic_70x70.h)(0x682840DF)
I (..\User\ui2.8\UI28_matrix.h)(0x6857730C)
I (..\User\ui2.8\UI28_menu.h)(0x68579EAC)
I (..\User\ui2.8\UI28_rolling_album.h)(0x6854C854)
I (..\User\ui2.8\UI28_arc.h)(0x685772E6)
I (..\User\ui2.8\UI28_animation.h)(0x6829BB09)
I (..\User\ui2.8\UI28_progress_bar.h)(0x68567976)
I (..\User\pro\pro_cus_com.h)(0x685605BA)
I (..\User\pro\pro_hardware.h)(0x6829DA80)
I (..\User\pro\pro_workflow.h)(0x682D8D63)
I (..\User\pro\pro_error.h)(0x682C215A)
I (..\User\pro\pro_pagekey.h)(0x68297893)
I (..\User\page\page.h)(0x68555953)
I (..\User\page\src_inc\page_start.h)(0x67966D00)
I (..\User\page\src_inc\page_standby.h)(0x67966D02)
I (..\User\page\src_inc\page_run.h)(0x67966D08)
I (..\User\page\src_inc\page_debug.h)(0x67966D0C)
I (..\User\page\src_inc\page_statistics.h)(0x682AF7F2)
I (..\User\page\src_inc\page_check.h)(0x67966D0E)
I (..\User\page\src_inc\page_developer.h)(0x67966D0C)
I (..\User\page\src_inc\page_qr_code.h)(0x681F13C4)
I (..\User\parame\parame.h)(0x68294931)
I (..\User\parame\parame_tongyong.h)(0x6857B6B2)
I (..\User\parame\flash_eeprom.h)(0x68560C9B)
F (..\User\page\src_inc\page_parameter.h)(0x68579AE2)()
F (..\User\page\src_inc\page_run.c)(0x685774BF)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/page_run.o -MD)
I (..\User\page\src_inc\page_run.h)(0x67966D08)
I (..\User\bsp\bsp.h)(0x6826A21F)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (D:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdlib.h)(0x603507D0)
I (..\User\bsp\inc\bsp_timer.h)(0x680C5798)
I (..\User\bsp\inc\bsp_uart_fifo.h)(0x682426F2)
I (..\User\bsp\inc\bsp_boot.h)(0x6832B3D5)
I (..\User\bsp\inc\bsp_key.h)(0x68576AFA)
I (..\User\bsp\inc\bsp_crc.h)(0x682C3545)
I (..\User\bsp\inc\bsp_utility.h)(0x68107F3A)
I (..\User\bsp\inc\bsp_temp.h)(0x681322C8)
I (..\User\bsp\inc\bsp_ntc.h)(0x678DDDCC)
I (..\User\bsp\inc\bsp_signal.h)(0x682ACFBF)
I (..\User\bsp\inc\bsp_beep.h)(0x6827E666)
I (..\User\bsp\inc\bsp_fifo.h)(0x682C143B)
I (..\User\bsp\inc\bsp_tim_pwm.h)(0x68143C04)
I (..\User\ui2.8\UI28.h)(0x6856620B)
I (..\User\ui2.8\UI28_auxiliary_table.h)(0x6801A019)
I (..\User\ui2.8\bsp_ili9340x_lcd.h)(0x6828483F)
I (..\User\ui2.8\UI28_font_16.h)(0x681EB98A)
I (..\User\ui2.8\UI28_font_20.h)(0x67F69774)
I (..\User\ui2.8\UI28_font_32.h)(0x681AFF0D)
I (..\User\ui2.8\UI28_font_72.h)(0x681D7617)
I (..\User\ui2.8\UI28_font_28.h)(0x681DBC62)
I (..\User\ui2.8\UI28_font_36.h)(0x681EACC0)
I (..\User\ui2.8\UI28_font_24.h)(0x682E7B21)
I (..\User\ui2.8\UI28_font_18.h)(0x681EF7D2)
I (..\User\ui2.8\UI28_font_14.h)(0x681ED798)
I (..\User\ui2.8\UI28_pic_120x120.h)(0x6857B1FC)
I (..\User\ui2.8\UI28_pic_32x32.h)(0x681D853E)
I (..\User\ui2.8\UI28_pic_22x30.h)(0x681D8CDE)
I (..\User\ui2.8\UI28_pic_14x18.h)(0x681DB278)
I (..\User\ui2.8\UI28_pic_48x88.h)(0x681DA6D8)
I (..\User\ui2.8\UI28_pic_36x36.h)(0x681DB27C)
I (..\User\ui2.8\UI28_pic_45x45.h)(0x68204BEA)
I (..\User\ui2.8\UI28_pic_40x20.h)(0x68283E6F)
I (..\User\ui2.8\UI28_pic_70x70.h)(0x682840DF)
I (..\User\ui2.8\UI28_matrix.h)(0x6857730C)
I (..\User\ui2.8\UI28_menu.h)(0x68579EAC)
I (..\User\ui2.8\UI28_rolling_album.h)(0x6854C854)
I (..\User\ui2.8\UI28_arc.h)(0x685772E6)
I (..\User\ui2.8\UI28_animation.h)(0x6829BB09)
I (..\User\ui2.8\UI28_progress_bar.h)(0x68567976)
I (..\User\pro\pro_cus_com.h)(0x685605BA)
I (..\User\pro\pro_hardware.h)(0x6829DA80)
I (..\User\pro\pro_workflow.h)(0x682D8D63)
I (..\User\pro\pro_error.h)(0x682C215A)
I (..\User\pro\pro_pagekey.h)(0x68297893)
I (..\User\page\page.h)(0x68555953)
I (..\User\page\src_inc\page_start.h)(0x67966D00)
I (..\User\page\src_inc\page_standby.h)(0x67966D02)
I (..\User\page\src_inc\page_parameter.h)(0x68579AE2)
I (..\User\page\src_inc\page_debug.h)(0x67966D0C)
I (..\User\page\src_inc\page_statistics.h)(0x682AF7F2)
I (..\User\page\src_inc\page_check.h)(0x67966D0E)
I (..\User\page\src_inc\page_developer.h)(0x67966D0C)
I (..\User\page\src_inc\page_qr_code.h)(0x681F13C4)
I (..\User\parame\parame.h)(0x68294931)
I (..\User\parame\parame_tongyong.h)(0x6857B6B2)
I (..\User\parame\flash_eeprom.h)(0x68560C9B)
F (..\User\page\src_inc\page_run.h)(0x67966D08)()
F (..\User\page\src_inc\page_standby.c)(0x6854C913)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/page_standby.o -MD)
I (..\User\page\src_inc\page_standby.h)(0x67966D02)
I (..\User\bsp\bsp.h)(0x6826A21F)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (D:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdlib.h)(0x603507D0)
I (..\User\bsp\inc\bsp_timer.h)(0x680C5798)
I (..\User\bsp\inc\bsp_uart_fifo.h)(0x682426F2)
I (..\User\bsp\inc\bsp_boot.h)(0x6832B3D5)
I (..\User\bsp\inc\bsp_key.h)(0x68576AFA)
I (..\User\bsp\inc\bsp_crc.h)(0x682C3545)
I (..\User\bsp\inc\bsp_utility.h)(0x68107F3A)
I (..\User\bsp\inc\bsp_temp.h)(0x681322C8)
I (..\User\bsp\inc\bsp_ntc.h)(0x678DDDCC)
I (..\User\bsp\inc\bsp_signal.h)(0x682ACFBF)
I (..\User\bsp\inc\bsp_beep.h)(0x6827E666)
I (..\User\bsp\inc\bsp_fifo.h)(0x682C143B)
I (..\User\bsp\inc\bsp_tim_pwm.h)(0x68143C04)
I (..\User\ui2.8\UI28.h)(0x6856620B)
I (..\User\ui2.8\UI28_auxiliary_table.h)(0x6801A019)
I (..\User\ui2.8\bsp_ili9340x_lcd.h)(0x6828483F)
I (..\User\ui2.8\UI28_font_16.h)(0x681EB98A)
I (..\User\ui2.8\UI28_font_20.h)(0x67F69774)
I (..\User\ui2.8\UI28_font_32.h)(0x681AFF0D)
I (..\User\ui2.8\UI28_font_72.h)(0x681D7617)
I (..\User\ui2.8\UI28_font_28.h)(0x681DBC62)
I (..\User\ui2.8\UI28_font_36.h)(0x681EACC0)
I (..\User\ui2.8\UI28_font_24.h)(0x682E7B21)
I (..\User\ui2.8\UI28_font_18.h)(0x681EF7D2)
I (..\User\ui2.8\UI28_font_14.h)(0x681ED798)
I (..\User\ui2.8\UI28_pic_120x120.h)(0x6857B1FC)
I (..\User\ui2.8\UI28_pic_32x32.h)(0x681D853E)
I (..\User\ui2.8\UI28_pic_22x30.h)(0x681D8CDE)
I (..\User\ui2.8\UI28_pic_14x18.h)(0x681DB278)
I (..\User\ui2.8\UI28_pic_48x88.h)(0x681DA6D8)
I (..\User\ui2.8\UI28_pic_36x36.h)(0x681DB27C)
I (..\User\ui2.8\UI28_pic_45x45.h)(0x68204BEA)
I (..\User\ui2.8\UI28_pic_40x20.h)(0x68283E6F)
I (..\User\ui2.8\UI28_pic_70x70.h)(0x682840DF)
I (..\User\ui2.8\UI28_matrix.h)(0x6857730C)
I (..\User\ui2.8\UI28_menu.h)(0x68579EAC)
I (..\User\ui2.8\UI28_rolling_album.h)(0x6854C854)
I (..\User\ui2.8\UI28_arc.h)(0x685772E6)
I (..\User\ui2.8\UI28_animation.h)(0x6829BB09)
I (..\User\ui2.8\UI28_progress_bar.h)(0x68567976)
I (..\User\pro\pro_cus_com.h)(0x685605BA)
I (..\User\pro\pro_hardware.h)(0x6829DA80)
I (..\User\pro\pro_workflow.h)(0x682D8D63)
I (..\User\pro\pro_error.h)(0x682C215A)
I (..\User\pro\pro_pagekey.h)(0x68297893)
I (..\User\page\page.h)(0x68555953)
I (..\User\page\src_inc\page_start.h)(0x67966D00)
I (..\User\page\src_inc\page_run.h)(0x67966D08)
I (..\User\page\src_inc\page_parameter.h)(0x68579AE2)
I (..\User\page\src_inc\page_debug.h)(0x67966D0C)
I (..\User\page\src_inc\page_statistics.h)(0x682AF7F2)
I (..\User\page\src_inc\page_check.h)(0x67966D0E)
I (..\User\page\src_inc\page_developer.h)(0x67966D0C)
I (..\User\page\src_inc\page_qr_code.h)(0x681F13C4)
I (..\User\parame\parame.h)(0x68294931)
I (..\User\parame\parame_tongyong.h)(0x6857B6B2)
I (..\User\parame\flash_eeprom.h)(0x68560C9B)
F (..\User\page\src_inc\page_standby.h)(0x67966D02)()
F (..\User\page\src_inc\page_start.c)(0x685772EA)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/page_start.o -MD)
I (..\User\page\src_inc\page_start.h)(0x67966D00)
I (..\User\bsp\bsp.h)(0x6826A21F)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (D:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdlib.h)(0x603507D0)
I (..\User\bsp\inc\bsp_timer.h)(0x680C5798)
I (..\User\bsp\inc\bsp_uart_fifo.h)(0x682426F2)
I (..\User\bsp\inc\bsp_boot.h)(0x6832B3D5)
I (..\User\bsp\inc\bsp_key.h)(0x68576AFA)
I (..\User\bsp\inc\bsp_crc.h)(0x682C3545)
I (..\User\bsp\inc\bsp_utility.h)(0x68107F3A)
I (..\User\bsp\inc\bsp_temp.h)(0x681322C8)
I (..\User\bsp\inc\bsp_ntc.h)(0x678DDDCC)
I (..\User\bsp\inc\bsp_signal.h)(0x682ACFBF)
I (..\User\bsp\inc\bsp_beep.h)(0x6827E666)
I (..\User\bsp\inc\bsp_fifo.h)(0x682C143B)
I (..\User\bsp\inc\bsp_tim_pwm.h)(0x68143C04)
I (..\User\ui2.8\UI28.h)(0x6856620B)
I (..\User\ui2.8\UI28_auxiliary_table.h)(0x6801A019)
I (..\User\ui2.8\bsp_ili9340x_lcd.h)(0x6828483F)
I (..\User\ui2.8\UI28_font_16.h)(0x681EB98A)
I (..\User\ui2.8\UI28_font_20.h)(0x67F69774)
I (..\User\ui2.8\UI28_font_32.h)(0x681AFF0D)
I (..\User\ui2.8\UI28_font_72.h)(0x681D7617)
I (..\User\ui2.8\UI28_font_28.h)(0x681DBC62)
I (..\User\ui2.8\UI28_font_36.h)(0x681EACC0)
I (..\User\ui2.8\UI28_font_24.h)(0x682E7B21)
I (..\User\ui2.8\UI28_font_18.h)(0x681EF7D2)
I (..\User\ui2.8\UI28_font_14.h)(0x681ED798)
I (..\User\ui2.8\UI28_pic_120x120.h)(0x6857B1FC)
I (..\User\ui2.8\UI28_pic_32x32.h)(0x681D853E)
I (..\User\ui2.8\UI28_pic_22x30.h)(0x681D8CDE)
I (..\User\ui2.8\UI28_pic_14x18.h)(0x681DB278)
I (..\User\ui2.8\UI28_pic_48x88.h)(0x681DA6D8)
I (..\User\ui2.8\UI28_pic_36x36.h)(0x681DB27C)
I (..\User\ui2.8\UI28_pic_45x45.h)(0x68204BEA)
I (..\User\ui2.8\UI28_pic_40x20.h)(0x68283E6F)
I (..\User\ui2.8\UI28_pic_70x70.h)(0x682840DF)
I (..\User\ui2.8\UI28_matrix.h)(0x6857730C)
I (..\User\ui2.8\UI28_menu.h)(0x68579EAC)
I (..\User\ui2.8\UI28_rolling_album.h)(0x6854C854)
I (..\User\ui2.8\UI28_arc.h)(0x685772E6)
I (..\User\ui2.8\UI28_animation.h)(0x6829BB09)
I (..\User\ui2.8\UI28_progress_bar.h)(0x68567976)
I (..\User\pro\pro_cus_com.h)(0x685605BA)
I (..\User\pro\pro_hardware.h)(0x6829DA80)
I (..\User\pro\pro_workflow.h)(0x682D8D63)
I (..\User\pro\pro_error.h)(0x682C215A)
I (..\User\pro\pro_pagekey.h)(0x68297893)
I (..\User\page\page.h)(0x68555953)
I (..\User\page\src_inc\page_standby.h)(0x67966D02)
I (..\User\page\src_inc\page_run.h)(0x67966D08)
I (..\User\page\src_inc\page_parameter.h)(0x68579AE2)
I (..\User\page\src_inc\page_debug.h)(0x67966D0C)
I (..\User\page\src_inc\page_statistics.h)(0x682AF7F2)
I (..\User\page\src_inc\page_check.h)(0x67966D0E)
I (..\User\page\src_inc\page_developer.h)(0x67966D0C)
I (..\User\page\src_inc\page_qr_code.h)(0x681F13C4)
I (..\User\parame\parame.h)(0x68294931)
I (..\User\parame\parame_tongyong.h)(0x6857B6B2)
I (..\User\parame\flash_eeprom.h)(0x68560C9B)
F (..\User\page\src_inc\page_start.h)(0x67966D00)()
F (..\User\page\src_inc\page_statistics.c)(0x685765FE)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/page_statistics.o -MD)
I (..\User\page\src_inc\page_statistics.h)(0x682AF7F2)
I (..\User\bsp\bsp.h)(0x6826A21F)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (D:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdlib.h)(0x603507D0)
I (..\User\bsp\inc\bsp_timer.h)(0x680C5798)
I (..\User\bsp\inc\bsp_uart_fifo.h)(0x682426F2)
I (..\User\bsp\inc\bsp_boot.h)(0x6832B3D5)
I (..\User\bsp\inc\bsp_key.h)(0x68576AFA)
I (..\User\bsp\inc\bsp_crc.h)(0x682C3545)
I (..\User\bsp\inc\bsp_utility.h)(0x68107F3A)
I (..\User\bsp\inc\bsp_temp.h)(0x681322C8)
I (..\User\bsp\inc\bsp_ntc.h)(0x678DDDCC)
I (..\User\bsp\inc\bsp_signal.h)(0x682ACFBF)
I (..\User\bsp\inc\bsp_beep.h)(0x6827E666)
I (..\User\bsp\inc\bsp_fifo.h)(0x682C143B)
I (..\User\bsp\inc\bsp_tim_pwm.h)(0x68143C04)
I (..\User\ui2.8\UI28.h)(0x6856620B)
I (..\User\ui2.8\UI28_auxiliary_table.h)(0x6801A019)
I (..\User\ui2.8\bsp_ili9340x_lcd.h)(0x6828483F)
I (..\User\ui2.8\UI28_font_16.h)(0x681EB98A)
I (..\User\ui2.8\UI28_font_20.h)(0x67F69774)
I (..\User\ui2.8\UI28_font_32.h)(0x681AFF0D)
I (..\User\ui2.8\UI28_font_72.h)(0x681D7617)
I (..\User\ui2.8\UI28_font_28.h)(0x681DBC62)
I (..\User\ui2.8\UI28_font_36.h)(0x681EACC0)
I (..\User\ui2.8\UI28_font_24.h)(0x682E7B21)
I (..\User\ui2.8\UI28_font_18.h)(0x681EF7D2)
I (..\User\ui2.8\UI28_font_14.h)(0x681ED798)
I (..\User\ui2.8\UI28_pic_120x120.h)(0x6857B1FC)
I (..\User\ui2.8\UI28_pic_32x32.h)(0x681D853E)
I (..\User\ui2.8\UI28_pic_22x30.h)(0x681D8CDE)
I (..\User\ui2.8\UI28_pic_14x18.h)(0x681DB278)
I (..\User\ui2.8\UI28_pic_48x88.h)(0x681DA6D8)
I (..\User\ui2.8\UI28_pic_36x36.h)(0x681DB27C)
I (..\User\ui2.8\UI28_pic_45x45.h)(0x68204BEA)
I (..\User\ui2.8\UI28_pic_40x20.h)(0x68283E6F)
I (..\User\ui2.8\UI28_pic_70x70.h)(0x682840DF)
I (..\User\ui2.8\UI28_matrix.h)(0x6857730C)
I (..\User\ui2.8\UI28_menu.h)(0x68579EAC)
I (..\User\ui2.8\UI28_rolling_album.h)(0x6854C854)
I (..\User\ui2.8\UI28_arc.h)(0x685772E6)
I (..\User\ui2.8\UI28_animation.h)(0x6829BB09)
I (..\User\ui2.8\UI28_progress_bar.h)(0x68567976)
I (..\User\pro\pro_cus_com.h)(0x685605BA)
I (..\User\pro\pro_hardware.h)(0x6829DA80)
I (..\User\pro\pro_workflow.h)(0x682D8D63)
I (..\User\pro\pro_error.h)(0x682C215A)
I (..\User\pro\pro_pagekey.h)(0x68297893)
I (..\User\page\page.h)(0x68555953)
I (..\User\page\src_inc\page_start.h)(0x67966D00)
I (..\User\page\src_inc\page_standby.h)(0x67966D02)
I (..\User\page\src_inc\page_run.h)(0x67966D08)
I (..\User\page\src_inc\page_parameter.h)(0x68579AE2)
I (..\User\page\src_inc\page_debug.h)(0x67966D0C)
I (..\User\page\src_inc\page_check.h)(0x67966D0E)
I (..\User\page\src_inc\page_developer.h)(0x67966D0C)
I (..\User\page\src_inc\page_qr_code.h)(0x681F13C4)
I (..\User\parame\parame.h)(0x68294931)
I (..\User\parame\parame_tongyong.h)(0x6857B6B2)
I (..\User\parame\flash_eeprom.h)(0x68560C9B)
F (..\User\page\src_inc\page_statistics.h)(0x682AF7F2)()
F (..\User\page\src_inc\page_qr_code.c)(0x68561443)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/page_qr_code.o -MD)
I (..\User\page\src_inc\page_qr_code.h)(0x681F13C4)
I (..\User\bsp\bsp.h)(0x6826A21F)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (D:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdlib.h)(0x603507D0)
I (..\User\bsp\inc\bsp_timer.h)(0x680C5798)
I (..\User\bsp\inc\bsp_uart_fifo.h)(0x682426F2)
I (..\User\bsp\inc\bsp_boot.h)(0x6832B3D5)
I (..\User\bsp\inc\bsp_key.h)(0x68576AFA)
I (..\User\bsp\inc\bsp_crc.h)(0x682C3545)
I (..\User\bsp\inc\bsp_utility.h)(0x68107F3A)
I (..\User\bsp\inc\bsp_temp.h)(0x681322C8)
I (..\User\bsp\inc\bsp_ntc.h)(0x678DDDCC)
I (..\User\bsp\inc\bsp_signal.h)(0x682ACFBF)
I (..\User\bsp\inc\bsp_beep.h)(0x6827E666)
I (..\User\bsp\inc\bsp_fifo.h)(0x682C143B)
I (..\User\bsp\inc\bsp_tim_pwm.h)(0x68143C04)
I (..\User\ui2.8\UI28.h)(0x6856620B)
I (..\User\ui2.8\UI28_auxiliary_table.h)(0x6801A019)
I (..\User\ui2.8\bsp_ili9340x_lcd.h)(0x6828483F)
I (..\User\ui2.8\UI28_font_16.h)(0x681EB98A)
I (..\User\ui2.8\UI28_font_20.h)(0x67F69774)
I (..\User\ui2.8\UI28_font_32.h)(0x681AFF0D)
I (..\User\ui2.8\UI28_font_72.h)(0x681D7617)
I (..\User\ui2.8\UI28_font_28.h)(0x681DBC62)
I (..\User\ui2.8\UI28_font_36.h)(0x681EACC0)
I (..\User\ui2.8\UI28_font_24.h)(0x682E7B21)
I (..\User\ui2.8\UI28_font_18.h)(0x681EF7D2)
I (..\User\ui2.8\UI28_font_14.h)(0x681ED798)
I (..\User\ui2.8\UI28_pic_120x120.h)(0x6857B1FC)
I (..\User\ui2.8\UI28_pic_32x32.h)(0x681D853E)
I (..\User\ui2.8\UI28_pic_22x30.h)(0x681D8CDE)
I (..\User\ui2.8\UI28_pic_14x18.h)(0x681DB278)
I (..\User\ui2.8\UI28_pic_48x88.h)(0x681DA6D8)
I (..\User\ui2.8\UI28_pic_36x36.h)(0x681DB27C)
I (..\User\ui2.8\UI28_pic_45x45.h)(0x68204BEA)
I (..\User\ui2.8\UI28_pic_40x20.h)(0x68283E6F)
I (..\User\ui2.8\UI28_pic_70x70.h)(0x682840DF)
I (..\User\ui2.8\UI28_matrix.h)(0x6857730C)
I (..\User\ui2.8\UI28_menu.h)(0x68579EAC)
I (..\User\ui2.8\UI28_rolling_album.h)(0x6854C854)
I (..\User\ui2.8\UI28_arc.h)(0x685772E6)
I (..\User\ui2.8\UI28_animation.h)(0x6829BB09)
I (..\User\ui2.8\UI28_progress_bar.h)(0x68567976)
I (..\User\pro\pro_cus_com.h)(0x685605BA)
I (..\User\pro\pro_hardware.h)(0x6829DA80)
I (..\User\pro\pro_workflow.h)(0x682D8D63)
I (..\User\pro\pro_error.h)(0x682C215A)
I (..\User\pro\pro_pagekey.h)(0x68297893)
I (..\User\page\page.h)(0x68555953)
I (..\User\page\src_inc\page_start.h)(0x67966D00)
I (..\User\page\src_inc\page_standby.h)(0x67966D02)
I (..\User\page\src_inc\page_run.h)(0x67966D08)
I (..\User\page\src_inc\page_parameter.h)(0x68579AE2)
I (..\User\page\src_inc\page_debug.h)(0x67966D0C)
I (..\User\page\src_inc\page_statistics.h)(0x682AF7F2)
I (..\User\page\src_inc\page_check.h)(0x67966D0E)
I (..\User\page\src_inc\page_developer.h)(0x67966D0C)
I (..\User\parame\parame.h)(0x68294931)
I (..\User\parame\parame_tongyong.h)(0x6857B6B2)
I (..\User\parame\flash_eeprom.h)(0x68560C9B)
F (..\User\page\src_inc\page_qr_code.h)(0x681F13C4)()
F (..\User\parame\parame.c)(0x68297F17)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/parame.o -MD)
I (..\User\bsp\bsp.h)(0x6826A21F)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (D:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdlib.h)(0x603507D0)
I (..\User\bsp\inc\bsp_timer.h)(0x680C5798)
I (..\User\bsp\inc\bsp_uart_fifo.h)(0x682426F2)
I (..\User\bsp\inc\bsp_boot.h)(0x6832B3D5)
I (..\User\bsp\inc\bsp_key.h)(0x68576AFA)
I (..\User\bsp\inc\bsp_crc.h)(0x682C3545)
I (..\User\bsp\inc\bsp_utility.h)(0x68107F3A)
I (..\User\bsp\inc\bsp_temp.h)(0x681322C8)
I (..\User\bsp\inc\bsp_ntc.h)(0x678DDDCC)
I (..\User\bsp\inc\bsp_signal.h)(0x682ACFBF)
I (..\User\bsp\inc\bsp_beep.h)(0x6827E666)
I (..\User\bsp\inc\bsp_fifo.h)(0x682C143B)
I (..\User\bsp\inc\bsp_tim_pwm.h)(0x68143C04)
I (..\User\ui2.8\UI28.h)(0x6856620B)
I (..\User\ui2.8\UI28_auxiliary_table.h)(0x6801A019)
I (..\User\ui2.8\bsp_ili9340x_lcd.h)(0x6828483F)
I (..\User\ui2.8\UI28_font_16.h)(0x681EB98A)
I (..\User\ui2.8\UI28_font_20.h)(0x67F69774)
I (..\User\ui2.8\UI28_font_32.h)(0x681AFF0D)
I (..\User\ui2.8\UI28_font_72.h)(0x681D7617)
I (..\User\ui2.8\UI28_font_28.h)(0x681DBC62)
I (..\User\ui2.8\UI28_font_36.h)(0x681EACC0)
I (..\User\ui2.8\UI28_font_24.h)(0x682E7B21)
I (..\User\ui2.8\UI28_font_18.h)(0x681EF7D2)
I (..\User\ui2.8\UI28_font_14.h)(0x681ED798)
I (..\User\ui2.8\UI28_pic_120x120.h)(0x6857B1FC)
I (..\User\ui2.8\UI28_pic_32x32.h)(0x681D853E)
I (..\User\ui2.8\UI28_pic_22x30.h)(0x681D8CDE)
I (..\User\ui2.8\UI28_pic_14x18.h)(0x681DB278)
I (..\User\ui2.8\UI28_pic_48x88.h)(0x681DA6D8)
I (..\User\ui2.8\UI28_pic_36x36.h)(0x681DB27C)
I (..\User\ui2.8\UI28_pic_45x45.h)(0x68204BEA)
I (..\User\ui2.8\UI28_pic_40x20.h)(0x68283E6F)
I (..\User\ui2.8\UI28_pic_70x70.h)(0x682840DF)
I (..\User\ui2.8\UI28_matrix.h)(0x6857730C)
I (..\User\ui2.8\UI28_menu.h)(0x68579EAC)
I (..\User\ui2.8\UI28_rolling_album.h)(0x6854C854)
I (..\User\ui2.8\UI28_arc.h)(0x685772E6)
I (..\User\ui2.8\UI28_animation.h)(0x6829BB09)
I (..\User\ui2.8\UI28_progress_bar.h)(0x68567976)
I (..\User\pro\pro_cus_com.h)(0x685605BA)
I (..\User\pro\pro_hardware.h)(0x6829DA80)
I (..\User\pro\pro_workflow.h)(0x682D8D63)
I (..\User\pro\pro_error.h)(0x682C215A)
I (..\User\pro\pro_pagekey.h)(0x68297893)
I (..\User\page\page.h)(0x68555953)
I (..\User\page\src_inc\page_start.h)(0x67966D00)
I (..\User\page\src_inc\page_standby.h)(0x67966D02)
I (..\User\page\src_inc\page_run.h)(0x67966D08)
I (..\User\page\src_inc\page_parameter.h)(0x68579AE2)
I (..\User\page\src_inc\page_debug.h)(0x67966D0C)
I (..\User\page\src_inc\page_statistics.h)(0x682AF7F2)
I (..\User\page\src_inc\page_check.h)(0x67966D0E)
I (..\User\page\src_inc\page_developer.h)(0x67966D0C)
I (..\User\page\src_inc\page_qr_code.h)(0x681F13C4)
I (..\User\parame\parame.h)(0x68294931)
I (..\User\parame\parame_tongyong.h)(0x6857B6B2)
I (..\User\parame\flash_eeprom.h)(0x68560C9B)
F (..\User\parame\parame.h)(0x68294931)()
F (..\User\parame\parame_tongyong.h)(0x6857B6B2)()
F (..\User\parame\flash_eeprom.c)(0x68561CFD)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/flash_eeprom.o -MD)
I (..\User\bsp\bsp.h)(0x6826A21F)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (D:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdlib.h)(0x603507D0)
I (..\User\bsp\inc\bsp_timer.h)(0x680C5798)
I (..\User\bsp\inc\bsp_uart_fifo.h)(0x682426F2)
I (..\User\bsp\inc\bsp_boot.h)(0x6832B3D5)
I (..\User\bsp\inc\bsp_key.h)(0x68576AFA)
I (..\User\bsp\inc\bsp_crc.h)(0x682C3545)
I (..\User\bsp\inc\bsp_utility.h)(0x68107F3A)
I (..\User\bsp\inc\bsp_temp.h)(0x681322C8)
I (..\User\bsp\inc\bsp_ntc.h)(0x678DDDCC)
I (..\User\bsp\inc\bsp_signal.h)(0x682ACFBF)
I (..\User\bsp\inc\bsp_beep.h)(0x6827E666)
I (..\User\bsp\inc\bsp_fifo.h)(0x682C143B)
I (..\User\bsp\inc\bsp_tim_pwm.h)(0x68143C04)
I (..\User\ui2.8\UI28.h)(0x6856620B)
I (..\User\ui2.8\UI28_auxiliary_table.h)(0x6801A019)
I (..\User\ui2.8\bsp_ili9340x_lcd.h)(0x6828483F)
I (..\User\ui2.8\UI28_font_16.h)(0x681EB98A)
I (..\User\ui2.8\UI28_font_20.h)(0x67F69774)
I (..\User\ui2.8\UI28_font_32.h)(0x681AFF0D)
I (..\User\ui2.8\UI28_font_72.h)(0x681D7617)
I (..\User\ui2.8\UI28_font_28.h)(0x681DBC62)
I (..\User\ui2.8\UI28_font_36.h)(0x681EACC0)
I (..\User\ui2.8\UI28_font_24.h)(0x682E7B21)
I (..\User\ui2.8\UI28_font_18.h)(0x681EF7D2)
I (..\User\ui2.8\UI28_font_14.h)(0x681ED798)
I (..\User\ui2.8\UI28_pic_120x120.h)(0x6857B1FC)
I (..\User\ui2.8\UI28_pic_32x32.h)(0x681D853E)
I (..\User\ui2.8\UI28_pic_22x30.h)(0x681D8CDE)
I (..\User\ui2.8\UI28_pic_14x18.h)(0x681DB278)
I (..\User\ui2.8\UI28_pic_48x88.h)(0x681DA6D8)
I (..\User\ui2.8\UI28_pic_36x36.h)(0x681DB27C)
I (..\User\ui2.8\UI28_pic_45x45.h)(0x68204BEA)
I (..\User\ui2.8\UI28_pic_40x20.h)(0x68283E6F)
I (..\User\ui2.8\UI28_pic_70x70.h)(0x682840DF)
I (..\User\ui2.8\UI28_matrix.h)(0x6857730C)
I (..\User\ui2.8\UI28_menu.h)(0x68579EAC)
I (..\User\ui2.8\UI28_rolling_album.h)(0x6854C854)
I (..\User\ui2.8\UI28_arc.h)(0x685772E6)
I (..\User\ui2.8\UI28_animation.h)(0x6829BB09)
I (..\User\ui2.8\UI28_progress_bar.h)(0x68567976)
I (..\User\pro\pro_cus_com.h)(0x685605BA)
I (..\User\pro\pro_hardware.h)(0x6829DA80)
I (..\User\pro\pro_workflow.h)(0x682D8D63)
I (..\User\pro\pro_error.h)(0x682C215A)
I (..\User\pro\pro_pagekey.h)(0x68297893)
I (..\User\page\page.h)(0x68555953)
I (..\User\page\src_inc\page_start.h)(0x67966D00)
I (..\User\page\src_inc\page_standby.h)(0x67966D02)
I (..\User\page\src_inc\page_run.h)(0x67966D08)
I (..\User\page\src_inc\page_parameter.h)(0x68579AE2)
I (..\User\page\src_inc\page_debug.h)(0x67966D0C)
I (..\User\page\src_inc\page_statistics.h)(0x682AF7F2)
I (..\User\page\src_inc\page_check.h)(0x67966D0E)
I (..\User\page\src_inc\page_developer.h)(0x67966D0C)
I (..\User\page\src_inc\page_qr_code.h)(0x681F13C4)
I (..\User\parame\parame.h)(0x68294931)
I (..\User\parame\parame_tongyong.h)(0x6857B6B2)
I (..\User\parame\flash_eeprom.h)(0x68560C9B)
F (..\User\parame\flash_eeprom.h)(0x68560C9B)()
F (..\User\ui2.8\UI28.c)(0x68577115)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/ui28.o -MD)
I (..\User\ui2.8\UI28.h)(0x6856620B)
I (..\User\ui2.8\UI28_auxiliary_table.h)(0x6801A019)
I (..\User\ui2.8\bsp_ili9340x_lcd.h)(0x6828483F)
I (..\User\bsp\bsp.h)(0x6826A21F)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (D:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdlib.h)(0x603507D0)
I (..\User\bsp\inc\bsp_timer.h)(0x680C5798)
I (..\User\bsp\inc\bsp_uart_fifo.h)(0x682426F2)
I (..\User\bsp\inc\bsp_boot.h)(0x6832B3D5)
I (..\User\bsp\inc\bsp_key.h)(0x68576AFA)
I (..\User\bsp\inc\bsp_crc.h)(0x682C3545)
I (..\User\bsp\inc\bsp_utility.h)(0x68107F3A)
I (..\User\bsp\inc\bsp_temp.h)(0x681322C8)
I (..\User\bsp\inc\bsp_ntc.h)(0x678DDDCC)
I (..\User\bsp\inc\bsp_signal.h)(0x682ACFBF)
I (..\User\bsp\inc\bsp_beep.h)(0x6827E666)
I (..\User\bsp\inc\bsp_fifo.h)(0x682C143B)
I (..\User\bsp\inc\bsp_tim_pwm.h)(0x68143C04)
I (..\User\pro\pro_cus_com.h)(0x685605BA)
I (..\User\pro\pro_hardware.h)(0x6829DA80)
I (..\User\pro\pro_workflow.h)(0x682D8D63)
I (..\User\pro\pro_error.h)(0x682C215A)
I (..\User\pro\pro_pagekey.h)(0x68297893)
I (..\User\page\page.h)(0x68555953)
I (..\User\page\src_inc\page_start.h)(0x67966D00)
I (..\User\page\src_inc\page_standby.h)(0x67966D02)
I (..\User\page\src_inc\page_run.h)(0x67966D08)
I (..\User\page\src_inc\page_parameter.h)(0x68579AE2)
I (..\User\page\src_inc\page_debug.h)(0x67966D0C)
I (..\User\page\src_inc\page_statistics.h)(0x682AF7F2)
I (..\User\page\src_inc\page_check.h)(0x67966D0E)
I (..\User\page\src_inc\page_developer.h)(0x67966D0C)
I (..\User\page\src_inc\page_qr_code.h)(0x681F13C4)
I (..\User\parame\parame.h)(0x68294931)
I (..\User\parame\parame_tongyong.h)(0x6857B6B2)
I (..\User\parame\flash_eeprom.h)(0x68560C9B)
I (..\User\ui2.8\UI28_font_16.h)(0x681EB98A)
I (..\User\ui2.8\UI28_font_20.h)(0x67F69774)
I (..\User\ui2.8\UI28_font_32.h)(0x681AFF0D)
I (..\User\ui2.8\UI28_font_72.h)(0x681D7617)
I (..\User\ui2.8\UI28_font_28.h)(0x681DBC62)
I (..\User\ui2.8\UI28_font_36.h)(0x681EACC0)
I (..\User\ui2.8\UI28_font_24.h)(0x682E7B21)
I (..\User\ui2.8\UI28_font_18.h)(0x681EF7D2)
I (..\User\ui2.8\UI28_font_14.h)(0x681ED798)
I (..\User\ui2.8\UI28_pic_120x120.h)(0x6857B1FC)
I (..\User\ui2.8\UI28_pic_32x32.h)(0x681D853E)
I (..\User\ui2.8\UI28_pic_22x30.h)(0x681D8CDE)
I (..\User\ui2.8\UI28_pic_14x18.h)(0x681DB278)
I (..\User\ui2.8\UI28_pic_48x88.h)(0x681DA6D8)
I (..\User\ui2.8\UI28_pic_36x36.h)(0x681DB27C)
I (..\User\ui2.8\UI28_pic_45x45.h)(0x68204BEA)
I (..\User\ui2.8\UI28_pic_40x20.h)(0x68283E6F)
I (..\User\ui2.8\UI28_pic_70x70.h)(0x682840DF)
I (..\User\ui2.8\UI28_matrix.h)(0x6857730C)
I (..\User\ui2.8\UI28_menu.h)(0x68579EAC)
I (..\User\ui2.8\UI28_rolling_album.h)(0x6854C854)
I (..\User\ui2.8\UI28_arc.h)(0x685772E6)
I (..\User\ui2.8\UI28_animation.h)(0x6829BB09)
I (..\User\ui2.8\UI28_progress_bar.h)(0x68567976)
F (..\User\ui2.8\UI28.h)(0x6856620B)()
F (..\User\ui2.8\bsp_ili9340x_lcd.c)(0x681D677C)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/bsp_ili9340x_lcd.o -MD)
I (..\User\bsp\bsp.h)(0x6826A21F)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (D:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdlib.h)(0x603507D0)
I (..\User\bsp\inc\bsp_timer.h)(0x680C5798)
I (..\User\bsp\inc\bsp_uart_fifo.h)(0x682426F2)
I (..\User\bsp\inc\bsp_boot.h)(0x6832B3D5)
I (..\User\bsp\inc\bsp_key.h)(0x68576AFA)
I (..\User\bsp\inc\bsp_crc.h)(0x682C3545)
I (..\User\bsp\inc\bsp_utility.h)(0x68107F3A)
I (..\User\bsp\inc\bsp_temp.h)(0x681322C8)
I (..\User\bsp\inc\bsp_ntc.h)(0x678DDDCC)
I (..\User\bsp\inc\bsp_signal.h)(0x682ACFBF)
I (..\User\bsp\inc\bsp_beep.h)(0x6827E666)
I (..\User\bsp\inc\bsp_fifo.h)(0x682C143B)
I (..\User\bsp\inc\bsp_tim_pwm.h)(0x68143C04)
I (..\User\ui2.8\UI28.h)(0x6856620B)
I (..\User\ui2.8\UI28_auxiliary_table.h)(0x6801A019)
I (..\User\ui2.8\bsp_ili9340x_lcd.h)(0x6828483F)
I (..\User\ui2.8\UI28_font_16.h)(0x681EB98A)
I (..\User\ui2.8\UI28_font_20.h)(0x67F69774)
I (..\User\ui2.8\UI28_font_32.h)(0x681AFF0D)
I (..\User\ui2.8\UI28_font_72.h)(0x681D7617)
I (..\User\ui2.8\UI28_font_28.h)(0x681DBC62)
I (..\User\ui2.8\UI28_font_36.h)(0x681EACC0)
I (..\User\ui2.8\UI28_font_24.h)(0x682E7B21)
I (..\User\ui2.8\UI28_font_18.h)(0x681EF7D2)
I (..\User\ui2.8\UI28_font_14.h)(0x681ED798)
I (..\User\ui2.8\UI28_pic_120x120.h)(0x6857B1FC)
I (..\User\ui2.8\UI28_pic_32x32.h)(0x681D853E)
I (..\User\ui2.8\UI28_pic_22x30.h)(0x681D8CDE)
I (..\User\ui2.8\UI28_pic_14x18.h)(0x681DB278)
I (..\User\ui2.8\UI28_pic_48x88.h)(0x681DA6D8)
I (..\User\ui2.8\UI28_pic_36x36.h)(0x681DB27C)
I (..\User\ui2.8\UI28_pic_45x45.h)(0x68204BEA)
I (..\User\ui2.8\UI28_pic_40x20.h)(0x68283E6F)
I (..\User\ui2.8\UI28_pic_70x70.h)(0x682840DF)
I (..\User\ui2.8\UI28_matrix.h)(0x6857730C)
I (..\User\ui2.8\UI28_menu.h)(0x68579EAC)
I (..\User\ui2.8\UI28_rolling_album.h)(0x6854C854)
I (..\User\ui2.8\UI28_arc.h)(0x685772E6)
I (..\User\ui2.8\UI28_animation.h)(0x6829BB09)
I (..\User\ui2.8\UI28_progress_bar.h)(0x68567976)
I (..\User\pro\pro_cus_com.h)(0x685605BA)
I (..\User\pro\pro_hardware.h)(0x6829DA80)
I (..\User\pro\pro_workflow.h)(0x682D8D63)
I (..\User\pro\pro_error.h)(0x682C215A)
I (..\User\pro\pro_pagekey.h)(0x68297893)
I (..\User\page\page.h)(0x68555953)
I (..\User\page\src_inc\page_start.h)(0x67966D00)
I (..\User\page\src_inc\page_standby.h)(0x67966D02)
I (..\User\page\src_inc\page_run.h)(0x67966D08)
I (..\User\page\src_inc\page_parameter.h)(0x68579AE2)
I (..\User\page\src_inc\page_debug.h)(0x67966D0C)
I (..\User\page\src_inc\page_statistics.h)(0x682AF7F2)
I (..\User\page\src_inc\page_check.h)(0x67966D0E)
I (..\User\page\src_inc\page_developer.h)(0x67966D0C)
I (..\User\page\src_inc\page_qr_code.h)(0x681F13C4)
I (..\User\parame\parame.h)(0x68294931)
I (..\User\parame\parame_tongyong.h)(0x6857B6B2)
I (..\User\parame\flash_eeprom.h)(0x68560C9B)
F (..\User\ui2.8\bsp_ili9340x_lcd.h)(0x6828483F)()
F (..\User\ui2.8\UI28_font_14.c)(0x681EDCFB)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/ui28_font_14.o -MD)
I (..\User\ui2.8\UI28.h)(0x6856620B)
I (..\User\ui2.8\UI28_auxiliary_table.h)(0x6801A019)
I (..\User\ui2.8\bsp_ili9340x_lcd.h)(0x6828483F)
I (..\User\bsp\bsp.h)(0x6826A21F)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (D:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdlib.h)(0x603507D0)
I (..\User\bsp\inc\bsp_timer.h)(0x680C5798)
I (..\User\bsp\inc\bsp_uart_fifo.h)(0x682426F2)
I (..\User\bsp\inc\bsp_boot.h)(0x6832B3D5)
I (..\User\bsp\inc\bsp_key.h)(0x68576AFA)
I (..\User\bsp\inc\bsp_crc.h)(0x682C3545)
I (..\User\bsp\inc\bsp_utility.h)(0x68107F3A)
I (..\User\bsp\inc\bsp_temp.h)(0x681322C8)
I (..\User\bsp\inc\bsp_ntc.h)(0x678DDDCC)
I (..\User\bsp\inc\bsp_signal.h)(0x682ACFBF)
I (..\User\bsp\inc\bsp_beep.h)(0x6827E666)
I (..\User\bsp\inc\bsp_fifo.h)(0x682C143B)
I (..\User\bsp\inc\bsp_tim_pwm.h)(0x68143C04)
I (..\User\pro\pro_cus_com.h)(0x685605BA)
I (..\User\pro\pro_hardware.h)(0x6829DA80)
I (..\User\pro\pro_workflow.h)(0x682D8D63)
I (..\User\pro\pro_error.h)(0x682C215A)
I (..\User\pro\pro_pagekey.h)(0x68297893)
I (..\User\page\page.h)(0x68555953)
I (..\User\page\src_inc\page_start.h)(0x67966D00)
I (..\User\page\src_inc\page_standby.h)(0x67966D02)
I (..\User\page\src_inc\page_run.h)(0x67966D08)
I (..\User\page\src_inc\page_parameter.h)(0x68579AE2)
I (..\User\page\src_inc\page_debug.h)(0x67966D0C)
I (..\User\page\src_inc\page_statistics.h)(0x682AF7F2)
I (..\User\page\src_inc\page_check.h)(0x67966D0E)
I (..\User\page\src_inc\page_developer.h)(0x67966D0C)
I (..\User\page\src_inc\page_qr_code.h)(0x681F13C4)
I (..\User\parame\parame.h)(0x68294931)
I (..\User\parame\parame_tongyong.h)(0x6857B6B2)
I (..\User\parame\flash_eeprom.h)(0x68560C9B)
I (..\User\ui2.8\UI28_font_16.h)(0x681EB98A)
I (..\User\ui2.8\UI28_font_20.h)(0x67F69774)
I (..\User\ui2.8\UI28_font_32.h)(0x681AFF0D)
I (..\User\ui2.8\UI28_font_72.h)(0x681D7617)
I (..\User\ui2.8\UI28_font_28.h)(0x681DBC62)
I (..\User\ui2.8\UI28_font_36.h)(0x681EACC0)
I (..\User\ui2.8\UI28_font_24.h)(0x682E7B21)
I (..\User\ui2.8\UI28_font_18.h)(0x681EF7D2)
I (..\User\ui2.8\UI28_font_14.h)(0x681ED798)
I (..\User\ui2.8\UI28_pic_120x120.h)(0x6857B1FC)
I (..\User\ui2.8\UI28_pic_32x32.h)(0x681D853E)
I (..\User\ui2.8\UI28_pic_22x30.h)(0x681D8CDE)
I (..\User\ui2.8\UI28_pic_14x18.h)(0x681DB278)
I (..\User\ui2.8\UI28_pic_48x88.h)(0x681DA6D8)
I (..\User\ui2.8\UI28_pic_36x36.h)(0x681DB27C)
I (..\User\ui2.8\UI28_pic_45x45.h)(0x68204BEA)
I (..\User\ui2.8\UI28_pic_40x20.h)(0x68283E6F)
I (..\User\ui2.8\UI28_pic_70x70.h)(0x682840DF)
I (..\User\ui2.8\UI28_matrix.h)(0x6857730C)
I (..\User\ui2.8\UI28_menu.h)(0x68579EAC)
I (..\User\ui2.8\UI28_rolling_album.h)(0x6854C854)
I (..\User\ui2.8\UI28_arc.h)(0x685772E6)
I (..\User\ui2.8\UI28_animation.h)(0x6829BB09)
I (..\User\ui2.8\UI28_progress_bar.h)(0x68567976)
F (..\User\ui2.8\UI28_font_16.c)(0x68576A52)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/ui28_font_16.o -MD)
I (..\User\ui2.8\UI28.h)(0x6856620B)
I (..\User\ui2.8\UI28_auxiliary_table.h)(0x6801A019)
I (..\User\ui2.8\bsp_ili9340x_lcd.h)(0x6828483F)
I (..\User\bsp\bsp.h)(0x6826A21F)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (D:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdlib.h)(0x603507D0)
I (..\User\bsp\inc\bsp_timer.h)(0x680C5798)
I (..\User\bsp\inc\bsp_uart_fifo.h)(0x682426F2)
I (..\User\bsp\inc\bsp_boot.h)(0x6832B3D5)
I (..\User\bsp\inc\bsp_key.h)(0x68576AFA)
I (..\User\bsp\inc\bsp_crc.h)(0x682C3545)
I (..\User\bsp\inc\bsp_utility.h)(0x68107F3A)
I (..\User\bsp\inc\bsp_temp.h)(0x681322C8)
I (..\User\bsp\inc\bsp_ntc.h)(0x678DDDCC)
I (..\User\bsp\inc\bsp_signal.h)(0x682ACFBF)
I (..\User\bsp\inc\bsp_beep.h)(0x6827E666)
I (..\User\bsp\inc\bsp_fifo.h)(0x682C143B)
I (..\User\bsp\inc\bsp_tim_pwm.h)(0x68143C04)
I (..\User\pro\pro_cus_com.h)(0x685605BA)
I (..\User\pro\pro_hardware.h)(0x6829DA80)
I (..\User\pro\pro_workflow.h)(0x682D8D63)
I (..\User\pro\pro_error.h)(0x682C215A)
I (..\User\pro\pro_pagekey.h)(0x68297893)
I (..\User\page\page.h)(0x68555953)
I (..\User\page\src_inc\page_start.h)(0x67966D00)
I (..\User\page\src_inc\page_standby.h)(0x67966D02)
I (..\User\page\src_inc\page_run.h)(0x67966D08)
I (..\User\page\src_inc\page_parameter.h)(0x68579AE2)
I (..\User\page\src_inc\page_debug.h)(0x67966D0C)
I (..\User\page\src_inc\page_statistics.h)(0x682AF7F2)
I (..\User\page\src_inc\page_check.h)(0x67966D0E)
I (..\User\page\src_inc\page_developer.h)(0x67966D0C)
I (..\User\page\src_inc\page_qr_code.h)(0x681F13C4)
I (..\User\parame\parame.h)(0x68294931)
I (..\User\parame\parame_tongyong.h)(0x6857B6B2)
I (..\User\parame\flash_eeprom.h)(0x68560C9B)
I (..\User\ui2.8\UI28_font_16.h)(0x681EB98A)
I (..\User\ui2.8\UI28_font_20.h)(0x67F69774)
I (..\User\ui2.8\UI28_font_32.h)(0x681AFF0D)
I (..\User\ui2.8\UI28_font_72.h)(0x681D7617)
I (..\User\ui2.8\UI28_font_28.h)(0x681DBC62)
I (..\User\ui2.8\UI28_font_36.h)(0x681EACC0)
I (..\User\ui2.8\UI28_font_24.h)(0x682E7B21)
I (..\User\ui2.8\UI28_font_18.h)(0x681EF7D2)
I (..\User\ui2.8\UI28_font_14.h)(0x681ED798)
I (..\User\ui2.8\UI28_pic_120x120.h)(0x6857B1FC)
I (..\User\ui2.8\UI28_pic_32x32.h)(0x681D853E)
I (..\User\ui2.8\UI28_pic_22x30.h)(0x681D8CDE)
I (..\User\ui2.8\UI28_pic_14x18.h)(0x681DB278)
I (..\User\ui2.8\UI28_pic_48x88.h)(0x681DA6D8)
I (..\User\ui2.8\UI28_pic_36x36.h)(0x681DB27C)
I (..\User\ui2.8\UI28_pic_45x45.h)(0x68204BEA)
I (..\User\ui2.8\UI28_pic_40x20.h)(0x68283E6F)
I (..\User\ui2.8\UI28_pic_70x70.h)(0x682840DF)
I (..\User\ui2.8\UI28_matrix.h)(0x6857730C)
I (..\User\ui2.8\UI28_menu.h)(0x68579EAC)
I (..\User\ui2.8\UI28_rolling_album.h)(0x6854C854)
I (..\User\ui2.8\UI28_arc.h)(0x685772E6)
I (..\User\ui2.8\UI28_animation.h)(0x6829BB09)
I (..\User\ui2.8\UI28_progress_bar.h)(0x68567976)
F (..\User\ui2.8\UI28_font_18.c)(0x6857A35F)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/ui28_font_18.o -MD)
I (..\User\ui2.8\UI28.h)(0x6856620B)
I (..\User\ui2.8\UI28_auxiliary_table.h)(0x6801A019)
I (..\User\ui2.8\bsp_ili9340x_lcd.h)(0x6828483F)
I (..\User\bsp\bsp.h)(0x6826A21F)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (D:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdlib.h)(0x603507D0)
I (..\User\bsp\inc\bsp_timer.h)(0x680C5798)
I (..\User\bsp\inc\bsp_uart_fifo.h)(0x682426F2)
I (..\User\bsp\inc\bsp_boot.h)(0x6832B3D5)
I (..\User\bsp\inc\bsp_key.h)(0x68576AFA)
I (..\User\bsp\inc\bsp_crc.h)(0x682C3545)
I (..\User\bsp\inc\bsp_utility.h)(0x68107F3A)
I (..\User\bsp\inc\bsp_temp.h)(0x681322C8)
I (..\User\bsp\inc\bsp_ntc.h)(0x678DDDCC)
I (..\User\bsp\inc\bsp_signal.h)(0x682ACFBF)
I (..\User\bsp\inc\bsp_beep.h)(0x6827E666)
I (..\User\bsp\inc\bsp_fifo.h)(0x682C143B)
I (..\User\bsp\inc\bsp_tim_pwm.h)(0x68143C04)
I (..\User\pro\pro_cus_com.h)(0x685605BA)
I (..\User\pro\pro_hardware.h)(0x6829DA80)
I (..\User\pro\pro_workflow.h)(0x682D8D63)
I (..\User\pro\pro_error.h)(0x682C215A)
I (..\User\pro\pro_pagekey.h)(0x68297893)
I (..\User\page\page.h)(0x68555953)
I (..\User\page\src_inc\page_start.h)(0x67966D00)
I (..\User\page\src_inc\page_standby.h)(0x67966D02)
I (..\User\page\src_inc\page_run.h)(0x67966D08)
I (..\User\page\src_inc\page_parameter.h)(0x68579AE2)
I (..\User\page\src_inc\page_debug.h)(0x67966D0C)
I (..\User\page\src_inc\page_statistics.h)(0x682AF7F2)
I (..\User\page\src_inc\page_check.h)(0x67966D0E)
I (..\User\page\src_inc\page_developer.h)(0x67966D0C)
I (..\User\page\src_inc\page_qr_code.h)(0x681F13C4)
I (..\User\parame\parame.h)(0x68294931)
I (..\User\parame\parame_tongyong.h)(0x6857B6B2)
I (..\User\parame\flash_eeprom.h)(0x68560C9B)
I (..\User\ui2.8\UI28_font_16.h)(0x681EB98A)
I (..\User\ui2.8\UI28_font_20.h)(0x67F69774)
I (..\User\ui2.8\UI28_font_32.h)(0x681AFF0D)
I (..\User\ui2.8\UI28_font_72.h)(0x681D7617)
I (..\User\ui2.8\UI28_font_28.h)(0x681DBC62)
I (..\User\ui2.8\UI28_font_36.h)(0x681EACC0)
I (..\User\ui2.8\UI28_font_24.h)(0x682E7B21)
I (..\User\ui2.8\UI28_font_18.h)(0x681EF7D2)
I (..\User\ui2.8\UI28_font_14.h)(0x681ED798)
I (..\User\ui2.8\UI28_pic_120x120.h)(0x6857B1FC)
I (..\User\ui2.8\UI28_pic_32x32.h)(0x681D853E)
I (..\User\ui2.8\UI28_pic_22x30.h)(0x681D8CDE)
I (..\User\ui2.8\UI28_pic_14x18.h)(0x681DB278)
I (..\User\ui2.8\UI28_pic_48x88.h)(0x681DA6D8)
I (..\User\ui2.8\UI28_pic_36x36.h)(0x681DB27C)
I (..\User\ui2.8\UI28_pic_45x45.h)(0x68204BEA)
I (..\User\ui2.8\UI28_pic_40x20.h)(0x68283E6F)
I (..\User\ui2.8\UI28_pic_70x70.h)(0x682840DF)
I (..\User\ui2.8\UI28_matrix.h)(0x6857730C)
I (..\User\ui2.8\UI28_menu.h)(0x68579EAC)
I (..\User\ui2.8\UI28_rolling_album.h)(0x6854C854)
I (..\User\ui2.8\UI28_arc.h)(0x685772E6)
I (..\User\ui2.8\UI28_animation.h)(0x6829BB09)
I (..\User\ui2.8\UI28_progress_bar.h)(0x68567976)
F (..\User\ui2.8\UI28_font_20.c)(0x68576E45)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/ui28_font_20.o -MD)
I (..\User\ui2.8\UI28.h)(0x6856620B)
I (..\User\ui2.8\UI28_auxiliary_table.h)(0x6801A019)
I (..\User\ui2.8\bsp_ili9340x_lcd.h)(0x6828483F)
I (..\User\bsp\bsp.h)(0x6826A21F)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (D:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdlib.h)(0x603507D0)
I (..\User\bsp\inc\bsp_timer.h)(0x680C5798)
I (..\User\bsp\inc\bsp_uart_fifo.h)(0x682426F2)
I (..\User\bsp\inc\bsp_boot.h)(0x6832B3D5)
I (..\User\bsp\inc\bsp_key.h)(0x68576AFA)
I (..\User\bsp\inc\bsp_crc.h)(0x682C3545)
I (..\User\bsp\inc\bsp_utility.h)(0x68107F3A)
I (..\User\bsp\inc\bsp_temp.h)(0x681322C8)
I (..\User\bsp\inc\bsp_ntc.h)(0x678DDDCC)
I (..\User\bsp\inc\bsp_signal.h)(0x682ACFBF)
I (..\User\bsp\inc\bsp_beep.h)(0x6827E666)
I (..\User\bsp\inc\bsp_fifo.h)(0x682C143B)
I (..\User\bsp\inc\bsp_tim_pwm.h)(0x68143C04)
I (..\User\pro\pro_cus_com.h)(0x685605BA)
I (..\User\pro\pro_hardware.h)(0x6829DA80)
I (..\User\pro\pro_workflow.h)(0x682D8D63)
I (..\User\pro\pro_error.h)(0x682C215A)
I (..\User\pro\pro_pagekey.h)(0x68297893)
I (..\User\page\page.h)(0x68555953)
I (..\User\page\src_inc\page_start.h)(0x67966D00)
I (..\User\page\src_inc\page_standby.h)(0x67966D02)
I (..\User\page\src_inc\page_run.h)(0x67966D08)
I (..\User\page\src_inc\page_parameter.h)(0x68579AE2)
I (..\User\page\src_inc\page_debug.h)(0x67966D0C)
I (..\User\page\src_inc\page_statistics.h)(0x682AF7F2)
I (..\User\page\src_inc\page_check.h)(0x67966D0E)
I (..\User\page\src_inc\page_developer.h)(0x67966D0C)
I (..\User\page\src_inc\page_qr_code.h)(0x681F13C4)
I (..\User\parame\parame.h)(0x68294931)
I (..\User\parame\parame_tongyong.h)(0x6857B6B2)
I (..\User\parame\flash_eeprom.h)(0x68560C9B)
I (..\User\ui2.8\UI28_font_16.h)(0x681EB98A)
I (..\User\ui2.8\UI28_font_20.h)(0x67F69774)
I (..\User\ui2.8\UI28_font_32.h)(0x681AFF0D)
I (..\User\ui2.8\UI28_font_72.h)(0x681D7617)
I (..\User\ui2.8\UI28_font_28.h)(0x681DBC62)
I (..\User\ui2.8\UI28_font_36.h)(0x681EACC0)
I (..\User\ui2.8\UI28_font_24.h)(0x682E7B21)
I (..\User\ui2.8\UI28_font_18.h)(0x681EF7D2)
I (..\User\ui2.8\UI28_font_14.h)(0x681ED798)
I (..\User\ui2.8\UI28_pic_120x120.h)(0x6857B1FC)
I (..\User\ui2.8\UI28_pic_32x32.h)(0x681D853E)
I (..\User\ui2.8\UI28_pic_22x30.h)(0x681D8CDE)
I (..\User\ui2.8\UI28_pic_14x18.h)(0x681DB278)
I (..\User\ui2.8\UI28_pic_48x88.h)(0x681DA6D8)
I (..\User\ui2.8\UI28_pic_36x36.h)(0x681DB27C)
I (..\User\ui2.8\UI28_pic_45x45.h)(0x68204BEA)
I (..\User\ui2.8\UI28_pic_40x20.h)(0x68283E6F)
I (..\User\ui2.8\UI28_pic_70x70.h)(0x682840DF)
I (..\User\ui2.8\UI28_matrix.h)(0x6857730C)
I (..\User\ui2.8\UI28_menu.h)(0x68579EAC)
I (..\User\ui2.8\UI28_rolling_album.h)(0x6854C854)
I (..\User\ui2.8\UI28_arc.h)(0x685772E6)
I (..\User\ui2.8\UI28_animation.h)(0x6829BB09)
I (..\User\ui2.8\UI28_progress_bar.h)(0x68567976)
F (..\User\ui2.8\UI28_font_24.c)(0x68576695)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/ui28_font_24.o -MD)
I (..\User\ui2.8\UI28.h)(0x6856620B)
I (..\User\ui2.8\UI28_auxiliary_table.h)(0x6801A019)
I (..\User\ui2.8\bsp_ili9340x_lcd.h)(0x6828483F)
I (..\User\bsp\bsp.h)(0x6826A21F)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (D:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdlib.h)(0x603507D0)
I (..\User\bsp\inc\bsp_timer.h)(0x680C5798)
I (..\User\bsp\inc\bsp_uart_fifo.h)(0x682426F2)
I (..\User\bsp\inc\bsp_boot.h)(0x6832B3D5)
I (..\User\bsp\inc\bsp_key.h)(0x68576AFA)
I (..\User\bsp\inc\bsp_crc.h)(0x682C3545)
I (..\User\bsp\inc\bsp_utility.h)(0x68107F3A)
I (..\User\bsp\inc\bsp_temp.h)(0x681322C8)
I (..\User\bsp\inc\bsp_ntc.h)(0x678DDDCC)
I (..\User\bsp\inc\bsp_signal.h)(0x682ACFBF)
I (..\User\bsp\inc\bsp_beep.h)(0x6827E666)
I (..\User\bsp\inc\bsp_fifo.h)(0x682C143B)
I (..\User\bsp\inc\bsp_tim_pwm.h)(0x68143C04)
I (..\User\pro\pro_cus_com.h)(0x685605BA)
I (..\User\pro\pro_hardware.h)(0x6829DA80)
I (..\User\pro\pro_workflow.h)(0x682D8D63)
I (..\User\pro\pro_error.h)(0x682C215A)
I (..\User\pro\pro_pagekey.h)(0x68297893)
I (..\User\page\page.h)(0x68555953)
I (..\User\page\src_inc\page_start.h)(0x67966D00)
I (..\User\page\src_inc\page_standby.h)(0x67966D02)
I (..\User\page\src_inc\page_run.h)(0x67966D08)
I (..\User\page\src_inc\page_parameter.h)(0x68579AE2)
I (..\User\page\src_inc\page_debug.h)(0x67966D0C)
I (..\User\page\src_inc\page_statistics.h)(0x682AF7F2)
I (..\User\page\src_inc\page_check.h)(0x67966D0E)
I (..\User\page\src_inc\page_developer.h)(0x67966D0C)
I (..\User\page\src_inc\page_qr_code.h)(0x681F13C4)
I (..\User\parame\parame.h)(0x68294931)
I (..\User\parame\parame_tongyong.h)(0x6857B6B2)
I (..\User\parame\flash_eeprom.h)(0x68560C9B)
I (..\User\ui2.8\UI28_font_16.h)(0x681EB98A)
I (..\User\ui2.8\UI28_font_20.h)(0x67F69774)
I (..\User\ui2.8\UI28_font_32.h)(0x681AFF0D)
I (..\User\ui2.8\UI28_font_72.h)(0x681D7617)
I (..\User\ui2.8\UI28_font_28.h)(0x681DBC62)
I (..\User\ui2.8\UI28_font_36.h)(0x681EACC0)
I (..\User\ui2.8\UI28_font_24.h)(0x682E7B21)
I (..\User\ui2.8\UI28_font_18.h)(0x681EF7D2)
I (..\User\ui2.8\UI28_font_14.h)(0x681ED798)
I (..\User\ui2.8\UI28_pic_120x120.h)(0x6857B1FC)
I (..\User\ui2.8\UI28_pic_32x32.h)(0x681D853E)
I (..\User\ui2.8\UI28_pic_22x30.h)(0x681D8CDE)
I (..\User\ui2.8\UI28_pic_14x18.h)(0x681DB278)
I (..\User\ui2.8\UI28_pic_48x88.h)(0x681DA6D8)
I (..\User\ui2.8\UI28_pic_36x36.h)(0x681DB27C)
I (..\User\ui2.8\UI28_pic_45x45.h)(0x68204BEA)
I (..\User\ui2.8\UI28_pic_40x20.h)(0x68283E6F)
I (..\User\ui2.8\UI28_pic_70x70.h)(0x682840DF)
I (..\User\ui2.8\UI28_matrix.h)(0x6857730C)
I (..\User\ui2.8\UI28_menu.h)(0x68579EAC)
I (..\User\ui2.8\UI28_rolling_album.h)(0x6854C854)
I (..\User\ui2.8\UI28_arc.h)(0x685772E6)
I (..\User\ui2.8\UI28_animation.h)(0x6829BB09)
I (..\User\ui2.8\UI28_progress_bar.h)(0x68567976)
F (..\User\ui2.8\UI28_font_28.c)(0x681E1E38)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/ui28_font_28.o -MD)
I (..\User\ui2.8\UI28.h)(0x6856620B)
I (..\User\ui2.8\UI28_auxiliary_table.h)(0x6801A019)
I (..\User\ui2.8\bsp_ili9340x_lcd.h)(0x6828483F)
I (..\User\bsp\bsp.h)(0x6826A21F)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (D:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdlib.h)(0x603507D0)
I (..\User\bsp\inc\bsp_timer.h)(0x680C5798)
I (..\User\bsp\inc\bsp_uart_fifo.h)(0x682426F2)
I (..\User\bsp\inc\bsp_boot.h)(0x6832B3D5)
I (..\User\bsp\inc\bsp_key.h)(0x68576AFA)
I (..\User\bsp\inc\bsp_crc.h)(0x682C3545)
I (..\User\bsp\inc\bsp_utility.h)(0x68107F3A)
I (..\User\bsp\inc\bsp_temp.h)(0x681322C8)
I (..\User\bsp\inc\bsp_ntc.h)(0x678DDDCC)
I (..\User\bsp\inc\bsp_signal.h)(0x682ACFBF)
I (..\User\bsp\inc\bsp_beep.h)(0x6827E666)
I (..\User\bsp\inc\bsp_fifo.h)(0x682C143B)
I (..\User\bsp\inc\bsp_tim_pwm.h)(0x68143C04)
I (..\User\pro\pro_cus_com.h)(0x685605BA)
I (..\User\pro\pro_hardware.h)(0x6829DA80)
I (..\User\pro\pro_workflow.h)(0x682D8D63)
I (..\User\pro\pro_error.h)(0x682C215A)
I (..\User\pro\pro_pagekey.h)(0x68297893)
I (..\User\page\page.h)(0x68555953)
I (..\User\page\src_inc\page_start.h)(0x67966D00)
I (..\User\page\src_inc\page_standby.h)(0x67966D02)
I (..\User\page\src_inc\page_run.h)(0x67966D08)
I (..\User\page\src_inc\page_parameter.h)(0x68579AE2)
I (..\User\page\src_inc\page_debug.h)(0x67966D0C)
I (..\User\page\src_inc\page_statistics.h)(0x682AF7F2)
I (..\User\page\src_inc\page_check.h)(0x67966D0E)
I (..\User\page\src_inc\page_developer.h)(0x67966D0C)
I (..\User\page\src_inc\page_qr_code.h)(0x681F13C4)
I (..\User\parame\parame.h)(0x68294931)
I (..\User\parame\parame_tongyong.h)(0x6857B6B2)
I (..\User\parame\flash_eeprom.h)(0x68560C9B)
I (..\User\ui2.8\UI28_font_16.h)(0x681EB98A)
I (..\User\ui2.8\UI28_font_20.h)(0x67F69774)
I (..\User\ui2.8\UI28_font_32.h)(0x681AFF0D)
I (..\User\ui2.8\UI28_font_72.h)(0x681D7617)
I (..\User\ui2.8\UI28_font_28.h)(0x681DBC62)
I (..\User\ui2.8\UI28_font_36.h)(0x681EACC0)
I (..\User\ui2.8\UI28_font_24.h)(0x682E7B21)
I (..\User\ui2.8\UI28_font_18.h)(0x681EF7D2)
I (..\User\ui2.8\UI28_font_14.h)(0x681ED798)
I (..\User\ui2.8\UI28_pic_120x120.h)(0x6857B1FC)
I (..\User\ui2.8\UI28_pic_32x32.h)(0x681D853E)
I (..\User\ui2.8\UI28_pic_22x30.h)(0x681D8CDE)
I (..\User\ui2.8\UI28_pic_14x18.h)(0x681DB278)
I (..\User\ui2.8\UI28_pic_48x88.h)(0x681DA6D8)
I (..\User\ui2.8\UI28_pic_36x36.h)(0x681DB27C)
I (..\User\ui2.8\UI28_pic_45x45.h)(0x68204BEA)
I (..\User\ui2.8\UI28_pic_40x20.h)(0x68283E6F)
I (..\User\ui2.8\UI28_pic_70x70.h)(0x682840DF)
I (..\User\ui2.8\UI28_matrix.h)(0x6857730C)
I (..\User\ui2.8\UI28_menu.h)(0x68579EAC)
I (..\User\ui2.8\UI28_rolling_album.h)(0x6854C854)
I (..\User\ui2.8\UI28_arc.h)(0x685772E6)
I (..\User\ui2.8\UI28_animation.h)(0x6829BB09)
I (..\User\ui2.8\UI28_progress_bar.h)(0x68567976)
F (..\User\ui2.8\UI28_font_32.c)(0x685767BB)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/ui28_font_32.o -MD)
I (..\User\ui2.8\UI28.h)(0x6856620B)
I (..\User\ui2.8\UI28_auxiliary_table.h)(0x6801A019)
I (..\User\ui2.8\bsp_ili9340x_lcd.h)(0x6828483F)
I (..\User\bsp\bsp.h)(0x6826A21F)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (D:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdlib.h)(0x603507D0)
I (..\User\bsp\inc\bsp_timer.h)(0x680C5798)
I (..\User\bsp\inc\bsp_uart_fifo.h)(0x682426F2)
I (..\User\bsp\inc\bsp_boot.h)(0x6832B3D5)
I (..\User\bsp\inc\bsp_key.h)(0x68576AFA)
I (..\User\bsp\inc\bsp_crc.h)(0x682C3545)
I (..\User\bsp\inc\bsp_utility.h)(0x68107F3A)
I (..\User\bsp\inc\bsp_temp.h)(0x681322C8)
I (..\User\bsp\inc\bsp_ntc.h)(0x678DDDCC)
I (..\User\bsp\inc\bsp_signal.h)(0x682ACFBF)
I (..\User\bsp\inc\bsp_beep.h)(0x6827E666)
I (..\User\bsp\inc\bsp_fifo.h)(0x682C143B)
I (..\User\bsp\inc\bsp_tim_pwm.h)(0x68143C04)
I (..\User\pro\pro_cus_com.h)(0x685605BA)
I (..\User\pro\pro_hardware.h)(0x6829DA80)
I (..\User\pro\pro_workflow.h)(0x682D8D63)
I (..\User\pro\pro_error.h)(0x682C215A)
I (..\User\pro\pro_pagekey.h)(0x68297893)
I (..\User\page\page.h)(0x68555953)
I (..\User\page\src_inc\page_start.h)(0x67966D00)
I (..\User\page\src_inc\page_standby.h)(0x67966D02)
I (..\User\page\src_inc\page_run.h)(0x67966D08)
I (..\User\page\src_inc\page_parameter.h)(0x68579AE2)
I (..\User\page\src_inc\page_debug.h)(0x67966D0C)
I (..\User\page\src_inc\page_statistics.h)(0x682AF7F2)
I (..\User\page\src_inc\page_check.h)(0x67966D0E)
I (..\User\page\src_inc\page_developer.h)(0x67966D0C)
I (..\User\page\src_inc\page_qr_code.h)(0x681F13C4)
I (..\User\parame\parame.h)(0x68294931)
I (..\User\parame\parame_tongyong.h)(0x6857B6B2)
I (..\User\parame\flash_eeprom.h)(0x68560C9B)
I (..\User\ui2.8\UI28_font_16.h)(0x681EB98A)
I (..\User\ui2.8\UI28_font_20.h)(0x67F69774)
I (..\User\ui2.8\UI28_font_32.h)(0x681AFF0D)
I (..\User\ui2.8\UI28_font_72.h)(0x681D7617)
I (..\User\ui2.8\UI28_font_28.h)(0x681DBC62)
I (..\User\ui2.8\UI28_font_36.h)(0x681EACC0)
I (..\User\ui2.8\UI28_font_24.h)(0x682E7B21)
I (..\User\ui2.8\UI28_font_18.h)(0x681EF7D2)
I (..\User\ui2.8\UI28_font_14.h)(0x681ED798)
I (..\User\ui2.8\UI28_pic_120x120.h)(0x6857B1FC)
I (..\User\ui2.8\UI28_pic_32x32.h)(0x681D853E)
I (..\User\ui2.8\UI28_pic_22x30.h)(0x681D8CDE)
I (..\User\ui2.8\UI28_pic_14x18.h)(0x681DB278)
I (..\User\ui2.8\UI28_pic_48x88.h)(0x681DA6D8)
I (..\User\ui2.8\UI28_pic_36x36.h)(0x681DB27C)
I (..\User\ui2.8\UI28_pic_45x45.h)(0x68204BEA)
I (..\User\ui2.8\UI28_pic_40x20.h)(0x68283E6F)
I (..\User\ui2.8\UI28_pic_70x70.h)(0x682840DF)
I (..\User\ui2.8\UI28_matrix.h)(0x6857730C)
I (..\User\ui2.8\UI28_menu.h)(0x68579EAC)
I (..\User\ui2.8\UI28_rolling_album.h)(0x6854C854)
I (..\User\ui2.8\UI28_arc.h)(0x685772E6)
I (..\User\ui2.8\UI28_animation.h)(0x6829BB09)
I (..\User\ui2.8\UI28_progress_bar.h)(0x68567976)
F (..\User\ui2.8\UI28_font_36.c)(0x682C2493)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/ui28_font_36.o -MD)
I (..\User\ui2.8\UI28.h)(0x6856620B)
I (..\User\ui2.8\UI28_auxiliary_table.h)(0x6801A019)
I (..\User\ui2.8\bsp_ili9340x_lcd.h)(0x6828483F)
I (..\User\bsp\bsp.h)(0x6826A21F)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (D:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdlib.h)(0x603507D0)
I (..\User\bsp\inc\bsp_timer.h)(0x680C5798)
I (..\User\bsp\inc\bsp_uart_fifo.h)(0x682426F2)
I (..\User\bsp\inc\bsp_boot.h)(0x6832B3D5)
I (..\User\bsp\inc\bsp_key.h)(0x68576AFA)
I (..\User\bsp\inc\bsp_crc.h)(0x682C3545)
I (..\User\bsp\inc\bsp_utility.h)(0x68107F3A)
I (..\User\bsp\inc\bsp_temp.h)(0x681322C8)
I (..\User\bsp\inc\bsp_ntc.h)(0x678DDDCC)
I (..\User\bsp\inc\bsp_signal.h)(0x682ACFBF)
I (..\User\bsp\inc\bsp_beep.h)(0x6827E666)
I (..\User\bsp\inc\bsp_fifo.h)(0x682C143B)
I (..\User\bsp\inc\bsp_tim_pwm.h)(0x68143C04)
I (..\User\pro\pro_cus_com.h)(0x685605BA)
I (..\User\pro\pro_hardware.h)(0x6829DA80)
I (..\User\pro\pro_workflow.h)(0x682D8D63)
I (..\User\pro\pro_error.h)(0x682C215A)
I (..\User\pro\pro_pagekey.h)(0x68297893)
I (..\User\page\page.h)(0x68555953)
I (..\User\page\src_inc\page_start.h)(0x67966D00)
I (..\User\page\src_inc\page_standby.h)(0x67966D02)
I (..\User\page\src_inc\page_run.h)(0x67966D08)
I (..\User\page\src_inc\page_parameter.h)(0x68579AE2)
I (..\User\page\src_inc\page_debug.h)(0x67966D0C)
I (..\User\page\src_inc\page_statistics.h)(0x682AF7F2)
I (..\User\page\src_inc\page_check.h)(0x67966D0E)
I (..\User\page\src_inc\page_developer.h)(0x67966D0C)
I (..\User\page\src_inc\page_qr_code.h)(0x681F13C4)
I (..\User\parame\parame.h)(0x68294931)
I (..\User\parame\parame_tongyong.h)(0x6857B6B2)
I (..\User\parame\flash_eeprom.h)(0x68560C9B)
I (..\User\ui2.8\UI28_font_16.h)(0x681EB98A)
I (..\User\ui2.8\UI28_font_20.h)(0x67F69774)
I (..\User\ui2.8\UI28_font_32.h)(0x681AFF0D)
I (..\User\ui2.8\UI28_font_72.h)(0x681D7617)
I (..\User\ui2.8\UI28_font_28.h)(0x681DBC62)
I (..\User\ui2.8\UI28_font_36.h)(0x681EACC0)
I (..\User\ui2.8\UI28_font_24.h)(0x682E7B21)
I (..\User\ui2.8\UI28_font_18.h)(0x681EF7D2)
I (..\User\ui2.8\UI28_font_14.h)(0x681ED798)
I (..\User\ui2.8\UI28_pic_120x120.h)(0x6857B1FC)
I (..\User\ui2.8\UI28_pic_32x32.h)(0x681D853E)
I (..\User\ui2.8\UI28_pic_22x30.h)(0x681D8CDE)
I (..\User\ui2.8\UI28_pic_14x18.h)(0x681DB278)
I (..\User\ui2.8\UI28_pic_48x88.h)(0x681DA6D8)
I (..\User\ui2.8\UI28_pic_36x36.h)(0x681DB27C)
I (..\User\ui2.8\UI28_pic_45x45.h)(0x68204BEA)
I (..\User\ui2.8\UI28_pic_40x20.h)(0x68283E6F)
I (..\User\ui2.8\UI28_pic_70x70.h)(0x682840DF)
I (..\User\ui2.8\UI28_matrix.h)(0x6857730C)
I (..\User\ui2.8\UI28_menu.h)(0x68579EAC)
I (..\User\ui2.8\UI28_rolling_album.h)(0x6854C854)
I (..\User\ui2.8\UI28_arc.h)(0x685772E6)
I (..\User\ui2.8\UI28_animation.h)(0x6829BB09)
I (..\User\ui2.8\UI28_progress_bar.h)(0x68567976)
F (..\User\ui2.8\UI28_font_72.c)(0x682D2ABC)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/ui28_font_72.o -MD)
I (..\User\ui2.8\UI28.h)(0x6856620B)
I (..\User\ui2.8\UI28_auxiliary_table.h)(0x6801A019)
I (..\User\ui2.8\bsp_ili9340x_lcd.h)(0x6828483F)
I (..\User\bsp\bsp.h)(0x6826A21F)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (D:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdlib.h)(0x603507D0)
I (..\User\bsp\inc\bsp_timer.h)(0x680C5798)
I (..\User\bsp\inc\bsp_uart_fifo.h)(0x682426F2)
I (..\User\bsp\inc\bsp_boot.h)(0x6832B3D5)
I (..\User\bsp\inc\bsp_key.h)(0x68576AFA)
I (..\User\bsp\inc\bsp_crc.h)(0x682C3545)
I (..\User\bsp\inc\bsp_utility.h)(0x68107F3A)
I (..\User\bsp\inc\bsp_temp.h)(0x681322C8)
I (..\User\bsp\inc\bsp_ntc.h)(0x678DDDCC)
I (..\User\bsp\inc\bsp_signal.h)(0x682ACFBF)
I (..\User\bsp\inc\bsp_beep.h)(0x6827E666)
I (..\User\bsp\inc\bsp_fifo.h)(0x682C143B)
I (..\User\bsp\inc\bsp_tim_pwm.h)(0x68143C04)
I (..\User\pro\pro_cus_com.h)(0x685605BA)
I (..\User\pro\pro_hardware.h)(0x6829DA80)
I (..\User\pro\pro_workflow.h)(0x682D8D63)
I (..\User\pro\pro_error.h)(0x682C215A)
I (..\User\pro\pro_pagekey.h)(0x68297893)
I (..\User\page\page.h)(0x68555953)
I (..\User\page\src_inc\page_start.h)(0x67966D00)
I (..\User\page\src_inc\page_standby.h)(0x67966D02)
I (..\User\page\src_inc\page_run.h)(0x67966D08)
I (..\User\page\src_inc\page_parameter.h)(0x68579AE2)
I (..\User\page\src_inc\page_debug.h)(0x67966D0C)
I (..\User\page\src_inc\page_statistics.h)(0x682AF7F2)
I (..\User\page\src_inc\page_check.h)(0x67966D0E)
I (..\User\page\src_inc\page_developer.h)(0x67966D0C)
I (..\User\page\src_inc\page_qr_code.h)(0x681F13C4)
I (..\User\parame\parame.h)(0x68294931)
I (..\User\parame\parame_tongyong.h)(0x6857B6B2)
I (..\User\parame\flash_eeprom.h)(0x68560C9B)
I (..\User\ui2.8\UI28_font_16.h)(0x681EB98A)
I (..\User\ui2.8\UI28_font_20.h)(0x67F69774)
I (..\User\ui2.8\UI28_font_32.h)(0x681AFF0D)
I (..\User\ui2.8\UI28_font_72.h)(0x681D7617)
I (..\User\ui2.8\UI28_font_28.h)(0x681DBC62)
I (..\User\ui2.8\UI28_font_36.h)(0x681EACC0)
I (..\User\ui2.8\UI28_font_24.h)(0x682E7B21)
I (..\User\ui2.8\UI28_font_18.h)(0x681EF7D2)
I (..\User\ui2.8\UI28_font_14.h)(0x681ED798)
I (..\User\ui2.8\UI28_pic_120x120.h)(0x6857B1FC)
I (..\User\ui2.8\UI28_pic_32x32.h)(0x681D853E)
I (..\User\ui2.8\UI28_pic_22x30.h)(0x681D8CDE)
I (..\User\ui2.8\UI28_pic_14x18.h)(0x681DB278)
I (..\User\ui2.8\UI28_pic_48x88.h)(0x681DA6D8)
I (..\User\ui2.8\UI28_pic_36x36.h)(0x681DB27C)
I (..\User\ui2.8\UI28_pic_45x45.h)(0x68204BEA)
I (..\User\ui2.8\UI28_pic_40x20.h)(0x68283E6F)
I (..\User\ui2.8\UI28_pic_70x70.h)(0x682840DF)
I (..\User\ui2.8\UI28_matrix.h)(0x6857730C)
I (..\User\ui2.8\UI28_menu.h)(0x68579EAC)
I (..\User\ui2.8\UI28_rolling_album.h)(0x6854C854)
I (..\User\ui2.8\UI28_arc.h)(0x685772E6)
I (..\User\ui2.8\UI28_animation.h)(0x6829BB09)
I (..\User\ui2.8\UI28_progress_bar.h)(0x68567976)
F (..\User\ui2.8\UI28_matrix.c)(0x682D55AE)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/ui28_matrix.o -MD)
I (..\User\ui2.8\UI28_matrix.h)(0x6857730C)
I (..\User\ui2.8\UI28.h)(0x6856620B)
I (..\User\ui2.8\UI28_auxiliary_table.h)(0x6801A019)
I (..\User\ui2.8\bsp_ili9340x_lcd.h)(0x6828483F)
I (..\User\bsp\bsp.h)(0x6826A21F)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (D:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdlib.h)(0x603507D0)
I (..\User\bsp\inc\bsp_timer.h)(0x680C5798)
I (..\User\bsp\inc\bsp_uart_fifo.h)(0x682426F2)
I (..\User\bsp\inc\bsp_boot.h)(0x6832B3D5)
I (..\User\bsp\inc\bsp_key.h)(0x68576AFA)
I (..\User\bsp\inc\bsp_crc.h)(0x682C3545)
I (..\User\bsp\inc\bsp_utility.h)(0x68107F3A)
I (..\User\bsp\inc\bsp_temp.h)(0x681322C8)
I (..\User\bsp\inc\bsp_ntc.h)(0x678DDDCC)
I (..\User\bsp\inc\bsp_signal.h)(0x682ACFBF)
I (..\User\bsp\inc\bsp_beep.h)(0x6827E666)
I (..\User\bsp\inc\bsp_fifo.h)(0x682C143B)
I (..\User\bsp\inc\bsp_tim_pwm.h)(0x68143C04)
I (..\User\pro\pro_cus_com.h)(0x685605BA)
I (..\User\pro\pro_hardware.h)(0x6829DA80)
I (..\User\pro\pro_workflow.h)(0x682D8D63)
I (..\User\pro\pro_error.h)(0x682C215A)
I (..\User\pro\pro_pagekey.h)(0x68297893)
I (..\User\page\page.h)(0x68555953)
I (..\User\page\src_inc\page_start.h)(0x67966D00)
I (..\User\page\src_inc\page_standby.h)(0x67966D02)
I (..\User\page\src_inc\page_run.h)(0x67966D08)
I (..\User\page\src_inc\page_parameter.h)(0x68579AE2)
I (..\User\page\src_inc\page_debug.h)(0x67966D0C)
I (..\User\page\src_inc\page_statistics.h)(0x682AF7F2)
I (..\User\page\src_inc\page_check.h)(0x67966D0E)
I (..\User\page\src_inc\page_developer.h)(0x67966D0C)
I (..\User\page\src_inc\page_qr_code.h)(0x681F13C4)
I (..\User\parame\parame.h)(0x68294931)
I (..\User\parame\parame_tongyong.h)(0x6857B6B2)
I (..\User\parame\flash_eeprom.h)(0x68560C9B)
I (..\User\ui2.8\UI28_font_16.h)(0x681EB98A)
I (..\User\ui2.8\UI28_font_20.h)(0x67F69774)
I (..\User\ui2.8\UI28_font_32.h)(0x681AFF0D)
I (..\User\ui2.8\UI28_font_72.h)(0x681D7617)
I (..\User\ui2.8\UI28_font_28.h)(0x681DBC62)
I (..\User\ui2.8\UI28_font_36.h)(0x681EACC0)
I (..\User\ui2.8\UI28_font_24.h)(0x682E7B21)
I (..\User\ui2.8\UI28_font_18.h)(0x681EF7D2)
I (..\User\ui2.8\UI28_font_14.h)(0x681ED798)
I (..\User\ui2.8\UI28_pic_120x120.h)(0x6857B1FC)
I (..\User\ui2.8\UI28_pic_32x32.h)(0x681D853E)
I (..\User\ui2.8\UI28_pic_22x30.h)(0x681D8CDE)
I (..\User\ui2.8\UI28_pic_14x18.h)(0x681DB278)
I (..\User\ui2.8\UI28_pic_48x88.h)(0x681DA6D8)
I (..\User\ui2.8\UI28_pic_36x36.h)(0x681DB27C)
I (..\User\ui2.8\UI28_pic_45x45.h)(0x68204BEA)
I (..\User\ui2.8\UI28_pic_40x20.h)(0x68283E6F)
I (..\User\ui2.8\UI28_pic_70x70.h)(0x682840DF)
I (..\User\ui2.8\UI28_menu.h)(0x68579EAC)
I (..\User\ui2.8\UI28_rolling_album.h)(0x6854C854)
I (..\User\ui2.8\UI28_arc.h)(0x685772E6)
I (..\User\ui2.8\UI28_animation.h)(0x6829BB09)
I (..\User\ui2.8\UI28_progress_bar.h)(0x68567976)
F (..\User\ui2.8\UI28_menu.c)(0x68566D50)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/ui28_menu.o -MD)
I (..\User\ui2.8\UI28_menu.h)(0x68579EAC)
I (..\User\ui2.8\UI28.h)(0x6856620B)
I (..\User\ui2.8\UI28_auxiliary_table.h)(0x6801A019)
I (..\User\ui2.8\bsp_ili9340x_lcd.h)(0x6828483F)
I (..\User\bsp\bsp.h)(0x6826A21F)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (D:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdlib.h)(0x603507D0)
I (..\User\bsp\inc\bsp_timer.h)(0x680C5798)
I (..\User\bsp\inc\bsp_uart_fifo.h)(0x682426F2)
I (..\User\bsp\inc\bsp_boot.h)(0x6832B3D5)
I (..\User\bsp\inc\bsp_key.h)(0x68576AFA)
I (..\User\bsp\inc\bsp_crc.h)(0x682C3545)
I (..\User\bsp\inc\bsp_utility.h)(0x68107F3A)
I (..\User\bsp\inc\bsp_temp.h)(0x681322C8)
I (..\User\bsp\inc\bsp_ntc.h)(0x678DDDCC)
I (..\User\bsp\inc\bsp_signal.h)(0x682ACFBF)
I (..\User\bsp\inc\bsp_beep.h)(0x6827E666)
I (..\User\bsp\inc\bsp_fifo.h)(0x682C143B)
I (..\User\bsp\inc\bsp_tim_pwm.h)(0x68143C04)
I (..\User\pro\pro_cus_com.h)(0x685605BA)
I (..\User\pro\pro_hardware.h)(0x6829DA80)
I (..\User\pro\pro_workflow.h)(0x682D8D63)
I (..\User\pro\pro_error.h)(0x682C215A)
I (..\User\pro\pro_pagekey.h)(0x68297893)
I (..\User\page\page.h)(0x68555953)
I (..\User\page\src_inc\page_start.h)(0x67966D00)
I (..\User\page\src_inc\page_standby.h)(0x67966D02)
I (..\User\page\src_inc\page_run.h)(0x67966D08)
I (..\User\page\src_inc\page_parameter.h)(0x68579AE2)
I (..\User\page\src_inc\page_debug.h)(0x67966D0C)
I (..\User\page\src_inc\page_statistics.h)(0x682AF7F2)
I (..\User\page\src_inc\page_check.h)(0x67966D0E)
I (..\User\page\src_inc\page_developer.h)(0x67966D0C)
I (..\User\page\src_inc\page_qr_code.h)(0x681F13C4)
I (..\User\parame\parame.h)(0x68294931)
I (..\User\parame\parame_tongyong.h)(0x6857B6B2)
I (..\User\parame\flash_eeprom.h)(0x68560C9B)
I (..\User\ui2.8\UI28_font_16.h)(0x681EB98A)
I (..\User\ui2.8\UI28_font_20.h)(0x67F69774)
I (..\User\ui2.8\UI28_font_32.h)(0x681AFF0D)
I (..\User\ui2.8\UI28_font_72.h)(0x681D7617)
I (..\User\ui2.8\UI28_font_28.h)(0x681DBC62)
I (..\User\ui2.8\UI28_font_36.h)(0x681EACC0)
I (..\User\ui2.8\UI28_font_24.h)(0x682E7B21)
I (..\User\ui2.8\UI28_font_18.h)(0x681EF7D2)
I (..\User\ui2.8\UI28_font_14.h)(0x681ED798)
I (..\User\ui2.8\UI28_pic_120x120.h)(0x6857B1FC)
I (..\User\ui2.8\UI28_pic_32x32.h)(0x681D853E)
I (..\User\ui2.8\UI28_pic_22x30.h)(0x681D8CDE)
I (..\User\ui2.8\UI28_pic_14x18.h)(0x681DB278)
I (..\User\ui2.8\UI28_pic_48x88.h)(0x681DA6D8)
I (..\User\ui2.8\UI28_pic_36x36.h)(0x681DB27C)
I (..\User\ui2.8\UI28_pic_45x45.h)(0x68204BEA)
I (..\User\ui2.8\UI28_pic_40x20.h)(0x68283E6F)
I (..\User\ui2.8\UI28_pic_70x70.h)(0x682840DF)
I (..\User\ui2.8\UI28_matrix.h)(0x6857730C)
I (..\User\ui2.8\UI28_rolling_album.h)(0x6854C854)
I (..\User\ui2.8\UI28_arc.h)(0x685772E6)
I (..\User\ui2.8\UI28_animation.h)(0x6829BB09)
I (..\User\ui2.8\UI28_progress_bar.h)(0x68567976)
F (..\User\ui2.8\UI28_auxiliary_table.c)(0x67F235AA)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/ui28_auxiliary_table.o -MD)
I (..\User\ui2.8\UI28_auxiliary_table.h)(0x6801A019)
F (..\User\ui2.8\UI28_rolling_album.c)(0x682D260C)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/ui28_rolling_album.o -MD)
I (..\User\ui2.8\UI28_rolling_album.h)(0x6854C854)
I (..\User\ui2.8\UI28.h)(0x6856620B)
I (..\User\ui2.8\UI28_auxiliary_table.h)(0x6801A019)
I (..\User\ui2.8\bsp_ili9340x_lcd.h)(0x6828483F)
I (..\User\bsp\bsp.h)(0x6826A21F)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (D:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdlib.h)(0x603507D0)
I (..\User\bsp\inc\bsp_timer.h)(0x680C5798)
I (..\User\bsp\inc\bsp_uart_fifo.h)(0x682426F2)
I (..\User\bsp\inc\bsp_boot.h)(0x6832B3D5)
I (..\User\bsp\inc\bsp_key.h)(0x68576AFA)
I (..\User\bsp\inc\bsp_crc.h)(0x682C3545)
I (..\User\bsp\inc\bsp_utility.h)(0x68107F3A)
I (..\User\bsp\inc\bsp_temp.h)(0x681322C8)
I (..\User\bsp\inc\bsp_ntc.h)(0x678DDDCC)
I (..\User\bsp\inc\bsp_signal.h)(0x682ACFBF)
I (..\User\bsp\inc\bsp_beep.h)(0x6827E666)
I (..\User\bsp\inc\bsp_fifo.h)(0x682C143B)
I (..\User\bsp\inc\bsp_tim_pwm.h)(0x68143C04)
I (..\User\pro\pro_cus_com.h)(0x685605BA)
I (..\User\pro\pro_hardware.h)(0x6829DA80)
I (..\User\pro\pro_workflow.h)(0x682D8D63)
I (..\User\pro\pro_error.h)(0x682C215A)
I (..\User\pro\pro_pagekey.h)(0x68297893)
I (..\User\page\page.h)(0x68555953)
I (..\User\page\src_inc\page_start.h)(0x67966D00)
I (..\User\page\src_inc\page_standby.h)(0x67966D02)
I (..\User\page\src_inc\page_run.h)(0x67966D08)
I (..\User\page\src_inc\page_parameter.h)(0x68579AE2)
I (..\User\page\src_inc\page_debug.h)(0x67966D0C)
I (..\User\page\src_inc\page_statistics.h)(0x682AF7F2)
I (..\User\page\src_inc\page_check.h)(0x67966D0E)
I (..\User\page\src_inc\page_developer.h)(0x67966D0C)
I (..\User\page\src_inc\page_qr_code.h)(0x681F13C4)
I (..\User\parame\parame.h)(0x68294931)
I (..\User\parame\parame_tongyong.h)(0x6857B6B2)
I (..\User\parame\flash_eeprom.h)(0x68560C9B)
I (..\User\ui2.8\UI28_font_16.h)(0x681EB98A)
I (..\User\ui2.8\UI28_font_20.h)(0x67F69774)
I (..\User\ui2.8\UI28_font_32.h)(0x681AFF0D)
I (..\User\ui2.8\UI28_font_72.h)(0x681D7617)
I (..\User\ui2.8\UI28_font_28.h)(0x681DBC62)
I (..\User\ui2.8\UI28_font_36.h)(0x681EACC0)
I (..\User\ui2.8\UI28_font_24.h)(0x682E7B21)
I (..\User\ui2.8\UI28_font_18.h)(0x681EF7D2)
I (..\User\ui2.8\UI28_font_14.h)(0x681ED798)
I (..\User\ui2.8\UI28_pic_120x120.h)(0x6857B1FC)
I (..\User\ui2.8\UI28_pic_32x32.h)(0x681D853E)
I (..\User\ui2.8\UI28_pic_22x30.h)(0x681D8CDE)
I (..\User\ui2.8\UI28_pic_14x18.h)(0x681DB278)
I (..\User\ui2.8\UI28_pic_48x88.h)(0x681DA6D8)
I (..\User\ui2.8\UI28_pic_36x36.h)(0x681DB27C)
I (..\User\ui2.8\UI28_pic_45x45.h)(0x68204BEA)
I (..\User\ui2.8\UI28_pic_40x20.h)(0x68283E6F)
I (..\User\ui2.8\UI28_pic_70x70.h)(0x682840DF)
I (..\User\ui2.8\UI28_matrix.h)(0x6857730C)
I (..\User\ui2.8\UI28_menu.h)(0x68579EAC)
I (..\User\ui2.8\UI28_arc.h)(0x685772E6)
I (..\User\ui2.8\UI28_animation.h)(0x6829BB09)
I (..\User\ui2.8\UI28_progress_bar.h)(0x68567976)
F (..\User\ui2.8\UI28_arc.c)(0x6821B43E)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/ui28_arc.o -MD)
I (..\User\ui2.8\UI28_arc.h)(0x685772E6)
I (..\User\ui2.8\UI28.h)(0x6856620B)
I (..\User\ui2.8\UI28_auxiliary_table.h)(0x6801A019)
I (..\User\ui2.8\bsp_ili9340x_lcd.h)(0x6828483F)
I (..\User\bsp\bsp.h)(0x6826A21F)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (D:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdlib.h)(0x603507D0)
I (..\User\bsp\inc\bsp_timer.h)(0x680C5798)
I (..\User\bsp\inc\bsp_uart_fifo.h)(0x682426F2)
I (..\User\bsp\inc\bsp_boot.h)(0x6832B3D5)
I (..\User\bsp\inc\bsp_key.h)(0x68576AFA)
I (..\User\bsp\inc\bsp_crc.h)(0x682C3545)
I (..\User\bsp\inc\bsp_utility.h)(0x68107F3A)
I (..\User\bsp\inc\bsp_temp.h)(0x681322C8)
I (..\User\bsp\inc\bsp_ntc.h)(0x678DDDCC)
I (..\User\bsp\inc\bsp_signal.h)(0x682ACFBF)
I (..\User\bsp\inc\bsp_beep.h)(0x6827E666)
I (..\User\bsp\inc\bsp_fifo.h)(0x682C143B)
I (..\User\bsp\inc\bsp_tim_pwm.h)(0x68143C04)
I (..\User\pro\pro_cus_com.h)(0x685605BA)
I (..\User\pro\pro_hardware.h)(0x6829DA80)
I (..\User\pro\pro_workflow.h)(0x682D8D63)
I (..\User\pro\pro_error.h)(0x682C215A)
I (..\User\pro\pro_pagekey.h)(0x68297893)
I (..\User\page\page.h)(0x68555953)
I (..\User\page\src_inc\page_start.h)(0x67966D00)
I (..\User\page\src_inc\page_standby.h)(0x67966D02)
I (..\User\page\src_inc\page_run.h)(0x67966D08)
I (..\User\page\src_inc\page_parameter.h)(0x68579AE2)
I (..\User\page\src_inc\page_debug.h)(0x67966D0C)
I (..\User\page\src_inc\page_statistics.h)(0x682AF7F2)
I (..\User\page\src_inc\page_check.h)(0x67966D0E)
I (..\User\page\src_inc\page_developer.h)(0x67966D0C)
I (..\User\page\src_inc\page_qr_code.h)(0x681F13C4)
I (..\User\parame\parame.h)(0x68294931)
I (..\User\parame\parame_tongyong.h)(0x6857B6B2)
I (..\User\parame\flash_eeprom.h)(0x68560C9B)
I (..\User\ui2.8\UI28_font_16.h)(0x681EB98A)
I (..\User\ui2.8\UI28_font_20.h)(0x67F69774)
I (..\User\ui2.8\UI28_font_32.h)(0x681AFF0D)
I (..\User\ui2.8\UI28_font_72.h)(0x681D7617)
I (..\User\ui2.8\UI28_font_28.h)(0x681DBC62)
I (..\User\ui2.8\UI28_font_36.h)(0x681EACC0)
I (..\User\ui2.8\UI28_font_24.h)(0x682E7B21)
I (..\User\ui2.8\UI28_font_18.h)(0x681EF7D2)
I (..\User\ui2.8\UI28_font_14.h)(0x681ED798)
I (..\User\ui2.8\UI28_pic_120x120.h)(0x6857B1FC)
I (..\User\ui2.8\UI28_pic_32x32.h)(0x681D853E)
I (..\User\ui2.8\UI28_pic_22x30.h)(0x681D8CDE)
I (..\User\ui2.8\UI28_pic_14x18.h)(0x681DB278)
I (..\User\ui2.8\UI28_pic_48x88.h)(0x681DA6D8)
I (..\User\ui2.8\UI28_pic_36x36.h)(0x681DB27C)
I (..\User\ui2.8\UI28_pic_45x45.h)(0x68204BEA)
I (..\User\ui2.8\UI28_pic_40x20.h)(0x68283E6F)
I (..\User\ui2.8\UI28_pic_70x70.h)(0x682840DF)
I (..\User\ui2.8\UI28_matrix.h)(0x6857730C)
I (..\User\ui2.8\UI28_menu.h)(0x68579EAC)
I (..\User\ui2.8\UI28_rolling_album.h)(0x6854C854)
I (..\User\ui2.8\UI28_animation.h)(0x6829BB09)
I (..\User\ui2.8\UI28_progress_bar.h)(0x68567976)
F (..\User\ui2.8\UI28_pic_120x120.c)(0x6857BE2B)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/ui28_pic_120x120.o -MD)
I (..\User\ui2.8\UI28.h)(0x6856620B)
I (..\User\ui2.8\UI28_auxiliary_table.h)(0x6801A019)
I (..\User\ui2.8\bsp_ili9340x_lcd.h)(0x6828483F)
I (..\User\bsp\bsp.h)(0x6826A21F)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (D:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdlib.h)(0x603507D0)
I (..\User\bsp\inc\bsp_timer.h)(0x680C5798)
I (..\User\bsp\inc\bsp_uart_fifo.h)(0x682426F2)
I (..\User\bsp\inc\bsp_boot.h)(0x6832B3D5)
I (..\User\bsp\inc\bsp_key.h)(0x68576AFA)
I (..\User\bsp\inc\bsp_crc.h)(0x682C3545)
I (..\User\bsp\inc\bsp_utility.h)(0x68107F3A)
I (..\User\bsp\inc\bsp_temp.h)(0x681322C8)
I (..\User\bsp\inc\bsp_ntc.h)(0x678DDDCC)
I (..\User\bsp\inc\bsp_signal.h)(0x682ACFBF)
I (..\User\bsp\inc\bsp_beep.h)(0x6827E666)
I (..\User\bsp\inc\bsp_fifo.h)(0x682C143B)
I (..\User\bsp\inc\bsp_tim_pwm.h)(0x68143C04)
I (..\User\pro\pro_cus_com.h)(0x685605BA)
I (..\User\pro\pro_hardware.h)(0x6829DA80)
I (..\User\pro\pro_workflow.h)(0x682D8D63)
I (..\User\pro\pro_error.h)(0x682C215A)
I (..\User\pro\pro_pagekey.h)(0x68297893)
I (..\User\page\page.h)(0x68555953)
I (..\User\page\src_inc\page_start.h)(0x67966D00)
I (..\User\page\src_inc\page_standby.h)(0x67966D02)
I (..\User\page\src_inc\page_run.h)(0x67966D08)
I (..\User\page\src_inc\page_parameter.h)(0x68579AE2)
I (..\User\page\src_inc\page_debug.h)(0x67966D0C)
I (..\User\page\src_inc\page_statistics.h)(0x682AF7F2)
I (..\User\page\src_inc\page_check.h)(0x67966D0E)
I (..\User\page\src_inc\page_developer.h)(0x67966D0C)
I (..\User\page\src_inc\page_qr_code.h)(0x681F13C4)
I (..\User\parame\parame.h)(0x68294931)
I (..\User\parame\parame_tongyong.h)(0x6857B6B2)
I (..\User\parame\flash_eeprom.h)(0x68560C9B)
I (..\User\ui2.8\UI28_font_16.h)(0x681EB98A)
I (..\User\ui2.8\UI28_font_20.h)(0x67F69774)
I (..\User\ui2.8\UI28_font_32.h)(0x681AFF0D)
I (..\User\ui2.8\UI28_font_72.h)(0x681D7617)
I (..\User\ui2.8\UI28_font_28.h)(0x681DBC62)
I (..\User\ui2.8\UI28_font_36.h)(0x681EACC0)
I (..\User\ui2.8\UI28_font_24.h)(0x682E7B21)
I (..\User\ui2.8\UI28_font_18.h)(0x681EF7D2)
I (..\User\ui2.8\UI28_font_14.h)(0x681ED798)
I (..\User\ui2.8\UI28_pic_120x120.h)(0x6857B1FC)
I (..\User\ui2.8\UI28_pic_32x32.h)(0x681D853E)
I (..\User\ui2.8\UI28_pic_22x30.h)(0x681D8CDE)
I (..\User\ui2.8\UI28_pic_14x18.h)(0x681DB278)
I (..\User\ui2.8\UI28_pic_48x88.h)(0x681DA6D8)
I (..\User\ui2.8\UI28_pic_36x36.h)(0x681DB27C)
I (..\User\ui2.8\UI28_pic_45x45.h)(0x68204BEA)
I (..\User\ui2.8\UI28_pic_40x20.h)(0x68283E6F)
I (..\User\ui2.8\UI28_pic_70x70.h)(0x682840DF)
I (..\User\ui2.8\UI28_matrix.h)(0x6857730C)
I (..\User\ui2.8\UI28_menu.h)(0x68579EAC)
I (..\User\ui2.8\UI28_rolling_album.h)(0x6854C854)
I (..\User\ui2.8\UI28_arc.h)(0x685772E6)
I (..\User\ui2.8\UI28_animation.h)(0x6829BB09)
I (..\User\ui2.8\UI28_progress_bar.h)(0x68567976)
F (..\User\ui2.8\UI28_pic_32x32.c)(0x685774FE)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/ui28_pic_32x32.o -MD)
I (..\User\ui2.8\UI28.h)(0x6856620B)
I (..\User\ui2.8\UI28_auxiliary_table.h)(0x6801A019)
I (..\User\ui2.8\bsp_ili9340x_lcd.h)(0x6828483F)
I (..\User\bsp\bsp.h)(0x6826A21F)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (D:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdlib.h)(0x603507D0)
I (..\User\bsp\inc\bsp_timer.h)(0x680C5798)
I (..\User\bsp\inc\bsp_uart_fifo.h)(0x682426F2)
I (..\User\bsp\inc\bsp_boot.h)(0x6832B3D5)
I (..\User\bsp\inc\bsp_key.h)(0x68576AFA)
I (..\User\bsp\inc\bsp_crc.h)(0x682C3545)
I (..\User\bsp\inc\bsp_utility.h)(0x68107F3A)
I (..\User\bsp\inc\bsp_temp.h)(0x681322C8)
I (..\User\bsp\inc\bsp_ntc.h)(0x678DDDCC)
I (..\User\bsp\inc\bsp_signal.h)(0x682ACFBF)
I (..\User\bsp\inc\bsp_beep.h)(0x6827E666)
I (..\User\bsp\inc\bsp_fifo.h)(0x682C143B)
I (..\User\bsp\inc\bsp_tim_pwm.h)(0x68143C04)
I (..\User\pro\pro_cus_com.h)(0x685605BA)
I (..\User\pro\pro_hardware.h)(0x6829DA80)
I (..\User\pro\pro_workflow.h)(0x682D8D63)
I (..\User\pro\pro_error.h)(0x682C215A)
I (..\User\pro\pro_pagekey.h)(0x68297893)
I (..\User\page\page.h)(0x68555953)
I (..\User\page\src_inc\page_start.h)(0x67966D00)
I (..\User\page\src_inc\page_standby.h)(0x67966D02)
I (..\User\page\src_inc\page_run.h)(0x67966D08)
I (..\User\page\src_inc\page_parameter.h)(0x68579AE2)
I (..\User\page\src_inc\page_debug.h)(0x67966D0C)
I (..\User\page\src_inc\page_statistics.h)(0x682AF7F2)
I (..\User\page\src_inc\page_check.h)(0x67966D0E)
I (..\User\page\src_inc\page_developer.h)(0x67966D0C)
I (..\User\page\src_inc\page_qr_code.h)(0x681F13C4)
I (..\User\parame\parame.h)(0x68294931)
I (..\User\parame\parame_tongyong.h)(0x6857B6B2)
I (..\User\parame\flash_eeprom.h)(0x68560C9B)
I (..\User\ui2.8\UI28_font_16.h)(0x681EB98A)
I (..\User\ui2.8\UI28_font_20.h)(0x67F69774)
I (..\User\ui2.8\UI28_font_32.h)(0x681AFF0D)
I (..\User\ui2.8\UI28_font_72.h)(0x681D7617)
I (..\User\ui2.8\UI28_font_28.h)(0x681DBC62)
I (..\User\ui2.8\UI28_font_36.h)(0x681EACC0)
I (..\User\ui2.8\UI28_font_24.h)(0x682E7B21)
I (..\User\ui2.8\UI28_font_18.h)(0x681EF7D2)
I (..\User\ui2.8\UI28_font_14.h)(0x681ED798)
I (..\User\ui2.8\UI28_pic_120x120.h)(0x6857B1FC)
I (..\User\ui2.8\UI28_pic_32x32.h)(0x681D853E)
I (..\User\ui2.8\UI28_pic_22x30.h)(0x681D8CDE)
I (..\User\ui2.8\UI28_pic_14x18.h)(0x681DB278)
I (..\User\ui2.8\UI28_pic_48x88.h)(0x681DA6D8)
I (..\User\ui2.8\UI28_pic_36x36.h)(0x681DB27C)
I (..\User\ui2.8\UI28_pic_45x45.h)(0x68204BEA)
I (..\User\ui2.8\UI28_pic_40x20.h)(0x68283E6F)
I (..\User\ui2.8\UI28_pic_70x70.h)(0x682840DF)
I (..\User\ui2.8\UI28_matrix.h)(0x6857730C)
I (..\User\ui2.8\UI28_menu.h)(0x68579EAC)
I (..\User\ui2.8\UI28_rolling_album.h)(0x6854C854)
I (..\User\ui2.8\UI28_arc.h)(0x685772E6)
I (..\User\ui2.8\UI28_animation.h)(0x6829BB09)
I (..\User\ui2.8\UI28_progress_bar.h)(0x68567976)
F (..\User\ui2.8\UI28_pic_22x30.c)(0x681D8D4B)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/ui28_pic_22x30.o -MD)
I (..\User\ui2.8\UI28.h)(0x6856620B)
I (..\User\ui2.8\UI28_auxiliary_table.h)(0x6801A019)
I (..\User\ui2.8\bsp_ili9340x_lcd.h)(0x6828483F)
I (..\User\bsp\bsp.h)(0x6826A21F)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (D:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdlib.h)(0x603507D0)
I (..\User\bsp\inc\bsp_timer.h)(0x680C5798)
I (..\User\bsp\inc\bsp_uart_fifo.h)(0x682426F2)
I (..\User\bsp\inc\bsp_boot.h)(0x6832B3D5)
I (..\User\bsp\inc\bsp_key.h)(0x68576AFA)
I (..\User\bsp\inc\bsp_crc.h)(0x682C3545)
I (..\User\bsp\inc\bsp_utility.h)(0x68107F3A)
I (..\User\bsp\inc\bsp_temp.h)(0x681322C8)
I (..\User\bsp\inc\bsp_ntc.h)(0x678DDDCC)
I (..\User\bsp\inc\bsp_signal.h)(0x682ACFBF)
I (..\User\bsp\inc\bsp_beep.h)(0x6827E666)
I (..\User\bsp\inc\bsp_fifo.h)(0x682C143B)
I (..\User\bsp\inc\bsp_tim_pwm.h)(0x68143C04)
I (..\User\pro\pro_cus_com.h)(0x685605BA)
I (..\User\pro\pro_hardware.h)(0x6829DA80)
I (..\User\pro\pro_workflow.h)(0x682D8D63)
I (..\User\pro\pro_error.h)(0x682C215A)
I (..\User\pro\pro_pagekey.h)(0x68297893)
I (..\User\page\page.h)(0x68555953)
I (..\User\page\src_inc\page_start.h)(0x67966D00)
I (..\User\page\src_inc\page_standby.h)(0x67966D02)
I (..\User\page\src_inc\page_run.h)(0x67966D08)
I (..\User\page\src_inc\page_parameter.h)(0x68579AE2)
I (..\User\page\src_inc\page_debug.h)(0x67966D0C)
I (..\User\page\src_inc\page_statistics.h)(0x682AF7F2)
I (..\User\page\src_inc\page_check.h)(0x67966D0E)
I (..\User\page\src_inc\page_developer.h)(0x67966D0C)
I (..\User\page\src_inc\page_qr_code.h)(0x681F13C4)
I (..\User\parame\parame.h)(0x68294931)
I (..\User\parame\parame_tongyong.h)(0x6857B6B2)
I (..\User\parame\flash_eeprom.h)(0x68560C9B)
I (..\User\ui2.8\UI28_font_16.h)(0x681EB98A)
I (..\User\ui2.8\UI28_font_20.h)(0x67F69774)
I (..\User\ui2.8\UI28_font_32.h)(0x681AFF0D)
I (..\User\ui2.8\UI28_font_72.h)(0x681D7617)
I (..\User\ui2.8\UI28_font_28.h)(0x681DBC62)
I (..\User\ui2.8\UI28_font_36.h)(0x681EACC0)
I (..\User\ui2.8\UI28_font_24.h)(0x682E7B21)
I (..\User\ui2.8\UI28_font_18.h)(0x681EF7D2)
I (..\User\ui2.8\UI28_font_14.h)(0x681ED798)
I (..\User\ui2.8\UI28_pic_120x120.h)(0x6857B1FC)
I (..\User\ui2.8\UI28_pic_32x32.h)(0x681D853E)
I (..\User\ui2.8\UI28_pic_22x30.h)(0x681D8CDE)
I (..\User\ui2.8\UI28_pic_14x18.h)(0x681DB278)
I (..\User\ui2.8\UI28_pic_48x88.h)(0x681DA6D8)
I (..\User\ui2.8\UI28_pic_36x36.h)(0x681DB27C)
I (..\User\ui2.8\UI28_pic_45x45.h)(0x68204BEA)
I (..\User\ui2.8\UI28_pic_40x20.h)(0x68283E6F)
I (..\User\ui2.8\UI28_pic_70x70.h)(0x682840DF)
I (..\User\ui2.8\UI28_matrix.h)(0x6857730C)
I (..\User\ui2.8\UI28_menu.h)(0x68579EAC)
I (..\User\ui2.8\UI28_rolling_album.h)(0x6854C854)
I (..\User\ui2.8\UI28_arc.h)(0x685772E6)
I (..\User\ui2.8\UI28_animation.h)(0x6829BB09)
I (..\User\ui2.8\UI28_progress_bar.h)(0x68567976)
F (..\User\ui2.8\UI28_pic_14x18.c)(0x681D93E7)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/ui28_pic_14x18.o -MD)
I (..\User\ui2.8\UI28.h)(0x6856620B)
I (..\User\ui2.8\UI28_auxiliary_table.h)(0x6801A019)
I (..\User\ui2.8\bsp_ili9340x_lcd.h)(0x6828483F)
I (..\User\bsp\bsp.h)(0x6826A21F)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (D:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdlib.h)(0x603507D0)
I (..\User\bsp\inc\bsp_timer.h)(0x680C5798)
I (..\User\bsp\inc\bsp_uart_fifo.h)(0x682426F2)
I (..\User\bsp\inc\bsp_boot.h)(0x6832B3D5)
I (..\User\bsp\inc\bsp_key.h)(0x68576AFA)
I (..\User\bsp\inc\bsp_crc.h)(0x682C3545)
I (..\User\bsp\inc\bsp_utility.h)(0x68107F3A)
I (..\User\bsp\inc\bsp_temp.h)(0x681322C8)
I (..\User\bsp\inc\bsp_ntc.h)(0x678DDDCC)
I (..\User\bsp\inc\bsp_signal.h)(0x682ACFBF)
I (..\User\bsp\inc\bsp_beep.h)(0x6827E666)
I (..\User\bsp\inc\bsp_fifo.h)(0x682C143B)
I (..\User\bsp\inc\bsp_tim_pwm.h)(0x68143C04)
I (..\User\pro\pro_cus_com.h)(0x685605BA)
I (..\User\pro\pro_hardware.h)(0x6829DA80)
I (..\User\pro\pro_workflow.h)(0x682D8D63)
I (..\User\pro\pro_error.h)(0x682C215A)
I (..\User\pro\pro_pagekey.h)(0x68297893)
I (..\User\page\page.h)(0x68555953)
I (..\User\page\src_inc\page_start.h)(0x67966D00)
I (..\User\page\src_inc\page_standby.h)(0x67966D02)
I (..\User\page\src_inc\page_run.h)(0x67966D08)
I (..\User\page\src_inc\page_parameter.h)(0x68579AE2)
I (..\User\page\src_inc\page_debug.h)(0x67966D0C)
I (..\User\page\src_inc\page_statistics.h)(0x682AF7F2)
I (..\User\page\src_inc\page_check.h)(0x67966D0E)
I (..\User\page\src_inc\page_developer.h)(0x67966D0C)
I (..\User\page\src_inc\page_qr_code.h)(0x681F13C4)
I (..\User\parame\parame.h)(0x68294931)
I (..\User\parame\parame_tongyong.h)(0x6857B6B2)
I (..\User\parame\flash_eeprom.h)(0x68560C9B)
I (..\User\ui2.8\UI28_font_16.h)(0x681EB98A)
I (..\User\ui2.8\UI28_font_20.h)(0x67F69774)
I (..\User\ui2.8\UI28_font_32.h)(0x681AFF0D)
I (..\User\ui2.8\UI28_font_72.h)(0x681D7617)
I (..\User\ui2.8\UI28_font_28.h)(0x681DBC62)
I (..\User\ui2.8\UI28_font_36.h)(0x681EACC0)
I (..\User\ui2.8\UI28_font_24.h)(0x682E7B21)
I (..\User\ui2.8\UI28_font_18.h)(0x681EF7D2)
I (..\User\ui2.8\UI28_font_14.h)(0x681ED798)
I (..\User\ui2.8\UI28_pic_120x120.h)(0x6857B1FC)
I (..\User\ui2.8\UI28_pic_32x32.h)(0x681D853E)
I (..\User\ui2.8\UI28_pic_22x30.h)(0x681D8CDE)
I (..\User\ui2.8\UI28_pic_14x18.h)(0x681DB278)
I (..\User\ui2.8\UI28_pic_48x88.h)(0x681DA6D8)
I (..\User\ui2.8\UI28_pic_36x36.h)(0x681DB27C)
I (..\User\ui2.8\UI28_pic_45x45.h)(0x68204BEA)
I (..\User\ui2.8\UI28_pic_40x20.h)(0x68283E6F)
I (..\User\ui2.8\UI28_pic_70x70.h)(0x682840DF)
I (..\User\ui2.8\UI28_matrix.h)(0x6857730C)
I (..\User\ui2.8\UI28_menu.h)(0x68579EAC)
I (..\User\ui2.8\UI28_rolling_album.h)(0x6854C854)
I (..\User\ui2.8\UI28_arc.h)(0x685772E6)
I (..\User\ui2.8\UI28_animation.h)(0x6829BB09)
I (..\User\ui2.8\UI28_progress_bar.h)(0x68567976)
F (..\User\ui2.8\UI28_pic_48x88.c)(0x681DA700)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/ui28_pic_48x88.o -MD)
I (..\User\ui2.8\UI28.h)(0x6856620B)
I (..\User\ui2.8\UI28_auxiliary_table.h)(0x6801A019)
I (..\User\ui2.8\bsp_ili9340x_lcd.h)(0x6828483F)
I (..\User\bsp\bsp.h)(0x6826A21F)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (D:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdlib.h)(0x603507D0)
I (..\User\bsp\inc\bsp_timer.h)(0x680C5798)
I (..\User\bsp\inc\bsp_uart_fifo.h)(0x682426F2)
I (..\User\bsp\inc\bsp_boot.h)(0x6832B3D5)
I (..\User\bsp\inc\bsp_key.h)(0x68576AFA)
I (..\User\bsp\inc\bsp_crc.h)(0x682C3545)
I (..\User\bsp\inc\bsp_utility.h)(0x68107F3A)
I (..\User\bsp\inc\bsp_temp.h)(0x681322C8)
I (..\User\bsp\inc\bsp_ntc.h)(0x678DDDCC)
I (..\User\bsp\inc\bsp_signal.h)(0x682ACFBF)
I (..\User\bsp\inc\bsp_beep.h)(0x6827E666)
I (..\User\bsp\inc\bsp_fifo.h)(0x682C143B)
I (..\User\bsp\inc\bsp_tim_pwm.h)(0x68143C04)
I (..\User\pro\pro_cus_com.h)(0x685605BA)
I (..\User\pro\pro_hardware.h)(0x6829DA80)
I (..\User\pro\pro_workflow.h)(0x682D8D63)
I (..\User\pro\pro_error.h)(0x682C215A)
I (..\User\pro\pro_pagekey.h)(0x68297893)
I (..\User\page\page.h)(0x68555953)
I (..\User\page\src_inc\page_start.h)(0x67966D00)
I (..\User\page\src_inc\page_standby.h)(0x67966D02)
I (..\User\page\src_inc\page_run.h)(0x67966D08)
I (..\User\page\src_inc\page_parameter.h)(0x68579AE2)
I (..\User\page\src_inc\page_debug.h)(0x67966D0C)
I (..\User\page\src_inc\page_statistics.h)(0x682AF7F2)
I (..\User\page\src_inc\page_check.h)(0x67966D0E)
I (..\User\page\src_inc\page_developer.h)(0x67966D0C)
I (..\User\page\src_inc\page_qr_code.h)(0x681F13C4)
I (..\User\parame\parame.h)(0x68294931)
I (..\User\parame\parame_tongyong.h)(0x6857B6B2)
I (..\User\parame\flash_eeprom.h)(0x68560C9B)
I (..\User\ui2.8\UI28_font_16.h)(0x681EB98A)
I (..\User\ui2.8\UI28_font_20.h)(0x67F69774)
I (..\User\ui2.8\UI28_font_32.h)(0x681AFF0D)
I (..\User\ui2.8\UI28_font_72.h)(0x681D7617)
I (..\User\ui2.8\UI28_font_28.h)(0x681DBC62)
I (..\User\ui2.8\UI28_font_36.h)(0x681EACC0)
I (..\User\ui2.8\UI28_font_24.h)(0x682E7B21)
I (..\User\ui2.8\UI28_font_18.h)(0x681EF7D2)
I (..\User\ui2.8\UI28_font_14.h)(0x681ED798)
I (..\User\ui2.8\UI28_pic_120x120.h)(0x6857B1FC)
I (..\User\ui2.8\UI28_pic_32x32.h)(0x681D853E)
I (..\User\ui2.8\UI28_pic_22x30.h)(0x681D8CDE)
I (..\User\ui2.8\UI28_pic_14x18.h)(0x681DB278)
I (..\User\ui2.8\UI28_pic_48x88.h)(0x681DA6D8)
I (..\User\ui2.8\UI28_pic_36x36.h)(0x681DB27C)
I (..\User\ui2.8\UI28_pic_45x45.h)(0x68204BEA)
I (..\User\ui2.8\UI28_pic_40x20.h)(0x68283E6F)
I (..\User\ui2.8\UI28_pic_70x70.h)(0x682840DF)
I (..\User\ui2.8\UI28_matrix.h)(0x6857730C)
I (..\User\ui2.8\UI28_menu.h)(0x68579EAC)
I (..\User\ui2.8\UI28_rolling_album.h)(0x6854C854)
I (..\User\ui2.8\UI28_arc.h)(0x685772E6)
I (..\User\ui2.8\UI28_animation.h)(0x6829BB09)
I (..\User\ui2.8\UI28_progress_bar.h)(0x68567976)
F (..\User\ui2.8\UI28_pic_36x36.c)(0x681DB1E4)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/ui28_pic_36x36.o -MD)
I (..\User\ui2.8\UI28.h)(0x6856620B)
I (..\User\ui2.8\UI28_auxiliary_table.h)(0x6801A019)
I (..\User\ui2.8\bsp_ili9340x_lcd.h)(0x6828483F)
I (..\User\bsp\bsp.h)(0x6826A21F)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (D:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdlib.h)(0x603507D0)
I (..\User\bsp\inc\bsp_timer.h)(0x680C5798)
I (..\User\bsp\inc\bsp_uart_fifo.h)(0x682426F2)
I (..\User\bsp\inc\bsp_boot.h)(0x6832B3D5)
I (..\User\bsp\inc\bsp_key.h)(0x68576AFA)
I (..\User\bsp\inc\bsp_crc.h)(0x682C3545)
I (..\User\bsp\inc\bsp_utility.h)(0x68107F3A)
I (..\User\bsp\inc\bsp_temp.h)(0x681322C8)
I (..\User\bsp\inc\bsp_ntc.h)(0x678DDDCC)
I (..\User\bsp\inc\bsp_signal.h)(0x682ACFBF)
I (..\User\bsp\inc\bsp_beep.h)(0x6827E666)
I (..\User\bsp\inc\bsp_fifo.h)(0x682C143B)
I (..\User\bsp\inc\bsp_tim_pwm.h)(0x68143C04)
I (..\User\pro\pro_cus_com.h)(0x685605BA)
I (..\User\pro\pro_hardware.h)(0x6829DA80)
I (..\User\pro\pro_workflow.h)(0x682D8D63)
I (..\User\pro\pro_error.h)(0x682C215A)
I (..\User\pro\pro_pagekey.h)(0x68297893)
I (..\User\page\page.h)(0x68555953)
I (..\User\page\src_inc\page_start.h)(0x67966D00)
I (..\User\page\src_inc\page_standby.h)(0x67966D02)
I (..\User\page\src_inc\page_run.h)(0x67966D08)
I (..\User\page\src_inc\page_parameter.h)(0x68579AE2)
I (..\User\page\src_inc\page_debug.h)(0x67966D0C)
I (..\User\page\src_inc\page_statistics.h)(0x682AF7F2)
I (..\User\page\src_inc\page_check.h)(0x67966D0E)
I (..\User\page\src_inc\page_developer.h)(0x67966D0C)
I (..\User\page\src_inc\page_qr_code.h)(0x681F13C4)
I (..\User\parame\parame.h)(0x68294931)
I (..\User\parame\parame_tongyong.h)(0x6857B6B2)
I (..\User\parame\flash_eeprom.h)(0x68560C9B)
I (..\User\ui2.8\UI28_font_16.h)(0x681EB98A)
I (..\User\ui2.8\UI28_font_20.h)(0x67F69774)
I (..\User\ui2.8\UI28_font_32.h)(0x681AFF0D)
I (..\User\ui2.8\UI28_font_72.h)(0x681D7617)
I (..\User\ui2.8\UI28_font_28.h)(0x681DBC62)
I (..\User\ui2.8\UI28_font_36.h)(0x681EACC0)
I (..\User\ui2.8\UI28_font_24.h)(0x682E7B21)
I (..\User\ui2.8\UI28_font_18.h)(0x681EF7D2)
I (..\User\ui2.8\UI28_font_14.h)(0x681ED798)
I (..\User\ui2.8\UI28_pic_120x120.h)(0x6857B1FC)
I (..\User\ui2.8\UI28_pic_32x32.h)(0x681D853E)
I (..\User\ui2.8\UI28_pic_22x30.h)(0x681D8CDE)
I (..\User\ui2.8\UI28_pic_14x18.h)(0x681DB278)
I (..\User\ui2.8\UI28_pic_48x88.h)(0x681DA6D8)
I (..\User\ui2.8\UI28_pic_36x36.h)(0x681DB27C)
I (..\User\ui2.8\UI28_pic_45x45.h)(0x68204BEA)
I (..\User\ui2.8\UI28_pic_40x20.h)(0x68283E6F)
I (..\User\ui2.8\UI28_pic_70x70.h)(0x682840DF)
I (..\User\ui2.8\UI28_matrix.h)(0x6857730C)
I (..\User\ui2.8\UI28_menu.h)(0x68579EAC)
I (..\User\ui2.8\UI28_rolling_album.h)(0x6854C854)
I (..\User\ui2.8\UI28_arc.h)(0x685772E6)
I (..\User\ui2.8\UI28_animation.h)(0x6829BB09)
I (..\User\ui2.8\UI28_progress_bar.h)(0x68567976)
F (..\User\ui2.8\UI28_pic_40x20.c)(0x68283E57)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/ui28_pic_40x20.o -MD)
I (..\User\ui2.8\UI28.h)(0x6856620B)
I (..\User\ui2.8\UI28_auxiliary_table.h)(0x6801A019)
I (..\User\ui2.8\bsp_ili9340x_lcd.h)(0x6828483F)
I (..\User\bsp\bsp.h)(0x6826A21F)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (D:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdlib.h)(0x603507D0)
I (..\User\bsp\inc\bsp_timer.h)(0x680C5798)
I (..\User\bsp\inc\bsp_uart_fifo.h)(0x682426F2)
I (..\User\bsp\inc\bsp_boot.h)(0x6832B3D5)
I (..\User\bsp\inc\bsp_key.h)(0x68576AFA)
I (..\User\bsp\inc\bsp_crc.h)(0x682C3545)
I (..\User\bsp\inc\bsp_utility.h)(0x68107F3A)
I (..\User\bsp\inc\bsp_temp.h)(0x681322C8)
I (..\User\bsp\inc\bsp_ntc.h)(0x678DDDCC)
I (..\User\bsp\inc\bsp_signal.h)(0x682ACFBF)
I (..\User\bsp\inc\bsp_beep.h)(0x6827E666)
I (..\User\bsp\inc\bsp_fifo.h)(0x682C143B)
I (..\User\bsp\inc\bsp_tim_pwm.h)(0x68143C04)
I (..\User\pro\pro_cus_com.h)(0x685605BA)
I (..\User\pro\pro_hardware.h)(0x6829DA80)
I (..\User\pro\pro_workflow.h)(0x682D8D63)
I (..\User\pro\pro_error.h)(0x682C215A)
I (..\User\pro\pro_pagekey.h)(0x68297893)
I (..\User\page\page.h)(0x68555953)
I (..\User\page\src_inc\page_start.h)(0x67966D00)
I (..\User\page\src_inc\page_standby.h)(0x67966D02)
I (..\User\page\src_inc\page_run.h)(0x67966D08)
I (..\User\page\src_inc\page_parameter.h)(0x68579AE2)
I (..\User\page\src_inc\page_debug.h)(0x67966D0C)
I (..\User\page\src_inc\page_statistics.h)(0x682AF7F2)
I (..\User\page\src_inc\page_check.h)(0x67966D0E)
I (..\User\page\src_inc\page_developer.h)(0x67966D0C)
I (..\User\page\src_inc\page_qr_code.h)(0x681F13C4)
I (..\User\parame\parame.h)(0x68294931)
I (..\User\parame\parame_tongyong.h)(0x6857B6B2)
I (..\User\parame\flash_eeprom.h)(0x68560C9B)
I (..\User\ui2.8\UI28_font_16.h)(0x681EB98A)
I (..\User\ui2.8\UI28_font_20.h)(0x67F69774)
I (..\User\ui2.8\UI28_font_32.h)(0x681AFF0D)
I (..\User\ui2.8\UI28_font_72.h)(0x681D7617)
I (..\User\ui2.8\UI28_font_28.h)(0x681DBC62)
I (..\User\ui2.8\UI28_font_36.h)(0x681EACC0)
I (..\User\ui2.8\UI28_font_24.h)(0x682E7B21)
I (..\User\ui2.8\UI28_font_18.h)(0x681EF7D2)
I (..\User\ui2.8\UI28_font_14.h)(0x681ED798)
I (..\User\ui2.8\UI28_pic_120x120.h)(0x6857B1FC)
I (..\User\ui2.8\UI28_pic_32x32.h)(0x681D853E)
I (..\User\ui2.8\UI28_pic_22x30.h)(0x681D8CDE)
I (..\User\ui2.8\UI28_pic_14x18.h)(0x681DB278)
I (..\User\ui2.8\UI28_pic_48x88.h)(0x681DA6D8)
I (..\User\ui2.8\UI28_pic_36x36.h)(0x681DB27C)
I (..\User\ui2.8\UI28_pic_45x45.h)(0x68204BEA)
I (..\User\ui2.8\UI28_pic_40x20.h)(0x68283E6F)
I (..\User\ui2.8\UI28_pic_70x70.h)(0x682840DF)
I (..\User\ui2.8\UI28_matrix.h)(0x6857730C)
I (..\User\ui2.8\UI28_menu.h)(0x68579EAC)
I (..\User\ui2.8\UI28_rolling_album.h)(0x6854C854)
I (..\User\ui2.8\UI28_arc.h)(0x685772E6)
I (..\User\ui2.8\UI28_animation.h)(0x6829BB09)
I (..\User\ui2.8\UI28_progress_bar.h)(0x68567976)
F (..\User\ui2.8\UI28_pic_45x45.c)(0x682194F9)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/ui28_pic_45x45.o -MD)
I (..\User\ui2.8\UI28.h)(0x6856620B)
I (..\User\ui2.8\UI28_auxiliary_table.h)(0x6801A019)
I (..\User\ui2.8\bsp_ili9340x_lcd.h)(0x6828483F)
I (..\User\bsp\bsp.h)(0x6826A21F)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (D:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdlib.h)(0x603507D0)
I (..\User\bsp\inc\bsp_timer.h)(0x680C5798)
I (..\User\bsp\inc\bsp_uart_fifo.h)(0x682426F2)
I (..\User\bsp\inc\bsp_boot.h)(0x6832B3D5)
I (..\User\bsp\inc\bsp_key.h)(0x68576AFA)
I (..\User\bsp\inc\bsp_crc.h)(0x682C3545)
I (..\User\bsp\inc\bsp_utility.h)(0x68107F3A)
I (..\User\bsp\inc\bsp_temp.h)(0x681322C8)
I (..\User\bsp\inc\bsp_ntc.h)(0x678DDDCC)
I (..\User\bsp\inc\bsp_signal.h)(0x682ACFBF)
I (..\User\bsp\inc\bsp_beep.h)(0x6827E666)
I (..\User\bsp\inc\bsp_fifo.h)(0x682C143B)
I (..\User\bsp\inc\bsp_tim_pwm.h)(0x68143C04)
I (..\User\pro\pro_cus_com.h)(0x685605BA)
I (..\User\pro\pro_hardware.h)(0x6829DA80)
I (..\User\pro\pro_workflow.h)(0x682D8D63)
I (..\User\pro\pro_error.h)(0x682C215A)
I (..\User\pro\pro_pagekey.h)(0x68297893)
I (..\User\page\page.h)(0x68555953)
I (..\User\page\src_inc\page_start.h)(0x67966D00)
I (..\User\page\src_inc\page_standby.h)(0x67966D02)
I (..\User\page\src_inc\page_run.h)(0x67966D08)
I (..\User\page\src_inc\page_parameter.h)(0x68579AE2)
I (..\User\page\src_inc\page_debug.h)(0x67966D0C)
I (..\User\page\src_inc\page_statistics.h)(0x682AF7F2)
I (..\User\page\src_inc\page_check.h)(0x67966D0E)
I (..\User\page\src_inc\page_developer.h)(0x67966D0C)
I (..\User\page\src_inc\page_qr_code.h)(0x681F13C4)
I (..\User\parame\parame.h)(0x68294931)
I (..\User\parame\parame_tongyong.h)(0x6857B6B2)
I (..\User\parame\flash_eeprom.h)(0x68560C9B)
I (..\User\ui2.8\UI28_font_16.h)(0x681EB98A)
I (..\User\ui2.8\UI28_font_20.h)(0x67F69774)
I (..\User\ui2.8\UI28_font_32.h)(0x681AFF0D)
I (..\User\ui2.8\UI28_font_72.h)(0x681D7617)
I (..\User\ui2.8\UI28_font_28.h)(0x681DBC62)
I (..\User\ui2.8\UI28_font_36.h)(0x681EACC0)
I (..\User\ui2.8\UI28_font_24.h)(0x682E7B21)
I (..\User\ui2.8\UI28_font_18.h)(0x681EF7D2)
I (..\User\ui2.8\UI28_font_14.h)(0x681ED798)
I (..\User\ui2.8\UI28_pic_120x120.h)(0x6857B1FC)
I (..\User\ui2.8\UI28_pic_32x32.h)(0x681D853E)
I (..\User\ui2.8\UI28_pic_22x30.h)(0x681D8CDE)
I (..\User\ui2.8\UI28_pic_14x18.h)(0x681DB278)
I (..\User\ui2.8\UI28_pic_48x88.h)(0x681DA6D8)
I (..\User\ui2.8\UI28_pic_36x36.h)(0x681DB27C)
I (..\User\ui2.8\UI28_pic_45x45.h)(0x68204BEA)
I (..\User\ui2.8\UI28_pic_40x20.h)(0x68283E6F)
I (..\User\ui2.8\UI28_pic_70x70.h)(0x682840DF)
I (..\User\ui2.8\UI28_matrix.h)(0x6857730C)
I (..\User\ui2.8\UI28_menu.h)(0x68579EAC)
I (..\User\ui2.8\UI28_rolling_album.h)(0x6854C854)
I (..\User\ui2.8\UI28_arc.h)(0x685772E6)
I (..\User\ui2.8\UI28_animation.h)(0x6829BB09)
I (..\User\ui2.8\UI28_progress_bar.h)(0x68567976)
F (..\User\ui2.8\UI28_pic_70x70.c)(0x68284107)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/ui28_pic_70x70.o -MD)
I (..\User\ui2.8\UI28.h)(0x6856620B)
I (..\User\ui2.8\UI28_auxiliary_table.h)(0x6801A019)
I (..\User\ui2.8\bsp_ili9340x_lcd.h)(0x6828483F)
I (..\User\bsp\bsp.h)(0x6826A21F)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (D:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdlib.h)(0x603507D0)
I (..\User\bsp\inc\bsp_timer.h)(0x680C5798)
I (..\User\bsp\inc\bsp_uart_fifo.h)(0x682426F2)
I (..\User\bsp\inc\bsp_boot.h)(0x6832B3D5)
I (..\User\bsp\inc\bsp_key.h)(0x68576AFA)
I (..\User\bsp\inc\bsp_crc.h)(0x682C3545)
I (..\User\bsp\inc\bsp_utility.h)(0x68107F3A)
I (..\User\bsp\inc\bsp_temp.h)(0x681322C8)
I (..\User\bsp\inc\bsp_ntc.h)(0x678DDDCC)
I (..\User\bsp\inc\bsp_signal.h)(0x682ACFBF)
I (..\User\bsp\inc\bsp_beep.h)(0x6827E666)
I (..\User\bsp\inc\bsp_fifo.h)(0x682C143B)
I (..\User\bsp\inc\bsp_tim_pwm.h)(0x68143C04)
I (..\User\pro\pro_cus_com.h)(0x685605BA)
I (..\User\pro\pro_hardware.h)(0x6829DA80)
I (..\User\pro\pro_workflow.h)(0x682D8D63)
I (..\User\pro\pro_error.h)(0x682C215A)
I (..\User\pro\pro_pagekey.h)(0x68297893)
I (..\User\page\page.h)(0x68555953)
I (..\User\page\src_inc\page_start.h)(0x67966D00)
I (..\User\page\src_inc\page_standby.h)(0x67966D02)
I (..\User\page\src_inc\page_run.h)(0x67966D08)
I (..\User\page\src_inc\page_parameter.h)(0x68579AE2)
I (..\User\page\src_inc\page_debug.h)(0x67966D0C)
I (..\User\page\src_inc\page_statistics.h)(0x682AF7F2)
I (..\User\page\src_inc\page_check.h)(0x67966D0E)
I (..\User\page\src_inc\page_developer.h)(0x67966D0C)
I (..\User\page\src_inc\page_qr_code.h)(0x681F13C4)
I (..\User\parame\parame.h)(0x68294931)
I (..\User\parame\parame_tongyong.h)(0x6857B6B2)
I (..\User\parame\flash_eeprom.h)(0x68560C9B)
I (..\User\ui2.8\UI28_font_16.h)(0x681EB98A)
I (..\User\ui2.8\UI28_font_20.h)(0x67F69774)
I (..\User\ui2.8\UI28_font_32.h)(0x681AFF0D)
I (..\User\ui2.8\UI28_font_72.h)(0x681D7617)
I (..\User\ui2.8\UI28_font_28.h)(0x681DBC62)
I (..\User\ui2.8\UI28_font_36.h)(0x681EACC0)
I (..\User\ui2.8\UI28_font_24.h)(0x682E7B21)
I (..\User\ui2.8\UI28_font_18.h)(0x681EF7D2)
I (..\User\ui2.8\UI28_font_14.h)(0x681ED798)
I (..\User\ui2.8\UI28_pic_120x120.h)(0x6857B1FC)
I (..\User\ui2.8\UI28_pic_32x32.h)(0x681D853E)
I (..\User\ui2.8\UI28_pic_22x30.h)(0x681D8CDE)
I (..\User\ui2.8\UI28_pic_14x18.h)(0x681DB278)
I (..\User\ui2.8\UI28_pic_48x88.h)(0x681DA6D8)
I (..\User\ui2.8\UI28_pic_36x36.h)(0x681DB27C)
I (..\User\ui2.8\UI28_pic_45x45.h)(0x68204BEA)
I (..\User\ui2.8\UI28_pic_40x20.h)(0x68283E6F)
I (..\User\ui2.8\UI28_pic_70x70.h)(0x682840DF)
I (..\User\ui2.8\UI28_matrix.h)(0x6857730C)
I (..\User\ui2.8\UI28_menu.h)(0x68579EAC)
I (..\User\ui2.8\UI28_rolling_album.h)(0x6854C854)
I (..\User\ui2.8\UI28_arc.h)(0x685772E6)
I (..\User\ui2.8\UI28_animation.h)(0x6829BB09)
I (..\User\ui2.8\UI28_progress_bar.h)(0x68567976)
F (..\User\ui2.8\UI28_animation.c)(0x68204AE0)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/ui28_animation.o -MD)
I (..\User\ui2.8\UI28_animation.h)(0x6829BB09)
I (..\User\ui2.8\UI28.h)(0x6856620B)
I (..\User\ui2.8\UI28_auxiliary_table.h)(0x6801A019)
I (..\User\ui2.8\bsp_ili9340x_lcd.h)(0x6828483F)
I (..\User\bsp\bsp.h)(0x6826A21F)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (D:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdlib.h)(0x603507D0)
I (..\User\bsp\inc\bsp_timer.h)(0x680C5798)
I (..\User\bsp\inc\bsp_uart_fifo.h)(0x682426F2)
I (..\User\bsp\inc\bsp_boot.h)(0x6832B3D5)
I (..\User\bsp\inc\bsp_key.h)(0x68576AFA)
I (..\User\bsp\inc\bsp_crc.h)(0x682C3545)
I (..\User\bsp\inc\bsp_utility.h)(0x68107F3A)
I (..\User\bsp\inc\bsp_temp.h)(0x681322C8)
I (..\User\bsp\inc\bsp_ntc.h)(0x678DDDCC)
I (..\User\bsp\inc\bsp_signal.h)(0x682ACFBF)
I (..\User\bsp\inc\bsp_beep.h)(0x6827E666)
I (..\User\bsp\inc\bsp_fifo.h)(0x682C143B)
I (..\User\bsp\inc\bsp_tim_pwm.h)(0x68143C04)
I (..\User\pro\pro_cus_com.h)(0x685605BA)
I (..\User\pro\pro_hardware.h)(0x6829DA80)
I (..\User\pro\pro_workflow.h)(0x682D8D63)
I (..\User\pro\pro_error.h)(0x682C215A)
I (..\User\pro\pro_pagekey.h)(0x68297893)
I (..\User\page\page.h)(0x68555953)
I (..\User\page\src_inc\page_start.h)(0x67966D00)
I (..\User\page\src_inc\page_standby.h)(0x67966D02)
I (..\User\page\src_inc\page_run.h)(0x67966D08)
I (..\User\page\src_inc\page_parameter.h)(0x68579AE2)
I (..\User\page\src_inc\page_debug.h)(0x67966D0C)
I (..\User\page\src_inc\page_statistics.h)(0x682AF7F2)
I (..\User\page\src_inc\page_check.h)(0x67966D0E)
I (..\User\page\src_inc\page_developer.h)(0x67966D0C)
I (..\User\page\src_inc\page_qr_code.h)(0x681F13C4)
I (..\User\parame\parame.h)(0x68294931)
I (..\User\parame\parame_tongyong.h)(0x6857B6B2)
I (..\User\parame\flash_eeprom.h)(0x68560C9B)
I (..\User\ui2.8\UI28_font_16.h)(0x681EB98A)
I (..\User\ui2.8\UI28_font_20.h)(0x67F69774)
I (..\User\ui2.8\UI28_font_32.h)(0x681AFF0D)
I (..\User\ui2.8\UI28_font_72.h)(0x681D7617)
I (..\User\ui2.8\UI28_font_28.h)(0x681DBC62)
I (..\User\ui2.8\UI28_font_36.h)(0x681EACC0)
I (..\User\ui2.8\UI28_font_24.h)(0x682E7B21)
I (..\User\ui2.8\UI28_font_18.h)(0x681EF7D2)
I (..\User\ui2.8\UI28_font_14.h)(0x681ED798)
I (..\User\ui2.8\UI28_pic_120x120.h)(0x6857B1FC)
I (..\User\ui2.8\UI28_pic_32x32.h)(0x681D853E)
I (..\User\ui2.8\UI28_pic_22x30.h)(0x681D8CDE)
I (..\User\ui2.8\UI28_pic_14x18.h)(0x681DB278)
I (..\User\ui2.8\UI28_pic_48x88.h)(0x681DA6D8)
I (..\User\ui2.8\UI28_pic_36x36.h)(0x681DB27C)
I (..\User\ui2.8\UI28_pic_45x45.h)(0x68204BEA)
I (..\User\ui2.8\UI28_pic_40x20.h)(0x68283E6F)
I (..\User\ui2.8\UI28_pic_70x70.h)(0x682840DF)
I (..\User\ui2.8\UI28_matrix.h)(0x6857730C)
I (..\User\ui2.8\UI28_menu.h)(0x68579EAC)
I (..\User\ui2.8\UI28_rolling_album.h)(0x6854C854)
I (..\User\ui2.8\UI28_arc.h)(0x685772E6)
I (..\User\ui2.8\UI28_progress_bar.h)(0x68567976)
F (..\User\ui2.8\UI28_progress_bar.c)(0x68567700)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/ui28_progress_bar.o -MD)
I (..\User\ui2.8\UI28_progress_bar.h)(0x68567976)
I (..\User\ui2.8\UI28.h)(0x6856620B)
I (..\User\ui2.8\UI28_auxiliary_table.h)(0x6801A019)
I (..\User\ui2.8\bsp_ili9340x_lcd.h)(0x6828483F)
I (..\User\bsp\bsp.h)(0x6826A21F)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (D:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdlib.h)(0x603507D0)
I (..\User\bsp\inc\bsp_timer.h)(0x680C5798)
I (..\User\bsp\inc\bsp_uart_fifo.h)(0x682426F2)
I (..\User\bsp\inc\bsp_boot.h)(0x6832B3D5)
I (..\User\bsp\inc\bsp_key.h)(0x68576AFA)
I (..\User\bsp\inc\bsp_crc.h)(0x682C3545)
I (..\User\bsp\inc\bsp_utility.h)(0x68107F3A)
I (..\User\bsp\inc\bsp_temp.h)(0x681322C8)
I (..\User\bsp\inc\bsp_ntc.h)(0x678DDDCC)
I (..\User\bsp\inc\bsp_signal.h)(0x682ACFBF)
I (..\User\bsp\inc\bsp_beep.h)(0x6827E666)
I (..\User\bsp\inc\bsp_fifo.h)(0x682C143B)
I (..\User\bsp\inc\bsp_tim_pwm.h)(0x68143C04)
I (..\User\pro\pro_cus_com.h)(0x685605BA)
I (..\User\pro\pro_hardware.h)(0x6829DA80)
I (..\User\pro\pro_workflow.h)(0x682D8D63)
I (..\User\pro\pro_error.h)(0x682C215A)
I (..\User\pro\pro_pagekey.h)(0x68297893)
I (..\User\page\page.h)(0x68555953)
I (..\User\page\src_inc\page_start.h)(0x67966D00)
I (..\User\page\src_inc\page_standby.h)(0x67966D02)
I (..\User\page\src_inc\page_run.h)(0x67966D08)
I (..\User\page\src_inc\page_parameter.h)(0x68579AE2)
I (..\User\page\src_inc\page_debug.h)(0x67966D0C)
I (..\User\page\src_inc\page_statistics.h)(0x682AF7F2)
I (..\User\page\src_inc\page_check.h)(0x67966D0E)
I (..\User\page\src_inc\page_developer.h)(0x67966D0C)
I (..\User\page\src_inc\page_qr_code.h)(0x681F13C4)
I (..\User\parame\parame.h)(0x68294931)
I (..\User\parame\parame_tongyong.h)(0x6857B6B2)
I (..\User\parame\flash_eeprom.h)(0x68560C9B)
I (..\User\ui2.8\UI28_font_16.h)(0x681EB98A)
I (..\User\ui2.8\UI28_font_20.h)(0x67F69774)
I (..\User\ui2.8\UI28_font_32.h)(0x681AFF0D)
I (..\User\ui2.8\UI28_font_72.h)(0x681D7617)
I (..\User\ui2.8\UI28_font_28.h)(0x681DBC62)
I (..\User\ui2.8\UI28_font_36.h)(0x681EACC0)
I (..\User\ui2.8\UI28_font_24.h)(0x682E7B21)
I (..\User\ui2.8\UI28_font_18.h)(0x681EF7D2)
I (..\User\ui2.8\UI28_font_14.h)(0x681ED798)
I (..\User\ui2.8\UI28_pic_120x120.h)(0x6857B1FC)
I (..\User\ui2.8\UI28_pic_32x32.h)(0x681D853E)
I (..\User\ui2.8\UI28_pic_22x30.h)(0x681D8CDE)
I (..\User\ui2.8\UI28_pic_14x18.h)(0x681DB278)
I (..\User\ui2.8\UI28_pic_48x88.h)(0x681DA6D8)
I (..\User\ui2.8\UI28_pic_36x36.h)(0x681DB27C)
I (..\User\ui2.8\UI28_pic_45x45.h)(0x68204BEA)
I (..\User\ui2.8\UI28_pic_40x20.h)(0x68283E6F)
I (..\User\ui2.8\UI28_pic_70x70.h)(0x682840DF)
I (..\User\ui2.8\UI28_matrix.h)(0x6857730C)
I (..\User\ui2.8\UI28_menu.h)(0x68579EAC)
I (..\User\ui2.8\UI28_rolling_album.h)(0x6854C854)
I (..\User\ui2.8\UI28_arc.h)(0x685772E6)
I (..\User\ui2.8\UI28_animation.h)(0x6829BB09)
F (..\Libraries\STARTUP\arm\startup_air32f10x.s)(0x68299A00)(--cpu Cortex-M3 -g --pd "__MICROLIB SETA 1"

-I.\RTE\_Target_1

-ID:\Users\mumu\AppData\Local\Arm\Packs\Keil\AIR32F103_DFP\1.1.9\Device\Include

-ID:\Keil_v5\ARM\CMSIS\Include

--pd "__UVISION_VERSION SETA 525"

--pd "AIR32F10X_MD SETA 1"

--list .\startup_air32f10x.lst

--xref -o .\obj\startup_air32f10x.o

--depend .\obj\startup_air32f10x.d)
F (..\Libraries\AIR32F10xLib\src\misc.c)(0x67E77A3B)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/misc.o -MD)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
F (..\Libraries\AIR32F10xLib\src\air32f10x_adc.c)(0x67BEDEFF)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/air32f10x_adc.o -MD)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
F (..\Libraries\AIR32F10xLib\src\air32f10x_bkp.c)(0x67BEDEFF)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/air32f10x_bkp.o -MD)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
F (..\Libraries\AIR32F10xLib\src\air32f10x_can.c)(0x67BEDEFF)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/air32f10x_can.o -MD)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
F (..\Libraries\AIR32F10xLib\src\air32f10x_cec.c)(0x67BEDEFF)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/air32f10x_cec.o -MD)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
F (..\Libraries\AIR32F10xLib\src\air32f10x_crc.c)(0x67BEDEFF)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/air32f10x_crc.o -MD)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
F (..\Libraries\AIR32F10xLib\src\air32f10x_dac.c)(0x67BEDEFF)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/air32f10x_dac.o -MD)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
F (..\Libraries\AIR32F10xLib\src\air32f10x_dbgmcu.c)(0x67BEDEFF)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/air32f10x_dbgmcu.o -MD)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
F (..\Libraries\AIR32F10xLib\src\air32f10x_dma.c)(0x67BEDEFF)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/air32f10x_dma.o -MD)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
F (..\Libraries\AIR32F10xLib\src\air32f10x_exti.c)(0x67BEDEFF)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/air32f10x_exti.o -MD)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
F (..\Libraries\AIR32F10xLib\src\air32f10x_flash.c)(0x67BEDEFF)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/air32f10x_flash.o -MD)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
F (..\Libraries\AIR32F10xLib\src\air32f10x_fsmc.c)(0x67BEDEFF)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/air32f10x_fsmc.o -MD)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
F (..\Libraries\AIR32F10xLib\src\air32f10x_gpio.c)(0x67BEDEFF)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/air32f10x_gpio.o -MD)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
F (..\Libraries\AIR32F10xLib\src\air32f10x_i2c.c)(0x67BEDEFF)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/air32f10x_i2c.o -MD)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
F (..\Libraries\AIR32F10xLib\src\air32f10x_iwdg.c)(0x67BEDEFF)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/air32f10x_iwdg.o -MD)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
F (..\Libraries\AIR32F10xLib\src\air32f10x_otp.c)(0x67BEDEFF)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/air32f10x_otp.o -MD)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_otp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
F (..\Libraries\AIR32F10xLib\src\air32f10x_pwr.c)(0x67BEDEFF)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/air32f10x_pwr.o -MD)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
F (..\Libraries\AIR32F10xLib\src\air32f10x_rcc.c)(0x67BEDEFF)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/air32f10x_rcc.o -MD)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
F (..\Libraries\AIR32F10xLib\src\air32f10x_rcc_ex.c)(0x67BEDEFF)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/air32f10x_rcc_ex.o -MD)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdlib.h)(0x603507D0)
I (D:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
F (..\Libraries\AIR32F10xLib\src\air32f10x_rtc.c)(0x67BEDEFF)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/air32f10x_rtc.o -MD)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
F (..\Libraries\AIR32F10xLib\src\air32f10x_sdio.c)(0x67BEDEFF)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/air32f10x_sdio.o -MD)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
F (..\Libraries\AIR32F10xLib\src\air32f10x_spi.c)(0x67BEDEFF)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/air32f10x_spi.o -MD)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
F (..\Libraries\AIR32F10xLib\src\air32f10x_tim.c)(0x6815CD68)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/air32f10x_tim.o -MD)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
F (..\Libraries\AIR32F10xLib\src\air32f10x_trng.c)(0x67BEDEFF)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/air32f10x_trng.o -MD)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_trng.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
F (..\Libraries\AIR32F10xLib\src\air32f10x_usart.c)(0x67BEDEFF)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/air32f10x_usart.o -MD)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
F (..\Libraries\AIR32F10xLib\src\air32f10x_wwdg.c)(0x67BEDEFF)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../User -I ../Libraries/AIR32F10xLib/inc -I ../Libraries/CMSIS/Include -I ../Libraries/AIR32F10xLib/lib -I ../User/bsp/inc -I ../User/bsp -I ../User/ui2.8 -I ../User/parame -I ../User/pro -I ../User/page -I ../User/page/src_inc

-I./RTE/_Target_1

-ID:/Users/<USER>/AppData/Local/Arm/Packs/Keil/AIR32F103_DFP/1.1.9/Device/Include

-ID:/Keil_v5/ARM/CMSIS/Include

-D__UVISION_VERSION="525" -DAIR32F10X_MD -DUSE_STDPERIPH_DRIVER

-o ./obj/air32f10x_wwdg.o -MD)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_wwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x.h)(0x67C56CDA)
I (..\Libraries\CMSIS\Include\core_cm3.h)(0x67C56D16)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Libraries\CMSIS\Include\cmsis_version.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_compiler.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\cmsis_armclang.h)(0x67BEDEFF)
I (..\Libraries\CMSIS\Include\mpu_armv7.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\system_air32f10x.h)(0x67BEDEFF)
I (..\User\air32f10x_conf.h)(0x64E02928)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_adc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_bkp.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_can.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_cec.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_crc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dac.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dbgmcu.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_dma.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_exti.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_flash.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_fsmc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_gpio.h)(0x682C059B)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_i2c.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_iwdg.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_pwr.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rcc_ex.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_rtc.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_sdio.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_spi.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_tim.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\air32f10x_usart.h)(0x67BEDEFF)
I (..\Libraries\AIR32F10xLib\inc\misc.h)(0x67BEDEFF)
