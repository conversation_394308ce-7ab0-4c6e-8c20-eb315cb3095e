#ifndef _BSP_H_
#define _BSP_H_

/* 开关全局中断的宏 */
#define ENABLE_INT()	__set_PRIMASK(0)	/* 使能全局中断 */
#define DISABLE_INT()	__set_PRIMASK(1)	/* 禁止全局中断 */

/* 这个宏仅用于调试阶段排错 */
#define BSP_Printf		printf
//#define BSP_Printf(...)

#include "air32f10x.h"
#include "air32f10x_conf.h"
#include <stdio.h>
#include <string.h>
#include <stdlib.h>

#ifndef TRUE
	#define TRUE  1
#endif

#ifndef FALSE
	#define FALSE 0
#endif

#ifndef ON
#define ON 1
#endif

#ifndef OFF
#define OFF 0
#endif

#ifndef YES
#define YES 1
#endif

#ifndef NO
#define NO 0
#endif

#include "bsp_timer.h"
#include "bsp_uart_fifo.h"
#include "bsp_key.h"
#include "bsp_crc.h"
#include "bsp_utility.h"
#include "bsp_temp.h"
#include "bsp_ntc.h"
#include "bsp_signal.h"
#include "bsp_beep.h"
#include "bsp_fifo.h"
#include "bsp_tim_pwm.h"
#include "bsp_boot.h"
#include "UI28.h"

#include "pro_cus_com.h"
#include "pro_hardware.h"
#include "pro_workflow.h"
#include "pro_error.h"
#include "pro_pagekey.h"

#include "page.h"

#include "parame.h"
#include "flash_eeprom.h"


/* 提供给其他C文件调用的函数 */
void bsp_Init(void);
/* 该函数每隔10ms被Systick中断调用1次。 */
void bsp_RunPer10ms(void);
#endif
