#include "bsp_crc.h"

const unsigned char table_CRC8_854[] =
    {
        0, 94, 188, 226, 97, 63, 221, 131, 194, 156, 126, 32, 163, 253, 31, 65,
        157, 195, 33, 127, 252, 162, 64, 30, 95, 1, 227, 189, 62, 96, 130, 220,
        35, 125, 159, 193, 66, 28, 254, 160, 225, 191, 93, 3, 128, 222, 60, 98,
        190, 224, 2, 92, 223, 129, 99, 61, 124, 34, 192, 158, 29, 67, 161, 255,
        70, 24, 250, 164, 39, 121, 155, 197, 132, 218, 56, 102, 229, 187, 89, 7,
        219, 133, 103, 57, 186, 228, 6, 88, 25, 71, 165, 251, 120, 38, 196, 154,
        101, 59, 217, 135, 4, 90, 184, 230, 167, 249, 27, 69, 198, 152, 122, 36,
        248, 166, 68, 26, 153, 199, 37, 123, 58, 100, 134, 216, 91, 5, 231, 185,
        140, 210, 48, 110, 237, 179, 81, 15, 78, 16, 242, 172, 47, 113, 147, 205,
        17, 79, 173, 243, 112, 46, 204, 146, 211, 141, 111, 49, 178, 236, 14, 80,
        175, 241, 19, 77, 206, 144, 114, 44, 109, 51, 209, 143, 12, 82, 176, 238,
        50, 108, 142, 208, 83, 13, 239, 177, 240, 174, 76, 18, 145, 207, 45, 115,
        202, 148, 118, 40, 171, 245, 23, 73, 8, 86, 180, 234, 105, 55, 213, 139,
        87, 9, 235, 181, 54, 104, 138, 212, 149, 203, 41, 119, 244, 170, 72, 22,
        233, 183, 85, 11, 136, 214, 52, 106, 43, 117, 151, 201, 74, 20, 246, 168,
        116, 42, 200, 150, 21, 75, 169, 247, 182, 232, 10, 84, 215, 137, 107, 53};

/*
*********************************************************************************************************
*	函 数 名: CalCrc8_Check
*	形    参: L_pt:需要进行CRC校验的数组
*	形    参: ucLengthTmp：数组长度
*	返 回 值: 校验结果
*	功能说明: CRC8 校验和计算
*********************************************************************************************************
*/
unsigned char CalCrc8_Check(unsigned char L_pt[], unsigned long ucLengthTmp)
{
     unsigned long ucTmp = 0;
     unsigned char ucCheckSum = 0;

     for (ucTmp = 0; ucTmp < ucLengthTmp; ucTmp++)
     {
          ucCheckSum = table_CRC8_854[ucCheckSum ^ (L_pt[ucTmp])];
     }
     return (ucCheckSum);
}

/**
 * @brief CRC16 校验和计算
 * @param {uint8_t} L_pt:需要进行CRC校验的数组
 * @param {uint16_t} INIT_:初始校验值（无初始校验，填0xFFFF）
 * @param {uint16_t} ucLengthTmp：数组长度
 * @return {校验结果}
 */
uint16_t CalCrc16_Check(uint8_t *L_pt,uint16_t INIT_, uint16_t ucLengthTmp)
{
	uint16_t buff;
	uint8_t* p=L_pt;
	if (INIT_==0xFFFF)
  {
		/* 将初始值填入初始值扩展寄存器 */
		CRC->INI = (uint32_t)INIT_;
  }
  else
  {
		/* 将初始值填入初始值扩展寄存器 */
		CRC->INI = (uint32_t)(INIT_ << 8) | (INIT_ >> 8);
		/* 重置 CRC 发生器 */
		CRC->CR = CRC_CR_RESET;
		/* 选择16位CRC计算 */
		CRC->CSR=0X54;
		
		buff=(CRC->DR & 0xFFFF);
		/* 将初始值填入初始值扩展寄存器 */
		CRC->INI = (uint32_t)buff;
  }

  /* 重置 CRC 发生器 */
  CRC->CR = CRC_CR_RESET;
	/* 选择32位CRC计算 */
	CRC->CSR = 0XD4;
	while (ucLengthTmp>3)
	{
		ucLengthTmp-=4;
		CRC->DR = *(uint32_t*)p;
		p+=4;
	}
	
	if (ucLengthTmp>1)
	{
		/* 选择16位CRC计算 */
		CRC->CSR=0X54;
		CRC->DR = (uint32_t)(*(uint16_t*)p);
		ucLengthTmp-=2;
		p+=2;
	}
	
	if (ucLengthTmp)
  {
		/* 选择8位CRC计算 */
		CRC->CSR=0X14;
		CRC->DR = (uint32_t)(*p);
  }
	
	buff=(CRC->DR & 0xFFFF);
	
	return (buff << 8) | (buff >> 8);
}
