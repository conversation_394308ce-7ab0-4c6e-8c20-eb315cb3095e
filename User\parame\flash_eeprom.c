#include "bsp.h"
#define FlashEepromAddress1 0x0803F000 /* 扇区1地址（主存储区） */
#define FlashEepromAddress2 0x0803F800 /* 扇区2地址（备份存储区） */

#if (BURN_CLEAR_EE == 1)
volatile const uint8_t __EEDATA1_[2048] __attribute__((section(".eedata1"))) = {0};
volatile const uint8_t __EEDATA2_[2048] __attribute__((section(".eedata2"))) = {0};
#else
#endif

#define PARAME_DEF_DATA_LENGTH (sizeof(parame_t))                            /* 默认数据大小（8位） */
#define PARAME_DATA_LENGTH (sizeof(parame_f_t))                              /* 数据大小（8位） */
#define PARAME_VER_DATA_LENGTH (PARAME_DATA_LENGTH - PARAME_DEF_DATA_LENGTH) /* 版本号与日期数据大小（8位） */
#define PARAME_DATA_LENG32 (PARAME_DATA_LENGTH / 4)                          /* 数据大小（32位） */

#define FlashEepromAddressCheck1 (FlashEepromAddress1 + PARAME_DATA_LENGTH) /* 扇区1校验地址（主存储区） */
#define FlashEepromAddressCheck2 (FlashEepromAddress2 + PARAME_DATA_LENGTH) /* 扇区2校验地址（备份存储区） */

// Flash数据是否相同
// 返回；1-数据发生改变，0-没有数据发生改变
uint8_t FlashIsTheDataTheSame(void)
{
    uint8_t i = PARAME_DATA_LENG32;
    uint32_t *p1 = (uint32_t *)&_sys_parames;
    uint32_t *p2 = (uint32_t *)FlashEepromAddress1;
    while (i--)
    {
        if (*p2++ != *p1++)
        {
            return 1; // 有一个数据不相等，数据发生改变
        }
    }
    // 都相等，没有数据发生改变
    return 0;
}

// Flash存数据
//  Address:FlashEepromAddress1 or FlashEepromAddress2
//  返 回 值: TRUE-成功，FALSE-写Flash出错(估计Flash寿命到)
uint8_t FlashSaveData(uint32_t Address)
{
    uint16_t i;
    uint32_t parame_ee_check;
    uint32_t usTemp;
    uint32_t *p1 = (uint32_t *)&_sys_parames;
    FLASH_Status status = FLASH_COMPLETE;

    __set_PRIMASK(1); /* 关中断 */

    // 生成校验码
    parame_ee_check = (uint32_t)CalCrc16_Check((uint8_t *)&_sys_parames, 0xFFFF, PARAME_DATA_LENGTH);

    /* FLASH 解锁 */
    FLASH_Unlock();

    /* Clear pending flags (if any) */
    FLASH_ClearFlag(FLASH_FLAG_BSY | FLASH_FLAG_EOP | FLASH_FLAG_PGERR | FLASH_FLAG_WRPRTERR);

    /* 擦除这个扇区 */
    status = FLASH_ErasePage(Address);

    /* 按字节模式编程（为提高效率，可以按字编程，一次写入4字节） */
    for (i = 0; i < PARAME_DATA_LENG32; i++)
    {
        usTemp = p1[i];
        status = FLASH_ProgramWord(Address, usTemp);
        if (status != FLASH_COMPLETE)
        {
            break;
        }

        Address += 4;
    }

    status = FLASH_ProgramWord(Address, parame_ee_check);

    /* Flash 加锁，禁止写Flash控制寄存器 */
    FLASH_Lock();

    __set_PRIMASK(0); /* 开中断 */

    if (status == FLASH_COMPLETE)
    {
        return TRUE;
    }

    return FALSE;
}

/* 保存数据 */
void SaveParameData(void)
{
    if (FlashIsTheDataTheSame() == 0)
    {
        /* 没有数据发生改变 */
        return;
    }

    /* 保存数据到 扇区1地址（主存储区） */
    FlashSaveData(FlashEepromAddress1);
    ms100_alarm_clock(ALARM_C1, 30, EE_BA); // Timer0任务闹钟（3秒后ee备份）
}

/* 备份数据 */
void BackupsParameData(void)
{
    /* 保存数据到 扇区2地址（备份存储区） */
    FlashSaveData(FlashEepromAddress2);
}

/* 延时保存数据 */
void DelayedSaveParameData(uint16_t time_100ms)
{
    if (time_100ms == 0)
    {
        SaveParameData();
    }
    ms100_alarm_clock(ALARM_C1, time_100ms, EE_SA); // Timer0任务闹钟（X100毫秒后ee保存）
}

// 上电存储操作
void PowerOnStorageOperation(void)
{
    uint32_t p = 0;
#if (BURN_CLEAR_EE == 1)
    if ((uint32_t)CalCrc16_Check((uint8_t *)&__EEDATA1_, 0xFFFF, PARAME_DATA_LENGTH) == *((uint32_t *)FlashEepromAddressCheck1))
#else
    if ((uint32_t)CalCrc16_Check((uint8_t *)FlashEepromAddress1, 0xFFFF, PARAME_DATA_LENGTH) == *((uint32_t *)FlashEepromAddressCheck1))
#endif
    {
        /* 扇区1数据正确 */
        p = FlashEepromAddress1;
    }
#if (BURN_CLEAR_EE == 1)
    else if ((uint32_t)CalCrc16_Check((uint8_t *)&__EEDATA2_, 0xFFFF, PARAME_DATA_LENGTH) == *((uint32_t *)FlashEepromAddressCheck2))
#else
    else if ((uint32_t)CalCrc16_Check((uint8_t *)FlashEepromAddress2, 0xFFFF, PARAME_DATA_LENGTH) == *((uint32_t *)FlashEepromAddressCheck2))
#endif
    {
        /* 存储数据错误，备份数据正确 */
        p = FlashEepromAddress2;
    }

    if (p != 0)
    {
        if (memcmp((const void *)&_sys_parames.VERSION[0], (const void *)(p + PARAME_DEF_DATA_LENGTH), PARAME_VER_DATA_LENGTH) == 0)
        {
            /* 如果版本号完全相同，则将EE中的数据取出 */
            memcpy((uint8_t *)&_sys_parames.sys, (uint8_t *)p, PARAME_DEF_DATA_LENGTH);
            memcpy((uint8_t *)&_sys_last_parames, (uint8_t *)p, PARAME_DEF_DATA_LENGTH);
            return;
        }
    }

    /* 使用 memcpy 按字节拷贝数据 */
    memcpy((uint8_t *)&_sys_parames.sys, (uint8_t *)&parames_config_init, PARAME_DEF_DATA_LENGTH);
    memcpy((uint8_t *)&_sys_last_parames, (uint8_t *)&parames_config_init, PARAME_DEF_DATA_LENGTH);
    /* 1秒后保存EE */
    DelayedSaveParameData(10);
}
