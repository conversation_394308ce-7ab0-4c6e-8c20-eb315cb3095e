#ifndef _BSP_BOOT_H_
#define _BSP_BOOT_H_
#include "bsp.h"
#define BOOT_MISJUDGMENT_CONDITIONS g_rfid_recv_lst_len /* BOOT误判条件：在宏中添加(此宏在if中被判断) */
/*
*********************************************************************************************************
*	函 数 名: BootAppReceiveDataProcessing
*	功能说明: BOOTAPP接收数据处理
*	形    参: _byte-接收的字节流
*	返 回 值: 无
*********************************************************************************************************
*/
void BootAppReceiveDataProcessing(uint8_t _data);

#endif
