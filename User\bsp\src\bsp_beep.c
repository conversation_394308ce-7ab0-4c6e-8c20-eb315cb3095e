#include "bsp.h"
#if BEEP_SWITCH
/* 音阶索引表 Hz */
const uint16_t musical_note_time[] =
	{
		0, /* 休止符，没有频率 */

		/* 第一个八度 */
		262, /* 低音哆 (C1) */
		277, /* 低音哆升半音 (C#1/Db1) */
		294, /* 低音瑞 (D1) */
		311, /* 低音瑞升半音 (D#1/Eb1) */
		330, /* 低音咪 (E1) */
		349, /* 低音发 (F1) */
		370, /* 低音发升半音 (F#1/Gb1) */
		392, /* 低音嗦 (G1) */
		415, /* 低音嗦升半音 (G#1/Ab1) */
		440, /* 低音啦 (A1) */
		466, /* 低音啦升半音 (A#1/Bb1) */
		494, /* 低音西 (B1) */

		/* 第二个八度 */
		523, /* 中音哆 (C2) */
		554, /* 中音哆升半音 (C#2/Db2) */
		587, /* 中音瑞 (D2) */
		622, /* 中音瑞升半音 (D#2/Eb2) */
		659, /* 中音咪 (E2) */
		698, /* 中音发 (F2) */
		740, /* 中音发升半音 (F#2/Gb2) */
		784, /* 中音嗦 (G2) */
		831, /* 中音嗦升半音 (G#2/Ab2) */
		880, /* 中音啦 (A2) */
		932, /* 中音啦升半音 (A#2/Bb2) */
		988, /* 中音西 (B2) */

		/* 第三个八度 */
		1047, /* 高音哆 (C3) */
		1109, /* 高音哆升半音 (C#3/Db3) */
		1175, /* 高音瑞 (D3) */
		1245, /* 高音瑞升半音 (D#3/Eb3) */
		1319, /* 高音咪 (E3) */
		1397, /* 高音发 (F3) */
		1480, /* 高音发升半音 (F#3/Gb3) */
		1568, /* 高音嗦 (G3) */
		1661, /* 高音嗦升半音 (G#3/Ab3) */
		1760, /* 高音啦 (A3) */
		1865, /* 高音啦升半音 (A#3/Bb3) */
		1976, /* 高音西 (B3) */

		/* 第四个八度 */
		2093, /* 超高音哆 (C4) */
		2217, /* 超高音哆升半音 (C#4/Db4) */
		2349, /* 超高音瑞 (D4) */
		2489, /* 超高音瑞升半音 (D#4/Eb4) */
		2637, /* 超高音咪 (E4) */
		2793, /* 超高音发 (F4) */
		2960, /* 超高音发升半音 (F#4/Gb4) */
		3136, /* 超高音嗦 (G4) */
		3322, /* 超高音嗦升半音 (G#4/Ab4) */
		3520, /* 超高音啦 (A4) */
		3729, /* 超高音啦升半音 (A#4/Bb4) */
		3951, /* 超高音西 (B4) */

		/* 第五个八度 */
		4186, /* 倍高音哆 (C5) */
		4435, /* 倍高音哆升半音 (C#5/Db5) */
		4699, /* 倍高音瑞 (D5) */
		4978, /* 倍高音瑞升半音 (D#5/Eb5) */
		5274, /* 倍高音咪 (E5) */
		5588, /* 倍高音发 (F5) */
		5920, /* 倍高音发升半音 (F#5/Gb5) */
		6272, /* 倍高音嗦 (G5) */
		6645, /* 倍高音嗦升半音 (G#5/Ab5) */
		7040, /* 倍高音啦 (A5) */
		7459, /* 倍高音啦升半音 (A#5/Bb5) */
		7902  /* 倍高音西 (B5) */
};

volatile beet_t g_tBeep = {NULL, 0, 0, 0, 0}; /* 定义蜂鸣器全局结构体变量 */

#define BEEP_DEBUG_X(name, Cycle, ...) const beats_t beep_beats_##name[] = __VA_ARGS__;
BEEP_ITEM_LIST
#undef BEEP_DEBUG_X
#define BEEP_DEBUG_X(name, Cycle, ...) const music_t beep_music_##name = {(beats_t const *)(&beep_beats_##name), (sizeof(beep_beats_##name) / sizeof(beats_t))};
BEEP_ITEM_LIST
#undef BEEP_DEBUG_X

/*
*********************************************************************************************************
*	函 数 名: BEEP_InitHard
*	功能说明: 初始化蜂鸣器硬件
*	形    参：无
*	返 回 值: 无
*********************************************************************************************************
*/
void BEEP_InitHard(void)
{
#ifdef BEEP_HAVE_POWER /* 有源蜂鸣器 */
	GPIO_InitTypeDef GPIO_InitStructure;

	/* 打开GPIO的时钟 */
#define BEEPIO_DEBUG_X(gpio_, pin_, tim_, ch_) RCC_APB2PeriphClockCmd(RCC_APB2Periph_##gpio_, ENABLE); /*使能GPIOX时钟*/
	BEEPIO_ITEM_LIST
#undef BEEPIO_DEBUG_X

	/* GPIO的设置成低电平 */
#define BEEPIO_DEBUG_X(gpio_, pin_, tim_, ch_) gpio_->BRR = pin_;
	BEEPIO_ITEM_LIST
#undef BEEPIO_DEBUG_X

#define BEEPIO_DEBUG_X(gpio_, pin_, tim_, ch_) GPIO_InitStructure.GPIO_Pin = pin_;
	BEEPIO_ITEM_LIST
#undef BEEPIO_DEBUG_X
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP;  /* 设置推挽输出模式 */
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz; /* GPIO速度等级 */

#define BEEPIO_DEBUG_X(gpio_, pin_, tim_, ch_) GPIO_Init(gpio_, &GPIO_InitStructure);
	BEEPIO_ITEM_LIST
#undef BEEPIO_DEBUG_X
#else																								   /* 无源蜂鸣器 */
	GPIO_InitTypeDef GPIO_InitStructure;

	/* 打开GPIO的时钟 */
#define BEEPIO_DEBUG_X(gpio_, pin_, tim_, ch_) RCC_APB2PeriphClockCmd(RCC_APB2Periph_##gpio_, ENABLE); /*使能GPIOX时钟*/
	BEEPIO_ITEM_LIST
#undef BEEPIO_DEBUG_X

#define BEEPIO_DEBUG_X(gpio_, pin_, tim_, ch_) tim_##_MARK_
#if (BEEPIO_ITEM_LIST == TIM1_MARK_)
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_TIM1, ENABLE);
#elif (BEEPIO_ITEM_LIST == TIM8_MARK_)
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_TIM8, ENABLE);
#elif (BEEPIO_ITEM_LIST == TIM9_MARK_)
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_TIM9, ENABLE);
#elif (BEEPIO_ITEM_LIST == TIM10_MARK_)
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_TIM10, ENABLE);
#elif (BEEPIO_ITEM_LIST == TIM11_MARK_)
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_TIM11, ENABLE);
#elif (BEEPIO_ITEM_LIST == TIM2_MARK_)
	RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM2, ENABLE);
#elif (BEEPIO_ITEM_LIST == TIM3_MARK_)
	RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM3, ENABLE);
#elif (BEEPIO_ITEM_LIST == TIM4_MARK_)
	RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM4, ENABLE);
#elif (BEEPIO_ITEM_LIST == TIM5_MARK_)
	RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM5, ENABLE);
#elif (BEEPIO_ITEM_LIST == TIM6_MARK_)
	RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM6, ENABLE);
#elif (BEEPIO_ITEM_LIST == TIM7_MARK_)
	RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM7, ENABLE);
#elif (BEEPIO_ITEM_LIST == TIM12_MARK_)
	RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM12, ENABLE);
#elif (BEEPIO_ITEM_LIST == TIM13_MARK_)
	RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM13, ENABLE);
#elif (BEEPIO_ITEM_LIST == TIM14_MARK_)
	RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM14, ENABLE);
#else
#endif
#undef BEEPIO_DEBUG_X

/* 配置GPIO */
#define BEEPIO_DEBUG_X(gpio_, pin_, tim_, ch_) GPIO_InitStructure.GPIO_Pin = pin_;
	BEEPIO_ITEM_LIST
#undef BEEPIO_DEBUG_X
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF_PP;	  /* 复用功能 */
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz; /* GPIO速度等级 */
#define BEEPIO_DEBUG_X(gpio_, pin_, tim_, ch_) GPIO_Init(gpio_, &GPIO_InitStructure);
	BEEPIO_ITEM_LIST
#undef BEEPIO_DEBUG_X

	TIM_TimeBaseInitTypeDef TIM_TimeBaseStructure;
	/* Time base configuration */
	TIM_TimeBaseStructure.TIM_Period = -1;
	TIM_TimeBaseStructure.TIM_Prescaler = BEEP_TIM_SCALER;
	TIM_TimeBaseStructure.TIM_ClockDivision = 0;
	TIM_TimeBaseStructure.TIM_CounterMode = TIM_CounterMode_Up;
	TIM_TimeBaseStructure.TIM_RepetitionCounter = 0;
#define BEEPIO_DEBUG_X(gpio_, pin_, tim_, ch_) TIM_TimeBaseInit(tim_, &TIM_TimeBaseStructure);
	BEEPIO_ITEM_LIST
#undef BEEPIO_DEBUG_X

	TIM_OCInitTypeDef TIM_OCInitStructure;
	TIM_OCStructInit(&TIM_OCInitStructure); /* 初始化结构体成员 */

	TIM_OCInitStructure.TIM_OCMode = TIM_OCMode_PWM1;
	TIM_OCInitStructure.TIM_OutputState = TIM_OutputState_Enable;
	TIM_OCInitStructure.TIM_Pulse = 0;
	TIM_OCInitStructure.TIM_OCPolarity = TIM_OCPolarity_High;

	TIM_OCInitStructure.TIM_OutputNState = TIM_OutputNState_Disable; /* only for TIM1 and TIM8. */
	TIM_OCInitStructure.TIM_OCNPolarity = TIM_OCNPolarity_High;		 /* only for TIM1 and TIM8. */
	TIM_OCInitStructure.TIM_OCIdleState = TIM_OCIdleState_Reset;	 /* only for TIM1 and TIM8. */
	TIM_OCInitStructure.TIM_OCNIdleState = TIM_OCNIdleState_Reset;	 /* only for TIM1 and TIM8. */
#define BEEPIO_DEBUG_X(gpio_, pin_, tim_, ch_)     \
	TIM_OC##ch_##Init(tim_, &TIM_OCInitStructure); \
	TIM_OC##ch_##PreloadConfig(tim_, TIM_OCPreload_Enable);
	BEEPIO_ITEM_LIST
#undef BEEPIO_DEBUG_X

#define BEEPIO_DEBUG_X(gpio_, pin_, tim_, ch_) TIM_ARRPreloadConfig(tim_, ENABLE);
	BEEPIO_ITEM_LIST
#undef BEEPIO_DEBUG_X

/* TIMx enable counter */
#define BEEPIO_DEBUG_X(gpio_, pin_, tim_, ch_) TIM_Cmd(tim_, ENABLE);
	BEEPIO_ITEM_LIST
#undef BEEPIO_DEBUG_X

#define BEEPIO_DEBUG_X(gpio_, pin_, tim_, ch_) tim_##_MARK_
#if (BEEPIO_ITEM_LIST == TIM1_MARK_)
	TIM_CtrlPWMOutputs(TIM1, ENABLE);
#elif (BEEPIO_ITEM_LIST == TIM8_MARK_)
	TIM_CtrlPWMOutputs(TIM8, ENABLE);
#else
#endif
#undef BEEPIO_DEBUG_X
#endif
}

/*
*********************************************************************************************************
*	函 数 名: BEEP_Start
*	形    参: mus： 播放的音乐
*	形    参: _usCycle： 鸣叫次数
*	返 回 值: 无
*	功能说明: 启动蜂鸣音。
*********************************************************************************************************
*/
void BEEP_Start(music_t const *mus, uint16_t _usCycle)
{
	g_tBeep.mus = mus;
	g_tBeep.usCycle = _usCycle;
	g_tBeep.usDuratCount = 0; /* 单节拍时长计数归0 */
	g_tBeep.usBeatsCount = 0; /* 单节拍计数归0 */
	g_tBeep.usCycleCount = 0; /* 循环次数计数归0 */
}

/*
*********************************************************************************************************
*	函 数 名: BEEP_Stop
*	形    参: 无
*	返 回 值: 无
*	功能说明: 停止蜂鸣音。
*********************************************************************************************************
*/
void BEEP_Stop(void)
{
	BEEP_Start(NULL, 0);
}

/*
*********************************************************************************************************
*	函 数 名: BEEP_Pro10ms
*	形    参: 无
*	返 回 值: 无
*	功能说明: 每隔10ms调用1次该函数，用于控制蜂鸣器发声。
*********************************************************************************************************
*/
void BEEP_Pro10ms(void)
{
#ifdef BEEP_HAVE_POWER /* 有源蜂鸣器 */
	if ((g_tBeep.mus == NULL) || (g_tBeep.usCycle == 0))
	{
#define BEEPIO_DEBUG_X(gpio_, pin_, tim_, ch_) gpio_->BRR = pin_;
		BEEPIO_ITEM_LIST
#undef BEEPIO_DEBUG_X
		return;
	}

	if (0 == g_tBeep.usDuratCount)
	{ /* 每次节拍时长计数开始就设定一次蜂鸣器*/
#define BEEPIO_DEBUG_X(gpio_, pin_, tim_, ch_) gpio_->BSRR = pin_;
		BEEPIO_ITEM_LIST
#undef BEEPIO_DEBUG_X
	}
#else /* 无源蜂鸣器 */
	if ((g_tBeep.mus == NULL) || (g_tBeep.usCycle == 0))
	{
		// BEEP_OPERATE(0, 0);

#define BEEPIO_DEBUG_X(gpio_, pin_, tim_, ch_) \
	tim_->ARR = -1;                            \
	tim_->PSC = BEEP_TIM_SCALER;               \
	tim_->EGR = TIM_PSCReloadMode_Immediate;
		BEEPIO_ITEM_LIST
#undef BEEPIO_DEBUG_X

#define BEEPIO_DEBUG_X(gpio_, pin_, tim_, ch_) \
	tim_->CCR##ch_ = 0;
		BEEPIO_ITEM_LIST
#undef BEEPIO_DEBUG_X
		return;
	}

	if (0 == g_tBeep.usDuratCount)
	{
		// BEEP_OPERATE(musical_note_time[g_tBeep.mus[0].bea[g_tBeep.usBeatsCount].note], 1);

#define BEEPIO_DEBUG_X(gpio_, pin_, tim_, ch_)                                                         \
	tim_->ARR = (BEEP_TIM_IOD / musical_note_time[g_tBeep.mus[0].bea[g_tBeep.usBeatsCount].note]) - 1; \
	tim_->PSC = BEEP_TIM_SCALER;                                                                       \
	tim_->EGR = TIM_PSCReloadMode_Immediate;
		BEEPIO_ITEM_LIST
#undef BEEPIO_DEBUG_X

#define BEEPIO_DEBUG_X(gpio_, pin_, tim_, ch_) \
	tim_->CCR##ch_ = ((BEEP_TIM_IOD / musical_note_time[g_tBeep.mus[0].bea[g_tBeep.usBeatsCount].note]) - 1) / 2;
		BEEPIO_ITEM_LIST
#undef BEEPIO_DEBUG_X
	}
#endif

	if (++g_tBeep.usDuratCount >= g_tBeep.mus[0].bea[g_tBeep.usBeatsCount].durat)
	{							  /* 每次节拍时长计数超过 */
		g_tBeep.usDuratCount = 0; /* 单节拍时长计数归0 */
		if (++g_tBeep.usBeatsCount >= g_tBeep.mus[0].len)
		{							  /* 每次单节拍计数超过 */
			g_tBeep.usBeatsCount = 0; /* 单节拍计数归0 */
			if (++g_tBeep.usCycleCount >= g_tBeep.usCycle)
			{				 /* 每次循环次数计数超过 */
				BEEP_Stop(); /* 停止蜂鸣音 */
			}
		}
	}
}
#endif
