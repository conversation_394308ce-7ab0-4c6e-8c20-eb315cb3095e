#include <stdlib.h>
#include <string.h>
#include <stdio.h>
#include "bsp.h" /* 底层硬件驱动 */

/* 加密/解密函数 */
void encrypt_decrypt(void);
int main(void)
{
	int tick1ms, tick10ms, tick100ms = 0, tick1000ms = 0;

	bsp_Init(); /* 硬件初始化 */

	bsp_InitUart();

	/* 加密/解密函数 */
	encrypt_decrypt();

	PowerOnStorageOperation(); /* 上电存储操作 */
	while (1)
	{
		// 后板信息数据流解析，非阻塞
		Backboard_data_receive();

		if (bsp_CheckRunTime(tick1ms) > 1) /* 1ms进入一次 */
		{
			tick1ms = bsp_GetRunTime(); /* 更新超时计数 */
#if FIFO_SIZE
			Handling_fifo_Events1ms(); /* 处理任务事件。非阻塞，被s1ms周期性的调用 */
			bsp_TempProbeScan1ms();	   /* 扫描所有探头，做滤波处理，被1ms周期性的调用 */
#endif
		}

		if (bsp_CheckRunTime(tick10ms) > 10) /* 10ms进入一次 */
		{
			tick10ms = bsp_GetRunTime(); /* 更新超时计数 */
			ScanDataChanges10ms();/*扫描数据变化。非阻塞，被10ms周期性的调用*/
#if (UI28_SCREEN_ENABLED == 1)
			UI28_Polling(); /*2.8寸屏幕轮询10ms*/
#endif
		}

		if (bsp_CheckRunTime(tick100ms) > 100) /* 100ms进入一次 */
		{
			tick100ms = bsp_GetRunTime();  /* 更新超时计数 */

			pro_signal_input();	 /* 业务信号传入 */
			pro_signal_output(); /* 业务信号输出 */

			DetectALARM100ms();	   // 扫描所有闹钟。非阻塞，被systick中断周期性的调用，100ms一次
			page_current_update(); /*当前交互界面更新，需要非阻塞轮询*/
		}

		if (bsp_CheckRunTime(tick1000ms) > 1000) /* 1000ms进入一次 */
		{
			tick1000ms = bsp_GetRunTime();											  /* 更新超时计数 */
			Ex_fault_alarm_detection_function();									  /* Ex故障报警检测函数（非阻塞轮询1S） */
			LockStateManagement();													  /* 锁机状态管理（非阻塞轮询1秒） */
			DishwashingBusinessOperation();											  /* 洗碗业务运行管理（非阻塞轮询1秒） */
			Power_on_water_full_once_detection_function();							  /* 上电水满一次检测函数（非阻塞轮询1秒） */
			Stop_operation_management_due_to_water_shortage();						  /* 缺水停止运行管理（非阻塞轮询1秒） */
			Power_on_screen_dwell_time();											  /* 开机画面停留时间（非阻塞轮询1秒） */
			Interface_no_operation_countdown_processing();							  /* 设置界面无操作倒计时处理（非阻塞轮询1S秒） */
			Countdown_processing_for_no_running_operation_on_the_running_interface(); /* 运行界面无运行操作倒计时处理（非阻塞轮询1S秒） */
			Dishwasher_usage_statistics();											  /* 洗碗机用量统计（非阻塞轮询1S秒） */
			Detergent_output_control();												  /* 洗涤剂输出控制（非阻塞轮询1秒） */
			Desiccant_output_control();												  /* 干燥剂输出控制（非阻塞轮询1秒） */
			Inlet_valve_output_control();											  /* 进水阀输出控制（非阻塞轮询1秒） */
			Rinse_heating_output_control();											  /* 漂洗加热输出控制（非阻塞轮询1秒） */
			Washing_and_heating_output_control();									  /* 洗涤加热输出控制（非阻塞轮询1秒） */
			Rinse_pump_output_control();											  /* 漂洗泵输出控制（非阻塞轮询1秒） */
			Washing_pump_output_control();											  /* 洗涤泵输出控制（非阻塞轮询1秒） */
			Door_closing_delay_start_function();									  /* 关门延时启动函数（非阻塞轮询1秒） */
			Business_EE_half_hour_storage_management();								  /*  业务ee半小时存储管理（非阻塞轮询1S秒） */
		}
	}
}

#define KeyAddress 0x08001800 /* 密钥地址APP */
#define UIDAddress 0x1FFFF7E8 /* 唯一ID地址 */

// 解码密钥数组定义
__I uint8_t SecretKey[12] =
	{
		0x0E, 0x1F, 0x18, 0x14, 0x17, 0x15, 0x19, 0x15, 0x15, 0x0B,
		0x16, 0x11 /*, 0x10, 0x15, 0x1C, 0x15, 0x1D, 0x1B, 0x1C, 0x1A,
		 0x12, 0x1A, 0x12, 0x14, 0x0B, 0x14, 0x12, 0x1A, 0x1B, 0x18,
		 0x0C, 0x18, 0x1C, 0x0D, 0x13, 0x11, 0x1C, 0x14, 0x18, 0x14,
		 0x1B, 0x0B*/
};
// 解码密钥数组定义
__I uint8_t ID_Sorting[12] = {9, 3, 8, 6, 11, 4, 0, 2, 1, 10, 5, 7};

// 加密/解密函数
void encrypt_decrypt(void)
{
	int i;
	uint8_t _xor;
	uint32_t ShiftingValue, KeyAdd;
	KeyAdd = KeyAddress;
	ShiftingValue = (((((*(uint32_t *)KeyAdd) >> 7) + ((*(uint32_t *)(KeyAdd + 4)) >> 14) + (*(uint32_t *)(KeyAdd + 8))) ^ (*(uint32_t *)(KeyAdd + 12))) >> 5) & 0x00000007; // 得到移位数

	for (i = 0; i < 12; i++)
	{
		_xor = ((*(uint8_t *)(KeyAdd + 16 + i)) >> ShiftingValue) | ((*(uint8_t *)(KeyAdd + 16 + i)) << (8 - ShiftingValue)); // X位移位
		_xor = _xor ^ (*(uint8_t *)(UIDAddress + ID_Sorting[i]));
		if (_xor != SecretKey[i])
		{
			comSendBuf(COM1, (uint8_t *)&i, 4); // 先数据发送出去
			while (1)
			{
			}
		}
	}
}
