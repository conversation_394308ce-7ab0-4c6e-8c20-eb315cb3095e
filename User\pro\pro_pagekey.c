#include "bsp.h"
volatile uint8_t Judging_the_status_flag; /* 判断状态标志 */

/* 键值处理:基于页面 */
void keyHandle(uint8_t key_V)
{
    interface_settings_No_Action_reset_count = 0; // 设置界面无操作无操作计数（1S\30S）

    if (key_V <= (KEY_ITEM_NUM << 2))
    {
        if ((key_V & 0x03) == 1 /* 同((key_V % 4) != 1)，表示键按下 */)
        {
            return;
        }
    }

    switch (pa_m_tVar.current_page /* 当前交互界面 */)
    {
        /**************************************************** 开机画面 *****************************************************/
    case page_start:
        switch (key_V)
        {
        case K1_SEQ /* 顺序按键1 */:
            page_goto(page_developer); /* 界面切换：开发者 */
            bsp_Putfifo(Bn_BEEP);      /* 压入任务：天空之城 */
            break;
        default:
            bsp_Putfifo(B3_BEEP); /* 压入任务：急促3声(无效提示) */
            break;
        }
        break;
        /**************************************************** 待机菜单 *****************************************************/
    case page_standby:
        if (YES == pro_lock /* 锁机 */)
        {
            bsp_Putfifo(B3_BEEP); /* 压入任务：急促3声(无效提示) */
            return;
        }

        switch (key_V)
        {
        case K1_UP /* 上:短按 */:
            UI28_single_step_album(ui28_1_album, ui28_up); /* 单步调节（上） */
            if (ALBUM_ANIMATION_OVERAMPLITUDE /* 超幅程度值 */)
            {
                bsp_Putfifo(B3_BEEP); /* 压入任务：急促3声(无效提示) */
            }
            else
            {
                bsp_Putfifo(B1_BEEP);                      /* 压入任务：短按键音 */
                UI28_init_text_gradient_alpha(ui28_T2_tg); /* 初始化渐变值 */
            }
            break;
        case K2_UP /* 下:短按 */:
            UI28_single_step_album(ui28_1_album, ui28_down); /* 单步调节（下） */
            if (ALBUM_ANIMATION_OVERAMPLITUDE /* 超幅程度值 */)
            {
                bsp_Putfifo(B3_BEEP); /* 压入任务：急促3声(无效提示) */
            }
            else
            {
                bsp_Putfifo(B1_BEEP);                      /* 压入任务：短按键音 */
                UI28_init_text_gradient_alpha(ui28_T2_tg); /* 初始化渐变值 */
            }
            break;
        case K3_UP /* 运行:短按 */:
            switch (ui28_album_photo_cur_val[ui28_1_album] /* 相册当前光标值 */)
            {
            case PIC_120_120_RUN_B0WL /* 图片【运行碗】索引 */:
                page_goto(page_run); /*切换页面：运行*/
                break;
            case PIC_120_120_SET_UP /* 图片【设置】索引 */:
                page_goto(page_parameter); /*切换页面：参数设置*/
                break;
            case PIC_120_120_DEBUG /* 图片【调试】索引 */:
                if (Not_receiving_correct_data_count > 0) // 正常是大于0的，等于0表示通讯异常但前板有电
                {
                    page_goto(page_debug); /*切换页面：调试*/
                }
                else
                {
                    bsp_Putfifo(B3_BEEP); /* 压入任务：急促3声(无效提示) */
                    return;
                }
                break;
            case PIC_120_120_QR_CODE /* 图片【二维码】索引 */:
                bsp_Putfifo(B3_BEEP); /* 压入任务：急促3声(无效提示) */
                return;
                break;
            case PIC_120_120_COUNT /* 图片【统计】索引 */:
                page_goto(page_statistics); /*切换页面：统计查看*/
                break;
            default:
                break;
            }
            bsp_Putfifo(B1_BEEP); /* 压入任务：短按键音 */
            break;
        case K2_SEQ /* 顺序按键2 */:
            page_goto(page_qr_code); /*切换页面：二维码(物联网)*/
            bsp_Putfifo(B1_BEEP); /* 压入任务：短按键音 */
            break;
        default:
            bsp_Putfifo(B3_BEEP); /* 压入任务：急促3声(无效提示) */
            break;
        }
        break;
        /**************************************************** 运行 *****************************************************/
    case page_run:
        if (Alarm_Flag != warn_empty)
        {
            /* 发生了报警 */
            if ((key_V & 0x03) == 2 /* 同((key_V % 4) != 1)，表示键抬起 */)
            {
                page_goto(page_standby); /*切换页面：待机菜单*/
                bsp_Putfifo(B1_BEEP);    /* 压入任务：短按键音 */
                return;
            }
        }

        switch (key_V)
        {
        case K3_UP /* 运行:短按 */:
        {
            uint8_t i = From_suspending_business_to_starting() /* 从暂停业务到启动 */;
            // 返回0：条件没达到 1：启动成功 2：已经在运行中
            if (i == 1)
            {
                bsp_Putfifo(B1_BEEP); /* 压入任务：短按键音 */
            }
            else if (i == 2)
            {
                DishwashingCloseBusiness(); // 关闭业务
                bsp_Putfifo(B1_BEEP);       /* 压入任务：短按键音 */
            }
            else
            {
                bsp_Putfifo(B3_BEEP); /* 压入任务：急促3声(无效提示) */
            }
        }
        break;
        case K3_LONG /* 运行:长按 */:
            page_goto(page_standby); /*切换页面：待机菜单*/
            bsp_Putfifo(B2_BEEP);    /* 压入任务：长按键音 */
            break;
        case K4_UP /* 模式-:短按 */:
            page_goto(page_check); /*切换页面：状态查看*/
            bsp_Putfifo(B1_BEEP);  /* 压入任务：短按键音 */
            break;
        case K5_UP /* 查看+:短按 */:
            parame_adj(USR_mode /*模式 */, 0, PARAME_INC /* 增加 */, PARAME_S_STEP /* 单步 */);
            bsp_Putfifo(B1_BEEP); /* 压入任务：短按键音 */
            break;
        default:
            bsp_Putfifo(B3_BEEP); /* 压入任务：急促3声(无效提示) */
            break;
        }
        break;
        /**************************************************** 参数设置 *****************************************************/
    case page_parameter:
        switch (ui28_menu_FontSize /* 菜单文本表选择 */)
        {
        case ui28_1_menu /* 选择用户或厂家 */:
            switch (key_V)
            {
            case K1_UP /* 上:短按 */:
                UI28_single_step_menu(ui28_up /* 单步调节（上） */); /* 单步调节（上下） */
                if (MENU_ANIMATION_OVERAMPLITUDE /* 超幅程度值 */)
                {
                    bsp_Putfifo(B3_BEEP); /* 压入任务：急促3声(无效提示) */
                }
                else
                {
                    bsp_Putfifo(B1_BEEP); /* 压入任务：短按键音 */
                }
                break;
            case K2_UP /* 下:短按 */:
                UI28_single_step_menu(ui28_down /* 单步调节（下） */); /* 单步调节（上下） */
                if (MENU_ANIMATION_OVERAMPLITUDE /* 超幅程度值 */)
                {
                    bsp_Putfifo(B3_BEEP); /* 压入任务：急促3声(无效提示) */
                }
                else
                {
                    bsp_Putfifo(B1_BEEP); /* 压入任务：短按键音 */
                }
                break;
            case K3_UP /* 运行:短按 */:
                if (ui28_menu_cur_val /* 当前光标值 */ == 0 /* 用户参数 */)
                {
                    switch (Dis_type /*洗碗机类型*/)
                    {
                    case Dis_t_1 /*5继电器，2调速（催干剂 洗涤剂）*/:
                        UI28_change_table_menu(ui28_2_menu /* 用户参数 */, 0, ui28_1_menu_order); /* 切换菜单表（切换菜单表需要设置光标位置） */
                        break;
                    case Dis_t_2 /*7继电器，无调速*/:
                        UI28_change_table_menu(ui28_2_menu /* 用户参数 */, 0, ui28_2_menu_order); /* 切换菜单表（切换菜单表需要设置光标位置） */
                        break;
                    default:
                        break;
                    }
                    bsp_Putfifo(B1_BEEP); /* 压入任务：短按键音 */
                }
                else
                {
                    bsp_Putfifo(B3_BEEP); /* 压入任务：急促3声(无效提示) */
                }
                break;
            case K3_LONG /* 运行:长按 */:
                page_goto(page_home); /*切换页面：返回根界面*/
                bsp_Putfifo(B2_BEEP); /* 压入任务：长按键音 */
                break;
            case K2_SEQ /* 顺序按键2 */:
                if (ui28_menu_cur_val /* 当前光标值 */ == 0 /* 用户参数 */)
                {
                    switch (Dis_type /*洗碗机类型*/)
                    {
                    case Dis_t_1 /*5继电器，2调速（催干剂 洗涤剂）*/:
                        UI28_change_table_menu(ui28_2_menu /* 用户参数 */, 0, ui28_1_menu_order); /* 切换菜单表（切换菜单表需要设置光标位置） */
                        break;
                    case Dis_t_2 /*7继电器，无调速*/:
                        UI28_change_table_menu(ui28_2_menu /* 用户参数 */, 0, ui28_2_menu_order); /* 切换菜单表（切换菜单表需要设置光标位置） */
                        break;
                    default:
                        break;
                    }
                }
                else if (ui28_menu_cur_val /* 当前光标值 */ == 2 /* 硬件配置 */)
                {
                    switch (Dis_type /*洗碗机类型*/)
                    {
                    case Dis_t_1 /*5继电器，2调速（催干剂 洗涤剂）*/:
                        UI28_change_table_menu(ui28_4_menu /* 硬件配置 */, 0, ui28_3_menu_order); /* 切换菜单表（切换菜单表需要设置光标位置） */
                        break;
                    case Dis_t_2 /*7继电器，无调速*/:
                        UI28_change_table_menu(ui28_4_menu /* 硬件配置 */, 0, ui28_4_menu_order); /* 切换菜单表（切换菜单表需要设置光标位置） */
                        break;
                    default:
                        break;
                    }
                }
                else
                {
                    UI28_change_table_menu(ui28_3_menu /* 厂家参数 */, 0, ui28_5_menu_order); /* 切换菜单表（切换菜单表需要设置光标位置） */
                }
                bsp_Putfifo(B1_BEEP); /* 压入任务：短按键音 */
                break;
            default:
                bsp_Putfifo(B3_BEEP); /* 压入任务：急促3声(无效提示) */
                break;
            }
            break;
        case ui28_2_menu /* 用户参数 */:
            if (Judging_the_status_flag /* 判断状态标志 */ == 1)
            {
                switch (key_V)
                {
                case K4_REPEAT /* 模式 -:连发 */:
                    break;
                case K4_UP /* 模式 -:短按 */:
                    Judging_the_status_flag = 0; /* 判断状态标志 */
                    bsp_Putfifo(B1_BEEP);        /* 压入任务：短按键音 */
                    break;
                case K5_REPEAT /* 查看 +:连发 */:
                    break;
                case K5_UP /* 查看 +:短按 */:
                    Init_parame_level(RST_LEVEL_USER, 0); /* 按等级初始化参数 */
                    Judging_the_status_flag = 0;          /* 判断状态标志 */
                    bsp_Putfifo(B1_BEEP);                 /* 压入任务：短按键音 */
                    break;
                default:
                    bsp_Putfifo(B3_BEEP); /* 压入任务：急促3声(无效提示) */
                    break;
                }
                return;
            }

            switch (key_V)
            {
            case K1_LONG:             /* 上:长按 */
            case K2_LONG:             /* 下:长按 */
            case K4_LONG:             /* 模式 -:长按 */
            case K5_LONG:             /* 查看 +:长按 */
                bsp_Putfifo(B2_BEEP); /* 压入任务：长按键音 */
                break;
            case K1_REPEAT /* 上:连发 */:
                UI28_single_step_menu(ui28_up /* 单步调节（上） */); /* 单步调节（上下） */
                break;
            case K1_UP /* 上:短按 */:
                UI28_single_step_menu(ui28_up /* 单步调节（上） */); /* 单步调节（上下） */
                if (MENU_ANIMATION_OVERAMPLITUDE /* 超幅程度值 */)
                {
                    bsp_Putfifo(B3_BEEP); /* 压入任务：急促3声(无效提示) */
                }
                else
                {
                    bsp_Putfifo(B1_BEEP); /* 压入任务：短按键音 */
                }
                break;
            case K2_REPEAT /* 下:连发 */:
                UI28_single_step_menu(ui28_down /* 单步调节（下） */); /* 单步调节（上下） */
                break;
            case K2_UP /* 下:短按 */:
                UI28_single_step_menu(ui28_down /* 单步调节（下） */); /* 单步调节（上下） */
                if (MENU_ANIMATION_OVERAMPLITUDE /* 超幅程度值 */)
                {
                    bsp_Putfifo(B3_BEEP); /* 压入任务：急促3声(无效提示) */
                }
                else
                {
                    bsp_Putfifo(B1_BEEP); /* 压入任务：短按键音 */
                }
                break;
            case K4_REPEAT /* 模式 -:连发 */:
                /*用户可调参数调整*/
                parame_adj((ui28_menu_order_cur_val /* 当前光标在顺序表中的值 */ + USR_user_1), 0, PARAME_DEC /* 减少 */, PARAME_D_STEP /* 连步 */);
                break;
            case K4_UP /* 模式 -:短按 */:
                /*用户可调参数调整*/
                parame_adj((ui28_menu_order_cur_val /* 当前光标在顺序表中的值 */ + USR_user_1), 0, PARAME_DEC /* 减少 */, PARAME_S_STEP /* 单步 */);
                bsp_Putfifo(B1_BEEP); /* 压入任务：短按键音 */
                break;
            case K5_REPEAT /* 查看 +:连发 */:
                /*用户可调参数调整*/
                parame_adj((ui28_menu_order_cur_val /* 当前光标在顺序表中的值 */ + USR_user_1), 0, PARAME_INC /* 增加 */, PARAME_D_STEP /* 连步 */);
                break;
            case K5_UP /* 查看 +:短按 */:
                /*用户可调参数调整*/
                parame_adj((ui28_menu_order_cur_val /* 当前光标在顺序表中的值 */ + USR_user_1), 0, PARAME_INC /* 增加 */, PARAME_S_STEP /* 单步 */);
                bsp_Putfifo(B1_BEEP); /* 压入任务：短按键音 */
                break;
            case K3_UP /* 运行:短按 */:
                UI28_change_table_menu(ui28_1_menu /* 用户参数 */, 0, UI28_MENU_ORDER_ITEM_NUM); /* 切换菜单表（切换菜单表需要设置光标位置） */
                bsp_Putfifo(B1_BEEP);                                                            /* 压入任务：短按键音 */
                break;
            case K3_LONG /* 运行:长按 */:
                UI28_change_table_menu(ui28_1_menu /* 用户参数 */, 0, UI28_MENU_ORDER_ITEM_NUM); /* 切换菜单表（切换菜单表需要设置光标位置） */
                bsp_Putfifo(B2_BEEP);                                                            /* 压入任务：长按键音 */
                break;
            case K4K5_LONG /* -+:长按 */:
                Judging_the_status_flag = 1; /* 判断状态标志 */
                bsp_Putfifo(B2_BEEP);        /* 压入任务：长按键音 */
                break;
            default:
                bsp_Putfifo(B3_BEEP); /* 压入任务：急促3声(无效提示) */
                break;
            }
            break;
        case ui28_3_menu /* 厂家参数 */:
            if (Judging_the_status_flag /* 判断状态标志 */ == 1)
            {
                switch (key_V)
                {
                case K4_REPEAT /* 模式 -:连发 */:
                    break;
                case K4_UP /* 模式 -:短按 */:
                    Judging_the_status_flag = 0; /* 判断状态标志 */
                    bsp_Putfifo(B1_BEEP);        /* 压入任务：短按键音 */
                    break;
                case K5_REPEAT /* 查看 +:连发 */:
                    break;
                case K5_UP /* 查看 +:短按 */:
                    Init_parame_level(RST_LEVEL_USER_1, PV(int, USR_set_tad, 0)); /* 按等级初始化参数 */
                    Judging_the_status_flag = 0;                                  /* 判断状态标志 */
                    bsp_Putfifo(B1_BEEP);                                         /* 压入任务：短按键音 */
                    break;
                default:
                    bsp_Putfifo(B3_BEEP); /* 压入任务：急促3声(无效提示) */
                    break;
                }
                return;
            }

            switch (key_V)
            {
            case K1_LONG:             /* 上:长按 */
            case K2_LONG:             /* 下:长按 */
            case K4_LONG:             /* 模式 -:长按 */
            case K5_LONG:             /* 查看 +:长按 */
                bsp_Putfifo(B2_BEEP); /* 压入任务：长按键音 */
                break;
            case K1_REPEAT /* 上:连发 */:
                UI28_single_step_menu(ui28_up /* 单步调节（上） */); /* 单步调节（上下） */
                break;
            case K1_UP /* 上:短按 */:
                UI28_single_step_menu(ui28_up /* 单步调节（上） */); /* 单步调节（上下） */
                if (MENU_ANIMATION_OVERAMPLITUDE /* 超幅程度值 */)
                {
                    bsp_Putfifo(B3_BEEP); /* 压入任务：急促3声(无效提示) */
                }
                else
                {
                    bsp_Putfifo(B1_BEEP); /* 压入任务：短按键音 */
                }
                break;
            case K2_REPEAT /* 下:连发 */:
                UI28_single_step_menu(ui28_down /* 单步调节（下） */); /* 单步调节（上下） */
                break;
            case K2_UP /* 下:短按 */:
                UI28_single_step_menu(ui28_down /* 单步调节（下） */); /* 单步调节（上下） */
                if (MENU_ANIMATION_OVERAMPLITUDE /* 超幅程度值 */)
                {
                    bsp_Putfifo(B3_BEEP); /* 压入任务：急促3声(无效提示) */
                }
                else
                {
                    bsp_Putfifo(B1_BEEP); /* 压入任务：短按键音 */
                }
                break;
            case K4_REPEAT /* 模式 -:连发 */:
                /*用户可调参数调整*/
                if (ui28_menu_cur_val == 0)
                {
                    parame_adj(USR_set_tad, 0, PARAME_DEC /* 减少 */, PARAME_D_STEP /* 连步 */);
                }
                else
                {
                    parame_adj((ui28_menu_order_cur_val /* 当前光标在顺序表中的值 */ + USR_set_tad), PV(int, USR_set_tad, 0), PARAME_DEC /* 减少 */, PARAME_D_STEP /* 连步 */);
                }
                break;
            case K4_UP /* 模式 -:短按 */:
                /*用户可调参数调整*/
                if (ui28_menu_cur_val == 0)
                {
                    parame_adj(USR_set_tad, 0, PARAME_DEC /* 减少 */, PARAME_S_STEP /* 单步 */);
                }
                else
                {
                    parame_adj((ui28_menu_order_cur_val /* 当前光标在顺序表中的值 */ + USR_set_tad), PV(int, USR_set_tad, 0), PARAME_DEC /* 减少 */, PARAME_S_STEP /* 单步 */);
                }
                bsp_Putfifo(B1_BEEP); /* 压入任务：短按键音 */
                break;
            case K5_REPEAT /* 查看 +:连发 */:
                /*用户可调参数调整*/
                if (ui28_menu_cur_val == 0)
                {
                    parame_adj(USR_set_tad, 0, PARAME_INC /* 增加 */, PARAME_D_STEP /* 连步 */);
                }
                else
                {
                    parame_adj((ui28_menu_order_cur_val /* 当前光标在顺序表中的值 */ + USR_set_tad), PV(int, USR_set_tad, 0), PARAME_INC /* 增加 */, PARAME_D_STEP /* 连步 */);
                }
                break;
            case K5_UP /* 查看 +:短按 */:
                /*用户可调参数调整*/
                if (ui28_menu_cur_val == 0)
                {
                    parame_adj(USR_set_tad, 0, PARAME_INC /* 增加 */, PARAME_S_STEP /* 单步 */);
                }
                else
                {
                    parame_adj((ui28_menu_order_cur_val /* 当前光标在顺序表中的值 */ + USR_set_tad), PV(int, USR_set_tad, 0), PARAME_INC /* 增加 */, PARAME_S_STEP /* 单步 */);
                }
                bsp_Putfifo(B1_BEEP); /* 压入任务：短按键音 */
                break;
            case K3_UP /* 运行:短按 */:
                UI28_change_table_menu(ui28_1_menu /* 用户参数 */, 1, UI28_MENU_ORDER_ITEM_NUM); /* 切换菜单表（切换菜单表需要设置光标位置） */
                bsp_Putfifo(B1_BEEP);                                                            /* 压入任务：短按键音 */
                break;
            case K3_LONG /* 运行:长按 */:
                UI28_change_table_menu(ui28_1_menu /* 用户参数 */, 1, UI28_MENU_ORDER_ITEM_NUM); /* 切换菜单表（切换菜单表需要设置光标位置） */
                bsp_Putfifo(B2_BEEP);                                                            /* 压入任务：长按键音 */
                break;
            case K4K5_LONG /* -+:长按 */:
                Judging_the_status_flag = 1; /* 判断状态标志 */
                bsp_Putfifo(B2_BEEP);        /* 压入任务：长按键音 */
                break;
            default:
                bsp_Putfifo(B3_BEEP); /* 压入任务：急促3声(无效提示) */
                break;
            }
            break;
        case ui28_4_menu /* 硬件配置 */:
            if (Judging_the_status_flag /* 判断状态标志 */ == 1)
            {
                switch (key_V)
                {
                case K4_REPEAT /* 模式 -:连发 */:
                    break;
                case K4_UP /* 模式 -:短按 */:
                    Judging_the_status_flag = 0; /* 判断状态标志 */
                    bsp_Putfifo(B1_BEEP);        /* 压入任务：短按键音 */
                    break;
                case K5_REPEAT /* 查看 +:连发 */:
                    break;
                case K5_UP /* 查看 +:短按 */:
                    Init_parame_level(RST_LEVEL_USER_2, 0); /* 按等级初始化参数 */
                    Judging_the_status_flag = 0;            /* 判断状态标志 */
                    bsp_Putfifo(B1_BEEP);                   /* 压入任务：短按键音 */
                    break;
                default:
                    bsp_Putfifo(B3_BEEP); /* 压入任务：急促3声(无效提示) */
                    break;
                }
                return;
            }

            switch (key_V)
            {
            case K1_LONG:             /* 上:长按 */
            case K2_LONG:             /* 下:长按 */
            case K4_LONG:             /* 模式 -:长按 */
            case K5_LONG:             /* 查看 +:长按 */
                bsp_Putfifo(B2_BEEP); /* 压入任务：长按键音 */
                break;
            case K1_REPEAT /* 上:连发 */:
                UI28_single_step_menu(ui28_up /* 单步调节（上） */); /* 单步调节（上下） */
                break;
            case K1_UP /* 上:短按 */:
                UI28_single_step_menu(ui28_up /* 单步调节（上） */); /* 单步调节（上下） */
                if (MENU_ANIMATION_OVERAMPLITUDE /* 超幅程度值 */)
                {
                    bsp_Putfifo(B3_BEEP); /* 压入任务：急促3声(无效提示) */
                }
                else
                {
                    bsp_Putfifo(B1_BEEP); /* 压入任务：短按键音 */
                }
                break;
            case K2_REPEAT /* 下:连发 */:
                UI28_single_step_menu(ui28_down /* 单步调节（下） */); /* 单步调节（上下） */
                break;
            case K2_UP /* 下:短按 */:
                UI28_single_step_menu(ui28_down /* 单步调节（下） */); /* 单步调节（上下） */
                if (MENU_ANIMATION_OVERAMPLITUDE /* 超幅程度值 */)
                {
                    bsp_Putfifo(B3_BEEP); /* 压入任务：急促3声(无效提示) */
                }
                else
                {
                    bsp_Putfifo(B1_BEEP); /* 压入任务：短按键音 */
                }
                break;
            case K4_REPEAT /* 模式 -:连发 */:
                /*用户可调参数调整*/
                parame_adj((ui28_menu_order_cur_val /* 当前光标在顺序表中的值 */ + USR_relay_1), 0, PARAME_DEC /* 减少 */, PARAME_D_STEP /* 连步 */);
                break;
            case K4_UP /* 模式 -:短按 */:
                /*用户可调参数调整*/
                parame_adj((ui28_menu_order_cur_val /* 当前光标在顺序表中的值 */ + USR_relay_1), 0, PARAME_DEC /* 减少 */, PARAME_S_STEP /* 单步 */);
                bsp_Putfifo(B1_BEEP); /* 压入任务：短按键音 */
                break;
            case K5_REPEAT /* 查看 +:连发 */:
                /*用户可调参数调整*/
                parame_adj((ui28_menu_order_cur_val /* 当前光标在顺序表中的值 */ + USR_relay_1), 0, PARAME_INC /* 增加 */, PARAME_D_STEP /* 连步 */);
                break;
            case K5_UP /* 查看 +:短按 */:
                /*用户可调参数调整*/
                parame_adj((ui28_menu_order_cur_val /* 当前光标在顺序表中的值 */ + USR_relay_1), 0, PARAME_INC /* 增加 */, PARAME_S_STEP /* 单步 */);
                bsp_Putfifo(B1_BEEP); /* 压入任务：短按键音 */
                break;
            case K3_UP /* 运行:短按 */:
                UI28_change_table_menu(ui28_1_menu /* 用户参数 */, 2, UI28_MENU_ORDER_ITEM_NUM); /* 切换菜单表（切换菜单表需要设置光标位置） */
                bsp_Putfifo(B1_BEEP);                                                            /* 压入任务：短按键音 */
                break;
            case K3_LONG /* 运行:长按 */:
                UI28_change_table_menu(ui28_1_menu /* 用户参数 */, 2, UI28_MENU_ORDER_ITEM_NUM); /* 切换菜单表（切换菜单表需要设置光标位置） */
                bsp_Putfifo(B2_BEEP);                                                            /* 压入任务：长按键音 */
                break;
            case K4K5_LONG /* -+:长按 */:
                Judging_the_status_flag = 1; /* 判断状态标志 */
                bsp_Putfifo(B2_BEEP);        /* 压入任务：长按键音 */
                break;
            default:
                bsp_Putfifo(B3_BEEP); /* 压入任务：急促3声(无效提示) */
                break;
            }
            break;
        default:
            break;
        }
        break;
        /**************************************************** 调试 *****************************************************/
    case page_debug:
        switch (key_V)
        {
        case K1_UP /* 上:短按 */:
            if (Judging_the_status_flag == 0)
            {
                Judging_the_status_flag = 6;
            }
            else
            {
                Judging_the_status_flag--;
            }
            bsp_Putfifo(B1_BEEP); /* 压入任务：短按键音 */
            break;
        case K2_UP /* 下:短按 */:
            if (Judging_the_status_flag == 6)
            {
                Judging_the_status_flag = 0;
            }
            else
            {
                Judging_the_status_flag++;
            }
            bsp_Putfifo(B1_BEEP); /* 压入任务：短按键音 */
            break;
        case K4_UP /* 模式 -:短按 */:
            switch (Dis_type /*洗碗机类型*/)
            {
            case Dis_t_1 /*5继电器，2调速（催干剂 洗涤剂）*/:
                if (Judging_the_status_flag < 5)
                {
                    (InformationOutput /* 开关量输出 */ ^= (1 << Judging_the_status_flag)); // 反转/* 继电器输出 */
                }
                else
                {
                    if (GearOutput[Judging_the_status_flag - 5] == 0)
                    {
                        GearOutput[Judging_the_status_flag - 5] = 100;
                    }
                    else
                    {
                        GearOutput[Judging_the_status_flag - 5]--;
                    }
                }
                break;
            case Dis_t_2 /*7继电器，无调速*/:
                if (Judging_the_status_flag < 7)
                {
                    (InformationOutput /* 开关量输出 */ ^= (1 << Judging_the_status_flag)); // 反转/* 继电器输出 */
                }
                break;
            default:
                break;
            }
            bsp_Putfifo(B1_BEEP); /* 压入任务：短按键音 */
            break;
        case K4_REPEAT /* 模式 -:连发 */:
            switch (Dis_type /*洗碗机类型*/)
            {
            case Dis_t_1 /*5继电器，2调速（催干剂 洗涤剂）*/:
                if (Judging_the_status_flag >= 5)
                {
                    if (GearOutput[Judging_the_status_flag - 5] == 0)
                    {
                        GearOutput[Judging_the_status_flag - 5] = 100;
                    }
                    else
                    {
                        GearOutput[Judging_the_status_flag - 5]--;
                    }
                }
                break;
            case Dis_t_2 /*7继电器，无调速*/:
                break;
            default:
                break;
            }
            break;
        case K5_UP /* 查看 +:短按 */:
            switch (Dis_type /*洗碗机类型*/)
            {
            case Dis_t_1 /*5继电器，2调速（催干剂 洗涤剂）*/:
                if (Judging_the_status_flag < 5)
                {
                    (InformationOutput /* 开关量输出 */ ^= (1 << Judging_the_status_flag)); // 反转/* 继电器输出 */
                }
                else
                {
                    if (GearOutput[Judging_the_status_flag - 5] == 100)
                    {
                        GearOutput[Judging_the_status_flag - 5] = 0;
                    }
                    else
                    {
                        GearOutput[Judging_the_status_flag - 5]++;
                    }
                }
                break;
            case Dis_t_2 /*7继电器，无调速*/:
                if (Judging_the_status_flag < 7)
                {
                    (InformationOutput /* 开关量输出 */ ^= (1 << Judging_the_status_flag)); // 反转/* 继电器输出 */
                }
                break;
            default:
                break;
            }
            bsp_Putfifo(B1_BEEP); /* 压入任务：短按键音 */
            break;
        case K5_REPEAT /* 查看 +:连发 */:
            switch (Dis_type /*洗碗机类型*/)
            {
            case Dis_t_1 /*5继电器，2调速（催干剂 洗涤剂）*/:
                if (Judging_the_status_flag >= 5)
                {
                    if (GearOutput[Judging_the_status_flag - 5] == 100)
                    {
                        GearOutput[Judging_the_status_flag - 5] = 0;
                    }
                    else
                    {
                        GearOutput[Judging_the_status_flag - 5]++;
                    }
                }
                break;
            case Dis_t_2 /*7继电器，无调速*/:
                break;
            default:
                break;
            }
            break;
        case K4_LONG:             /* 模式 -:长按 */
        case K5_LONG:             /* 查看 +:长按 */
            bsp_Putfifo(B2_BEEP); /* 压入任务：长按键音 */
            break;
        case K3_UP /* 运行:短按 */:
            page_goto(page_home); /*切换页面：返回根界面*/
            bsp_Putfifo(B1_BEEP); /* 压入任务：短按键音 */
            break;
        case K3_LONG /* 运行:长按 */:
            page_goto(page_home); /*切换页面：返回根界面*/
            bsp_Putfifo(B2_BEEP); /* 压入任务：长按键音 */
            break;
        default:
            bsp_Putfifo(B3_BEEP); /* 压入任务：急促3声(无效提示) */
            break;
        }
        break;
        /**************************************************** 统计查看 *****************************************************/
    case page_statistics:
        if (Judging_the_status_flag /* 判断状态标志 */ == 1)
        {
            switch (key_V)
            {
            case K4_UP /* 模式 -:短按 */:
                Judging_the_status_flag = 0; /* 判断状态标志 */
                bsp_Putfifo(B1_BEEP);        /* 压入任务：短按键音 */
                break;
            case K5_UP /* 查看 +:短按 */:
                if (ui28_menu_FontSize /* 菜单文本表选择 */ == ui28_5_menu /* 用户用量统计 */)
                {
                    Init_parame_level(RST_LEVEL_USER_3, 0); /* 按等级初始化参数 */
                }
                else if (ui28_menu_FontSize /* 菜单文本表选择 */ == ui28_6_menu /* 厂家用量统计 */)
                {
                    Init_parame_level(RST_LEVEL_USER_4, 0); /* 按等级初始化参数 */
                }
                Judging_the_status_flag = 0; /* 判断状态标志 */
                bsp_Putfifo(B1_BEEP);        /* 压入任务：短按键音 */
                break;
            default:
                bsp_Putfifo(B3_BEEP); /* 压入任务：急促3声(无效提示) */
                break;
            }
            return;
        }

        if (ui28_menu_FontSize /* 菜单文本表选择 */ == ui28_5_menu /* 用户用量统计 */)
        {
            switch (key_V)
            {
            case K3_UP /* 运行:短按 */:
                page_goto(page_home); /*切换页面：返回根界面*/
                bsp_Putfifo(B1_BEEP); /* 压入任务：短按键音 */
                break;
            case K3_LONG /* 运行:长按 */:
                page_goto(page_home); /*切换页面：返回根界面*/
                bsp_Putfifo(B2_BEEP); /* 压入任务：长按键音 */
                break;
            case K4K5_LONG /* -+:长按 */:
                Judging_the_status_flag = 1; /* 判断状态标志 */
                bsp_Putfifo(B2_BEEP);        /* 压入任务：长按键音 */
                break;
            case K2_SEQ /* 顺序按键2 */:
                UI28_change_table_menu(ui28_6_menu /* 总统计 */, 0, UI28_MENU_ORDER_ITEM_NUM); /* 切换菜单表（切换菜单表需要设置光标位置） */
                bsp_Putfifo(B1_BEEP);                                                            /* 压入任务：短按键音 */
                break;
            default:
                bsp_Putfifo(B3_BEEP); /* 压入任务：急促3声(无效提示) */
                break;
            }
        }
        else if (ui28_menu_FontSize /* 菜单文本表选择 */ == ui28_6_menu /* 厂家用量统计 */)
        {
            switch (key_V)
            {
            case K3_UP /* 运行:短按 */:
                page_goto(page_home); /*切换页面：返回根界面*/
                bsp_Putfifo(B1_BEEP); /* 压入任务：短按键音 */
                break;
            case K3_LONG /* 运行:长按 */:
                page_goto(page_home); /*切换页面：返回根界面*/
                bsp_Putfifo(B2_BEEP); /* 压入任务：长按键音 */
                break;
            case K4K5_LONG /* -+:长按 */:
                Judging_the_status_flag = 1; /* 判断状态标志 */
                bsp_Putfifo(B2_BEEP);        /* 压入任务：长按键音 */
                break;
            default:
                bsp_Putfifo(B3_BEEP); /* 压入任务：急促3声(无效提示) */
                break;
            }
        }
        break;
        /**************************************************** 状态查看 *****************************************************/
    case page_check:
        switch (key_V)
        {
        case K1_UP /* 上:短按 */:
        case K2_UP /* 下:短按 */:
        case K3_UP /* 运行:短按 */:
        case K4_UP /* 模式 -:短按 */:
        case K5_UP /* 查看 +:短按 */:
            page_goto(page_home); /*切换页面：返回根界面*/
            bsp_Putfifo(B1_BEEP); /* 压入任务：短按键音 */
            break;
        case K1_LONG /* 上:长按 */:
        case K2_LONG /* 下:长按 */:
        case K3_LONG /* 运行:长按 */:
        case K4_LONG /* 模式 -:长按 */:
        case K5_LONG /* 查看 +:长按 */:
            page_goto(page_home); /*切换页面：返回根界面*/
            bsp_Putfifo(B2_BEEP); /* 压入任务：长按键音 */
            break;
        default:
            bsp_Putfifo(B3_BEEP); /* 压入任务：急促3声(无效提示) */
            break;
        }
        break;
        /**************************************************** 开发者 *****************************************************/
    case page_developer:
        switch (key_V)
        {
        case K1_UP /* 上:短按 */:
        case K2_UP /* 下:短按 */:
        case K3_UP /* 运行:短按 */:
        case K4_UP /* 模式 -:短按 */:
        case K5_UP /* 查看 +:短按 */:
            page_goto(page_home); /*切换页面：返回根界面*/
            bsp_Putfifo(B1_BEEP); /* 压入任务：短按键音 */
            break;
        case K1_LONG /* 上:长按 */:
        case K2_LONG /* 下:长按 */:
        case K3_LONG /* 运行:长按 */:
        case K4_LONG /* 模式 -:长按 */:
        case K5_LONG /* 查看 +:长按 */:
            page_goto(page_home); /*切换页面：返回根界面*/
            bsp_Putfifo(B2_BEEP); /* 压入任务：长按键音 */
            break;
        default:
            bsp_Putfifo(B3_BEEP); /* 压入任务：急促3声(无效提示) */
            break;
        }
        break;
        /**************************************************** 二维码 *****************************************************/
    case page_qr_code:
        switch (key_V)
        {
        case K1_UP /* 上:短按 */:
        case K2_UP /* 下:短按 */:
        case K3_UP /* 运行:短按 */:
        case K4_UP /* 模式 -:短按 */:
        case K5_UP /* 查看 +:短按 */:
            page_goto(page_home); /*切换页面：返回根界面*/
            bsp_Putfifo(B1_BEEP); /* 压入任务：短按键音 */
            break;
        case K1_LONG /* 上:长按 */:
        case K2_LONG /* 下:长按 */:
        case K3_LONG /* 运行:长按 */:
        case K4_LONG /* 模式 -:长按 */:
        case K5_LONG /* 查看 +:长按 */:
            page_goto(page_home); /*切换页面：返回根界面*/
            bsp_Putfifo(B2_BEEP); /* 压入任务：长按键音 */
            break;
        default:
            bsp_Putfifo(B3_BEEP); /* 压入任务：急促3声(无效提示) */
            break;
        }
        break;
    default:
        /* 其它值不处理 */
        break;
    }
}
