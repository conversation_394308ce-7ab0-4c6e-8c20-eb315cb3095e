/*******************************************************************************
 * 页面管理器
 ******************************************************************************/
#include "page.h"
/*根页面初始化*/
#define ROOT_PAGE_INIT(page) {ROOT_PAGE, page_##page##_root_load, page_##page##_view_load, page_##page##_update}
/*子页面初始化*/
#define SUB_PAGE_INIT(page) {SUB_PAGE, NULL, page_##page##_view_load, page_##page##_update}

/*页面配置列表*/
const page_t pa_tVar[] =
    {
#define PAGE_ID_DEBUG_X(name, CLASS) CLASS##_INIT(name),
        PAGE_ID_ITEM_LIST
#undef PAGE_ID_DEBUG_X
}; /* 界面结构体 */
volatile page_mgr_t pa_m_tVar; /* 界面管理器 */

/*初始化页面管理器并切换到第一个根页面，注意必须是根界面*/
#define PAGE_INITMGR_GOTU(page)             \
    do                                      \
    {                                       \
        pa_m_tVar.current_page = page;  \
        pa_m_tVar.root_page = page;     \
        pa_tVar[page].page_view_load(); \
    } while (0)
/*
*********************************************************************************************************
*	函 数 名: page_mgr_init
*	形    参: 无
*	返 回 值: 无
*	功能说明: 界面管理器初始化
*********************************************************************************************************
*/
void page_mgr_init(void)
{
    /*切换到开机页面*/
    /*注意，初始化时候不能使用函数page_goto切换*/
    PAGE_INITMGR_GOTU(page_start);
}

/*
*********************************************************************************************************
*	函 数 名: page_goto
*	形    参: page-要切换的界面
*	返 回 值: 无
*	功能说明: 切换界面，只能通过此函数
*********************************************************************************************************
*/
void page_goto(uint8_t page)
{
    uint8_t gotopage;
    gotopage = page;
    if (gotopage == pa_m_tVar.current_page)
    {
        /* 要切换的界面与当前交互界面相同，切换无效 */
        return;
    }

    if (gotopage == page_home)
    {
        /* gotu：home */
        if (pa_tVar[pa_m_tVar.current_page].page_class == SUB_PAGE)
        {
            /* 此时当前交互界面是子界面 */
            /* home，回根界面 */
            pa_tVar[pa_m_tVar.root_page].page_view_load(); /* 先调用界面加载回调 */
            pa_m_tVar.current_page = pa_m_tVar.root_page;  /* 再更新管理器 */
        }
    }
    else if (pa_tVar[gotopage].page_class == SUB_PAGE)
    {
        /* gotu：子界面 */
        pa_tVar[gotopage].page_view_load(); /* 先调用要切换界面的加载回调 */
        pa_m_tVar.current_page = gotopage;  /* 再更新管理器 */
    }
    else
    {
        /* gotu：根界面 */
        if (pa_tVar[pa_m_tVar.current_page].page_class == SUB_PAGE)
        {
            /* 此时当前交互界面是子界面 */
            if (gotopage == pa_m_tVar.root_page)
            {
                /* 要切换的界面与当前根界面相同，切换无效 */
                return;
            }
            pa_tVar[gotopage].page_root_load(); /* 先调用根界面切换回调 */
            pa_m_tVar.root_page = gotopage;     /* 再更新管理器 */
        }
        else
        {
            /* 此时当前交互界面是根界面 */
            pa_tVar[gotopage].page_root_load(); /* 先调用根界面切换回调 */
            pa_tVar[gotopage].page_view_load(); /* 先调用要切换界面的加载回调 */
            pa_m_tVar.root_page = gotopage;     /* 再更新管理器 */
            pa_m_tVar.current_page = gotopage;  /* 再更新管理器 */
        }
    }
}
/*
*********************************************************************************************************
*	函 数 名: page_current_update
*	形    参: 无
*	返 回 值: 无
*	功能说明: 当前交互界面更新，此函数需要非阻塞轮询，更新周期取决于轮询周期
*********************************************************************************************************
*/
void page_current_update(void)
{
    pa_tVar[pa_m_tVar.current_page].page_update(); /* 当前交互界面更新 */
}
/*
*********************************************************************************************************
*	函 数 名: page_mgr_curpage
*	形    参: 无
*	返 回 值: 当前交互界面
*	功能说明: 获取当前交互界面
*********************************************************************************************************
*/
uint8_t page_mgr_curpage(void)
{
    return pa_m_tVar.current_page;
}
/*
*********************************************************************************************************
*	函 数 名: page_mgr_root
*	形    参: 无
*	返 回 值: 当前根界面
*	功能说明: 获取当前根界面
*********************************************************************************************************
*/
uint8_t page_mgr_root(void)
{
    return pa_m_tVar.root_page;
}
