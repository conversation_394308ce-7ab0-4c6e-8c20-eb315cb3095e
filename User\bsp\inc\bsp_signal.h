#ifndef __BSP_SIGNAL_H
#define __BSP_SIGNAL_H
#include "bsp.h"
#define SIGNAL_SWITCH 1 /* 信号开关 */
#if SIGNAL_SWITCH
#define SIGNAL_DELAY_FILTERING_SWITCH 0 /* 信号延时滤波开关 */

#define SIGNAL_IO_DIRECT_DRIVE_SWITCH 0 /* 信号IO直驱开关 */
#if (SIGNAL_IO_DIRECT_DRIVE_SWITCH == 0)
// #error "将 获取信号高低电平 宏定义后，删除该行"
#define IS_SIGNAL_DOWN_FUNC(_id) ((InformationSignal & (1 << _id)) >> _id)
#endif

#define SIGNAL_FIXED_FIFO_SWITCH 0  /* 信号固定FIFO任务开关 */
#define SIGNAL_CUSTOM_FIFO_SWITCH 1 /* 信号自定义FIFO任务开关 */

#define SIGNAL_CLOSE 1
#define SIGNAL_OPEN 0

#define SIGNAL_ITEM_LIST                                                                                                                                              \
    SIGNAL_DEBUG_X(S1 /* 名称 */, GPIOC /* GPIO */, GPIO_Pin_5 /* pin */, 0 /* 开路时的电平 */, 30 /* 上升沿检测延时10MS */, 30 /* 下降沿检测延时10MS */) /* 信号1 */ \
    SIGNAL_DEBUG_X(S2 /* 名称 */, GPIOB /* GPIO */, GPIO_Pin_1 /* pin */, 0 /* 开路时的电平 */, 30 /* 上升沿检测延时10MS */, 30 /* 下降沿检测延时10MS */) /* 信号2 */ \
    SIGNAL_DEBUG_X(S3 /* 名称 */, GPIOA /* GPIO */, GPIO_Pin_7 /* pin */, 0 /* 开路时的电平 */, 30 /* 上升沿检测延时10MS */, 30 /* 下降沿检测延时10MS */) /* 信号3 */ \
    SIGNAL_DEBUG_X(S4 /* 名称 */, GPIOA /* GPIO */, GPIO_Pin_6 /* pin */, 0 /* 开路时的电平 */, 30 /* 上升沿检测延时10MS */, 30 /* 下降沿检测延时10MS */) /* 信号4 */ \
    SIGNAL_DEBUG_X(S5 /* 名称 */, GPIOA /* GPIO */, GPIO_Pin_5 /* pin */, 0 /* 开路时的电平 */, 30 /* 上升沿检测延时10MS */, 30 /* 下降沿检测延时10MS */) /* 信号5 */

/* 信号ID */
enum sig_e_id
{
#define SIGNAL_DEBUG_X(name, gpio_, pin_, open, rise_d, fall_d) SID_##name,
    SIGNAL_ITEM_LIST
#undef SIGNAL_DEBUG_X
        SIGNAL_ITEM_NUM,
};

/*
    每个信号对应1个全局的结构体变量。
*/
typedef struct
{
    uint8_t sig;        /* 当前信号状态，非电平 */
    uint8_t open_level; /* 开路时的电平 */
#if SIGNAL_DELAY_FILTERING_SWITCH
    uint16_t rise_delay; /* 上升沿延时 */
    uint16_t fall_delay; /* 下降沿延时 */
    uint16_t Count;      /* 延时计数器 */
#endif
#if SIGNAL_CUSTOM_FIFO_SWITCH
    uint8_t fifo_rise_task; /* FIFO上升沿任务 */
    uint8_t fifo_fall_task; /* FIFO下降沿任务 */
#endif
} signal_t;

extern volatile signal_t t_signal[SIGNAL_ITEM_NUM]; /* 信号结构体 */

/* 初始化信号 */
void bsp_InitSignal(void);
/* 扫描所有信号。非阻塞，被10ms周期性的调用 */
void bsp_SignalScan10ms(void);

#if SIGNAL_DELAY_FILTERING_SWITCH
void bsp_SetSignalParam(uint8_t _ID, uint8_t open_level, uint8_t rise_delay, uint8_t fall_delay);
#else
/* 设置信号参数 */
void bsp_SetSignalParam(uint8_t _ID, uint8_t open_level);
#endif

#if SIGNAL_CUSTOM_FIFO_SWITCH
/* 设置信号fifo任务 */
void bsp_SetSignalfifotask(uint8_t _ID, uint8_t fifo_rise_task, uint8_t fifo_fall_task);
#endif

#endif
#endif