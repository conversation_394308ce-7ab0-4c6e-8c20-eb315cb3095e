#ifndef __BSP_UTILITY_H
#define __BSP_UTILITY_H

/* BCD码与真实数值的转换 */
/* 十进制转BCD */
unsigned char DEC_TO_BCD(unsigned char dec);
/* BCD转十进制 */
unsigned char BCD_TO_DEC(unsigned char bcd);

/* 二分查找 升序 */
short Binary_search_asc(unsigned short const *arr, unsigned short len, unsigned short value);
/* 二分查找 降序 */
short Binary_search_desc(unsigned short const *arr, unsigned short len, unsigned short value);

/* 卡尔曼结构体 */
typedef struct
{
	float E_mea;      /* 测量误差（高斯分布：方差）（实际（噪声）：最高-最低） */
	float X_k;        /* 估计值 */
	float E_est;      /* 估计误差 */
}Kalman_InitDef;
/* 初始化卡尔曼结构体 */
void Kaleman_Parameter_Init(Kalman_InitDef *kalman_Structure, float measured_value,float E_mea,float E_est);
/* 卡尔曼估计值计算 */
float Kalman_X_K_Calculation(Kalman_InitDef *kalman_Structure,float measured_value);

/* 窗口平滑滤波结构体 */
typedef struct 
{
	volatile short *buffer;        //数据缓冲区指针
	unsigned char const bufCount;    //滤波的缓存数量
	unsigned char position;    //数据缓存区轮询位置
}FilterObjectType;
/* 窗口平滑滤波 */
short SmoothingFilter(FilterObjectType *filter, short _num);
/* 降权重滤波 */
short filter1Step(short a, short b, short sens);
#endif
