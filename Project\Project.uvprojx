<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<Project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_projx.xsd">

  <SchemaVersion>2.1</SchemaVersion>

  <Header>### uVision Project, (C) Keil Software</Header>

  <Targets>
    <Target>
      <TargetName>Target 1</TargetName>
      <ToolsetNumber>0x4</ToolsetNumber>
      <ToolsetName>ARM-ADS</ToolsetName>
      <pArmCC>6160000::V6.16::ARMCLANG</pArmCC>
      <pCCUsed>6160000::V6.16::ARMCLANG</pCCUsed>
      <uAC6>1</uAC6>
      <TargetOption>
        <TargetCommonOption>
          <Device>AIR32F103CC</Device>
          <Vendor>Generic</Vendor>
          <PackID>Keil.AIR32F103_DFP.1.1.9</PackID>
          <PackURL>https://luatos.com/t/air32f103/</PackURL>
          <Cpu>IRAM(0x20000000,0x18000) IROM(0x08000000,0x40000) CPUTYPE("Cortex-M3") CLOCK(12000000) ELITTLE</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile></StartupFile>
          <FlashDriverDll>UL2CM3(-S0 -C0 -P0 -********** -FC8000 -FN1 -FF0Air32F103CCT6 -********** -FL040000 -FP0($$Device:AIR32F103CC$Flash\Air32F103CCT6.FLM))</FlashDriverDll>
          <DeviceId>0</DeviceId>
          <RegisterFile>$$Device:AIR32F103CC$Device\Include\air32f10x.h</RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile>$$Device:AIR32F103CC$SVD\AIR32F103xx.svd</SFDFile>
          <bCustSvd>0</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath></RegisterFilePath>
          <DBRegisterFilePath></DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>.\OBJ\</OutputDirectory>
          <OutputName>AIR</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>1</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>1</BrowseInformation>
          <ListingPath>.\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopB1X>0</nStopB1X>
            <nStopB2X>0</nStopB2X>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopA1X>0</nStopA1X>
            <nStopA2X>0</nStopA2X>
          </AfterMake>
          <SelectedForBatchBuild>0</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
          <ComprImg>1</ComprImg>
        </CommonProperty>
        <DllOption>
          <SimDllName>SARMCM3.DLL</SimDllName>
          <SimDllArguments> -REMAP -MPU</SimDllArguments>
          <SimDlgDll>DCM.DLL</SimDlgDll>
          <SimDlgDllArguments>-pCM3</SimDlgDllArguments>
          <TargetDllName>SARMCM3.DLL</TargetDllName>
          <TargetDllArguments> -MPU</TargetDllArguments>
          <TargetDlgDll>TCM.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM3</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>1</Capability>
            <DriverSelection>4096</DriverSelection>
          </Flash1>
          <bUseTDR>1</bUseTDR>
          <Flash2>BIN\UL2CM3.DLL</Flash2>
          <Flash3>"" ()</Flash3>
          <Flash4></Flash4>
          <pFcarmOut></pFcarmOut>
          <pFcarmGrp></pFcarmGrp>
          <pFcArmRoot></pFcArmRoot>
          <FcArmLst>0</FcArmLst>
        </Utilities>
        <TargetArmAds>
          <ArmAdsMisc>
            <GenerateListings>0</GenerateListings>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <AdsALst>1</AdsALst>
            <AdsACrf>1</AdsACrf>
            <AdsANop>0</AdsANop>
            <AdsANot>0</AdsANot>
            <AdsLLst>1</AdsLLst>
            <AdsLmap>1</AdsLmap>
            <AdsLcgr>1</AdsLcgr>
            <AdsLsym>1</AdsLsym>
            <AdsLszi>1</AdsLszi>
            <AdsLtoi>1</AdsLtoi>
            <AdsLsun>1</AdsLsun>
            <AdsLven>1</AdsLven>
            <AdsLsxf>1</AdsLsxf>
            <RvctClst>0</RvctClst>
            <GenPPlst>0</GenPPlst>
            <AdsCpuType>"Cortex-M3"</AdsCpuType>
            <RvctDeviceName></RvctDeviceName>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>1</hadIROM>
            <hadIRAM>1</hadIRAM>
            <hadXRAM>0</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>0</RvdsVP>
            <hadIRAM2>0</hadIRAM2>
            <hadIROM2>0</hadIROM2>
            <StupSel>8</StupSel>
            <useUlib>1</useUlib>
            <EndSel>0</EndSel>
            <uLtcg>0</uLtcg>
            <nSecure>0</nSecure>
            <RoSelD>3</RoSelD>
            <RwSelD>3</RwSelD>
            <CodeSel>0</CodeSel>
            <OptFeed>0</OptFeed>
            <NoZi1>0</NoZi1>
            <NoZi2>0</NoZi2>
            <NoZi3>0</NoZi3>
            <NoZi4>0</NoZi4>
            <NoZi5>0</NoZi5>
            <Ro1Chk>0</Ro1Chk>
            <Ro2Chk>0</Ro2Chk>
            <Ro3Chk>0</Ro3Chk>
            <Ir1Chk>1</Ir1Chk>
            <Ir2Chk>1</Ir2Chk>
            <Ra1Chk>0</Ra1Chk>
            <Ra2Chk>0</Ra2Chk>
            <Ra3Chk>0</Ra3Chk>
            <Im1Chk>1</Im1Chk>
            <Im2Chk>0</Im2Chk>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x18000</Size>
              </IRAM>
              <IROM>
                <Type>1</Type>
                <StartAddress>0x8000000</StartAddress>
                <Size>0x40000</Size>
              </IROM>
              <XRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </XRAM>
              <OCR_RVCT1>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT1>
              <OCR_RVCT2>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT2>
              <OCR_RVCT3>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT3>
              <OCR_RVCT4>
                <Type>1</Type>
                <StartAddress>0x8002000</StartAddress>
                <Size>0x3c800</Size>
              </OCR_RVCT4>
              <OCR_RVCT5>
                <Type>1</Type>
                <StartAddress>0x803e800</StartAddress>
                <Size>0x1800</Size>
              </OCR_RVCT5>
              <OCR_RVCT6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT6>
              <OCR_RVCT7>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT7>
              <OCR_RVCT8>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT8>
              <OCR_RVCT9>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x18000</Size>
              </OCR_RVCT9>
              <OCR_RVCT10>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT10>
            </OnChipMemories>
            <RvctStartVector></RvctStartVector>
          </ArmAdsMisc>
          <Cads>
            <interw>1</interw>
            <Optim>4</Optim>
            <oTime>0</oTime>
            <SplitLS>0</SplitLS>
            <OneElfS>1</OneElfS>
            <Strict>0</Strict>
            <EnumInt>0</EnumInt>
            <PlainCh>0</PlainCh>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <wLevel>3</wLevel>
            <uThumb>0</uThumb>
            <uSurpInc>0</uSurpInc>
            <uC99>1</uC99>
            <uGnu>1</uGnu>
            <useXO>0</useXO>
            <v6Lang>3</v6Lang>
            <v6LangP>3</v6LangP>
            <vShortEn>1</vShortEn>
            <vShortWch>1</vShortWch>
            <v6Lto>0</v6Lto>
            <v6WtE>0</v6WtE>
            <v6Rtti>0</v6Rtti>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define>USE_STDPERIPH_DRIVER</Define>
              <Undefine></Undefine>
              <IncludePath>..\User;..\Libraries\AIR32F10xLib\inc;..\Libraries\CMSIS\Include;..\Libraries\AIR32F10xLib\lib;..\User\bsp\inc;..\User\bsp;..\User\ui2.8;..\User\parame;..\User\pro;..\User\page;..\User\page\src_inc</IncludePath>
            </VariousControls>
          </Cads>
          <Aads>
            <interw>1</interw>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <thumb>0</thumb>
            <SplitLS>0</SplitLS>
            <SwStkChk>0</SwStkChk>
            <NoWarn>0</NoWarn>
            <uSurpInc>0</uSurpInc>
            <useXO>0</useXO>
            <uClangAs>0</uClangAs>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Aads>
          <LDads>
            <umfTarg>0</umfTarg>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <noStLib>0</noStLib>
            <RepFail>1</RepFail>
            <useFile>0</useFile>
            <TextAddressRange>0x08000000</TextAddressRange>
            <DataAddressRange>0x20000000</DataAddressRange>
            <pXoBase></pXoBase>
            <ScatterFile>.\AIR.sct</ScatterFile>
            <IncludeLibs></IncludeLibs>
            <IncludeLibsPath></IncludeLibsPath>
            <Misc></Misc>
            <LinkerInputFile></LinkerInputFile>
            <DisabledWarnings></DisabledWarnings>
          </LDads>
        </TargetArmAds>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>User</GroupName>
          <Files>
            <File>
              <FileName>main.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\main.c</FilePath>
            </File>
            <File>
              <FileName>air32f10x_it.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\air32f10x_it.c</FilePath>
            </File>
            <File>
              <FileName>system_air32f10x.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\AIR32F10xLib\src\system_air32f10x.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>bsp</GroupName>
          <Files>
            <File>
              <FileName>bsp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\bsp\bsp.c</FilePath>
            </File>
            <File>
              <FileName>bsp.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\User\bsp\bsp.h</FilePath>
            </File>
            <File>
              <FileName>bsp_timer.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\bsp\src\bsp_timer.c</FilePath>
            </File>
            <File>
              <FileName>bsp_uart_fifo.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\bsp\src\bsp_uart_fifo.c</FilePath>
            </File>
            <File>
              <FileName>bsp_fifo.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\bsp\src\bsp_fifo.c</FilePath>
            </File>
            <File>
              <FileName>bsp_key.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\bsp\src\bsp_key.c</FilePath>
            </File>
            <File>
              <FileName>bsp_crc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\bsp\src\bsp_crc.c</FilePath>
            </File>
            <File>
              <FileName>bsp_utility.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\bsp\src\bsp_utility.c</FilePath>
            </File>
            <File>
              <FileName>bsp_temp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\bsp\src\bsp_temp.c</FilePath>
            </File>
            <File>
              <FileName>bsp_ntc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\bsp\src\bsp_ntc.c</FilePath>
            </File>
            <File>
              <FileName>bsp_signal.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\bsp\src\bsp_signal.c</FilePath>
            </File>
            <File>
              <FileName>bsp_beep.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\bsp\src\bsp_beep.c</FilePath>
            </File>
            <File>
              <FileName>bsp_tim_pwm.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\bsp\src\bsp_tim_pwm.c</FilePath>
            </File>
            <File>
              <FileName>bsp_boot.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\bsp\src\bsp_boot.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>pro</GroupName>
          <Files>
            <File>
              <FileName>pro_cus_com.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\pro\pro_cus_com.c</FilePath>
            </File>
            <File>
              <FileName>pro_cus_com.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\User\pro\pro_cus_com.h</FilePath>
            </File>
            <File>
              <FileName>pro_hardware.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\pro\pro_hardware.c</FilePath>
            </File>
            <File>
              <FileName>pro_hardware.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\User\pro\pro_hardware.h</FilePath>
            </File>
            <File>
              <FileName>pro_workflow.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\pro\pro_workflow.c</FilePath>
            </File>
            <File>
              <FileName>pro_workflow.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\User\pro\pro_workflow.h</FilePath>
            </File>
            <File>
              <FileName>pro_error.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\pro\pro_error.c</FilePath>
            </File>
            <File>
              <FileName>pro_error.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\User\pro\pro_error.h</FilePath>
            </File>
            <File>
              <FileName>pro_pagekey.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\pro\pro_pagekey.c</FilePath>
            </File>
            <File>
              <FileName>pro_pagekey.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\User\pro\pro_pagekey.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>page</GroupName>
          <Files>
            <File>
              <FileName>page.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\page\page.c</FilePath>
            </File>
            <File>
              <FileName>page.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\User\page\page.h</FilePath>
            </File>
            <File>
              <FileName>page_check.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\page\src_inc\page_check.c</FilePath>
            </File>
            <File>
              <FileName>page_check.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\User\page\src_inc\page_check.h</FilePath>
            </File>
            <File>
              <FileName>page_debug.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\page\src_inc\page_debug.c</FilePath>
            </File>
            <File>
              <FileName>page_debug.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\User\page\src_inc\page_debug.h</FilePath>
            </File>
            <File>
              <FileName>page_developer.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\page\src_inc\page_developer.c</FilePath>
            </File>
            <File>
              <FileName>page_developer.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\User\page\src_inc\page_developer.h</FilePath>
            </File>
            <File>
              <FileName>page_parameter.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\page\src_inc\page_parameter.c</FilePath>
            </File>
            <File>
              <FileName>page_parameter.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\User\page\src_inc\page_parameter.h</FilePath>
            </File>
            <File>
              <FileName>page_run.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\page\src_inc\page_run.c</FilePath>
            </File>
            <File>
              <FileName>page_run.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\User\page\src_inc\page_run.h</FilePath>
            </File>
            <File>
              <FileName>page_standby.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\page\src_inc\page_standby.c</FilePath>
            </File>
            <File>
              <FileName>page_standby.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\User\page\src_inc\page_standby.h</FilePath>
            </File>
            <File>
              <FileName>page_start.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\page\src_inc\page_start.c</FilePath>
            </File>
            <File>
              <FileName>page_start.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\User\page\src_inc\page_start.h</FilePath>
            </File>
            <File>
              <FileName>page_statistics.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\page\src_inc\page_statistics.c</FilePath>
            </File>
            <File>
              <FileName>page_statistics.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\User\page\src_inc\page_statistics.h</FilePath>
            </File>
            <File>
              <FileName>page_qr_code.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\page\src_inc\page_qr_code.c</FilePath>
            </File>
            <File>
              <FileName>page_qr_code.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\User\page\src_inc\page_qr_code.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>parame</GroupName>
          <Files>
            <File>
              <FileName>parame.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\parame\parame.c</FilePath>
            </File>
            <File>
              <FileName>parame.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\User\parame\parame.h</FilePath>
            </File>
            <File>
              <FileName>parame_tongyong.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\User\parame\parame_tongyong.h</FilePath>
            </File>
            <File>
              <FileName>flash_eeprom.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\parame\flash_eeprom.c</FilePath>
            </File>
            <File>
              <FileName>flash_eeprom.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\User\parame\flash_eeprom.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>ui2.8</GroupName>
          <Files>
            <File>
              <FileName>UI28.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\ui2.8\UI28.c</FilePath>
            </File>
            <File>
              <FileName>UI28.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\User\ui2.8\UI28.h</FilePath>
            </File>
            <File>
              <FileName>bsp_ili9340x_lcd.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\ui2.8\bsp_ili9340x_lcd.c</FilePath>
            </File>
            <File>
              <FileName>bsp_ili9340x_lcd.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\User\ui2.8\bsp_ili9340x_lcd.h</FilePath>
            </File>
            <File>
              <FileName>UI28_font_14.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\ui2.8\UI28_font_14.c</FilePath>
            </File>
            <File>
              <FileName>UI28_font_16.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\ui2.8\UI28_font_16.c</FilePath>
            </File>
            <File>
              <FileName>UI28_font_18.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\ui2.8\UI28_font_18.c</FilePath>
            </File>
            <File>
              <FileName>UI28_font_20.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\ui2.8\UI28_font_20.c</FilePath>
            </File>
            <File>
              <FileName>UI28_font_24.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\ui2.8\UI28_font_24.c</FilePath>
            </File>
            <File>
              <FileName>UI28_font_28.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\ui2.8\UI28_font_28.c</FilePath>
            </File>
            <File>
              <FileName>UI28_font_32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\ui2.8\UI28_font_32.c</FilePath>
            </File>
            <File>
              <FileName>UI28_font_36.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\ui2.8\UI28_font_36.c</FilePath>
            </File>
            <File>
              <FileName>UI28_font_72.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\ui2.8\UI28_font_72.c</FilePath>
            </File>
            <File>
              <FileName>UI28_matrix.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\ui2.8\UI28_matrix.c</FilePath>
            </File>
            <File>
              <FileName>UI28_menu.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\ui2.8\UI28_menu.c</FilePath>
            </File>
            <File>
              <FileName>UI28_auxiliary_table.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\ui2.8\UI28_auxiliary_table.c</FilePath>
            </File>
            <File>
              <FileName>UI28_rolling_album.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\ui2.8\UI28_rolling_album.c</FilePath>
            </File>
            <File>
              <FileName>UI28_arc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\ui2.8\UI28_arc.c</FilePath>
            </File>
            <File>
              <FileName>UI28_pic_120x120.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\ui2.8\UI28_pic_120x120.c</FilePath>
            </File>
            <File>
              <FileName>UI28_pic_32x32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\ui2.8\UI28_pic_32x32.c</FilePath>
            </File>
            <File>
              <FileName>UI28_pic_22x30.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\ui2.8\UI28_pic_22x30.c</FilePath>
            </File>
            <File>
              <FileName>UI28_pic_14x18.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\ui2.8\UI28_pic_14x18.c</FilePath>
            </File>
            <File>
              <FileName>UI28_pic_48x88.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\ui2.8\UI28_pic_48x88.c</FilePath>
            </File>
            <File>
              <FileName>UI28_pic_36x36.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\ui2.8\UI28_pic_36x36.c</FilePath>
            </File>
            <File>
              <FileName>UI28_pic_40x20.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\ui2.8\UI28_pic_40x20.c</FilePath>
            </File>
            <File>
              <FileName>UI28_pic_45x45.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\ui2.8\UI28_pic_45x45.c</FilePath>
            </File>
            <File>
              <FileName>UI28_pic_70x70.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\ui2.8\UI28_pic_70x70.c</FilePath>
            </File>
            <File>
              <FileName>UI28_animation.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\ui2.8\UI28_animation.c</FilePath>
            </File>
            <File>
              <FileName>UI28_progress_bar.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\ui2.8\UI28_progress_bar.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>CORE</GroupName>
          <Files>
            <File>
              <FileName>startup_air32f10x.s</FileName>
              <FileType>2</FileType>
              <FilePath>..\Libraries\STARTUP\arm\startup_air32f10x.s</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>FWLid</GroupName>
          <Files>
            <File>
              <FileName>misc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\AIR32F10xLib\src\misc.c</FilePath>
            </File>
            <File>
              <FileName>air32f10x_adc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\AIR32F10xLib\src\air32f10x_adc.c</FilePath>
            </File>
            <File>
              <FileName>air32f10x_bkp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\AIR32F10xLib\src\air32f10x_bkp.c</FilePath>
            </File>
            <File>
              <FileName>air32f10x_can.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\AIR32F10xLib\src\air32f10x_can.c</FilePath>
            </File>
            <File>
              <FileName>air32f10x_cec.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\AIR32F10xLib\src\air32f10x_cec.c</FilePath>
            </File>
            <File>
              <FileName>air32f10x_crc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\AIR32F10xLib\src\air32f10x_crc.c</FilePath>
            </File>
            <File>
              <FileName>air32f10x_dac.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\AIR32F10xLib\src\air32f10x_dac.c</FilePath>
            </File>
            <File>
              <FileName>air32f10x_dbgmcu.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\AIR32F10xLib\src\air32f10x_dbgmcu.c</FilePath>
            </File>
            <File>
              <FileName>air32f10x_dma.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\AIR32F10xLib\src\air32f10x_dma.c</FilePath>
            </File>
            <File>
              <FileName>air32f10x_exti.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\AIR32F10xLib\src\air32f10x_exti.c</FilePath>
            </File>
            <File>
              <FileName>air32f10x_flash.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\AIR32F10xLib\src\air32f10x_flash.c</FilePath>
            </File>
            <File>
              <FileName>air32f10x_fsmc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\AIR32F10xLib\src\air32f10x_fsmc.c</FilePath>
            </File>
            <File>
              <FileName>air32f10x_gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\AIR32F10xLib\src\air32f10x_gpio.c</FilePath>
            </File>
            <File>
              <FileName>air32f10x_i2c.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\AIR32F10xLib\src\air32f10x_i2c.c</FilePath>
            </File>
            <File>
              <FileName>air32f10x_iwdg.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\AIR32F10xLib\src\air32f10x_iwdg.c</FilePath>
            </File>
            <File>
              <FileName>air32f10x_otp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\AIR32F10xLib\src\air32f10x_otp.c</FilePath>
            </File>
            <File>
              <FileName>air32f10x_pwr.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\AIR32F10xLib\src\air32f10x_pwr.c</FilePath>
            </File>
            <File>
              <FileName>air32f10x_rcc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\AIR32F10xLib\src\air32f10x_rcc.c</FilePath>
            </File>
            <File>
              <FileName>air32f10x_rcc_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\AIR32F10xLib\src\air32f10x_rcc_ex.c</FilePath>
            </File>
            <File>
              <FileName>air32f10x_rtc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\AIR32F10xLib\src\air32f10x_rtc.c</FilePath>
            </File>
            <File>
              <FileName>air32f10x_sdio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\AIR32F10xLib\src\air32f10x_sdio.c</FilePath>
            </File>
            <File>
              <FileName>air32f10x_spi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\AIR32F10xLib\src\air32f10x_spi.c</FilePath>
            </File>
            <File>
              <FileName>air32f10x_tim.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\AIR32F10xLib\src\air32f10x_tim.c</FilePath>
            </File>
            <File>
              <FileName>air32f10x_trng.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\AIR32F10xLib\src\air32f10x_trng.c</FilePath>
            </File>
            <File>
              <FileName>air32f10x_usart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\AIR32F10xLib\src\air32f10x_usart.c</FilePath>
            </File>
            <File>
              <FileName>air32f10x_wwdg.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\AIR32F10xLib\src\air32f10x_wwdg.c</FilePath>
            </File>
          </Files>
        </Group>
      </Groups>
    </Target>
  </Targets>

  <RTE>
    <apis/>
    <components/>
    <files>
      <file attr="config" category="header" name="RTE_Driver\Config\RTE_Device.h" version="1.0.0">
        <instance index="0" removed="1">RTE\Device\AIR32F103CC\RTE_Device.h</instance>
        <component Cclass="Device" Cgroup="Startup" Cvendor="Keil" Cversion="1.0.0" condition="AIR32F1xx CMSIS Device"/>
        <package name="AIR32F103_DFP" schemaVersion="1.2" url="https://luatos.com/t/air32f103/" vendor="Keil" version="1.1.9"/>
        <targetInfos/>
      </file>
      <file attr="config" category="source" name="Device\StdPeriph_Driver\templates\air32f10x_conf.h" version="1.0.0">
        <instance index="0" removed="1">RTE\Device\AIR32F103CC\air32f10x_conf.h</instance>
        <component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="Framework" Cvendor="Keil" Cversion="1.0.1" condition="AIR32F1xx STDPERIPH"/>
        <package name="AIR32F103_DFP" schemaVersion="1.2" url="https://luatos.com/t/air32f103/" vendor="Keil" version="1.1.9"/>
        <targetInfos/>
      </file>
      <file attr="config" category="source" condition="AIR32F1xx CMSIS Device" name="Device\Source\ARM\startup_air32f10x.s" version="1.0.0">
        <instance index="0" removed="1">RTE\Device\AIR32F103CC\startup_air32f10x.s</instance>
        <component Cclass="Device" Cgroup="Startup" Cvendor="Keil" Cversion="1.0.0" condition="AIR32F1xx CMSIS Device"/>
        <package name="AIR32F103_DFP" schemaVersion="1.2" url="https://luatos.com/t/air32f103/" vendor="Keil" version="1.1.9"/>
        <targetInfos/>
      </file>
      <file attr="config" category="source" name="Device\Source\system_air32f10x.c" version="1.0.0">
        <instance index="0" removed="1">RTE\Device\AIR32F103CC\system_air32f10x.c</instance>
        <component Cclass="Device" Cgroup="Startup" Cvendor="Keil" Cversion="1.0.0" condition="AIR32F1xx CMSIS Device"/>
        <package name="AIR32F103_DFP" schemaVersion="1.2" url="https://luatos.com/t/air32f103/" vendor="Keil" version="1.1.9"/>
        <targetInfos/>
      </file>
    </files>
  </RTE>

</Project>
