#ifndef _LZMUI_H_
#define _LZMUI_H_

#define LZM_UI_H 240 // 屏幕高度
#define LZM_UI_W 320 // 屏幕宽度
#define LZM_UI_W_Byte_count (LZM_UI_W/8) // 屏幕宽度字节数

#include "lzm_font_16.c"
#include "lzm_font_20.c"
#include "lzm_font_32.c"

#include "lzm_matrix.h"
#include "lzm_menu.h"

/* 字模 */
#define LZM_MATRIX_ITEM_LIST          \
	LZM_MATRIX_DEBUG_X(16 /* 字高 */) \
	LZM_MATRIX_DEBUG_X(20 /* 字高 */) \
	LZM_MATRIX_DEBUG_X(32 /* 字高 */)

/* 字模ID */
enum LZM_MATRIX_e_
{
#define LZM_MATRIX_DEBUG_X(heigh) lzm_char_##heigh,
	LZM_MATRIX_ITEM_LIST
#undef LZM_MATRIX_DEBUG_X
		LZM_MATRIX_ITEM_NUM,
};

extern const unsigned char lzm_matrix_heigh_list[LZM_MATRIX_ITEM_NUM];
extern const unsigned char lzm_matrix_ascii_quan_list[LZM_MATRIX_ITEM_NUM];
extern const unsigned char lzm_matrix_chinese_quan_list[LZM_MATRIX_ITEM_NUM];

extern volatile unsigned char PixelPageCache2D[LZM_UI_H][LZM_UI_W_Byte_count];

/*对二维像素页缓存-开窗*/
void PixelPageCache2D_OpenWin(unsigned short usX, unsigned short usY, unsigned short usWidth, unsigned short usHeight, unsigned char usBit);
/*对二维像素页缓存的某一窗口刷双色图*/
void PixelPageCache2D_TwoColorChart(unsigned short usX, unsigned short usY, unsigned short usWidth, unsigned short usHeight, unsigned short usY_R, unsigned short usH_R, unsigned char *usTwoColor);
/*对二维像素页缓存的某一窗口刷字符串*/
void PixelPageCache2D_Text(unsigned short usX, unsigned short usY, enum LZM_MATRIX_e_ FontSize, unsigned short usY_R, unsigned short usH_R, char *usText);

#endif
