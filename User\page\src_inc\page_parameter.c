#include "page_parameter.h"

/*
*********************************************************************************************************
*	函 数 名: page_root_load
*	形    参: 无
*	返 回 值: 无
*	功能说明: 根界面切换时调用
*********************************************************************************************************
*/
void page_parameter_root_load(void)
{
}
/*
*********************************************************************************************************
*	函 数 名: page_view_load
*	形    参: 无
*	返 回 值: 无
*	功能说明: 交互界面切换时调用
*********************************************************************************************************
*/
void page_parameter_view_load(void)
{
    bsp_SetKeyParam(KID_K1, 100, 0); // 支持长按1秒，无连发
    bsp_SetKeyParam(KID_K2, 100, 0); // 支持长按1秒，无连发
    bsp_SetKeyParam(KID_K3, 100, 0); // 支持长按1秒，无连发
    bsp_SetKeyParam(KID_K4, 100, 0); // 支持长按1秒，无连发
    bsp_SetKeyParam(KID_K5, 100, 0); // 支持长按1秒，无连发
    Judging_the_status_flag = 0;     /* 判断状态标志 */

    UI28_Clear_Screen();                                              /*2.8寸屏幕清屏，并关闭所有模块，一般切换界面时调用*/
    ui28_Play_Count_Animation(ui28_2_animation, 1);                   /* 设置动画次数 */
    ILI9340X_Clear(0, 45, 320, 5, DGREY2);                            /*对ILI9340X显示器的某一窗口以某种颜色进行清屏*/
    UI28_change_table_menu(ui28_1_menu, 0, UI28_MENU_ORDER_ITEM_NUM); /* 切换菜单表（切换菜单表需要设置光标位置） */
    UI28_display_onoff_menu(1);                                       /* 菜单显示开关（关，实际只是不刷新了） */

    UI28_set_up_text_gradient_add(ui28_T16_tg, "是否恢复出厂设置"); /* 设置文本地址 */
    UI28_set_up_text_gradient_add(ui28_T17_tg, "- 取消    + 确认"); /* 设置文本地址 */
}

char value_user_1[7];   /*菜单数值文本*/
char value_user_2[7];   /*菜单数值文本*/
char value_user_3[7];   /*菜单数值文本*/
char value_user_4[7];   /*菜单数值文本*/
char value_user_5[7];   /*菜单数值文本*/
char value_user_6[10];  /*菜单数值文本*/
char value_user_7[7];   /*菜单数值文本*/
char value_user_8[7];   /*菜单数值文本*/
char value_user_9[7];   /*菜单数值文本*/
char value_user_10[7];  /*菜单数值文本*/
char value_user_11[7];  /*菜单数值文本*/
char value_user_12[7];  /*菜单数值文本*/
char value_user_13[7];  /*菜单数值文本*/
char value_user_14[10]; /*菜单数值文本*/
char value_user_15[10]; /*菜单数值文本*/

char value_set_tad[4];  /*菜单数值文本*/
char value_mill_1[16];  /*菜单数值文本*/
char value_mill_2[16];  /*菜单数值文本*/
char value_mill_3[10];  /*菜单数值文本*/
char value_mill_4[10];  /*菜单数值文本*/
char value_mill_5[13];  /*菜单数值文本*/
char value_mill_6[13];  /*菜单数值文本*/
char value_mill_7[13];  /*菜单数值文本*/
char value_mill_8[13];  /*菜单数值文本*/
char value_mill_9[10];  /*菜单数值文本*/
char value_mill_10[13]; /*菜单数值文本*/
char value_mill_11[13]; /*菜单数值文本*/
char value_mill_12[13]; /*菜单数值文本*/
char value_mill_13[13]; /*菜单数值文本*/
char value_mill_14[13]; /*菜单数值文本*/
char value_mill_15[13]; /*菜单数值文本*/
char value_mill_16[13]; /*菜单数值文本*/
char value_mill_17[7];  /*菜单数值文本*/
char value_mill_18[7];  /*菜单数值文本*/
char value_mill_19[7];  /*菜单数值文本*/
char value_mill_20[10]; /*菜单数值文本*/
char value_mill_21[10]; /*菜单数值文本*/
char value_mill_22[6];  /*菜单数值文本*/
char value_mill_23[7];  /*菜单数值文本*/
char value_mill_24[13]; /*菜单数值文本*/
char value_mill_25[7];  /*菜单数值文本*/
char value_mill_26[7];  /*菜单数值文本*/
char value_mill_27[13]; /*菜单数值文本*/
char value_mill_28[7];  /*菜单数值文本*/
char value_mill_29[7];  /*菜单数值文本*/

char value_relay_[7][10]; /*菜单数值文本*/
/*
*********************************************************************************************************
*	函 数 名: page_update
*	形    参: 无
*	返 回 值: 无
*	功能说明: 为交互界面时，会周期调用
*********************************************************************************************************
*/
void page_parameter_update(void)
{
    int len;
    static int ui28_menu_FontSize_ = ui28_1_menu;
    if (ui28_menu_FontSize_ != ui28_menu_FontSize /* 菜单文本表选择 */)
    {
        switch (ui28_menu_FontSize /* 菜单文本表选择 */)
        {
        case ui28_1_menu /* 选择用户或厂家 */:
            bsp_SetKeyParam(KID_K1, 100, 0); // 支持长按1秒，无连发
            bsp_SetKeyParam(KID_K2, 100, 0); // 支持长按1秒，无连发
            bsp_SetKeyParam(KID_K3, 100, 0); // 支持长按1秒，无连发
            bsp_SetKeyParam(KID_K4, 100, 0); // 支持长按1秒，无连发
            bsp_SetKeyParam(KID_K5, 100, 0); // 支持长按1秒，无连发
            break;
        case ui28_2_menu /* 用户参数 */:
        case ui28_3_menu /* 厂家参数 */:
        case ui28_4_menu /* 硬件配置 */:
            bsp_SetKeyParam(KID_K1, 100, 10); // 支持长按1秒，自动连发
            bsp_SetKeyParam(KID_K2, 100, 10); // 支持长按1秒，自动连发
            bsp_SetKeyParam(KID_K3, 100, 0);  // 支持长按1秒，无连发
            bsp_SetKeyParam(KID_K4, 100, 10); // 支持长按1秒，自动连发
            bsp_SetKeyParam(KID_K5, 100, 10); // 支持长按1秒，自动连发
            break;
        default:
            break;
        }
        ui28_menu_FontSize_ = ui28_menu_FontSize /* 菜单文本表选择 */;
    }

    if (ui28_animation_Ao_list[ui28_2_animation] == 0)
    {
        if (Judging_the_status_flag /* 判断状态标志 */ == 0)
        {
            UI28_display_onoff_text_gradient(ui28_T16_tg, 0);       /* 文本渐变显示开关（关实际只是不刷新了） */
            UI28_display_onoff_text_gradient(ui28_T17_tg, 0);       /* 文本渐变显示开关（关实际只是不刷新了） */
            UI28_set_up_text_gradient_add(ui28_T14_tg, "参数设置"); /* 设置文本地址 */
            UI28_init_text_gradient_alpha(ui28_T14_tg);             /* 初始化渐变值 */
            UI28_update_text_gradient(ui28_T14_tg);                 /* 更新文本渐变 */
            ILI9340X_Clear(119, 2, 144, 4, BLACK);                  /*对ILI9340X显示器的某一窗口以某种颜色进行清屏*/
            ILI9340X_Clear(119, 38, 144, 5, BLACK);                 /*对ILI9340X显示器的某一窗口以某种颜色进行清屏*/
            ILI9340X_Clear(119, 6, 16, 32, BLACK);                  /*对ILI9340X显示器的某一窗口以某种颜色进行清屏*/
        }
        else
        {
            UI28_display_onoff_text_gradient(ui28_T16_tg, 1); /* 文本渐变显示开关（关实际只是不刷新了） */
            UI28_display_onoff_text_gradient(ui28_T17_tg, 1); /* 文本渐变显示开关（关实际只是不刷新了） */
            ILI9340X_Clear(119, 20, 144, 5, BLACK);           /*对ILI9340X显示器的某一窗口以某种颜色进行清屏*/
        }
    }

    switch (ui28_menu_FontSize /* 菜单文本表选择 */)
    {
    case ui28_2_menu /* 用户参数 */:
        len = sprintf(&value_user_1[0], "%d", (PV(int, USR_user_1, 0) / 10));
        sprintf(&value_user_1[len], "%s", &Unit_Text4[0]);

        len = sprintf(&value_user_2[0], "%d", (PV(int, USR_user_2, 0) / 10));
        sprintf(&value_user_2[len], "%s", &Unit_Text4[0]);

        len = sprintf(&value_user_3[0], "%d", (PV(int, USR_user_3, 0) / 10));
        sprintf(&value_user_3[len], "%s", &Unit_Text4[0]);

        len = sprintf(&value_user_4[0], "%d", (PV(int, USR_user_4, 0) / 10));
        sprintf(&value_user_4[len], "%s", &Unit_Text4[0]);

        if (PV(int, USR_user_5 /* 洗涤门槛温度 */, 0) /*获取参数值*/ == PMIN(int, USR_user_5) /*获取参数下限*/)
        {
            sprintf(&value_user_5[0], "%s", "关闭");
        }
        else
        {
            len = sprintf(&value_user_5[0], "%d", (PV(int, USR_user_5, 0) / 10));
            sprintf(&value_user_5[len], "%s", &Unit_Text4[0]);
        }

        if (PV(int, USR_user_6 /* 进水阀延时进水时间 */, 0) /*获取参数值*/ == PMIN(int, USR_user_6) /*获取参数下限*/)
        {
            sprintf(&value_user_6[0], "%s", "无延时");
        }
        else
        {
            len = sprintf(&value_user_6[0], "%d", PV(int, USR_user_6, 0));
            sprintf(&value_user_6[len], "%s", &Unit_Text1[0]);
        }

        len = sprintf(&value_user_7[0], "%d", (PV(int, USR_user_7, 0) / 10));
        sprintf(&value_user_7[len], "%s", &Unit_Text4[0]);

        len = sprintf(&value_user_8[0], "%d", PV(int, USR_user_8, 0));
        sprintf(&value_user_8[len], "%s", &Unit_Text1[0]);

        len = sprintf(&value_user_9[0], "%d", PV(int, USR_user_9, 0));
        sprintf(&value_user_9[len], "%s", &Unit_Text1[0]);

        if (PV(int, USR_user_10 /* 洗涤剂强度 */, 0) /*获取参数值*/ == PMIN(int, USR_user_10) /*获取参数下限*/)
        {
            sprintf(&value_user_10[0], "%s", "关闭");
        }
        else
        {
            len = sprintf(&value_user_10[0], "%d", PV(int, USR_user_10, 0));
            sprintf(&value_user_10[len], "%s", &Unit_Text7[0]);
        }

        len = sprintf(&value_user_11[0], "%d", PV(int, USR_user_11, 0));
        sprintf(&value_user_11[len], "%s", &Unit_Text1[0]);

        if (PV(int, USR_user_12 /* 干燥剂强度 */, 0) /*获取参数值*/ == PMIN(int, USR_user_12) /*获取参数下限*/)
        {
            sprintf(&value_user_12[0], "%s", "关闭");
        }
        else
        {
            len = sprintf(&value_user_12[0], "%d", PV(int, USR_user_12, 0));
            sprintf(&value_user_12[len], "%s", &Unit_Text7[0]);
        }

        len = sprintf(&value_user_13[0], "%d", PV(int, USR_user_13, 0));
        sprintf(&value_user_13[len], "%s", &Unit_Text1[0]);

        if (PV(int, USR_user_14 /* 自动关机延时 */, 0) /*获取参数值*/ == PMIN(int, USR_user_14) /*获取参数下限*/)
        {
            sprintf(&value_user_14[0], "%s", "不关机");
        }
        else
        {
            len = sprintf(&value_user_14[0], "%d", PV(int, USR_user_14, 0));
            sprintf(&value_user_14[len], "%s", &Unit_Text2[0]);
        }

        if (PV(int, USR_user_15 /* 进水超时 */, 0) /*获取参数值*/ == PMIN(int, USR_user_15) /*获取参数下限*/)
        {
            sprintf(&value_user_15[0], "%s", "不检测");
        }
        else
        {
            len = sprintf(&value_user_15[0], "%d", PV(int, USR_user_15, 0));
            sprintf(&value_user_15[len], "%s", &Unit_Text2[0]);
        }
        break;
    case ui28_3_menu /* 厂家参数 */:
        len = sprintf(&value_set_tad[0], "%d", (PV(int, USR_set_tad, 0) + 1));
        sprintf(&value_set_tad[len], "%s", &Unit_Text9[0]);

        if (PV(int, USR_mill_1 /* 水满判断 */, PV(int, USR_set_tad, 0)) == water_full_1 /* 仅洗高 */)
        {
            sprintf(&value_mill_1[0], "%s", "仅洗高");
        }
        else if (PV(int, USR_mill_1 /* 水满判断 */, PV(int, USR_set_tad, 0)) == water_full_2 /* 洗高与漂高 */)
        {
            sprintf(&value_mill_1[0], "%s", "洗高与漂高");
        }

        if (PV(int, USR_mill_2 /* 漂洗控温开启条件 */, PV(int, USR_set_tad, 0)) == R_t_c_o_c_1 /* 仅洗高 */)
        {
            sprintf(&value_mill_2[0], "%s", "仅洗高");
        }
        else if (PV(int, USR_mill_2 /* 漂洗控温开启条件 */, PV(int, USR_set_tad, 0)) == R_t_c_o_c_2 /* 仅洗低 */)
        {
            sprintf(&value_mill_2[0], "%s", "仅洗低");
        }
        else if (PV(int, USR_mill_2 /* 漂洗控温开启条件 */, PV(int, USR_set_tad, 0)) == R_t_c_o_c_3 /* 仅漂高 */)
        {
            sprintf(&value_mill_2[0], "%s", "仅漂高");
        }
        else if (PV(int, USR_mill_2 /* 漂洗控温开启条件 */, PV(int, USR_set_tad, 0)) == R_t_c_o_c_4 /* 仅漂低 */)
        {
            sprintf(&value_mill_2[0], "%s", "仅漂低");
        }
        else if (PV(int, USR_mill_2 /* 漂洗控温开启条件 */, PV(int, USR_set_tad, 0)) == R_t_c_o_c_5 /* 洗高与漂高 */)
        {
            sprintf(&value_mill_2[0], "%s", "洗高与漂高");
        }

        if (PV(int, USR_mill_3 /* 洗涤控温开启条件 */, PV(int, USR_set_tad, 0)) == W_t_c_o_c_1 /* 仅洗高 */)
        {
            sprintf(&value_mill_3[0], "%s", "仅洗高");
        }
        else if (PV(int, USR_mill_3 /* 洗涤控温开启条件 */, PV(int, USR_set_tad, 0)) == W_t_c_o_c_2 /* 仅洗低 */)
        {
            sprintf(&value_mill_3[0], "%s", "仅洗低");
        }

        if (PV(int, USR_mill_4 /* 进水保温回差 */, PV(int, USR_set_tad, 0)) /*获取参数值*/ == PMAX(int, USR_mill_4) /*获取参数上限*/)
        {
            sprintf(&value_mill_4[0], "%s", "无保温");
        }
        else
        {
            len = sprintf(&value_mill_4[0], "%d", (PV(int, USR_mill_4 /* 进水保温回差 */, PV(int, USR_set_tad, 0)) / 10));
            sprintf(&value_mill_4[len], "%s", &Unit_Text4[0]);
        }

        if (PV(int, USR_mill_5 /* 机门未关能否运行 */, PV(int, USR_set_tad, 0)) /*获取参数值*/ == YES)
        {
            sprintf(&value_mill_5[0], "%s", "能");
        }
        else
        {
            sprintf(&value_mill_5[0], "%s", "不能");
        }

        if (PV(int, USR_mill_6 /* 缺水能否启动运行 */, PV(int, USR_set_tad, 0)) /*获取参数值*/ == YES)
        {
            sprintf(&value_mill_6[0], "%s", "能");
        }
        else
        {
            sprintf(&value_mill_6[0], "%s", "不能");
        }

        if (PV(int, USR_mill_7 /* 运行中缺水是否暂停 */, PV(int, USR_set_tad, 0)) /*获取参数值*/ == UP_w_s_s_1 /* 洗高暂停 */)
        {
            sprintf(&value_mill_7[0], "%s", "洗高暂停");
        }
        if (PV(int, USR_mill_7 /* 运行中缺水是否暂停 */, PV(int, USR_set_tad, 0)) /*获取参数值*/ == UP_w_s_s_2 /* 洗低暂停 */)
        {
            sprintf(&value_mill_7[0], "%s", "洗低暂停");
        }
        if (PV(int, USR_mill_7 /* 运行中缺水是否暂停 */, PV(int, USR_set_tad, 0)) /*获取参数值*/ == UP_w_s_s_3 /* 漂高暂停 */)
        {
            sprintf(&value_mill_7[0], "%s", "漂高暂停");
        }
        if (PV(int, USR_mill_7 /* 运行中缺水是否暂停 */, PV(int, USR_set_tad, 0)) /*获取参数值*/ == UP_w_s_s_4 /* 漂低暂停 */)
        {
            sprintf(&value_mill_7[0], "%s", "漂低暂停");
        }
        else
        {
            sprintf(&value_mill_7[0], "%s", "不暂停");
        }

        if (PV(int, USR_mill_8 /* 机门打开进水是否关闭 */, PV(int, USR_set_tad, 0)) /*获取参数值*/ == YES)
        {
            sprintf(&value_mill_8[0], "%s", "关闭");
        }
        else
        {
            sprintf(&value_mill_8[0], "%s", "不关闭");
        }

        if (PV(int, USR_mill_9 /* 门关后延时启动时间 */, PV(int, USR_set_tad, 0)) /*获取参数值*/ == PMIN(int, USR_mill_9) /*获取参数下限*/)
        {
            sprintf(&value_mill_9[0], "%s", "无延时");
        }
        else
        {
            len = sprintf(&value_mill_9[0], "%d", PV(int, USR_mill_9 /* 门关后延时启动时间 */, PV(int, USR_set_tad, 0)));
            sprintf(&value_mill_9[len], "%s", &Unit_Text1[0]);
        }

        if (PV(int, USR_mill_10 /* 洗涤时缺水能否补水 */, PV(int, USR_set_tad, 0)) /*获取参数值*/ == YES)
        {
            sprintf(&value_mill_10[0], "%s", "能");
        }
        else
        {
            sprintf(&value_mill_10[0], "%s", "不能");
        }

        if (PV(int, USR_mill_11 /* 漂洗暂停时漂加热是否关闭 */, PV(int, USR_set_tad, 0)) /*获取参数值*/ == YES)
        {
            sprintf(&value_mill_11[0], "%s", "关闭");
        }
        else
        {
            sprintf(&value_mill_11[0], "%s", "不关闭");
        }

        if (PV(int, USR_mill_12 /* 漂洗泵辅助进水开关 */, PV(int, USR_set_tad, 0)) /*获取参数值*/ == YES)
        {
            sprintf(&value_mill_12[0], "%s", "开");
        }
        else
        {
            sprintf(&value_mill_12[0], "%s", "关");
        }

        if (PV(int, USR_mill_13 /* 漂洗泵打水延时 */, PV(int, USR_set_tad, 0)) /*获取参数值*/ == PMAX(int, USR_mill_13) /*获取参数上限*/)
        {
            sprintf(&value_mill_13[0], "%s", "不停止");
        }
        else if (PV(int, USR_mill_13 /* 漂洗泵打水延时 */, PV(int, USR_set_tad, 0)) /*获取参数值*/ == PMIN(int, USR_mill_13) /*获取参数下限*/)
        {
            sprintf(&value_mill_13[0], "%s", "不打水");
        }
        else
        {
            len = sprintf(&value_mill_13[0], "%d", PV(int, USR_mill_13 /* 门关后延时启动时间 */, PV(int, USR_set_tad, 0)));
            sprintf(&value_mill_13[len], "%s", &Unit_Text1[0]);
        }

        if (PV(int, USR_mill_14 /* 进水阀与漂洗泵联动开关 */, PV(int, USR_set_tad, 0)) /*获取参数值*/ == YES)
        {
            sprintf(&value_mill_14[0], "%s", "开");
        }
        else
        {
            sprintf(&value_mill_14[0], "%s", "关");
        }

        if (PV(int, USR_mill_15 /* 洗涤时漂洗泵是否强制关闭 */, PV(int, USR_set_tad, 0)) /*获取参数值*/ == YES)
        {
            sprintf(&value_mill_15[0], "%s", "是");
        }
        else
        {
            sprintf(&value_mill_15[0], "%s", "否");
        }

        if (PV(int, USR_mill_16 /* 是否错峰加热 */, PV(int, USR_set_tad, 0)) /*获取参数值*/ == YES)
        {
            sprintf(&value_mill_16[0], "%s", "是");
        }
        else
        {
            sprintf(&value_mill_16[0], "%s", "否");
        }

        len = sprintf(&value_mill_17[0], "%d", PV(int, USR_mill_17 /* 节能时间 */, PV(int, USR_set_tad, 0)));
        sprintf(&value_mill_17[len], "%s", &Unit_Text1[0]);

        len = sprintf(&value_mill_18[0], "%d", PV(int, USR_mill_18 /* 标准时间 */, PV(int, USR_set_tad, 0)));
        sprintf(&value_mill_18[len], "%s", &Unit_Text1[0]);

        len = sprintf(&value_mill_19[0], "%d", PV(int, USR_mill_19 /* 强力时间 */, PV(int, USR_set_tad, 0)));
        sprintf(&value_mill_19[len], "%s", &Unit_Text1[0]);

        if (PV(int, USR_mill_20 /* 漂洗水位状态是否显示 */, PV(int, USR_set_tad, 0)) /*获取参数值*/ == YES)
        {
            sprintf(&value_mill_20[0], "%s", "显示");
        }
        else
        {
            sprintf(&value_mill_20[0], "%s", "不显示");
        }

        if (PV(int, USR_mill_21 /* 延时进水中是否开启加热 */, PV(int, USR_set_tad, 0)) /*获取参数值*/ == YES)
        {
            sprintf(&value_mill_21[0], "%s", "是");
        }
        else
        {
            sprintf(&value_mill_21[0], "%s", "否");
        }

        len = sprintf(&value_mill_22[0], "%d", (PV(int, USR_mill_22 /* 机门打开漂洗目标下降温度 */, PV(int, USR_set_tad, 0)) / 10));
        sprintf(&value_mill_22[len], "%s", &Unit_Text4[0]);

        len = sprintf(&value_mill_23[0], "%d", PV(int, USR_mill_23 /* 停顿时间 */, PV(int, USR_set_tad, 0)));
        sprintf(&value_mill_23[len], "%s", &Unit_Text1[0]);

        if (PV(int, USR_mill_24 /* 洗涤探头故障能否运行 */, PV(int, USR_set_tad, 0)) /*获取参数值*/ == YES)
        {
            sprintf(&value_mill_24[0], "%s", "能");
        }
        else
        {
            sprintf(&value_mill_24[0], "%s", "不能");
        }

        len = sprintf(&value_mill_25[0], "%d", PV(int, USR_mill_25 /* 探头故障洗涤加热开周期 */, PV(int, USR_set_tad, 0)));
        sprintf(&value_mill_25[len], "%s", &Unit_Text1[0]);

        len = sprintf(&value_mill_26[0], "%d", PV(int, USR_mill_26 /* 探头故障洗涤加热关周期 */, PV(int, USR_set_tad, 0)));
        sprintf(&value_mill_26[len], "%s", &Unit_Text1[0]);

        if (PV(int, USR_mill_27 /* 漂洗探头故障能否运行 */, PV(int, USR_set_tad, 0)) /*获取参数值*/ == YES)
        {
            sprintf(&value_mill_27[0], "%s", "能");
        }
        else
        {
            sprintf(&value_mill_27[0], "%s", "不能");
        }

        len = sprintf(&value_mill_28[0], "%d", PV(int, USR_mill_28 /* 探头故障漂洗加热开周期 */, PV(int, USR_set_tad, 0)));
        sprintf(&value_mill_28[len], "%s", &Unit_Text1[0]);

        len = sprintf(&value_mill_29[0], "%d", PV(int, USR_mill_29 /* 探头故障漂洗加热关周期 */, PV(int, USR_set_tad, 0)));
        sprintf(&value_mill_29[len], "%s", &Unit_Text1[0]);
        break;
    case ui28_4_menu /* 硬件配置 */:
        for (len = USR_relay_1; len <= USR_relay_7; len++)
        {
            switch (PV(int, len, 0))
            {
            case HA_RELAY_empty /* 闲置 */:
                sprintf(&value_relay_[len - USR_relay_1][0], "%s", "闲置");
                break;
            case HA_RELAY_DET /* 洗涤剂 */:
                sprintf(&value_relay_[len - USR_relay_1][0], "%s", "洗涤剂");
                break;
            case HA_RELAY_DRY /* 干燥机 */:
                sprintf(&value_relay_[len - USR_relay_1][0], "%s", "干燥机");
                break;
            case HA_RELAY_RI_HE /* 漂加热 */:
                sprintf(&value_relay_[len - USR_relay_1][0], "%s", "漂加热");
                break;
            case HA_RELAY_WA_HE /* 洗加热 */:
                sprintf(&value_relay_[len - USR_relay_1][0], "%s", "洗加热");
                break;
            case HA_RELAY_WAT /* 进水阀 */:
                sprintf(&value_relay_[len - USR_relay_1][0], "%s", "进水阀");
                break;
            case HA_RELAY_RI_P /* 漂洗泵 */:
                sprintf(&value_relay_[len - USR_relay_1][0], "%s", "漂洗泵");
                break;
            case HA_RELAY_WA_P /* 洗涤泵 */:
                sprintf(&value_relay_[len - USR_relay_1][0], "%s", "洗涤泵");
                break;
            default:
                break;
            }

            if (Dis_type /*洗碗机类型*/ == Dis_t_1 /*5继电器，2调速（催干剂 洗涤剂）*/)
            {
                if (len == USR_relay_5)
                {
                    break;
                }
            }
            else if (Dis_type /*洗碗机类型*/ == Dis_t_2 /*7继电器，无调速*/)
            {
                /* code */
            }
        }
        break;
    default:
        break;
    }
}
