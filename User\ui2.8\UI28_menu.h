#ifndef _UI28_MENU_
#define _UI28_MENU_
#include "UI28.h"
#if (UI28_MENU_ENABLED == 1)
#define UI28_MENU_TRANSPARENCY_ENABLED 0 /* 启用菜单透明度 开关 */

#define UI28_MENU_COLOR_0 BLACK /*背景颜色*/
#define UI28_MENU_COLOR_1 WHITE /*文字颜色*/
#define UI28_MENU_LX 0			/*菜单左上角X坐标*/
#define UI28_MENU_LY 50			/*菜单左上角Y坐标*/
#define UI28_MENU_LW 320		/*菜单宽*/
#define UI28_MENU_LH 190		/*菜单高*/
#define UI28_MENU_LBH 18		/*字高*/
#define UI28_MENU_LM 5			/*边距*/
#define UI28_MENU_LTX 0			/*菜单文本X轴偏移*/
#define UI28_MENU_LR 6			/*R角*/
#define UI28_MENU_LNX 237		/*数值位置X坐标左*/
#define UI28_MENU_LNXY 309		/*数值位置X坐标右*/
#define UI28_MENU_LSW 5			/*滚动条宽*/

#define UI28_MENU_MAX_DIG_PO  2	/*数字编码最大位*/

/*菜单项文本结构体*/
typedef struct MENU_TEXT_
{
	const char *name; /*菜单显示文本*/
	char *value;	  /*菜单数值文本*/
} MENU_TEXT;

#define MENU_CHAR_1        \
	{                      \
		{"用户参数", 0}, \
		{"厂家参数", 0}, \
		{"硬件配置", 0}}
#define MENU_CHAR_2                   \
	{                                 \
		{"洗涤控温温度", &value_user_1[0]},       \
		{"漂洗控温温度", &value_user_2[0]},       \
		{"洗涤控温回差温度", &value_user_3[0]},   \
		{"漂洗控温回差温度", &value_user_4[0]},   \
		{"洗涤门槛温度", &value_user_5[0]},       \
		{"进水阀延时进水时间", &value_user_6[0]}, \
		{"漂洗进水降温温度", &value_user_7[0]},   \
		{"进水降温时间", &value_user_8[0]},       \
		{"洗涤剂工作时间", &value_user_9[0]},     \
		{"洗涤剂浓度", &value_user_10[0]},         \
		{"干燥剂工作时间", &value_user_11[0]},     \
		{"干燥剂浓度", &value_user_12[0]},         \
		{"漂洗工作时间", &value_user_13[0]},       \
		{"自动关机延时", &value_user_14[0]},       \
		{"进水超时", &value_user_15[0]}}
#define MENU_CHAR_3                       \
	{                                     \
		{"参数表", &value_set_tad[0]},                 \
		{"水满判断", &value_mill_1[0]},               \
		{"漂洗加热开启条件", &value_mill_2[0]},       \
		{"洗涤加热开启条件", &value_mill_3[0]},       \
		{"进水保温回差", &value_mill_4[0]},           \
		{"机门未关能否运行", &value_mill_5[0]},       \
		{"二次缺水能否启动运行", &value_mill_6[0]},       \
		{"运行中缺水是否暂停", &value_mill_7[0]},     \
		{"机门打开进水是否关闭", &value_mill_8[0]},       \
		{"门关后延时启动时间", &value_mill_9[0]},     \
		{"洗涤时缺水能否补水", &value_mill_10[0]},     \
		{"漂洗暂停漂加热是否关闭", &value_mill_11[0]}, \
		{"漂洗泵辅助进水开关", &value_mill_12[0]},     \
		{"漂洗泵打水延时", &value_mill_13[0]},         \
		{"进水阀与漂洗泵联动开关", &value_mill_14[0]}, \
		{"洗涤时漂洗泵是否强制关", &value_mill_15[0]}, \
		{"错峰加热", &value_mill_16[0]},           \
		{"节能时间", &value_mill_17[0]},               \
		{"标准时间", &value_mill_18[0]},               \
		{"强力时间", &value_mill_19[0]},               \
		{"漂洗水位状态是否显示", &value_mill_20[0]},   \
		{"延时进水中是否开启加热", &value_mill_21[0]}, \
		{"机门开漂洗控温下降温度", &value_mill_22[0]}, \
		{"停顿时间", &value_mill_23[0]}, \
		{"洗涤探头故障能否运行", &value_mill_24[0]}, \
		{"探头故障洗涤加热开周期", &value_mill_25[0]}, \
		{"探头故障洗涤加热关周期", &value_mill_26[0]}, \
		{"漂洗探头故障能否运行", &value_mill_27[0]}, \
		{"探头故障漂洗加热开周期", &value_mill_28[0]}, \
		{"探头故障漂洗加热关周期", &value_mill_29[0]}}
#define MENU_CHAR_4      \
	{                    \
		{"继电器#1", &value_relay_[0][0]}, \
		{"继电器#2", &value_relay_[1][0]}, \
		{"继电器#3", &value_relay_[2][0]}, \
		{"继电器#4", &value_relay_[3][0]}, \
		{"继电器#5", &value_relay_[4][0]}, \
		{"继电器#6", &value_relay_[5][0]}, \
		{"继电器#7", &value_relay_[6][0]}}

#define MENU_CHAR_5            \
	{                          \
		{"洗涤剂工作时长", &value_stat_[0][0]}, \
		{"干燥剂工作时长", &value_stat_[1][0]}, \
		{"进水阀工作时长", &value_stat_[2][0]}, \
		{"洗加热工作时长", &value_stat_[3][0]}, \
		{"漂加热工作时长", &value_stat_[4][0]}}
#define MENU_CHAR_6                \
	{                              \
		{"出厂洗涤剂工作时长", &value_stat_[5][0]}, \
		{"出厂干燥剂工作时长", &value_stat_[6][0]}, \
		{"出厂进水阀工作时长", &value_stat_[7][0]}, \
		{"出厂洗加热工作时长", &value_stat_[8][0]}, \
		{"出厂漂加热工作时长", &value_stat_[9][0]}}

/*创建菜单顺序表*/
#define UI28_MENU_ORDER_ITEM_LIST                                 \
	UI28_MENU_ORDER_DEBUG_X(1 /*名字*/, {0,2,1,3,4,6,7,8,9,10,11,12,5,13,14} /* 顺序表 */) \
	UI28_MENU_ORDER_DEBUG_X(2 /*名字*/, {0,2,1,3,4,6,7,8,10,12,5,13,14} /* 顺序表 */) \
	UI28_MENU_ORDER_DEBUG_X(3 /*名字*/, {0,1,2,3,4} /* 顺序表 */) \
	UI28_MENU_ORDER_DEBUG_X(4 /*名字*/, {0,1,2,3,4,5,6} /* 顺序表 */)\
	UI28_MENU_ORDER_DEBUG_X(5 /*名字*/, {0,1,3,2,10,12,13,14,16,17,18,19,23,22,21,20,4,5,6,7,8,9,11,15,24,25,26,27,28,29} /* 顺序表 */)

/*创建菜单表*/
#define UI28_MENU_ITEM_LIST                                 \
	UI28_MENU_DEBUG_X(1 /*名字*/,1 /*是(1)否(0)添加数字编码*/, MENU_CHAR_1 /* 菜单表 */) \
	UI28_MENU_DEBUG_X(2 /*名字*/,1 /*是(1)否(0)添加数字编码*/, MENU_CHAR_2 /* 菜单表 */) \
	UI28_MENU_DEBUG_X(3 /*名字*/,1 /*是(1)否(0)添加数字编码*/, MENU_CHAR_3 /* 菜单表 */) \
	UI28_MENU_DEBUG_X(4 /*名字*/,0 /*是(1)否(0)添加数字编码*/, MENU_CHAR_4 /* 菜单表 */) \
	UI28_MENU_DEBUG_X(5 /*名字*/,0 /*是(1)否(0)添加数字编码*/, MENU_CHAR_5 /* 菜单表 */) \
	UI28_MENU_DEBUG_X(6 /*名字*/,0 /*是(1)否(0)添加数字编码*/, MENU_CHAR_6 /* 菜单表 */)

/* 菜单顺序ID */
enum UI28_MENU_ORDER_e_
{
#define UI28_MENU_ORDER_DEBUG_X(Lname, ...) ui28_##Lname##_menu_order,
	UI28_MENU_ORDER_ITEM_LIST
#undef UI28_MENU_ORDER_DEBUG_X
		UI28_MENU_ORDER_ITEM_NUM,
};

/* 菜单ID */
enum UI28_MENU_e_
{
#define UI28_MENU_DEBUG_X(Lname, Ldigdis, ...) ui28_##Lname##_menu,
	UI28_MENU_ITEM_LIST
#undef UI28_MENU_DEBUG_X
		UI28_MENU_ITEM_NUM,
};

extern volatile unsigned char ui28_menu_display; /* 菜单显示标志位 */
extern volatile enum UI28_MENU_e_ ui28_menu_FontSize; /* 菜单文本表选择 */
extern volatile char MENU_ANIMATION_OVERAMPLITUDE; // 超幅程度值
extern volatile unsigned char ui28_menu_cur_val; /* 当前光标值 */
extern volatile unsigned char ui28_menu_order_cur_val;  /* 当前光标在顺序表中的值 */

/* 菜单显示开关（关，实际只是不刷新了） */
void UI28_display_onoff_menu(unsigned char _onoff_);
/* 设置光标是否隐藏 */
void UI28_is_the_cursor_hidden_menu(unsigned char switch_);
/* 设置光标位置 */
void UI28_change_cursor_value_menu(unsigned char cur_val);
/* 切换菜单表（切换菜单表需要设置光标位置） */
/* FontSize：菜单表 */
/* cur_val：光标位置 */
/* _order：菜单顺序表，为UI28_MENU_ORDER_ITEM_NUM表示不使用顺序表 */
void UI28_change_table_menu(enum UI28_MENU_e_ FontSize, unsigned char cur_val, enum UI28_MENU_ORDER_e_ _order);
/* 单步调节（上下） */
unsigned char UI28_single_step_menu(enum UI28_STEP_ step__);
#if (UI28_MENU_TRANSPARENCY_ENABLED == 1)
/* 更改透明度 */
void UI28_change_transparency_menu(unsigned char alpha);
#endif

/* 更新菜单 */
void UI28_update_menu(void);
/* 根据目标菜单更新当前 */
void Update_the_current_menu_based_on_the_target_menu(void);
/* 初始化菜单 */
void ui28_Init_Menu(void);
/* 工程初始化菜单（在系统上电执行一次） */
void ui28_Init_Menu_Engineering(void);
#endif
#endif
