#include "UI28_menu.h"
#if (UI28_MENU_ENABLED == 1)

#define UI28_MENU_L2M (UI28_MENU_LM * 2)                /*双边距*/
#define UI28_MENU_LROWH (UI28_MENU_L2M + UI28_MENU_LBH) /*行高*/
// 中间宏：强制展开参数
#define _EXPAND_LBHE(heigh) ui28_char_##heigh
// 最终宏：调用中间宏
#define EXPAND_LBHE(heigh) _EXPAND_LBHE(heigh)
#define UI28_MENU_LBHE EXPAND_LBHE(UI28_MENU_LBH)           /*字高枚举*/
#define UI28_MENU_LWINBIAS (2 * UI28_MENU_LROWH)            /*窗口偏置*/
#define UI28_MENU_LWINTX (UI28_MENU_LTX + UI28_MENU_LM)     /*菜单文本在窗口中X轴坐标*/
#define UI28_MENU_LSWX (UI28_MENU_LW - UI28_MENU_LSW)       /*滚动条X起点坐标*/
#define UI28_MENU_LSWSTX (UI28_MENU_LSWX / 8)               /*滚动条X起点坐标所在字节*/
#define UI28_MENU_LSWSTXP (UI28_MENU_LSWX % 8)              /*滚动条X起点坐标所在字节偏移*/
#define UI28_MENU_LWST ((UI28_MENU_LW + 7) / 8)             /*菜单一行的字节数*/
#define UI28_MENU_LCX (UI28_MENU_LX + UI28_MENU_LTX)        /*光标X坐标*/
#define UI28_MENU_LWINYR (UI28_MENU_LH % UI28_MENU_LROWH)   /*窗口Y轴错位余值*/
#define UI28_MENU_LCEY (UI28_MENU_LH - UI28_MENU_LROWH)     /*光标所在窗口最后一行Y值*/
#define UI28_MENU_LNXW (UI28_MENU_LNXY - UI28_MENU_LNX + 1) /*数值最大宽度*/
#define UI28_MENU_LNPX (UI28_MENU_LX + UI28_MENU_LNX)       /*数值在屏幕位置X坐标左*/

#define UI28_MENU_ACMAX_DIG_PO (UI28_MENU_MAX_DIG_PO + 1)                     /*数字编码实际最大位*/
#define UI28_MENU_ACMAX_DIG_PO_X (UI28_MENU_ACMAX_DIG_PO * UI28_MENU_LBH / 2) /*数字编码X坐标大小*/

volatile unsigned char ui28_menu_display = 0; /* 菜单显示标志位 */
#if (UI28_MENU_TRANSPARENCY_ENABLED == 1)
volatile unsigned short ui28_menu_color_1 = UI28_MENU_COLOR_1; /* 菜单显示文字颜色 */
#endif
volatile enum UI28_MENU_e_ ui28_menu_FontSize = 0;   /* 菜单文本表选择 */
volatile unsigned char ui28_menu_total_list_ = 0;    /* 菜单项列数 */
volatile unsigned char *ui28_menu_order_total_q = 0; /* 菜单顺序表指针 */
volatile unsigned char ui28_menu_order_cur_val = 0;  /* 当前光标在顺序表中的值 */

volatile unsigned char ui28_menu_cur_val = 0;  /* 当前光标值 */
volatile unsigned char ui28_menu_cur_hide = 0; /* 隐藏光标标志 */

volatile int MENU_ANIMATION_CUR_XW_TAR; // 光标目标X宽度(256倍)
volatile int MENU_ANIMATION_CUR_Y_TAR;  // 光标目标Y(256倍)
volatile int MENU_ANIMATION_CUR_YH_TAR; // 光标目标Y高度(256倍)
volatile int MENU_ANIMATION_WIN_Y_TAR;  // 窗口目标Y(256倍)
volatile int MENU_ANIMATION_ORLL_Y_TAR; // 滚动条目标Y(256倍)

volatile int MENU_ANIMATION_CUR_XW; // 光标当前X宽度(256倍)
volatile int MENU_ANIMATION_CUR_Y;  // 光标当前Y(256倍)
volatile int MENU_ANIMATION_CUR_YH; // 光标当前Y高度(256倍)
volatile int MENU_ANIMATION_WIN_Y;  // 窗口当前Y(256倍)
volatile int MENU_ANIMATION_ORLL_Y; // 滚动条当前Y(256倍)

volatile char MENU_ANIMATION_OVERAMPLITUDE; // 超幅程度值

/* 菜单项文本 */
#define UI28_MENU_DEBUG_X(Lname, Ldigdis, ...) const MENU_TEXT ui28_menu_TEXT##Lname##_list[] = __VA_ARGS__;
UI28_MENU_ITEM_LIST
#undef UI28_MENU_DEBUG_X

/* 菜单项总数 */
const unsigned char ui28_menu_total_list[UI28_MENU_ITEM_NUM] =
    {
#define UI28_MENU_DEBUG_X(Lname, Ldigdis, ...) (sizeof(ui28_menu_TEXT##Lname##_list) / sizeof(MENU_TEXT)),
        UI28_MENU_ITEM_LIST
#undef UI28_MENU_DEBUG_X
};

/* 行字宽 */
#define UI28_MENU_DEBUG_X(Lname, Ldigdis, ...) volatile unsigned short ui28_menu_Row_width_TEXT##Lname##_list[(sizeof(ui28_menu_TEXT##Lname##_list) / sizeof(MENU_TEXT))];
UI28_MENU_ITEM_LIST
#undef UI28_MENU_DEBUG_X

/* 菜单顺序表 */
#define UI28_MENU_ORDER_DEBUG_X(Lname, ...) const unsigned char ui28_menu_order_##Lname##_list[] = __VA_ARGS__;
UI28_MENU_ORDER_ITEM_LIST
#undef UI28_MENU_ORDER_DEBUG_X

/* 菜单顺序项总数 */
const unsigned char ui28_menu_order_total_list[UI28_MENU_ORDER_ITEM_NUM] =
    {
#define UI28_MENU_ORDER_DEBUG_X(Lname, ...) (sizeof(ui28_menu_order_##Lname##_list) / sizeof(unsigned char)),
        UI28_MENU_ORDER_ITEM_LIST
#undef UI28_MENU_ORDER_DEBUG_X
};

// 根据光标更新目标
void Update_the_target_based_on_the_cursor_menu(void)
{
    // short CUR_Y = MENU_ANIMATION_CUR_Y_TAR >> 8;                                     // 光标目标Y(256倍)
    short WIN_Y = MENU_ANIMATION_WIN_Y_TAR >> 8;                                  // 窗口目标Y(256倍)
    short cur_val_Y = (ui28_menu_cur_val * UI28_MENU_LROWH) + UI28_MENU_LWINBIAS; // 当前设定光标Y值(256倍)
    short TEXT_TOTAL_H = ui28_menu_total_list_ * UI28_MENU_LROWH;                 // 文本总高度

    if (ui28_menu_order_total_q /* 菜单顺序表指针 */ == 0)
    {
        ui28_menu_order_cur_val = ui28_menu_cur_val;
    }
    else
    {
        ui28_menu_order_cur_val = ui28_menu_order_total_q[ui28_menu_cur_val];
    }

    if (cur_val_Y < WIN_Y)
    {
        /* 在窗口上方 */
        MENU_ANIMATION_WIN_Y_TAR = cur_val_Y;
        MENU_ANIMATION_WIN_Y_TAR = MENU_ANIMATION_WIN_Y_TAR << 8; // 窗口目标Y(256倍)
        MENU_ANIMATION_CUR_Y_TAR = 0;                             // 光标目标Y(256倍)
    }
    else if ((cur_val_Y + 30) <= (WIN_Y + UI28_MENU_LH))
    {
        /* 在窗口中间 */
        MENU_ANIMATION_CUR_Y_TAR = cur_val_Y - WIN_Y;
        MENU_ANIMATION_CUR_Y_TAR = MENU_ANIMATION_CUR_Y_TAR << 8; // 光标目标Y(256倍)
    }
    else
    {
        /* 在窗口下方 */
        MENU_ANIMATION_WIN_Y_TAR = cur_val_Y - UI28_MENU_LCEY;
        MENU_ANIMATION_WIN_Y_TAR = MENU_ANIMATION_WIN_Y_TAR << 8; // 窗口目标Y(256倍)
        MENU_ANIMATION_CUR_Y_TAR = UI28_MENU_LCEY << 8;           // 光标目标Y(256倍)
    }

    switch (ui28_menu_FontSize)
    {
#define UI28_MENU_DEBUG_X(Lname, Ldigdis, ...)                                                                                 \
    case ui28_##Lname##_menu:                                                                                                  \
        MENU_ANIMATION_CUR_XW_TAR = ui28_menu_Row_width_TEXT##Lname##_list[ui28_menu_order_cur_val]; /* 光标目标X宽度(256倍)*/ \
        MENU_ANIMATION_CUR_XW_TAR = MENU_ANIMATION_CUR_XW_TAR << 8;                                                            \
        break;
        UI28_MENU_ITEM_LIST
#undef UI28_MENU_DEBUG_X
    default:
        break;
    }

    if (TEXT_TOTAL_H <= UI28_MENU_LH)
    {
        MENU_ANIMATION_ORLL_Y_TAR = UI28_MENU_LH << 8; // 滚动条目标Y(256倍)
    }
    else
    {
        MENU_ANIMATION_ORLL_Y_TAR = (MENU_ANIMATION_WIN_Y_TAR * UI28_MENU_LH) / (TEXT_TOTAL_H + UI28_MENU_LWINBIAS - UI28_MENU_LH); // 滚动条目标Y(256倍)
    }
}

// 根据目标菜单更新当前
void Update_the_current_menu_based_on_the_target_menu(void)
{
    UI28_anima((int *)&MENU_ANIMATION_CUR_XW, (int *)&MENU_ANIMATION_CUR_XW_TAR, 2); // 光标当前X宽度(256倍)
    UI28_anima((int *)&MENU_ANIMATION_CUR_Y, (int *)&MENU_ANIMATION_CUR_Y_TAR, 2);   // 光标当前Y(256倍)
    UI28_anima((int *)&MENU_ANIMATION_CUR_YH, (int *)&MENU_ANIMATION_CUR_YH_TAR, 3); // 光标当前Y高度(256倍)
    UI28_anima((int *)&MENU_ANIMATION_WIN_Y, (int *)&MENU_ANIMATION_WIN_Y_TAR, 2);   // 窗口当前Y(256倍)
    UI28_anima((int *)&MENU_ANIMATION_ORLL_Y, (int *)&MENU_ANIMATION_ORLL_Y_TAR, 3); // 滚动条当前Y(256倍)
    if (MENU_ANIMATION_OVERAMPLITUDE == 0)
    {
        return;
    }
    else if (MENU_ANIMATION_OVERAMPLITUDE > 127)
    {
        /* 上调节超幅 */
        // MENU_ANIMATION_CUR_XW -= (UI28_MENU_LBH << 8);   // 光标当前X宽度(256倍)
        MENU_ANIMATION_CUR_YH -= (UI28_MENU_LROWH << 6); // 光标当前Y高度(256倍)
        MENU_ANIMATION_WIN_Y -= (UI28_MENU_LROWH << 7);  // 窗口当前Y(256倍)
        MENU_ANIMATION_ORLL_Y -= (UI28_MENU_LROWH << 7); // 滚动条当前Y(256倍)
    }
    else
    {
        /* 下调节超幅 */
        // MENU_ANIMATION_CUR_XW -= (UI28_MENU_LBH << 8);   // 光标当前X宽度(256倍)
        MENU_ANIMATION_CUR_YH -= (UI28_MENU_LROWH << 6); // 光标当前Y高度(256倍)
        MENU_ANIMATION_WIN_Y += (UI28_MENU_LROWH << 7);  // 窗口当前Y(256倍)
        MENU_ANIMATION_ORLL_Y -= (UI28_MENU_LROWH << 7); // 滚动条当前Y(256倍)
    }

    // if (MENU_ANIMATION_CUR_XW < 0)
    // {
    //     MENU_ANIMATION_CUR_XW = 0;
    // }
    if (MENU_ANIMATION_CUR_YH < 0)
    {
        MENU_ANIMATION_CUR_YH = 0;
    }
    if (MENU_ANIMATION_WIN_Y < 0)
    {
        MENU_ANIMATION_WIN_Y = 0;
    }
    if (MENU_ANIMATION_ORLL_Y < 0)
    {
        MENU_ANIMATION_ORLL_Y = 0;
    }

    MENU_ANIMATION_OVERAMPLITUDE = 0;
}

// 更新数字编码字符串
static void UI28_dig_txt_menu(char *_txt, short dig, unsigned char Ldigdis)
{
    short digit = dig + 1;                              // 数字
    unsigned char subscript = UI28_MENU_MAX_DIG_PO - 1; // 下标
    if (Ldigdis)
    {
        while (digit)
        {
            _txt[subscript] = 48 /*0x30*/ + digit % 10;
            digit /= 10;
            subscript--;
        }

        while (subscript < UI28_MENU_MAX_DIG_PO)
        {
            _txt[subscript] = ' ';
            subscript--;
        }

        _txt[UI28_MENU_MAX_DIG_PO] = '.';
        _txt[UI28_MENU_ACMAX_DIG_PO] = 0;
    }
    else
    {
        _txt[0] = 0;
    }
}

// 更新菜单
void UI28_update_menu(void)
{
    char dig_txt[UI28_MENU_ACMAX_DIG_PO + 1];

    short CUR_XW = MENU_ANIMATION_CUR_XW >> 8;                // 光标当前宽度
    short CUR_Y = (MENU_ANIMATION_CUR_Y >> 8) + UI28_MENU_LY; // 光标当前Y
    short CUR_YH = MENU_ANIMATION_CUR_YH >> 8;                // 光标当前Y高度(256倍)
    short WIN_Y = MENU_ANIMATION_WIN_Y >> 8;                  // 窗口当前Y
    short ORLL_Y = MENU_ANIMATION_ORLL_Y >> 8;                // 滚动条当前Y

    short start_Y, start_WIN_Y, usY, usVY, I;
    unsigned char *Win = (unsigned char *)&PixelPageCache2D[0][0];

    // 计算当前行涉及的字节范围
    start_Y = WIN_Y / UI28_MENU_LROWH;
    start_WIN_Y = WIN_Y % UI28_MENU_LROWH;
    start_WIN_Y = UI28_MENU_LM - start_WIN_Y;

    /*窗口原先就偏移了2行*/
    start_Y -= 2;

    /*对二维像素页缓存-开窗(自定义)（窗口内bit全为0）*/
    PixelPageCache2D_OpenWin_customize(UI28_MENU_LW, UI28_MENU_LH, 0);
    /*刷字符串*/
    for (usY = start_WIN_Y; usY < UI28_MENU_LH; usY += UI28_MENU_LROWH)
    {
        if (start_Y >= 0 && start_Y < ui28_menu_total_list_)
        {
            if (ui28_menu_order_total_q /* 菜单顺序表指针 */ == 0)
            {
                I = start_Y;
            }
            else
            {
                I = ui28_menu_order_total_q[start_Y];
            }

            /*对二维像素页缓存的某一窗口刷字符串*/
            switch (ui28_menu_FontSize)
            {
#define UI28_MENU_DEBUG_X(Lname, Ldigdis, ...)                                                                                                                                                     \
    case ui28_##Lname##_menu:                                                                                                                                                                      \
        UI28_dig_txt_menu(&dig_txt[0], start_Y, Ldigdis);                                                                                                                                          \
        PixelPageCache2D_Text_customize(UI28_MENU_LW, UI28_MENU_LH, UI28_MENU_LWINTX, usY, UI28_MENU_LBHE, (char *)&dig_txt[0]);                                                                   \
        PixelPageCache2D_Text_customize(UI28_MENU_LW, UI28_MENU_LH, (UI28_MENU_LWINTX + (UI28_MENU_ACMAX_DIG_PO_X * Ldigdis)), usY, UI28_MENU_LBHE, (char *)ui28_menu_TEXT##Lname##_list[I].name); \
        usVY = UI28_MENU_LNXY + 1 - UI28_character_pixel_width_calculation(UI28_MENU_LBHE, (char *)ui28_menu_TEXT##Lname##_list[I].value);                                                         \
        PixelPageCache2D_Text_customize(UI28_MENU_LW, UI28_MENU_LH, usVY, usY, UI28_MENU_LBHE, (char *)ui28_menu_TEXT##Lname##_list[I].value);                                                     \
        break;
                UI28_MENU_ITEM_LIST
#undef UI28_MENU_DEBUG_X
            default:
                break;
            }
        }
        start_Y++;
    }
    /*刷滚动条*/
    for (I = 0; I < ORLL_Y; I++)
    {
        for (start_Y = UI28_MENU_LSWSTX; start_Y < UI28_MENU_LWST; start_Y++)
        {
            if (start_Y > UI28_MENU_LSWSTX)
            {
                Win[start_Y] = 0xFF;
            }
            else
            {
                Win[start_Y] |= (0xFF >> UI28_MENU_LSWSTXP);
            }
        }
        Win += UI28_MENU_LWST;
    }
#if (UI28_MENU_TRANSPARENCY_ENABLED == 1)
    if (ui28_menu_cur_hide /* 隐藏光标标志 */)
    {
        ILI9340X_TwoColorChart_XYReversed(UI28_MENU_LX, UI28_MENU_LY, UI28_MENU_LW, UI28_MENU_LH, UI28_MENU_LCX, 0, 0, 0, GET_R_ANGLE_BY_ROW(UI28_MENU_LR), UI28_MENU_COLOR_0, ui28_menu_color_1, (unsigned char *)&PixelPageCache2D[0][0]);
    }
    else
    {
        ILI9340X_TwoColorChart_XYReversed(UI28_MENU_LX, UI28_MENU_LY, UI28_MENU_LW, UI28_MENU_LH, UI28_MENU_LCX, CUR_XW, CUR_Y, CUR_YH, GET_R_ANGLE_BY_ROW(UI28_MENU_LR), UI28_MENU_COLOR_0, ui28_menu_color_1, (unsigned char *)&PixelPageCache2D[0][0]);
    }
#else
    if (ui28_menu_cur_hide /* 隐藏光标标志 */)
    {
        ILI9340X_TwoColorChart_XYReversed(UI28_MENU_LX, UI28_MENU_LY, UI28_MENU_LW, UI28_MENU_LH, UI28_MENU_LCX, 0, 0, 0, GET_R_ANGLE_BY_ROW(UI28_MENU_LR), UI28_MENU_COLOR_0, UI28_MENU_COLOR_1, (unsigned char *)&PixelPageCache2D[0][0]);
    }
    else
    {
        ILI9340X_TwoColorChart_XYReversed(UI28_MENU_LX, UI28_MENU_LY, UI28_MENU_LW, UI28_MENU_LH, UI28_MENU_LCX, CUR_XW, CUR_Y, CUR_YH, GET_R_ANGLE_BY_ROW(UI28_MENU_LR), UI28_MENU_COLOR_0, UI28_MENU_COLOR_1, (unsigned char *)&PixelPageCache2D[0][0]);
    }

#endif
}

// 计算UTF-8字符串显示宽度
unsigned short calc_string_width(const char *str)
{
    unsigned short width = 0;
    while (*str)
    {
        if ((unsigned char)*str >= 0xE0)
        { // 全角字符（UTF-8三字节）
            width += UI28_MENU_LBH;
            str += 3; // 跳过全角字符的后续字节
        }
        else
        { // 半角字符
            width += (UI28_MENU_LBH >> 1);
            str++;
        }
    }
    return width;
}

/* 工程初始化菜单（在系统上电执行一次） */
void ui28_Init_Menu_Engineering(void)
{
    int i;
#define UI28_MENU_DEBUG_X(Lname, Ldigdis, ...)                                                                                                                      \
    for (i = 0; i < ui28_menu_total_list[ui28_##Lname##_menu]; i++)                                                                                                 \
    {                                                                                                                                                               \
        ui28_menu_Row_width_TEXT##Lname##_list[i] = calc_string_width(ui28_menu_TEXT##Lname##_list[i].name) + UI28_MENU_L2M + (Ldigdis * UI28_MENU_ACMAX_DIG_PO_X); \
    }
    UI28_MENU_ITEM_LIST
#undef UI28_MENU_DEBUG_X
    MENU_ANIMATION_CUR_YH_TAR = UI28_MENU_LROWH << 8;   // 光标目标Y高度(256倍)
    MENU_ANIMATION_WIN_Y_TAR = UI28_MENU_LWINBIAS << 8; // 窗口目标Y(256倍)
}

/* 初始化菜单 */
void ui28_Init_Menu(void)
{
    // MENU_ANIMATION_WIN_Y_LAST = 0; // 上一次 窗口当前Y(256倍)

    MENU_ANIMATION_CUR_XW = 0;                   // 光标当前X宽度(256倍)
    MENU_ANIMATION_CUR_Y = 0;                    // 光标当前Y(256倍)
    MENU_ANIMATION_CUR_YH = 0;                   // 光标当前Y高度(256倍)
    MENU_ANIMATION_WIN_Y = UI28_MENU_LROWH << 8; // 窗口当前Y(256倍)
    MENU_ANIMATION_ORLL_Y = 0;                   // 滚动条当前Y(256倍)
#if (UI28_MENU_TRANSPARENCY_ENABLED == 1)
    ui28_menu_color_1 = UI28_MENU_COLOR_1;
#endif

    ui28_menu_total_list_ = ui28_menu_total_list[ui28_menu_FontSize];
}

/* 菜单显示开关（关实际只是不刷新了） */
void UI28_display_onoff_menu(unsigned char _onoff_)
{
    ui28_menu_display = _onoff_;
}
/* 设置光标是否隐藏 */
void UI28_is_the_cursor_hidden_menu(unsigned char switch_)
{
    ui28_menu_cur_hide = switch_; /* 隐藏光标标志 */
}
/* 设置光标位置 */
void UI28_change_cursor_value_menu(unsigned char cur_val)
{
    if (cur_val < ui28_menu_total_list_)
    {
        ui28_menu_cur_val = cur_val;
    }
    else
    {
        ui28_menu_cur_val = ui28_menu_total_list_ - 1;
    }
    Update_the_target_based_on_the_cursor_menu(); // 根据光标更新目标
}
/* 切换菜单表（切换菜单表需要设置光标位置） */
/* FontSize：菜单表 */
/* cur_val：光标位置 */
/* _order：菜单顺序表，为UI28_MENU_ORDER_ITEM_NUM表示不使用顺序表 */
void UI28_change_table_menu(enum UI28_MENU_e_ FontSize, unsigned char cur_val, enum UI28_MENU_ORDER_e_ _order)
{
    if (FontSize < UI28_MENU_ITEM_NUM)
    {
        if (ui28_menu_FontSize != FontSize)
        {
            ui28_menu_FontSize = FontSize;
            MENU_ANIMATION_WIN_Y = UI28_MENU_LROWH << 8;        // 窗口当前Y(256倍)
            MENU_ANIMATION_WIN_Y_TAR = UI28_MENU_LWINBIAS << 8; // 窗口目标Y(256倍)
        }

        switch (_order)
        {
#define UI28_MENU_ORDER_DEBUG_X(Lname, ...)                                                                          \
    case ui28_##Lname##_menu_order:                                                                                  \
        ui28_menu_order_total_q /* 菜单顺序表指针 */ = (volatile unsigned char *)&ui28_menu_order_##Lname##_list[0]; \
        ui28_menu_total_list_ = ui28_menu_order_total_list[_order];                                                  \
        break;
            UI28_MENU_ORDER_ITEM_LIST
#undef UI28_MENU_ORDER_DEBUG_X
        case UI28_MENU_ORDER_ITEM_NUM:
            ui28_menu_order_total_q /* 菜单顺序表指针 */ = 0;
            ui28_menu_total_list_ = ui28_menu_total_list[ui28_menu_FontSize];
            break;
        default:
            break;
        }

        UI28_change_cursor_value_menu(cur_val); /* 设置光标位置 */
    }
}
/* 单步调节（上下） */
unsigned char UI28_single_step_menu(enum UI28_STEP_ step__)
{
    if (step__ == ui28_up)
    {
        /* 单步调节（上） */
        if (ui28_menu_cur_val > 0)
        {
            ui28_menu_cur_val--;
        }
        else
        {
            MENU_ANIMATION_OVERAMPLITUDE--;
        }
    }
    else
    {
        /* 单步调节（下） */
        if (ui28_menu_cur_val < (ui28_menu_total_list_ - 1))
        {
            ui28_menu_cur_val++;
        }
        else
        {
            MENU_ANIMATION_OVERAMPLITUDE++;
        }
    }
    Update_the_target_based_on_the_cursor_menu(); // 根据光标更新目标
    return ui28_menu_cur_val;
}
#if (UI28_MENU_TRANSPARENCY_ENABLED == 1)
/* 更改透明度 */
void UI28_change_transparency_menu(unsigned char alpha)
{
    ui28_menu_color_1 = alpha_blend_rgb565(UI28_MENU_COLOR_0, UI28_MENU_COLOR_1, alpha);
}
#endif
#endif