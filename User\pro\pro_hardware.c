#include "bsp.h"
__IO uint32_t pro_relay = 0;                           // 业务用继电器输出
__IO uint8_t pro_pwm[2] = {0};                         // 业务用pwm输出
__IO uint32_t pro_switch_signal = 0;                   // 业务用开关信号
__IO int pro_temp_use[TEMPERATURE_ITEM_NUM] = {0};     // 业务用温度信号(使用0.1℃)
__IO int pro_temp_see[TEMPERATURE_ITEM_NUM] = {0};     // 业务用温度信号(显示0.1℃)
__IO uint8_t pro_temp_abn[TEMPERATURE_ITEM_NUM] = {0}; // 业务用温度探头异常标志位(大于0为异常)

/* 业务信号传入 */
void pro_signal_input(void)
{
    uint8_t i;
    /* 根据设置将传感器匹配 */
    // 暂时没有参数业务

    /* 将温度信号传入 */
    for (i = 0; i < TEMPERATURE_ITEM_NUM; i++)
    {
        pro_temp_use[i] = (int)temp_e_t[i].probe[0].Control_temp;
        pro_temp_see[i] = (int)temp_e_t[i].Displaytemp;
        pro_temp_abn[i] = temp_e_t[i].probe[0].abnormal;
    }
    /* 将开关量信号传入 */
    for (i = 0; i < 5; i++)
    {
        if (t_signal[i].sig)
        {
            pro_switch_signal |= (1 << i); // 置位
        }
        else
        {
            pro_switch_signal &= ~(1 << i); // 清零
        }
        bsp_SetSignalfifotask(i, ((i << 1) + Wa_H_SWR), ((i << 1) + Wa_H_SWR + 1));//配置上升下降沿任务
    }
}

/* 业务信号输出 */
void pro_signal_output(void)
{
    if (pa_m_tVar.current_page /* 当前交互界面 */ == page_debug /* 调试 */)
    {
        /* 保持状态 */
        return;
    }

    uint8_t i;
    /* 将继电器输出 */
    // 只有继电器有参数业务
    for (i = 0; i < 8; i++)
    {
        if (PV(int, (i+USR_relay_1), 0)/*获取参数值*/)
        {
            if (pro_relay & (1 << (PV(int, (i+USR_relay_1), 0)/*获取参数值*/-1)))/*获取bit*/
            {
                InformationOutput |= (1 << i); // 置位
            }
            else
            {
                InformationOutput &= ~(1 << i); // 清零
            }
        }
        else
        {
            InformationOutput &= ~(1 << i); // 清零
        }
    }
    /* 将PWM输出 */
    for (i = 0; i < 2; i++)
    {
        GearOutput[i]=pro_pwm[i];
    }
}
