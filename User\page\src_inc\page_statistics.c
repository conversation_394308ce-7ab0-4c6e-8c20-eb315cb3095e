#include "page_statistics.h"

/*
*********************************************************************************************************
*	函 数 名: page_root_load
*	形    参: 无
*	返 回 值: 无
*	功能说明: 根界面切换时调用
*********************************************************************************************************
*/
void page_statistics_root_load(void)
{
}
/*
*********************************************************************************************************
*	函 数 名: page_view_load
*	形    参: 无
*	返 回 值: 无
*	功能说明: 交互界面切换时调用
*********************************************************************************************************
*/
void page_statistics_view_load(void)
{
    bsp_SetKeyParam(KID_K1, 100, 0);                        // 支持长按1秒，无连发
    bsp_SetKeyParam(KID_K2, 100, 0);                        // 支持长按1秒，无连发
    bsp_SetKeyParam(KID_K3, 100, 0);                        // 支持长按1秒，无连发
    bsp_SetKeyParam(KID_K4, 100, 0);                        // 支持长按1秒，无连发
    bsp_SetKeyParam(KID_K5, 100, 0);                        // 支持长按1秒，无连发
    Judging_the_status_flag = 0;                            /* 判断状态标志 */
    Business_EE_immediate_storage_and_storage_management(); /*  业务ee立刻存储存储管理 */

    UI28_Clear_Screen();                                              /*2.8寸屏幕清屏，并关闭所有模块，一般切换界面时调用*/
    ui28_Play_Count_Animation(ui28_4_animation, 1);                   /* 设置动画次数 */
    ILI9340X_Clear(0, 45, 320, 5, DGREY2);                            /*对ILI9340X显示器的某一窗口以某种颜色进行清屏*/
    UI28_change_table_menu(ui28_5_menu, 0, UI28_MENU_ORDER_ITEM_NUM); /* 切换菜单表（切换菜单表需要设置光标位置） */
    UI28_is_the_cursor_hidden_menu(1);                                /* 设置光标是否隐藏 */
    UI28_display_onoff_menu(1);                                       /* 菜单显示开关（关，实际只是不刷新了） */

    UI28_set_up_text_gradient_add(ui28_T16_tg, "是否清零用量统计"); /* 设置文本地址 */
    UI28_set_up_text_gradient_add(ui28_T17_tg, "- 取消    + 确认"); /* 设置文本地址 */
}
char value_stat_[10][10]; /*菜单数值文本*/
/*
*********************************************************************************************************
*	函 数 名: page_update
*	形    参: 无
*	返 回 值: 无
*	功能说明: 为交互界面时，会周期调用
*********************************************************************************************************
*/
void page_statistics_update(void)
{
    int integer;
    int decimal;
    int len, i;

    if (ui28_animation_Ao_list[ui28_4_animation] == 0)
    {
        if (Judging_the_status_flag /* 判断状态标志 */ == 0)
        {
            UI28_display_onoff_text_gradient(ui28_T16_tg, 0);       /* 文本渐变显示开关（关实际只是不刷新了） */
            UI28_display_onoff_text_gradient(ui28_T17_tg, 0);       /* 文本渐变显示开关（关实际只是不刷新了） */
            UI28_set_up_text_gradient_add(ui28_T14_tg, "用量统计"); /* 设置文本地址 */
            UI28_init_text_gradient_alpha(ui28_T14_tg);             /* 初始化渐变值 */
            UI28_update_text_gradient(ui28_T14_tg);                 /* 更新文本渐变 */
            ILI9340X_Clear(119, 2, 144, 4, BLACK);                  /*对ILI9340X显示器的某一窗口以某种颜色进行清屏*/
            ILI9340X_Clear(119, 38, 144, 5, BLACK);                 /*对ILI9340X显示器的某一窗口以某种颜色进行清屏*/
            ILI9340X_Clear(119, 6, 16, 32, BLACK);                  /*对ILI9340X显示器的某一窗口以某种颜色进行清屏*/
        }
        else
        {
            UI28_display_onoff_text_gradient(ui28_T16_tg, 1); /* 文本渐变显示开关（关实际只是不刷新了） */
            UI28_display_onoff_text_gradient(ui28_T17_tg, 1); /* 文本渐变显示开关（关实际只是不刷新了） */
            ILI9340X_Clear(119, 20, 144, 5, BLACK);           /*对ILI9340X显示器的某一窗口以某种颜色进行清屏*/
        }

        if (ui28_menu_FontSize /* 菜单文本表选择 */ == ui28_5_menu /* 用户用量统计 */)
        {
            PixelPageCache2D_Text_customize(96, 24, 0, 0, ui28_char_24, "当前累计");                       /*对二维像素页缓存的某一窗口刷字符串(自定义)*/
            ILI9340X_TwoColorChart(3, 12, 96, 24, BLACK, WHITE, (unsigned char *)&PixelPageCache2D[0][0]); /*对ILI9340X显示器的某一窗口刷双色图*/
        }
        else if (ui28_menu_FontSize /* 菜单文本表选择 */ == ui28_6_menu /* 厂家用量统计 */)
        {
            PixelPageCache2D_Text_customize(96, 24, 0, 0, ui28_char_24, "总累计  ");                       /*对二维像素页缓存的某一窗口刷字符串(自定义)*/
            ILI9340X_TwoColorChart(3, 12, 96, 24, BLACK, WHITE, (unsigned char *)&PixelPageCache2D[0][0]); /*对ILI9340X显示器的某一窗口刷双色图*/
        }
    }

    if (ui28_menu_FontSize /* 菜单文本表选择 */ == ui28_5_menu /* 用户用量统计 */)
    {
        for (i = USR_stat_1; i <= USR_stat_5; i++)
        {
            // 计算整数部分和小数部分
            int integer = PV(int, i, 0) / 3600;
            int decimal = (PV(int, i, 0) / 36) % 100;
            if (decimal == 0)
            {
                len = sprintf(&value_stat_[i - USR_stat_1][0], "%d", integer);
            }
            else if (decimal < 10)
            {
                len = sprintf(&value_stat_[i - USR_stat_1][0], "%d.0%d", integer, decimal);
            }
            else
            {
                len = sprintf(&value_stat_[i - USR_stat_1][0], "%d.%d", integer, decimal);
            }
            sprintf(&value_stat_[i - USR_stat_1][len], "%s", &Unit_Text3[0] /* 时 */);
        }
    }
    else if (ui28_menu_FontSize /* 菜单文本表选择 */ == ui28_6_menu /* 厂家用量统计 */)
    {
        for (i = USR_Ostat_1; i <= USR_Ostat_5; i++)
        {
            // 计算整数部分和小数部分
            int integer = PV(int, i, 0) / 3600;
            int decimal = (PV(int, i, 0) / 36) % 100;
            if (decimal == 0)
            {
                len = sprintf(&value_stat_[i - USR_stat_1][0], "%d", integer);
            }
            else if (decimal < 10)
            {
                len = sprintf(&value_stat_[i - USR_stat_1][0], "%d.0%d", integer, decimal);
            }
            else
            {
                len = sprintf(&value_stat_[i - USR_stat_1][0], "%d.%d", integer, decimal);
            }
            sprintf(&value_stat_[i - USR_stat_1][len], "%s", &Unit_Text3[0] /* 时 */);
        }
    }
}
