#ifndef __BSP_KEY_H
#define __BSP_KEY_H
#include <stdint.h>
#include <stdio.h>
#define KEY_SWITCH 1 /* 按键开关 */
#if KEY_SWITCH
#define MULTIPLE_KEY_STROKES 0	/* 多击按键开关（启用多击检测， 会影响单击体验变慢） */
#define SEQUENTIAL_KEY_SWITCH 1 /* 顺序按键开关 */

/*
	按键滤波时间50ms, 单位10ms。
	只有连续检测到50ms状态不变才认为有效，包括弹起和按下两种事件
	即使按键电路不做硬件滤波，该滤波机制也可以保证可靠地检测到按键事件
*/
#define KEY_FILTER_TIME 5
#define KEY_LONG_TIME 100 /* 单位10ms， 持续1秒，认为长按事件 */

#define KEY_ITEM_LIST                                                                                                                   \
	KEY_DEBUG_X(K1 /* 名称 */ /* 上 */, (1UL << 0) /* 硬件位置 */, KEY_LONG_TIME /* 长按时间 */, 0 /* 连发周期 */)                      \
	KEY_DEBUG_X(K2 /* 名称 */ /* 下 */, (1UL << 1) /* 硬件位置 */, KEY_LONG_TIME /* 长按时间 */, 0 /* 连发周期 */)                      \
	KEY_DEBUG_X(K3 /* 名称 */ /* 运行 */, (1UL << 2) /* 硬件位置 */, KEY_LONG_TIME /* 长按时间 */, 0 /* 连发周期 */)                    \
	KEY_DEBUG_X(K4 /* 名称 */ /* 模式 + */, (1UL << 4) /* 硬件位置 */, KEY_LONG_TIME /* 长按时间 */, 0 /* 连发周期 */)                  \
	KEY_DEBUG_X(K5 /* 名称 */ /* 查看 - */, (1UL << 3) /* 硬件位置 */, KEY_LONG_TIME /* 长按时间 */, 0 /* 连发周期 */)                  \
	KEY_DEBUG_X(K2K3 /* 名称 */, ((1UL << 2) + (1UL << 1)) /* 硬件位置 */, KEY_LONG_TIME /* 长按时间 */, 0 /* 连发周期 */) /* 组合键 */ \
	KEY_DEBUG_X(K4K5 /* 名称 */, ((1UL << 4) + (1UL << 3)) /* 硬件位置 */, KEY_LONG_TIME /* 长按时间 */, 0 /* 连发周期 */) /* 组合键 */

/* 按键ID */
enum key_e_id
{
#define KEY_DEBUG_X(name, help, LTime, RSpeed) KID_##name,
	KEY_ITEM_LIST
#undef KEY_DEBUG_X
		KEY_ITEM_NUM,
};

/*
	每个按键对应1个全局的结构体变量。
*/
typedef struct
{
	uint8_t Count;		 /* 滤波器计数器 */
	uint16_t LongCount;	 /* 长按计数器 */
	uint16_t LongTime;	 /* 按键按下持续时间, 0表示不检测长按 */
	uint8_t State;		 /* 按键当前状态（按下还是弹起） */
	uint8_t RepeatSpeed; /* 连续按键周期 */
	uint8_t RepeatCount; /* 连续按键计数器 */
} key_t;

#if SEQUENTIAL_KEY_SWITCH

/*
	顺序按键之间的间隔时间10000ms, 单位10ms。
	顺序按键宏定义
*/
#define KEY_INTERVAL_TIME 1000

/*
	每个顺序按键对应1个全局的结构体变量。
*/
typedef struct
{
	uint16_t count;			  /* 顺序按键时间计数 */
	uint8_t subscript_record; /* 顺序按键下标记录 */
	uint8_t const *fixed;	  /* 按键顺序固定参数指针 */
	uint8_t NUM;			  /* 顺序按键固定参数的个数 */
} seq_key_t;

#define KEY_SEQ_ITEM_LIST                                                                                                 \
	KEY_SEQ_DEBUG_X(K1 /* 名称 */, {KID_K4, KID_K4, KID_K2, KID_K1, KID_K2, KID_K1, KID_K3} /* 顺序表 */) \
	KEY_SEQ_DEBUG_X(K2 /* 名称 */, {KID_K5, KID_K4, KID_K5, KID_K4, KID_K3} /* 顺序表 */)                                 \
	KEY_SEQ_DEBUG_X(K3 /* 名称 */, {KID_K1, KID_K2, KID_K1, KID_K2, KID_K3} /* 顺序表 */)

/* 顺序按键ID */
enum seq_key_e_id
{
#define KEY_SEQ_DEBUG_X(name, ...) KID_SEQ_##name,
	KEY_SEQ_ITEM_LIST
#undef KEY_SEQ_DEBUG_X
		KEY_SEQ_ITEM_NUM /* 顺序按键个数 */,
};

#endif

#if MULTIPLE_KEY_STROKES

/*
	按键多击时间100ms, 单位10ms。
	按键多击时间宏定义
*/
#define KEY_MUL_TIME 10

/*
	每个多击按键对应1个全局的结构体变量。
*/
typedef struct
{
	uint8_t EnableFlag;	 /* 使能标志 */
	uint16_t DelayCount; /* 延迟计数器，用于多击检测 */
	uint8_t ClickCount;	 /* 单击次数计数 */
} mul_key_t;

#define KEY_MUL_ITEM_LIST                                                   \
	KEY_MUL_DEBUG_X(K1 /* 名称 */, KID_K3 /* 多击键值 */, 2 /* 多击次数 */) \
	KEY_MUL_DEBUG_X(K2 /* 名称 */, KID_K3 /* 多击键值 */, 3 /* 多击次数 */) \
	KEY_MUL_DEBUG_X(K3 /* 名称 */, KID_K5 /* 多击键值 */, 5 /* 多击次数 */)

/* 多击按键ID */
enum mul_key_e_id
{
#define KEY_MUL_DEBUG_X(name, KID_K_, NUM_) KID_MUL_##name##,
	KEY_MUL_ITEM_LIST
#undef KEY_MUL_DEBUG_X
		KEY_MUL_ITEM_NUM /* 多击按键个数 */,
};
#endif

/* 设置按键参数 */
void bsp_SetKeyParam(uint8_t _ucKeyID, uint16_t _LongTime, uint8_t _RepeatSpeed);
/* 读取按键的状态 */
uint8_t bsp_GetKeyState(uint8_t _ucKeyID);
/* 初始化按键 */
void bsp_InitKey(void);
/* 扫描所有按键。非阻塞，被10ms周期性的调用 */
void bsp_KeyScan10ms(void);
#endif
#endif
