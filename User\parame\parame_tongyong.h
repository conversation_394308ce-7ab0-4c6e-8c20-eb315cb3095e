#ifndef _PARAME_TONGYONG_H
#define _PARAME_TONGYONG_H
#include "parame.h"

#define ValueUnion_int8 0	// int8_t
#define ValueUnion_int16 0	// int16_t
#define ValueUnion_int32 0	// int32_t
#define ValueUnion_int64 0	// int64_t
#define ValueUnion_uint8 0	// uint8_t
#define ValueUnion_uint16 0 // uint16_t
#define ValueUnion_uint32 0 // uint32_t
#define ValueUnion_uint64 0 // uint64_t
#define ValueUnion_char 0	// char
#define ValueUnion_int 1	// int
#define ValueUnion_float 0	// float
#define ValueUnion_double 0 // double

// 客户名称
#define CUSTOMER_NAME "通用"
// 参数版本号 每修改一次+1
#define PARAM_VERSION "V000.000.000.0"
// 参数配置最后更新
#define PARAM_LAST_UPDATED "250124"
// 参数配置名称
#define PARAM_LIST_NAME "通用默认参数"
// 客户编号
#define CUSTOMER_SN "000"
// 负责的销售
#define SALESPERSON "XXX"

#define PARAM_SET_TAD_MAX 7 
#define PARAM_SET_TAD_MAX_SET_TAD (PARAM_SET_TAD_MAX-1) 

#define PARAM_ITEM_LIST \
	PARAM_DEBUG_X(set_tad            /* 设置记录表 */, int /* 参数类型 */,  1 /* 单次步进 */, 1 /* 连续步进 */,  UNIT_WELL /* 单位 */, ADJ_MODE_CIRC /* 调节模式 */, RST_LEVEL_SYS  /* 重置等级 */,    0xFF /* 内部上限ID，无内部上限 */,    0xFF /* 内部下限ID，无内部下限 */, NULL /*参数更新回调，无更新回调*/,                 1 /* 个数 */,                         0 /* 下限值 */,     PARAM_SET_TAD_MAX_SET_TAD  /* 上限值 */,  0 /* 初始值 */)\
	PARAM_DEBUG_X(mill_1               /* 水满判断 */, int /* 参数类型 */,  1 /* 单次步进 */, 1 /* 连续步进 */, UNIT_NONE /* 单位 */ , ADJ_MODE_CIRC /* 调节模式 */, RST_LEVEL_USER_1 /* 重置等级 */,    0xFF /* 内部上限ID，无内部上限 */,    0xFF /* 内部下限ID，无内部下限 */, NULL /*参数更新回调，无更新回调*/, PARAM_SET_TAD_MAX /* 个数 */,              water_full_1 /* 下限值 */,                 water_full_2 /* 上限值 */, {water_full_1/* 仅洗高 */,water_full_2/* 洗高与漂高 */,water_full_2/* 洗高与漂高 */,water_full_1/* 洗高与漂高 */,water_full_1/* 仅洗高 */,water_full_1/* 洗高与漂高 */,water_full_1/* 洗高与漂高 */} /* 初始值 */)\
	PARAM_DEBUG_X(mill_2        /* 漂洗控温开启条件 */, int /* 参数类型 */,  1 /* 单次步进 */, 1 /* 连续步进 */, UNIT_NONE /* 单位 */ , ADJ_MODE_CIRC /* 调节模式 */, RST_LEVEL_USER_1 /* 重置等级 */,    0xFF /* 内部上限ID，无内部上限 */,    0xFF /* 内部下限ID，无内部下限 */, NULL /*参数更新回调，无更新回调*/, PARAM_SET_TAD_MAX /* 个数 */,               R_t_c_o_c_1 /* 下限值 */,                  R_t_c_o_c_5 /* 上限值 */, {R_t_c_o_c_1/* 仅洗高 */,R_t_c_o_c_3/* 仅漂高 */,     R_t_c_o_c_3/* 仅漂高 */,     R_t_c_o_c_4/* 仅漂低 */,     R_t_c_o_c_2/* 仅洗低 */,R_t_c_o_c_4/* 仅漂低 */,     R_t_c_o_c_4/* 仅漂低 */} /* 初始值 */)\
	PARAM_DEBUG_X(mill_3        /* 洗涤控温开启条件 */, int /* 参数类型 */,  1 /* 单次步进 */, 1 /* 连续步进 */, UNIT_NONE /* 单位 */ , ADJ_MODE_CIRC /* 调节模式 */, RST_LEVEL_USER_1 /* 重置等级 */,    0xFF /* 内部上限ID，无内部上限 */,    0xFF /* 内部下限ID，无内部下限 */, NULL /*参数更新回调，无更新回调*/, PARAM_SET_TAD_MAX /* 个数 */,               W_t_c_o_c_1 /* 下限值 */,                  W_t_c_o_c_2 /* 上限值 */, {W_t_c_o_c_1/* 仅洗高 */,W_t_c_o_c_1/* 仅洗高 */,     W_t_c_o_c_1/* 仅洗高 */,     W_t_c_o_c_2/* 仅洗低 */,     W_t_c_o_c_2/* 仅洗低 */,W_t_c_o_c_2/* 仅洗低 */,     W_t_c_o_c_1/* 仅洗高 */} /* 初始值 */)\
	PARAM_DEBUG_X(mill_4            /*进水保温回差 */, int /* 参数类型 */, 10 /* 单次步进 */, 10 /* 连续步进 */,  UNIT_01C /* 单位 */, ADJ_MODE_CIRC /* 调节模式 */, RST_LEVEL_USER_1 /* 重置等级 */,    0xFF /* 内部上限ID，无内部上限 */,    0xFF /* 内部下限ID，无内部下限 */, NULL /*参数更新回调，无更新回调*/, PARAM_SET_TAD_MAX /* 个数 */,                        20 /* 下限值 */,   210 /* 达到上限显示“无保温” */ /* 上限值 */, {210/* 无保温 */,      210/* 无保温 */,             100,                         210/* 无保温 */,             210/* 无保温 */,        100,                        100} /* 初始值 */)\
	PARAM_DEBUG_X(mill_5         /*机门未关能否运行 */, int /* 参数类型 */, 1 /* 单次步进 */,  1 /* 连续步进 */, UNIT_NONE /* 单位 */, ADJ_MODE_CIRC /* 调节模式 */, RST_LEVEL_USER_1 /* 重置等级 */,    0xFF /* 内部上限ID，无内部上限 */,    0xFF /* 内部下限ID，无内部下限 */, NULL /*参数更新回调，无更新回调*/, PARAM_SET_TAD_MAX /* 个数 */,                         NO /* 下限值 */,                          YES  /* 上限值 */, {NO/* 不能 */,          NO/* 不能 */,               NO/* 不能 */,                 NO/* 不能 */,                NO/* 不能 */,          NO/* 不能 */,                NO/* 不能 */} /* 初始值 */)\
	PARAM_DEBUG_X(mill_6         /*缺水能否启动运行 */, int /* 参数类型 */, 1 /* 单次步进 */,  1 /* 连续步进 */, UNIT_NONE /* 单位 */, ADJ_MODE_CIRC /* 调节模式 */, RST_LEVEL_USER_1 /* 重置等级 */,    0xFF /* 内部上限ID，无内部上限 */,    0xFF /* 内部下限ID，无内部下限 */, NULL /*参数更新回调，无更新回调*/, PARAM_SET_TAD_MAX /* 个数 */,                         NO /* 下限值 */,                          YES  /* 上限值 */, {YES/* 能 */,            YES/* 能 */,               YES/* 能 */,                 YES/* 能 */,                 YES/* 能 */,           YES/* 能 */,                 YES/* 能 */} /* 初始值 */)\
	PARAM_DEBUG_X(mill_7       /*运行中缺水是否暂停 */, int /* 参数类型 */, 1 /* 单次步进 */,  1 /* 连续步进 */, UNIT_NONE /* 单位 */, ADJ_MODE_CIRC /* 调节模式 */, RST_LEVEL_USER_1 /* 重置等级 */,    0xFF /* 内部上限ID，无内部上限 */,    0xFF /* 内部下限ID，无内部下限 */, NULL /*参数更新回调，无更新回调*/, PARAM_SET_TAD_MAX /* 个数 */,                 UP_w_s_s_0 /* 下限值 */,                   UP_w_s_s_4  /* 上限值 */, {UP_w_s_s_0/* 不暂停 */, UP_w_s_s_0/* 不暂停 */,     UP_w_s_s_0/* 不暂停 */,       UP_w_s_s_0/* 不暂停 */,     UP_w_s_s_0/* 不暂停 */, UP_w_s_s_0/* 不暂停 */,      UP_w_s_s_0/* 不暂停 */} /* 初始值 */)\
	PARAM_DEBUG_X(mill_8     /*机门打开进水是否关闭 */, int /* 参数类型 */, 1 /* 单次步进 */,  1 /* 连续步进 */, UNIT_NONE /* 单位 */, ADJ_MODE_CIRC /* 调节模式 */, RST_LEVEL_USER_1 /* 重置等级 */,    0xFF /* 内部上限ID，无内部上限 */,    0xFF /* 内部下限ID，无内部下限 */, NULL /*参数更新回调，无更新回调*/, PARAM_SET_TAD_MAX /* 个数 */,                         NO /* 下限值 */,                          YES  /* 上限值 */, {YES/* 关闭 */,          YES/* 关闭 */,              YES/* 关闭 */,                YES/* 关闭 */,             YES/* 关闭 */,          YES/* 关闭 */,               YES/* 关闭 */} /* 初始值 */)\
	PARAM_DEBUG_X(mill_9       /*门关后延时启动时间 */, int /* 参数类型 */, 1 /* 单次步进 */,  1 /* 连续步进 */,  UNIT_SEC /* 单位 */, ADJ_MODE_CIRC /* 调节模式 */, RST_LEVEL_USER_1 /* 重置等级 */,    0xFF /* 内部上限ID，无内部上限 */,    0xFF /* 内部下限ID，无内部下限 */, NULL /*参数更新回调，无更新回调*/, PARAM_SET_TAD_MAX /* 个数 */,  0 /* 达到下限显示“无延时” *//* 下限值 */,                           20  /* 上限值 */, {0/* 无延时 */,          0/* 无延时 */,              0/* 无延时 */,                0/* 无延时 */,             0/* 无延时 */,           0/* 无延时 */,               0/* 无延时 */} /* 初始值 */)\
	PARAM_DEBUG_X(mill_10      /*洗涤时缺水能否补水 */, int /* 参数类型 */, 1 /* 单次步进 */,  1 /* 连续步进 */, UNIT_NONE /* 单位 */, ADJ_MODE_CIRC /* 调节模式 */, RST_LEVEL_USER_1 /* 重置等级 */,    0xFF /* 内部上限ID，无内部上限 */,    0xFF /* 内部下限ID，无内部下限 */, NULL /*参数更新回调，无更新回调*/, PARAM_SET_TAD_MAX /* 个数 */,                         NO /* 下限值 */,                          YES  /* 上限值 */,  {NO/* 不能 */,          NO/* 不能 */,               NO/* 不能 */,                 NO/* 不能 */,              NO/* 不能 */,            NO/* 不能 */,               NO/* 不能 */} /* 初始值 */)\
	PARAM_DEBUG_X(mill_11 /*漂洗暂停时漂加热是否关闭 */, int /* 参数类型 */, 1 /* 单次步进 */,  1 /* 连续步进 */, UNIT_NONE /* 单位 */, ADJ_MODE_CIRC /* 调节模式 */, RST_LEVEL_USER_1 /* 重置等级 */,    0xFF /* 内部上限ID，无内部上限 */,    0xFF /* 内部下限ID，无内部下限 */, NULL /*参数更新回调，无更新回调*/, PARAM_SET_TAD_MAX /* 个数 */,                         NO /* 下限值 */,                          YES  /* 上限值 */,  {YES/* 关闭 */,        YES/* 关闭 */,               YES/* 关闭 */,                NO/* 不关闭 */,            YES/* 关闭 */,           NO/* 不关闭 */,             NO/* 不关闭 */} /* 初始值 */)\
	PARAM_DEBUG_X(mill_12      /*漂洗泵辅助进水开关 */, int /* 参数类型 */, 1 /* 单次步进 */,  1 /* 连续步进 */, UNIT_NONE /* 单位 */, ADJ_MODE_CIRC /* 调节模式 */, RST_LEVEL_USER_1 /* 重置等级 */,    0xFF /* 内部上限ID，无内部上限 */,    0xFF /* 内部下限ID，无内部下限 */, NULL /*参数更新回调，无更新回调*/, PARAM_SET_TAD_MAX /* 个数 */,                         NO /* 下限值 */,                          YES  /* 上限值 */,  {NO/* 关 */,           NO/* 关 */,                  YES/* 开 */,                  NO/* 关 */,               NO/* 关 */,              NO/* 关 */,                 YES/* 开 */} /* 初始值 */)\
	PARAM_DEBUG_X(mill_13          /*漂洗泵打水延时 */, int /* 参数类型 */, 1 /* 单次步进 */,  1 /* 连续步进 */,  UNIT_SEC /* 单位 */, ADJ_MODE_CIRC /* 调节模式 */, RST_LEVEL_USER_1 /* 重置等级 */,    0xFF /* 内部上限ID，无内部上限 */,    0xFF /* 内部下限ID，无内部下限 */, NULL /*参数更新回调，无更新回调*/, PARAM_SET_TAD_MAX /* 个数 */, 0 /* 达到下限显示“不打水” */ /* 下限值 */,  21 /* 达到上限显示“不停止” */  /* 上限值 */,  {0/* 不打水 */,        0/* 不打水 */,               0/* 不打水 */,                0/* 不打水 */,            0/* 不打水 */,            5,                          0/* 不打水 */} /* 初始值 */)\
	PARAM_DEBUG_X(mill_14   /*进水阀与漂洗泵联动开关 */, int /* 参数类型 */, 1 /* 单次步进 */,  1 /* 连续步进 */, UNIT_NONE /* 单位 */, ADJ_MODE_CIRC /* 调节模式 */, RST_LEVEL_USER_1 /* 重置等级 */,    0xFF /* 内部上限ID，无内部上限 */,    0xFF /* 内部下限ID，无内部下限 */, NULL /*参数更新回调，无更新回调*/, PARAM_SET_TAD_MAX /* 个数 */,                         NO /* 下限值 */,                          YES  /* 上限值 */,  {NO/* 关 */,           NO/* 关 */,                  NO/* 关 */,                  NO/* 关 */,               NO/* 关 */,               NO/* 关 */,                 NO/* 关 */} /* 初始值 */)\
	PARAM_DEBUG_X(mill_15 /*洗涤时漂洗泵是否强制关闭 */, int /* 参数类型 */, 1 /* 单次步进 */,  1 /* 连续步进 */, UNIT_NONE /* 单位 */, ADJ_MODE_CIRC /* 调节模式 */, RST_LEVEL_USER_1 /* 重置等级 */,    0xFF /* 内部上限ID，无内部上限 */,    0xFF /* 内部下限ID，无内部下限 */, NULL /*参数更新回调，无更新回调*/, PARAM_SET_TAD_MAX /* 个数 */,                         NO /* 下限值 */,                          YES  /* 上限值 */,  {YES/* 是 */,          YES/* 是 */,                 YES/* 是 */,                 YES/* 是 */,              YES/* 是 */,              YES/* 是 */,                YES} /* 初始值 */)\
	PARAM_DEBUG_X(mill_16            /*是否错峰加热 */, int /* 参数类型 */, 1 /* 单次步进 */,  1 /* 连续步进 */, UNIT_NONE /* 单位 */, ADJ_MODE_CIRC /* 调节模式 */, RST_LEVEL_USER_1 /* 重置等级 */,    0xFF /* 内部上限ID，无内部上限 */,    0xFF /* 内部下限ID，无内部下限 */, NULL /*参数更新回调，无更新回调*/, PARAM_SET_TAD_MAX /* 个数 */,                         NO /* 下限值 */,                          YES  /* 上限值 */,  {NO/* 否 */,           NO/* 否 */,                  NO/* 否 */,                  NO/* 否 */,               NO/* 否 */,               NO/* 否 */,                 NO/* 否 */} /* 初始值 */)\
	PARAM_DEBUG_X(mill_17                /*节能时间 */, int /* 参数类型 */, 1 /* 单次步进 */,  1 /* 连续步进 */,  UNIT_SEC /* 单位 */, ADJ_MODE_CIRC /* 调节模式 */, RST_LEVEL_USER_1 /* 重置等级 */, USR_mill_18 /* 内部上限ID，无内部上限 */,        0xFF /* 内部下限ID，无内部下限 */, NULL /*参数更新回调，无更新回调*/, PARAM_SET_TAD_MAX /* 个数 */,                          1 /* 下限值 */,                          999  /* 上限值 */,  {40,          40,                          40,                          40,                       40,                       40,                         40} /* 初始值 */)\
	PARAM_DEBUG_X(mill_18                /*标准时间 */, int /* 参数类型 */, 1 /* 单次步进 */,  1 /* 连续步进 */,  UNIT_SEC /* 单位 */, ADJ_MODE_CIRC /* 调节模式 */, RST_LEVEL_USER_1 /* 重置等级 */, USR_mill_19 /* 内部上限ID，无内部上限 */, USR_mill_17 /* 内部下限ID，无内部下限 */, NULL /*参数更新回调，无更新回调*/, PARAM_SET_TAD_MAX /* 个数 */,                          1 /* 下限值 */,                          999  /* 上限值 */,  {70,          70,                          70,                          70,                       70,                       70,                         70} /* 初始值 */)\
	PARAM_DEBUG_X(mill_19                /*强力时间 */, int /* 参数类型 */, 1 /* 单次步进 */,  1 /* 连续步进 */,  UNIT_SEC /* 单位 */, ADJ_MODE_CIRC /* 调节模式 */, RST_LEVEL_USER_1 /* 重置等级 */,        0xFF /* 内部上限ID，无内部上限 */, USR_mill_18 /* 内部下限ID，无内部下限 */, NULL /*参数更新回调，无更新回调*/, PARAM_SET_TAD_MAX /* 个数 */,                          1 /* 下限值 */,                          999  /* 上限值 */,  {100,         100,                         100,                         100,                      100,                      100,                        100} /* 初始值 */)\
	PARAM_DEBUG_X(mill_20     /*漂洗水位状态是否显示 */, int /* 参数类型 */, 1 /* 单次步进 */,  1 /* 连续步进 */, UNIT_NONE /* 单位 */, ADJ_MODE_CIRC /* 调节模式 */, RST_LEVEL_USER_1 /* 重置等级 */,    0xFF /* 内部上限ID，无内部上限 */,    0xFF /* 内部下限ID，无内部下限 */, NULL /*参数更新回调，无更新回调*/, PARAM_SET_TAD_MAX /* 个数 */,                         NO /* 下限值 */,                          YES  /* 上限值 */,  {NO/* 不显示 */,       YES/* 显示 */,               YES/* 显示 */,               YES/* 显示 */,             NO/* 不显示 */,           YES/* 显示 */,               YES/* 显示 */} /* 初始值 */)\
	PARAM_DEBUG_X(mill_21   /*延时进水中是否开启加热 */, int /* 参数类型 */, 1 /* 单次步进 */,  1 /* 连续步进 */, UNIT_NONE /* 单位 */, ADJ_MODE_CIRC /* 调节模式 */, RST_LEVEL_USER_1 /* 重置等级 */,    0xFF /* 内部上限ID，无内部上限 */,    0xFF /* 内部下限ID，无内部下限 */, NULL /*参数更新回调，无更新回调*/, PARAM_SET_TAD_MAX /* 个数 */,                         NO /* 下限值 */,                          YES  /* 上限值 */,  {NO/* 否 */,           NO/* 否 */,                  NO/* 否 */,                  NO/* 否 */,               NO/* 否 */,               NO/* 否 */,                  NO/* 否 */} /* 初始值 */)\
	PARAM_DEBUG_X(mill_22 /*机门打开漂洗目标下降温度 */, int /* 参数类型 */, 10 /* 单次步进 */, 10 /* 连续步进 */,  UNIT_01C /* 单位 */, ADJ_MODE_CIRC /* 调节模式 */, RST_LEVEL_USER_1 /* 重置等级 */,    0xFF /* 内部上限ID，无内部上限 */,    0xFF /* 内部下限ID，无内部下限 */, NULL /*参数更新回调，无更新回调*/, PARAM_SET_TAD_MAX /* 个数 */,                         0 /* 下限值 */,                           150 /* 上限值 */,  {0,                    0,                           0,                           0,                        0,                        0,                          0} /* 初始值 */)\
	PARAM_DEBUG_X(mill_23                /*停顿时间 */, int /* 参数类型 */, 1 /* 单次步进 */, 1 /* 连续步进 */,  UNIT_SEC /* 单位 */, ADJ_MODE_CIRC /* 调节模式 */, RST_LEVEL_USER_1 /* 重置等级 */,    0xFF /* 内部上限ID，无内部上限 */,    0xFF /* 内部下限ID，无内部下限 */, NULL /*参数更新回调，无更新回调*/, PARAM_SET_TAD_MAX /* 个数 */,                         1 /* 下限值 */,                              60 /* 上限值 */,  {3,                    3,                          3,                            3,                        3,                       3,                           3} /* 初始值 */)\
	PARAM_DEBUG_X(mill_24     /*洗涤探头故障能否运行 */, int /* 参数类型 */, 1 /* 单次步进 */,  1 /* 连续步进 */, UNIT_NONE /* 单位 */, ADJ_MODE_CIRC /* 调节模式 */, RST_LEVEL_USER_1 /* 重置等级 */,    0xFF /* 内部上限ID，无内部上限 */,    0xFF /* 内部下限ID，无内部下限 */, NULL /*参数更新回调，无更新回调*/, PARAM_SET_TAD_MAX /* 个数 */,                         NO /* 下限值 */,                          YES  /* 上限值 */,  {NO,                   NO,                         NO,                           NO,                       NO,                      NO,                          NO} /* 初始值 */)\
	PARAM_DEBUG_X(mill_25   /*探头故障洗涤加热开周期 */, int /* 参数类型 */, 1 /* 单次步进 */,  1 /* 连续步进 */, UNIT_SEC /* 单位 */, ADJ_MODE_CIRC /* 调节模式 */, RST_LEVEL_USER_1 /* 重置等级 */,    0xFF /* 内部上限ID，无内部上限 */,    0xFF /* 内部下限ID，无内部下限 */, NULL /*参数更新回调，无更新回调*/, PARAM_SET_TAD_MAX /* 个数 */,                         0 /* 下限值 */,                            999  /* 上限值 */,  {20,                   20,                         20,                           20,                       20,                      20,                          20} /* 初始值 */)\
	PARAM_DEBUG_X(mill_26   /*探头故障洗涤加热关周期 */, int /* 参数类型 */, 1 /* 单次步进 */,  1 /* 连续步进 */, UNIT_SEC /* 单位 */, ADJ_MODE_CIRC /* 调节模式 */, RST_LEVEL_USER_1 /* 重置等级 */,    0xFF /* 内部上限ID，无内部上限 */,    0xFF /* 内部下限ID，无内部下限 */, NULL /*参数更新回调，无更新回调*/, PARAM_SET_TAD_MAX /* 个数 */,                         0 /* 下限值 */,                            999  /* 上限值 */,  {20,                   20,                         20,                           20,                       20,                      20,                          20} /* 初始值 */)\
	PARAM_DEBUG_X(mill_27     /*漂洗探头故障能否运行 */, int /* 参数类型 */, 1 /* 单次步进 */,  1 /* 连续步进 */, UNIT_NONE /* 单位 */, ADJ_MODE_CIRC /* 调节模式 */, RST_LEVEL_USER_1 /* 重置等级 */,    0xFF /* 内部上限ID，无内部上限 */,    0xFF /* 内部下限ID，无内部下限 */, NULL /*参数更新回调，无更新回调*/, PARAM_SET_TAD_MAX /* 个数 */,                         NO /* 下限值 */,                          YES  /* 上限值 */,  {NO,                   NO,                         NO,                           NO,                       NO,                      NO,                          NO} /* 初始值 */)\
	PARAM_DEBUG_X(mill_28   /*探头故障漂洗加热开周期 */, int /* 参数类型 */, 1 /* 单次步进 */,  1 /* 连续步进 */, UNIT_SEC /* 单位 */, ADJ_MODE_CIRC /* 调节模式 */, RST_LEVEL_USER_1 /* 重置等级 */,    0xFF /* 内部上限ID，无内部上限 */,    0xFF /* 内部下限ID，无内部下限 */, NULL /*参数更新回调，无更新回调*/, PARAM_SET_TAD_MAX /* 个数 */,                         0 /* 下限值 */,                            999  /* 上限值 */,  {20,                   20,                         20,                           20,                       20,                      20,                          20} /* 初始值 */)\
	PARAM_DEBUG_X(mill_29   /*探头故障漂洗加热关周期 */, int /* 参数类型 */, 1 /* 单次步进 */,  1 /* 连续步进 */, UNIT_SEC /* 单位 */, ADJ_MODE_CIRC /* 调节模式 */, RST_LEVEL_USER_1 /* 重置等级 */,    0xFF /* 内部上限ID，无内部上限 */,    0xFF /* 内部下限ID，无内部下限 */, NULL /*参数更新回调，无更新回调*/, PARAM_SET_TAD_MAX /* 个数 */,                         0 /* 下限值 */,                            999  /* 上限值 */,  {20,                   20,                         20,                           20,                       20,                      20,                          20} /* 初始值 */)\
	\
	PARAM_DEBUG_X(mode                      /*模式 */, int /* 参数类型 */, 1 /* 单次步进 */,  1 /* 连续步进 */,  UNIT_NONE /* 单位 */, ADJ_MODE_CIRC /* 调节模式 */, RST_LEVEL_SYS /* 重置等级 */,    0xFF /* 内部上限ID，无内部上限 */,    0xFF /* 内部下限ID，无内部下限 */, Business_EE_immediate_storage_and_storage_management /*参数更新回调*/,                 1 /* 个数 */,                Wash_Mode_1 /* 下限值 */,                  Wash_Mode_3  /* 上限值 */,   Wash_Mode_2/* 初始值 */)\
	\
	PARAM_DEBUG_X(user_1              /*洗涤控温温度 */, int /* 参数类型 */,10 /* 单次步进 */, 10 /* 连续步进 */,  UNIT_01C /* 单位 */, ADJ_MODE_CIRC /* 调节模式 */, RST_LEVEL_USER /* 重置等级 */,    0xFF /* 内部上限ID，无内部上限 */,    0xFF /* 内部下限ID，无内部下限 */, NULL /*参数更新回调，无更新回调*/,                 1 /* 个数 */,                        200 /* 下限值 */,                           800  /* 上限值 */,   650/* 初始值 */)\
	PARAM_DEBUG_X(user_2              /*漂洗控温温度 */, int /* 参数类型 */,10 /* 单次步进 */, 10 /* 连续步进 */,  UNIT_01C /* 单位 */, ADJ_MODE_CIRC /* 调节模式 */, RST_LEVEL_USER /* 重置等级 */,USR_user_7 /* 内部上限ID，无内部上限 */, 0xFF /* 内部下限ID，无内部下限 */, NULL /*参数更新回调，无更新回调*/,                 1 /* 个数 */,                         200 /* 下限值 */,                           990  /* 上限值 */,   800/* 初始值 */)\
	PARAM_DEBUG_X(user_3          /*洗涤控温回差温度 */, int /* 参数类型 */,10 /* 单次步进 */, 10 /* 连续步进 */,  UNIT_01C /* 单位 */, ADJ_MODE_CIRC /* 调节模式 */, RST_LEVEL_USER /* 重置等级 */,    0xFF /* 内部上限ID，无内部上限 */,    0xFF /* 内部下限ID，无内部下限 */, NULL /*参数更新回调，无更新回调*/,                 1 /* 个数 */,                         10 /* 下限值 */,                           200  /* 上限值 */,   30/* 初始值 */)\
	PARAM_DEBUG_X(user_4          /*漂洗控温回差温度 */, int /* 参数类型 */,10 /* 单次步进 */, 10 /* 连续步进 */,  UNIT_01C /* 单位 */, ADJ_MODE_CIRC /* 调节模式 */, RST_LEVEL_USER /* 重置等级 */,    0xFF /* 内部上限ID，无内部上限 */,    0xFF /* 内部下限ID，无内部下限 */, NULL /*参数更新回调，无更新回调*/,                 1 /* 个数 */,                         10 /* 下限值 */,                           200  /* 上限值 */,   30/* 初始值 */)\
	PARAM_DEBUG_X(user_5              /*洗涤门槛温度 */, int /* 参数类型 */,10 /* 单次步进 */, 10 /* 连续步进 */,  UNIT_01C /* 单位 */, ADJ_MODE_CIRC /* 调节模式 */, RST_LEVEL_USER /* 重置等级 */,    0xFF /* 内部上限ID，无内部上限 */,    0xFF /* 内部下限ID，无内部下限 */, NULL /*参数更新回调，无更新回调*/,                 1 /* 个数 */,  0 /* 达到下限显示“关闭” */ /* 下限值 */,                            500 /* 上限值 */,    0/* 初始值 */)\
	PARAM_DEBUG_X(user_6         /*进水阀延时进水时间 */, int /* 参数类型 */, 1 /* 单次步进 */,  1 /* 连续步进 */,  UNIT_SEC /* 单位 */, ADJ_MODE_CIRC /* 调节模式 */, RST_LEVEL_USER /* 重置等级 */,    0xFF /* 内部上限ID，无内部上限 */,    0xFF /* 内部下限ID，无内部下限 */, NULL /*参数更新回调，无更新回调*/,                1 /* 个数 */, 0 /* 达到下限显示“无延时” */ /* 下限值 */,                            99  /* 上限值 */,    3/* 初始值 */)\
	PARAM_DEBUG_X(user_7           /*漂洗进水降温温度 */, int /* 参数类型 */,10 /* 单次步进 */, 10 /* 连续步进 */,  UNIT_01C /* 单位 */, ADJ_MODE_CIRC /* 调节模式 */, RST_LEVEL_USER /* 重置等级 */,  0xFF /* 内部上限ID，无内部上限 */,USR_user_2 /* 内部下限ID，无内部下限 */, NULL /*参数更新回调，无更新回调*/,                1 /* 个数 */,                         70 /* 下限值 */,                            990 /* 上限值 */,  950/* 初始值 */)\
	PARAM_DEBUG_X(user_8              /*进水降温时间 */, int /* 参数类型 */, 1 /* 单次步进 */,  1 /* 连续步进 */,  UNIT_SEC /* 单位 */, ADJ_MODE_CIRC /* 调节模式 */, RST_LEVEL_USER /* 重置等级 */,    0xFF /* 内部上限ID，无内部上限 */,    0xFF /* 内部下限ID，无内部下限 */, NULL /*参数更新回调，无更新回调*/,                1 /* 个数 */,                           5 /* 下限值 */,                            99  /* 上限值 */,  20/* 初始值 */)\
	PARAM_DEBUG_X(user_9            /*洗涤剂工作时间 */, int /* 参数类型 */, 1 /* 单次步进 */,  1 /* 连续步进 */,  UNIT_SEC /* 单位 */, ADJ_MODE_CIRC /* 调节模式 */, RST_LEVEL_USER /* 重置等级 */,    0xFF /* 内部上限ID，无内部上限 */,    0xFF /* 内部下限ID，无内部下限 */, NULL /*参数更新回调，无更新回调*/,                1 /* 个数 */,                           2 /* 下限值 */,                            40  /* 上限值 */,  10/* 初始值 */)\
	PARAM_DEBUG_X(user_10               /*洗涤剂强度 */, int /* 参数类型 */, 1 /* 单次步进 */,  1 /* 连续步进 */,  UNIT_PER /* 单位 */, ADJ_MODE_CIRC /* 调节模式 */, RST_LEVEL_USER /* 重置等级 */,    0xFF /* 内部上限ID，无内部上限 */,    0xFF /* 内部下限ID，无内部下限 */, NULL /*参数更新回调，无更新回调*/,                1 /* 个数 */,   0 /* 达到下限显示“关闭” */ /* 下限值 */,                           100  /* 上限值 */,  99/* 初始值 */)\
	PARAM_DEBUG_X(user_11           /*干燥剂工作时间 */, int /* 参数类型 */, 1 /* 单次步进 */,  1 /* 连续步进 */,  UNIT_SEC /* 单位 */, ADJ_MODE_CIRC /* 调节模式 */, RST_LEVEL_USER /* 重置等级 */,    0xFF /* 内部上限ID，无内部上限 */,    0xFF /* 内部下限ID，无内部下限 */, NULL /*参数更新回调，无更新回调*/,                1 /* 个数 */,                           5 /* 下限值 */,                            99  /* 上限值 */,  10/* 初始值 */)\
	PARAM_DEBUG_X(user_12               /*干燥剂强度 */, int /* 参数类型 */, 1 /* 单次步进 */,  1 /* 连续步进 */,  UNIT_PER /* 单位 */, ADJ_MODE_CIRC /* 调节模式 */, RST_LEVEL_USER /* 重置等级 */,    0xFF /* 内部上限ID，无内部上限 */,    0xFF /* 内部下限ID，无内部下限 */, NULL /*参数更新回调，无更新回调*/,                1 /* 个数 */,   0 /* 达到下限显示“关闭” */ /* 下限值 */,                           100  /* 上限值 */,  99/* 初始值 */)\
	PARAM_DEBUG_X(user_13             /*漂洗工作时间 */, int /* 参数类型 */, 1 /* 单次步进 */,  1 /* 连续步进 */,  UNIT_SEC /* 单位 */, ADJ_MODE_CIRC /* 调节模式 */, RST_LEVEL_USER /* 重置等级 */,    0xFF /* 内部上限ID，无内部上限 */,    0xFF /* 内部下限ID，无内部下限 */, NULL /*参数更新回调，无更新回调*/,                1 /* 个数 */,                           1 /* 下限值 */,                           999  /* 上限值 */,  30/* 初始值 */)\
	PARAM_DEBUG_X(user_14             /*自动关机延时 */, int /* 参数类型 */, 1 /* 单次步进 */,  1 /* 连续步进 */,  UNIT_MIN /* 单位 */, ADJ_MODE_CIRC /* 调节模式 */, RST_LEVEL_USER /* 重置等级 */,    0xFF /* 内部上限ID，无内部上限 */,    0xFF /* 内部下限ID，无内部下限 */, NULL /*参数更新回调，无更新回调*/,                1 /* 个数 */,  0 /* 达到下限显示“不关机” */ /* 下限值 */,                            99  /* 上限值 */,   0/* 初始值 */)\
	PARAM_DEBUG_X(user_15                /*进水超时 */, int /* 参数类型 */, 1 /* 单次步进 */,  1 /* 连续步进 */,  UNIT_MIN /* 单位 */, ADJ_MODE_CIRC /* 调节模式 */, RST_LEVEL_USER /* 重置等级 */,    0xFF /* 内部上限ID，无内部上限 */,    0xFF /* 内部下限ID，无内部下限 */, NULL /*参数更新回调，无更新回调*/,                1 /* 个数 */,  0 /* 达到下限显示“不检测” */ /* 下限值 */,                            99  /* 上限值 */,   0/* 初始值 */)\
	\
	PARAM_DEBUG_X(relay_1                 /*继电器1 */, int /* 参数类型 */, 1 /* 单次步进 */,  1 /* 连续步进 */, UNIT_NONE /* 单位 */, ADJ_MODE_CIRC /* 调节模式 */, RST_LEVEL_USER_2 /* 重置等级 */,    0xFF /* 内部上限ID，无内部上限 */,    0xFF /* 内部下限ID，无内部下限 */, NULL /*参数更新回调，无更新回调*/,                1 /* 个数 */,              HA_RELAY_empty /* 下限值 */,                 HA_RELAY_WA_P  /* 上限值 */,   HA_RELAY_WAT/* 初始值 */)\
	PARAM_DEBUG_X(relay_2                 /*继电器2 */, int /* 参数类型 */, 1 /* 单次步进 */,  1 /* 连续步进 */, UNIT_NONE /* 单位 */, ADJ_MODE_CIRC /* 调节模式 */, RST_LEVEL_USER_2 /* 重置等级 */,    0xFF /* 内部上限ID，无内部上限 */,    0xFF /* 内部下限ID，无内部下限 */, NULL /*参数更新回调，无更新回调*/,                1 /* 个数 */,              HA_RELAY_empty /* 下限值 */,                 HA_RELAY_WA_P  /* 上限值 */,   HA_RELAY_RI_HE/* 初始值 */)\
	PARAM_DEBUG_X(relay_3                 /*继电器3 */, int /* 参数类型 */, 1 /* 单次步进 */,  1 /* 连续步进 */, UNIT_NONE /* 单位 */, ADJ_MODE_CIRC /* 调节模式 */, RST_LEVEL_USER_2 /* 重置等级 */,    0xFF /* 内部上限ID，无内部上限 */,    0xFF /* 内部下限ID，无内部下限 */, NULL /*参数更新回调，无更新回调*/,                1 /* 个数 */,              HA_RELAY_empty /* 下限值 */,                 HA_RELAY_WA_P  /* 上限值 */,   HA_RELAY_WA_HE/* 初始值 */)\
	PARAM_DEBUG_X(relay_4                 /*继电器4 */, int /* 参数类型 */, 1 /* 单次步进 */,  1 /* 连续步进 */, UNIT_NONE /* 单位 */, ADJ_MODE_CIRC /* 调节模式 */, RST_LEVEL_USER_2 /* 重置等级 */,    0xFF /* 内部上限ID，无内部上限 */,    0xFF /* 内部下限ID，无内部下限 */, NULL /*参数更新回调，无更新回调*/,                1 /* 个数 */,              HA_RELAY_empty /* 下限值 */,                 HA_RELAY_WA_P  /* 上限值 */,   HA_RELAY_RI_P/* 初始值 */)\
	PARAM_DEBUG_X(relay_5                 /*继电器5 */, int /* 参数类型 */, 1 /* 单次步进 */,  1 /* 连续步进 */, UNIT_NONE /* 单位 */, ADJ_MODE_CIRC /* 调节模式 */, RST_LEVEL_USER_2 /* 重置等级 */,    0xFF /* 内部上限ID，无内部上限 */,    0xFF /* 内部下限ID，无内部下限 */, NULL /*参数更新回调，无更新回调*/,                1 /* 个数 */,              HA_RELAY_empty /* 下限值 */,                 HA_RELAY_WA_P  /* 上限值 */,   HA_RELAY_WA_P/* 初始值 */)\
	PARAM_DEBUG_X(relay_6                 /*继电器6 */, int /* 参数类型 */, 1 /* 单次步进 */,  1 /* 连续步进 */, UNIT_NONE /* 单位 */, ADJ_MODE_CIRC /* 调节模式 */, RST_LEVEL_USER_2 /* 重置等级 */,    0xFF /* 内部上限ID，无内部上限 */,    0xFF /* 内部下限ID，无内部下限 */, NULL /*参数更新回调，无更新回调*/,                1 /* 个数 */,              HA_RELAY_empty /* 下限值 */,                 HA_RELAY_WA_P  /* 上限值 */,   HA_RELAY_DET/* 初始值 */)\
	PARAM_DEBUG_X(relay_7                 /*继电器7 */, int /* 参数类型 */, 1 /* 单次步进 */,  1 /* 连续步进 */, UNIT_NONE /* 单位 */, ADJ_MODE_CIRC /* 调节模式 */, RST_LEVEL_USER_2 /* 重置等级 */,    0xFF /* 内部上限ID，无内部上限 */,    0xFF /* 内部下限ID，无内部下限 */, NULL /*参数更新回调，无更新回调*/,                1 /* 个数 */,              HA_RELAY_empty /* 下限值 */,                 HA_RELAY_WA_P  /* 上限值 */,   HA_RELAY_DRY/* 初始值 */)\
	\
	PARAM_DEBUG_X(stat_1            /*洗涤剂工作时长 */, int /* 参数类型 */, 1 /* 单次步进 */,  1 /* 连续步进 */,  UNIT_SEC /* 单位 */, ADJ_MODE_CIRC /* 调节模式 */, RST_LEVEL_USER_3 /* 重置等级 */,    0xFF /* 内部上限ID，无内部上限 */,    0xFF /* 内部下限ID，无内部下限 */, NULL /*参数更新回调，无更新回调*/,                1 /* 个数 */,                           0 /* 下限值 */,                    2147483647  /* 上限值 */,   0/* 初始值 */)\
	PARAM_DEBUG_X(stat_2            /*干燥剂工作时长 */, int /* 参数类型 */, 1 /* 单次步进 */,  1 /* 连续步进 */,  UNIT_SEC /* 单位 */, ADJ_MODE_CIRC /* 调节模式 */, RST_LEVEL_USER_3 /* 重置等级 */,    0xFF /* 内部上限ID，无内部上限 */,    0xFF /* 内部下限ID，无内部下限 */, NULL /*参数更新回调，无更新回调*/,                1 /* 个数 */,                           0 /* 下限值 */,                    2147483647  /* 上限值 */,   0/* 初始值 */)\
	PARAM_DEBUG_X(stat_3            /*进水阀工作时长 */, int /* 参数类型 */, 1 /* 单次步进 */,  1 /* 连续步进 */,  UNIT_SEC /* 单位 */, ADJ_MODE_CIRC /* 调节模式 */, RST_LEVEL_USER_3 /* 重置等级 */,    0xFF /* 内部上限ID，无内部上限 */,    0xFF /* 内部下限ID，无内部下限 */, NULL /*参数更新回调，无更新回调*/,                1 /* 个数 */,                           0 /* 下限值 */,                    2147483647  /* 上限值 */,   0/* 初始值 */)\
	PARAM_DEBUG_X(stat_4            /*洗加热工作时长 */, int /* 参数类型 */, 1 /* 单次步进 */,  1 /* 连续步进 */,  UNIT_SEC /* 单位 */, ADJ_MODE_CIRC /* 调节模式 */, RST_LEVEL_USER_3 /* 重置等级 */,    0xFF /* 内部上限ID，无内部上限 */,    0xFF /* 内部下限ID，无内部下限 */, NULL /*参数更新回调，无更新回调*/,                1 /* 个数 */,                           0 /* 下限值 */,                    2147483647  /* 上限值 */,   0/* 初始值 */)\
	PARAM_DEBUG_X(stat_5            /*漂加热工作时长 */, int /* 参数类型 */, 1 /* 单次步进 */,  1 /* 连续步进 */,  UNIT_SEC /* 单位 */, ADJ_MODE_CIRC /* 调节模式 */, RST_LEVEL_USER_3 /* 重置等级 */,    0xFF /* 内部上限ID，无内部上限 */,    0xFF /* 内部下限ID，无内部下限 */, NULL /*参数更新回调，无更新回调*/,                1 /* 个数 */,                           0 /* 下限值 */,                    2147483647  /* 上限值 */,   0/* 初始值 */)\
	PARAM_DEBUG_X(Ostat_1         /*总洗涤剂工作时长 */, int /* 参数类型 */, 1 /* 单次步进 */,  1 /* 连续步进 */,  UNIT_SEC /* 单位 */, ADJ_MODE_CIRC /* 调节模式 */, RST_LEVEL_USER_4 /* 重置等级 */,    0xFF /* 内部上限ID，无内部上限 */,    0xFF /* 内部下限ID，无内部下限 */, NULL /*参数更新回调，无更新回调*/,                1 /* 个数 */,                           0 /* 下限值 */,                    2147483647  /* 上限值 */,   0/* 初始值 */)\
	PARAM_DEBUG_X(Ostat_2         /*总干燥剂工作时长 */, int /* 参数类型 */, 1 /* 单次步进 */,  1 /* 连续步进 */,  UNIT_SEC /* 单位 */, ADJ_MODE_CIRC /* 调节模式 */, RST_LEVEL_USER_4 /* 重置等级 */,    0xFF /* 内部上限ID，无内部上限 */,    0xFF /* 内部下限ID，无内部下限 */, NULL /*参数更新回调，无更新回调*/,                1 /* 个数 */,                           0 /* 下限值 */,                    2147483647  /* 上限值 */,   0/* 初始值 */)\
	PARAM_DEBUG_X(Ostat_3         /*总进水阀工作时长 */, int /* 参数类型 */, 1 /* 单次步进 */,  1 /* 连续步进 */,  UNIT_SEC /* 单位 */, ADJ_MODE_CIRC /* 调节模式 */, RST_LEVEL_USER_4 /* 重置等级 */,    0xFF /* 内部上限ID，无内部上限 */,    0xFF /* 内部下限ID，无内部下限 */, NULL /*参数更新回调，无更新回调*/,                1 /* 个数 */,                           0 /* 下限值 */,                    2147483647  /* 上限值 */,   0/* 初始值 */)\
	PARAM_DEBUG_X(Ostat_4         /*总洗加热工作时长 */, int /* 参数类型 */, 1 /* 单次步进 */,  1 /* 连续步进 */,  UNIT_SEC /* 单位 */, ADJ_MODE_CIRC /* 调节模式 */, RST_LEVEL_USER_4 /* 重置等级 */,    0xFF /* 内部上限ID，无内部上限 */,    0xFF /* 内部下限ID，无内部下限 */, NULL /*参数更新回调，无更新回调*/,                1 /* 个数 */,                           0 /* 下限值 */,                    2147483647  /* 上限值 */,   0/* 初始值 */)\
	PARAM_DEBUG_X(Ostat_5         /*总漂加热工作时长 */, int /* 参数类型 */, 1 /* 单次步进 */,  1 /* 连续步进 */,  UNIT_SEC /* 单位 */, ADJ_MODE_CIRC /* 调节模式 */, RST_LEVEL_USER_4 /* 重置等级 */,    0xFF /* 内部上限ID，无内部上限 */,    0xFF /* 内部下限ID，无内部下限 */, NULL /*参数更新回调，无更新回调*/,                1 /* 个数 */,                           0 /* 下限值 */,                    2147483647  /* 上限值 */,   0/* 初始值 */)\
	\
	PARAM_DEBUG_X(qr_code                  /*二维码 */, int /* 参数类型 */, 1 /* 单次步进 */,  1 /* 连续步进 */,  UNIT_NONE /* 单位 */, ADJ_MODE_AMP /* 调节模式 */, RST_LEVEL_SYS /* 重置等级 */,    0xFF /* 内部上限ID，无内部上限 */,    0xFF /* 内部下限ID，无内部下限 */, Business_EE_immediate_storage_and_storage_management /*参数更新回调*/,    1 /* 个数 */,    NO /* 下限值 */,                          YES  /* 上限值 */,   NO/* 初始值 */)
	
/* 参数ID */
enum param_id
{
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) USR_##name,
	PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
		PARAM_ITEM_NUM /* 参数个数 */,
};

/* 水满判断 */
enum water_full_Type
{
	water_full_1,/* 仅洗高 */
	water_full_2,/* 洗高与漂高 */
};

/* 漂洗控温开启条件 */
enum R_t_c_o_c_Type
{
	R_t_c_o_c_1,/* 仅洗高 */
	R_t_c_o_c_2,/* 仅洗低 */
	R_t_c_o_c_3,/* 仅漂高 */
	R_t_c_o_c_4,/* 仅漂低 */
	R_t_c_o_c_5,/* 洗高与漂高 */
};

/* 洗涤控温开启条件 */
enum W_t_c_o_c_Type
{
	W_t_c_o_c_1,/* 仅洗高 */
	W_t_c_o_c_2,/* 仅洗低 */
};

/* 运行中缺水是否暂停 */
enum UP_w_s_s_Type
{
	UP_w_s_s_0,/* 不暂停 */
	UP_w_s_s_1,/* 洗高暂停 */
	UP_w_s_s_2,/* 洗低暂停 */
	UP_w_s_s_3,/* 漂高暂停 */
	UP_w_s_s_4,/* 漂低暂停 */
};

/* 洗碗模式 */
enum Wash_Mode_Type
{
	Wash_Mode_1,	 /* 节能 */
	Wash_Mode_2,	 /* 标准 */
	Wash_Mode_3,     /* 强力 */
};

/* 硬件继电器 */
enum Hard_RELAY
{
	HA_RELAY_empty, /* 闲置 */
	HA_RELAY_DET,	/* 洗涤剂 */
	HA_RELAY_DRY,	/* 干燥机 */
	HA_RELAY_RI_HE, /* 漂加热 */
	HA_RELAY_WA_HE, /* 洗加热 */
	HA_RELAY_WAT,	/* 进水阀 */
	HA_RELAY_RI_P,	/* 漂洗泵 */
	HA_RELAY_WA_P,	/* 洗涤泵 */
};

#ifndef ON
#define ON 1
#endif

#ifndef OFF
#define OFF 0
#endif

#ifndef YES
#define YES 1
#endif

#ifndef NO
#define NO 0
#endif

#endif
