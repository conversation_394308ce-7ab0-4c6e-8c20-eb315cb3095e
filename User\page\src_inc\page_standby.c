#include "page_standby.h"

/*
*********************************************************************************************************
*	函 数 名: page_root_load
*	形    参: 无
*	返 回 值: 无
*	功能说明: 根界面切换时调用
*********************************************************************************************************
*/
void page_standby_root_load(void)
{
}
/*
*********************************************************************************************************
*	函 数 名: page_view_load
*	形    参: 无
*	返 回 值: 无
*	功能说明: 交互界面切换时调用
*********************************************************************************************************
*/
void page_standby_view_load(void)
{
    InformationOutput = 0;              /* 开关量输出 */
    GearOutput[0] = 0; /* 模拟量输出 */ /* pwm输出 */
    GearOutput[1] = 0; /* 模拟量输出 */ /* pwm输出 */
    bsp_SetKeyParam(KID_K1, 100, 0);    // 支持长按1秒，无连发
    bsp_SetKeyParam(KID_K2, 100, 0);    // 支持长按1秒，无连发
    bsp_SetKeyParam(KID_K3, 100, 0);    // 支持长按1秒，无连发
    bsp_SetKeyParam(KID_K4, 100, 0);    // 支持长按1秒，无连发
    bsp_SetKeyParam(KID_K5, 100, 0);    // 支持长按1秒，无连发
    switch (pa_m_tVar.current_page /* 当前交互界面 */)
    {
        /**************************************************** 运行 *****************************************************/
    case page_run:
        DishwashingCloseBusiness(); // 关闭业务
        break;
        /**************************************************** 二维码(物联网) *****************************************************/
    case page_qr_code:
        break;
        /**************************************************** 参数设置 *****************************************************/
    case page_parameter:
        /**************************************************** 统计查看 *****************************************************/
    case page_statistics:
        Business_EE_immediate_storage_and_storage_management(); /*  业务ee立刻存储存储管理 */
        break;
    default:
        /* 其它值不处理 */
        break;
    }

    ui28_Text_Gradient_Synchronized = 0;             /* 文本渐变同步标志位 */
    UI28_Clear_Screen();                             /* 2.8寸屏幕清屏，并关闭所有模块，一般切换界面时调用 */
    UI28_display_onoff_album(ui28_1_album, 1);       /* 菜单显示开关（关实际只是不刷新了） */
    UI28_display_onoff_text_gradient(ui28_T2_tg, 1); /* 文本渐变显示开关（关实际只是不刷新了） */
    UI28_is_the_cursor_hidden_menu(0);               /* 设置光标是否隐藏 */
}
/*
*********************************************************************************************************
*	函 数 名: page_update
*	形    参: 无
*	返 回 值: 无
*	功能说明: 为交互界面时，会周期调用
*********************************************************************************************************
*/
void page_standby_update(void)
{
    switch (ui28_album_photo_cur_val[ui28_1_album] /* 相册当前光标值 */)
    {
    case PIC_120_120_RUN_B0WL /* 图片【运行碗】索引 */:
        UI28_set_up_text_gradient_add(ui28_T2_tg, "  运行  "); /* 设置文本地址 */
        break;
    case PIC_120_120_SET_UP /* 图片【设置】索引 */:
        UI28_set_up_text_gradient_add(ui28_T2_tg, "参数设置"); /* 设置文本地址 */
        break;
    case PIC_120_120_DEBUG /* 图片【调试】索引 */:
        UI28_set_up_text_gradient_add(ui28_T2_tg, "系统调试"); /* 设置文本地址 */
        break;
    case PIC_120_120_QR_CODE /* 图片【二维码】索引 */:
        UI28_set_up_text_gradient_add(ui28_T2_tg, " 物联网 "); /* 设置文本地址 */
        break;
    case PIC_120_120_COUNT /* 图片【统计】索引 */:
        UI28_set_up_text_gradient_add(ui28_T2_tg, "用量统计"); /* 设置文本地址 */
        break;
    default:
        break;
    }
}
