#ifndef _PRO_WORKFLOW_H
#define _PRO_WORKFLOW_H

/* 洗碗状态 */
enum Dishwashing_status
{
    DIS_S_STOP, /* 停 */
    DIS_S_RUN,  /* 动 */
};
/* 洗碗进程 */
enum Dishwashing_process
{
    DIS_P_WASH,   /* 洗涤 */
    DIS_P_STOP,   /* 停顿 */
    DIS_P_RINSE,  /* 漂洗 */
    DIS_P_FINISH, /* 完成 */
};
/* 洗碗条件 */
enum Dishwashing_conditions
{
    DIS_C_SATISFY, /* 满足 */
    DIS_C_DOOR,  /*  门 */
    DIS_C_WATER,  /*  水 */
    DIS_C_TEMP,  /*  温度 */
};
/* 洗碗结构体 */
typedef struct
{
    enum Dishwashing_status DIS_S_;  /* 洗碗状态 */
    enum Dishwashing_process DIS_P_; /* 洗碗进程 */
    int count;                       /* 洗碗计数 */
} Dishwashing_PRO_T;

extern volatile Dishwashing_PRO_T DIS_P_T; /* 洗碗结构体 */
extern volatile uint8_t pro_lock; // 锁机标志位（是否锁机）
/* 锁机状态管理（非阻塞轮询1秒） */
void LockStateManagement(void);

/* 洗碗业务运行管理（非阻塞轮询1秒） */
void DishwashingBusinessOperation(void);
/* 洗碗关闭业务 */
void DishwashingCloseBusiness(void);
/* 洗碗暂停业务 */
void DishwashingSuspendBusiness(void);
/* 水满检查函数 */
uint8_t Full_water_check_function(void);
/* 上电水满一次检测函数（非阻塞轮询1秒） */
void Power_on_water_full_once_detection_function(void);
/* 运行条件判断 */
uint8_t DishwashingDeterminationOfOperatingConditions(void);
/* 从暂停业务到启动 */
/* 返回0：条件没达到 1：启动成功 2：已经在运行中 */
uint8_t From_suspending_business_to_starting(void);
/* 缺水停止运行管理（非阻塞轮询1秒） */
void Stop_operation_management_due_to_water_shortage(void);
/* 开机画面停留时间（非阻塞轮询1秒） */
void Power_on_screen_dwell_time(void);

extern volatile uint8_t interface_settings_No_Action_reset_count; // 设置界面无操作无操作计数（1S\60S）
/* 设置界面无操作倒计时处理（非阻塞轮询1S秒） */
void Interface_no_operation_countdown_processing(void);

/* 运行界面无运行操作倒计时处理（非阻塞轮询1S秒） */
void Countdown_processing_for_no_running_operation_on_the_running_interface(void);
/* 洗碗机用量统计（非阻塞轮询1S秒） */
void Dishwasher_usage_statistics(void);
/*  业务ee立刻存储存储管理 */
void Business_EE_immediate_storage_and_storage_management(void);
/*  业务ee半小时存储管理（非阻塞轮询1S秒） */
void Business_EE_half_hour_storage_management(void);

/* 洗涤剂输出控制（非阻塞轮询1秒） */
void Detergent_output_control(void);
/* 干燥剂输出控制（非阻塞轮询1秒） */
void Desiccant_output_control(void);
/* 进水阀输出控制（非阻塞轮询1秒） */
void Inlet_valve_output_control(void);
/* 漂洗加热输出控制（非阻塞轮询1秒） */
void Rinse_heating_output_control(void);
/* 洗涤加热输出控制（非阻塞轮询1秒） */
void Washing_and_heating_output_control(void);
/* 漂洗泵输出控制（非阻塞轮询1秒） */
void Rinse_pump_output_control(void);
/* 洗涤泵输出控制（非阻塞轮询1秒） */
void Washing_pump_output_control(void);
/* 关门延时启动函数（非阻塞轮询1秒） */
void Door_closing_delay_start_function(void);
extern volatile int Water_inlet_delay_count; /* 进水延时计数（1秒） */

/* 洗高从开到关触发一次 */
void Wa_H_from_open_to_closed(void);
/* 洗高从关到开触发一次 */
void Wa_H_from_closed_to_open(void);
/* 洗低从开到关触发一次 */
void Wa_L_from_open_to_closed(void);
/* 洗低从关到开触发一次 */
void Wa_L_from_closed_to_open(void);
/* 漂高从开到关触发一次 */
void RI_H_from_open_to_closed(void);
/* 漂高从关到开触发一次 */
void RI_H_from_closed_to_open(void);
/* 漂低从开到关触发一次 */
void RI_L_from_open_to_closed(void);
/* 漂低从关到开触发一次 */
void RI_L_from_closed_to_open(void);
/* 机门从开到关触发一次 */
void Door_from_open_to_closed(void);
/* 机门从关到开触发一次 */
void Door_from_closed_to_open(void);

#endif
