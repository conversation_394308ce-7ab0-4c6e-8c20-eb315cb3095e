#include "bsp.h"

/*
	全局运行时间，单位1ms
	最长可以表示 24.85天，如果你的产品连续运行时间超过这个数，则必须考虑溢出问题
*/
__IO int32_t g_iRunTime = 0;
/* 这个全局变量转用于 bsp_DelayMS() 函数 */
__IO uint32_t s_uiDelayCount = 0;
/* 创建闹钟 */
__IO ALARM_CLOCK_T a_talarm_clock[FALARM_CLOCK_ITEM_NUM] = 
{
	#define ALARM_CLOCK_DEBUG_X(name) {0,0xFF},
	ALARM_CLOCK_ITEM_LIST
#undef ALARM_CLOCK_DEBUG_X
};		

/*
*********************************************************************************************************
*	函 数 名: Timer0_alarm_clock
*	功能说明: Timer0任务闹钟
*	形    参: sto_-选择的闹钟,time_100ms- 闹钟时间（单位100ms）,
            End_Message-结束后发送的消息（在按键FIFO中，注意不要使用已有的命令消息）（不可以为0xFF，0xFF为闹钟失能）
*	返 回 值: 无
*********************************************************************************************************
*/
void ms100_alarm_clock(ALARM_CLOCK_ID_E sto_, uint16_t time_100ms, uint8_t End_Message)
{
	a_talarm_clock[sto_].usTime = time_100ms;//设置闹钟时间
	a_talarm_clock[sto_].message = End_Message;//非0xFF为使能，否则失能
}

/*
*********************************************************************************************************
*	函 数 名: DetectALARM100ms
*	功能说明: 扫描所有闹钟。非阻塞，被systick中断周期性的调用，100ms一次
*	形    参: 无
*	返 回 值: 无
*********************************************************************************************************
*/
void DetectALARM100ms(void)
{
	uint8_t i;
	for (i = 0; i < FALARM_CLOCK_ITEM_NUM; i++)
	{
		if ((a_talarm_clock[i].message == 0xFF) || (a_talarm_clock[i].usTime == 0))
		{
			continue; // 跳过本次循环
		}
		if (--a_talarm_clock[i].usTime == 0)//时间计数+1，并且判断是否到0
		{
			bsp_Putfifo(a_talarm_clock[i].message);//发送命令消息
		}
	}
}

/*
*********************************************************************************************************
*	函 数 名: bsp_InitTimer
*	功能说明: 配置systick中断，并初始化软件定时器变量
*	形    参:  无
*	返 回 值: 无
*********************************************************************************************************
*/
void bsp_InitTimer(void)
{
	/* 配置systic中断周期为1ms，并启动systick中断。*/
	RCC_ClocksTypeDef clocks;
	uint32_t MsNumber=0;	
	RCC_GetClocksFreq(&clocks); /* 获取时钟频率 */ 
	MsNumber=clocks.HCLK_Frequency/8/1000;/* 除以 8 被选为 SysTick 时钟源再除以 1000 得到SysTick中断计数，1ms一次中断 */ 

  SysTick->LOAD  = MsNumber - 1UL;                         /* 设置重载寄存器 */
  NVIC_SetPriority (SysTick_IRQn, (1UL << __NVIC_PRIO_BITS) - 1UL); /* 设置 Systick 中断的优先级 */
  SysTick->VAL   = 0UL;                                             /* 加载 SysTick 计数器值 */
  SysTick->CTRL  = SysTick_CTRL_TICKINT_Msk   |
                   SysTick_CTRL_ENABLE_Msk;                         /* 启用 SysTick IRQ 和 SysTick 定时器 */
}

/*
*********************************************************************************************************
*	函 数 名: bsp_GetRunTime
*	功能说明: 获取CPU运行时间，单位1ms。最长可以表示 24.85天，如果你的产品连续运行时间超过这个数，则必须考虑溢出问题
*	形    参:  无
*	返 回 值: CPU运行时间，单位1ms
*********************************************************************************************************
*/
int32_t bsp_GetRunTime(void)
{
	int32_t runtime;

	DISABLE_INT();  	/* 关中断 */

	runtime = g_iRunTime;	/* 这个变量在Systick中断中被改写，因此需要关中断进行保护 */

	ENABLE_INT();  		/* 开中断 */

	return runtime;
}

/*
*********************************************************************************************************
*	函 数 名: bsp_CheckRunTime
*	功能说明: 计算当前运行时间和给定时刻之间的差值。处理了计数器循环。
*	形    参:  _LastTime 上个时刻
*	返 回 值: 当前时间和过去时间的差值，单位1ms
*********************************************************************************************************
*/
int32_t bsp_CheckRunTime(int32_t _LastTime)
{
	int32_t now_time;
	int32_t time_diff;

	DISABLE_INT();  	/* 关中断 */

	now_time = g_iRunTime;	/* 这个变量在Systick中断中被改写，因此需要关中断进行保护 */

	ENABLE_INT();  		/* 开中断 */
	
	if (now_time >= _LastTime)
	{
		time_diff = now_time - _LastTime;
	}
	else
	{
		time_diff = 0x7FFFFFFF - _LastTime + now_time;
	}

	return time_diff;
}

/*
*********************************************************************************************************
*	函 数 名: bsp_DelayMS
*	功能说明: ms级延迟，延迟精度为正负1ms
*	形    参:  n : 延迟长度，单位1 ms。 n 应大于2
*	返 回 值: 无
*********************************************************************************************************
*/
void bsp_DelayMS(uint32_t n)
{
//	DISABLE_INT();  			/* 关中断 */
	s_uiDelayCount = n;
//	ENABLE_INT();  				/* 开中断 */
	while (s_uiDelayCount);
}

/*
*********************************************************************************************************
*	函 数 名: SysTick_Handler
*	功能说明: 系统嘀嗒定时器中断服务程序。启动文件中引用了该函数。
*	形    参:  无
*	返 回 值: 无
*********************************************************************************************************
*/
void SysTick_Handler(void)
{
	static uint8_t s_count = 0;
	
	/* 全局运行时间每1ms增1 */
	g_iRunTime++;
	if (g_iRunTime == 0x7FFFFFFF)	/* 这个变量是 int32_t 类型，最大数为 0x7FFFFFFF */
	{
		g_iRunTime = 0;
	}
	
	/* 每隔1ms进来1次 （仅用于 bsp_DelayMS） */
	if (s_uiDelayCount > 0)
	{
		s_uiDelayCount--;
	}

	if (++s_count >= 10)
	{
		s_count = 0;

		bsp_RunPer10ms();	/* 每隔10ms调用一次此函数，此函数在 bsp.c */
	}
}
