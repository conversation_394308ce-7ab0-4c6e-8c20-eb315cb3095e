#include "UI28.h"
struct FONT72_ASCII const Font72_Ascii[] =
    {
        "-", 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xC0, 0x00,
        0x00, 0xFF, 0xFF, 0xC0, 0x00, 0x00, 0xFF, 0xFF, 0xC0, 0x00, 0x00, 0xFF, 0xFF, 0xC0, 0x00, 0x00,
        0xFF, 0xFF, 0xC0, 0x00, 0x00, 0xFF, 0xFF, 0xC0, 0x00, 0x00, 0xFF, 0xFF, 0xC0, 0x00, 0x00, 0xFF,
        0xFF, 0xC0, 0x00, 0x00, 0xFF, 0xFF, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"-",0*/

        "0", 0x00, 0x03, 0xFC, 0x00, 0x00, 0x00, 0x0F, 0xFF, 0x00, 0x00, 0x00, 0x3F, 0xFF, 0xC0, 0x00, 0x00,
        0x7F, 0xFF, 0xE0, 0x00, 0x00, 0xFF, 0xFF, 0xF0, 0x00, 0x01, 0xFF, 0xFF, 0xF8, 0x00, 0x03, 0xFF,
        0xFF, 0xF8, 0x00, 0x03, 0xFF, 0xFF, 0xFC, 0x00, 0x07, 0xFF, 0xFF, 0xFE, 0x00, 0x07, 0xFF, 0xFF,
        0xFE, 0x00, 0x0F, 0xFF, 0xFF, 0xFF, 0x00, 0x0F, 0xFF, 0xFF, 0xFF, 0x00, 0x1F, 0xFF, 0xFF, 0xFF,
        0x00, 0x1F, 0xFF, 0xFF, 0xFF, 0x80, 0x1F, 0xFF, 0xFF, 0xFF, 0x80, 0x1F, 0xFF, 0xFF, 0xFF, 0x80,
        0x3F, 0xFF, 0xFF, 0xFF, 0x80, 0x3F, 0xFF, 0xFF, 0xFF, 0xC0, 0x3F, 0xFF, 0x0F, 0xFF, 0xC0, 0x3F,
        0xFE, 0x0F, 0xFF, 0xC0, 0x3F, 0xFE, 0x07, 0xFF, 0xC0, 0x3F, 0xFE, 0x07, 0xFF, 0xC0, 0x7F, 0xFE,
        0x07, 0xFF, 0xC0, 0x7F, 0xFE, 0x07, 0xFF, 0xC0, 0x7F, 0xFE, 0x07, 0xFF, 0xC0, 0x7F, 0xFE, 0x07,
        0xFF, 0xC0, 0x7F, 0xFE, 0x07, 0xFF, 0xE0, 0x7F, 0xFE, 0x07, 0xFF, 0xE0, 0x7F, 0xFE, 0x07, 0xFF,
        0xE0, 0x7F, 0xFE, 0x07, 0xFF, 0xE0, 0x7F, 0xFE, 0x07, 0xFF, 0xE0, 0x7F, 0xFE, 0x07, 0xFF, 0xE0,
        0x7F, 0xFE, 0x07, 0xFF, 0xE0, 0x7F, 0xFE, 0x07, 0xFF, 0xE0, 0x7F, 0xFE, 0x07, 0xFF, 0xE0, 0x7F,
        0xFE, 0x07, 0xFF, 0xE0, 0x7F, 0xFE, 0x07, 0xFF, 0xE0, 0x7F, 0xFE, 0x07, 0xFF, 0xE0, 0x7F, 0xFE,
        0x07, 0xFF, 0xE0, 0x7F, 0xFE, 0x07, 0xFF, 0xE0, 0x7F, 0xFE, 0x07, 0xFF, 0xE0, 0x7F, 0xFE, 0x07,
        0xFF, 0xE0, 0x7F, 0xFE, 0x07, 0xFF, 0xE0, 0x7F, 0xFE, 0x07, 0xFF, 0xE0, 0x7F, 0xFE, 0x07, 0xFF,
        0xC0, 0x7F, 0xFE, 0x07, 0xFF, 0xC0, 0x7F, 0xFE, 0x07, 0xFF, 0xC0, 0x7F, 0xFE, 0x07, 0xFF, 0xC0,
        0x3F, 0xFE, 0x07, 0xFF, 0xC0, 0x3F, 0xFE, 0x07, 0xFF, 0xC0, 0x3F, 0xFE, 0x07, 0xFF, 0xC0, 0x3F,
        0xFF, 0x0F, 0xFF, 0xC0, 0x3F, 0xFF, 0xFF, 0xFF, 0xC0, 0x3F, 0xFF, 0xFF, 0xFF, 0x80, 0x1F, 0xFF,
        0xFF, 0xFF, 0x80, 0x1F, 0xFF, 0xFF, 0xFF, 0x80, 0x1F, 0xFF, 0xFF, 0xFF, 0x80, 0x1F, 0xFF, 0xFF,
        0xFF, 0x00, 0x0F, 0xFF, 0xFF, 0xFF, 0x00, 0x0F, 0xFF, 0xFF, 0xFF, 0x00, 0x07, 0xFF, 0xFF, 0xFE,
        0x00, 0x07, 0xFF, 0xFF, 0xFE, 0x00, 0x03, 0xFF, 0xFF, 0xFC, 0x00, 0x03, 0xFF, 0xFF, 0xFC, 0x00,
        0x01, 0xFF, 0xFF, 0xF8, 0x00, 0x00, 0xFF, 0xFF, 0xF0, 0x00, 0x00, 0x7F, 0xFF, 0xE0, 0x00, 0x00,
        0x3F, 0xFF, 0xC0, 0x00, 0x00, 0x1F, 0xFF, 0x00, 0x00, 0x00, 0x03, 0xFC, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"0",0*/

        "1", 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0x80, 0x00, 0x00,
        0x1F, 0xFF, 0x80, 0x00, 0x00, 0x1F, 0xFF, 0x80, 0x00, 0x00, 0x1F, 0xFF, 0x80, 0x00, 0x00, 0x1F,
        0xFF, 0x80, 0x00, 0x00, 0x1F, 0xFF, 0x80, 0x00, 0x00, 0x1F, 0xFF, 0x80, 0x00, 0x00, 0x1F, 0xFF,
        0x80, 0x00, 0x00, 0x1F, 0xFF, 0x80, 0x00, 0x00, 0x1F, 0xFF, 0x80, 0x00, 0x00, 0x1F, 0xFF, 0x80,
        0x00, 0x00, 0x1F, 0xFF, 0x80, 0x00, 0x00, 0x1F, 0xFF, 0x80, 0x00, 0x00, 0x1F, 0xFF, 0x80, 0x00,
        0x00, 0x1F, 0xFF, 0x80, 0x00, 0x00, 0x1F, 0xFF, 0x80, 0x00, 0x00, 0x1F, 0xFF, 0x80, 0x00, 0x00,
        0x1F, 0xFF, 0x80, 0x00, 0x00, 0x1F, 0xFF, 0x80, 0x00, 0x00, 0x1F, 0xFF, 0x80, 0x00, 0x00, 0x1F,
        0xFF, 0x80, 0x00, 0x00, 0x1F, 0xFF, 0x80, 0x00, 0x00, 0x1F, 0xFF, 0x80, 0x00, 0x00, 0x1F, 0xFF,
        0x80, 0x00, 0x00, 0x1F, 0xFF, 0x80, 0x00, 0x00, 0x1F, 0xFF, 0x80, 0x00, 0x00, 0x1F, 0xFF, 0x80,
        0x00, 0x00, 0x1F, 0xFF, 0x80, 0x00, 0x00, 0x1F, 0xFF, 0x80, 0x00, 0x00, 0x1F, 0xFF, 0x80, 0x00,
        0x00, 0x1F, 0xFF, 0x80, 0x00, 0x00, 0x1F, 0xFF, 0x80, 0x00, 0x00, 0x1F, 0xFF, 0x80, 0x00, 0x00,
        0x1F, 0xFF, 0x80, 0x00, 0x00, 0x1F, 0xFF, 0x80, 0x00, 0x00, 0x1F, 0xFF, 0x80, 0x00, 0x00, 0x1F,
        0xFF, 0x80, 0x00, 0x00, 0x1F, 0xFF, 0x80, 0x00, 0x00, 0x1F, 0xFF, 0x80, 0x00, 0x00, 0x1F, 0xFF,
        0x80, 0x00, 0x00, 0x1F, 0xFF, 0x80, 0x00, 0x00, 0x1F, 0xFF, 0x80, 0x00, 0x00, 0x1F, 0xFF, 0x80,
        0x00, 0x00, 0x1F, 0xFF, 0x80, 0x00, 0x00, 0x1F, 0xFF, 0x80, 0x00, 0x00, 0x1F, 0xFF, 0x80, 0x00,
        0x00, 0x1F, 0xFF, 0x80, 0x00, 0x00, 0x1F, 0xFF, 0x80, 0x00, 0x00, 0x1F, 0xFF, 0x80, 0x00, 0x00,
        0x1F, 0xFF, 0x80, 0x00, 0x00, 0x1F, 0xFF, 0x80, 0x00, 0x00, 0x1F, 0xFF, 0x80, 0x00, 0x00, 0x1F,
        0xFF, 0x80, 0x00, 0x00, 0x1F, 0xFF, 0x80, 0x00, 0x00, 0x1F, 0xFF, 0x80, 0x00, 0x00, 0x1F, 0xFF,
        0x80, 0x00, 0x00, 0x1F, 0xFF, 0x80, 0x00, 0x00, 0x1F, 0xFF, 0x80, 0x00, 0x00, 0x1F, 0xFF, 0x80,
        0x00, 0x00, 0x1F, 0xFF, 0x80, 0x00, 0x00, 0x1F, 0xFF, 0x80, 0x00, 0x00, 0x1F, 0xFF, 0x80, 0x00,
        0x00, 0x1F, 0xFF, 0x80, 0x00, 0x00, 0x1F, 0xFF, 0x80, 0x00, 0x00, 0x1F, 0xFF, 0x80, 0x00, 0x00,
        0x1F, 0xFF, 0x80, 0x00, 0x00, 0x1F, 0xFF, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"1",1*/

        "2", 0x00, 0x03, 0xF8, 0x00, 0x00, 0x00, 0x0F, 0xFF, 0x00, 0x00, 0x00, 0x3F, 0xFF, 0x80, 0x00, 0x00,
        0x7F, 0xFF, 0xC0, 0x00, 0x00, 0xFF, 0xFF, 0xE0, 0x00, 0x01, 0xFF, 0xFF, 0xF0, 0x00, 0x03, 0xFF,
        0xFF, 0xF8, 0x00, 0x03, 0xFF, 0xFF, 0xFC, 0x00, 0x07, 0xFF, 0xFF, 0xFC, 0x00, 0x07, 0xFF, 0xFF,
        0xFE, 0x00, 0x0F, 0xFF, 0xFF, 0xFE, 0x00, 0x0F, 0xFF, 0xFF, 0xFF, 0x00, 0x1F, 0xFF, 0xFF, 0xFF,
        0x00, 0x1F, 0xFF, 0xFF, 0xFF, 0x00, 0x1F, 0xFF, 0xFF, 0xFF, 0x80, 0x3F, 0xFF, 0xFF, 0xFF, 0x80,
        0x3F, 0xFF, 0xFF, 0xFF, 0x80, 0x3F, 0xFF, 0xFF, 0xFF, 0x80, 0x3F, 0xFF, 0xFF, 0xFF, 0xC0, 0x3F,
        0xFF, 0xFF, 0xFF, 0xC0, 0x3F, 0xFE, 0x1F, 0xFF, 0xC0, 0x7F, 0xFE, 0x0F, 0xFF, 0xC0, 0x7F, 0xFC,
        0x0F, 0xFF, 0xC0, 0x7F, 0xFC, 0x0F, 0xFF, 0xC0, 0x7F, 0xFC, 0x0F, 0xFF, 0xC0, 0x7F, 0xFC, 0x0F,
        0xFF, 0xC0, 0x3F, 0xFC, 0x0F, 0xFF, 0xC0, 0x3F, 0xFE, 0x0F, 0xFF, 0xC0, 0x3F, 0xFE, 0x1F, 0xFF,
        0xC0, 0x3F, 0xFE, 0x1F, 0xFF, 0x80, 0x3F, 0xFC, 0x1F, 0xFF, 0x80, 0x3F, 0xFC, 0x3F, 0xFF, 0x80,
        0x1F, 0xF8, 0x3F, 0xFF, 0x80, 0x1F, 0xF8, 0x7F, 0xFF, 0x00, 0x1F, 0xF8, 0x7F, 0xFF, 0x00, 0x0F,
        0xF0, 0x7F, 0xFF, 0x00, 0x0F, 0xF0, 0xFF, 0xFE, 0x00, 0x07, 0xF0, 0xFF, 0xFE, 0x00, 0x07, 0xE0,
        0xFF, 0xFE, 0x00, 0x03, 0xE1, 0xFF, 0xFC, 0x00, 0x03, 0xC1, 0xFF, 0xFC, 0x00, 0x01, 0xC3, 0xFF,
        0xF8, 0x00, 0x00, 0xC3, 0xFF, 0xF8, 0x00, 0x00, 0x03, 0xFF, 0xF8, 0x00, 0x00, 0x07, 0xFF, 0xF0,
        0x00, 0x00, 0x07, 0xFF, 0xF0, 0x00, 0x00, 0x07, 0xFF, 0xF0, 0x00, 0x00, 0x0F, 0xFF, 0xE0, 0x00,
        0x00, 0x0F, 0xFF, 0xE0, 0x00, 0x00, 0x1F, 0xFF, 0xFF, 0xC0, 0x00, 0x1F, 0xFF, 0xFF, 0xC0, 0x00,
        0x1F, 0xFF, 0xFF, 0xC0, 0x00, 0x3F, 0xFF, 0xFF, 0xC0, 0x00, 0x3F, 0xFF, 0xFF, 0xC0, 0x00, 0x3F,
        0xFF, 0xFF, 0xC0, 0x00, 0x7F, 0xFF, 0xFF, 0xC0, 0x00, 0x7F, 0xFF, 0xFF, 0xC0, 0x00, 0x7F, 0xFF,
        0xFF, 0xC0, 0x00, 0xFF, 0xFF, 0xFF, 0xC0, 0x00, 0xFF, 0xFF, 0xFF, 0xC0, 0x01, 0xFF, 0xFF, 0xFF,
        0xC0, 0x01, 0xFF, 0xFF, 0xFF, 0xC0, 0x01, 0xFF, 0xFF, 0xFF, 0xC0, 0x03, 0xFF, 0xFF, 0xFF, 0xC0,
        0x03, 0xFF, 0xFF, 0xFF, 0xC0, 0x03, 0xFF, 0xFF, 0xFF, 0xC0, 0x07, 0xFF, 0xFF, 0xFF, 0xC0, 0x07,
        0xFF, 0xFF, 0xFF, 0xC0, 0x0F, 0xFF, 0xFF, 0xFF, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"2",2*/

        "3", 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0xFF, 0xFF, 0xFC, 0x00, 0x07,
        0xFF, 0xFF, 0xFC, 0x00, 0x07, 0xFF, 0xFF, 0xF8, 0x00, 0x07, 0xFF, 0xFF, 0xF8, 0x00, 0x07, 0xFF,
        0xFF, 0xF8, 0x00, 0x07, 0xFF, 0xFF, 0xF0, 0x00, 0x07, 0xFF, 0xFF, 0xF0, 0x00, 0x07, 0xFF, 0xFF,
        0xF0, 0x00, 0x07, 0xFF, 0xFF, 0xF0, 0x00, 0x07, 0xFF, 0xFF, 0xE0, 0x00, 0x07, 0xFF, 0xFF, 0xE0,
        0x00, 0x07, 0xFF, 0xFF, 0xE0, 0x00, 0x07, 0xFF, 0xFF, 0xC0, 0x00, 0x07, 0xFF, 0xFF, 0xC0, 0x00,
        0x07, 0xFF, 0xFF, 0xC0, 0x00, 0x07, 0xFF, 0xFF, 0xC0, 0x00, 0x07, 0xFF, 0xFF, 0x80, 0x00, 0x07,
        0xFF, 0xFF, 0x80, 0x00, 0x07, 0xFF, 0xFF, 0x80, 0x00, 0x00, 0x1F, 0xFF, 0x00, 0x00, 0x00, 0x3F,
        0xFF, 0x00, 0x00, 0x00, 0x3F, 0xFF, 0x00, 0x00, 0x00, 0x3F, 0xFF, 0x00, 0x00, 0x00, 0x3F, 0xFE,
        0x00, 0x00, 0x00, 0x7F, 0xFE, 0x00, 0x00, 0x00, 0x7F, 0xFE, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0x00,
        0x00, 0x00, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0x80, 0x00, 0x00, 0xFF, 0xFF, 0xC0, 0x00,
        0x00, 0xFF, 0xFF, 0xE0, 0x00, 0x01, 0xFF, 0xFF, 0xE0, 0x00, 0x00, 0x7F, 0xFF, 0xF0, 0x00, 0x00,
        0x0F, 0xFF, 0xF0, 0x00, 0x00, 0x07, 0xFF, 0xF8, 0x00, 0x00, 0x03, 0xFF, 0xF8, 0x00, 0x00, 0x01,
        0xFF, 0xF8, 0x00, 0x00, 0x01, 0xFF, 0xFC, 0x00, 0x00, 0x00, 0xFF, 0xFC, 0x00, 0x00, 0x00, 0xFF,
        0xFC, 0x00, 0x00, 0x00, 0xFF, 0xFC, 0x00, 0x00, 0x00, 0xFF, 0xFC, 0x00, 0x00, 0x00, 0xFF, 0xFC,
        0x00, 0x00, 0x00, 0xFF, 0xFC, 0x00, 0x00, 0x00, 0xFF, 0xFC, 0x00, 0x00, 0x00, 0xFF, 0xFC, 0x00,
        0x00, 0x01, 0xFF, 0xFC, 0x00, 0x00, 0x01, 0xFF, 0xFC, 0x00, 0x06, 0x03, 0xFF, 0xFC, 0x00, 0x07,
        0xCF, 0xFF, 0xFC, 0x00, 0x07, 0xFF, 0xFF, 0xFC, 0x00, 0x07, 0xFF, 0xFF, 0xFC, 0x00, 0x07, 0xFF,
        0xFF, 0xFC, 0x00, 0x07, 0xFF, 0xFF, 0xFC, 0x00, 0x07, 0xFF, 0xFF, 0xF8, 0x00, 0x07, 0xFF, 0xFF,
        0xF8, 0x00, 0x07, 0xFF, 0xFF, 0xF8, 0x00, 0x07, 0xFF, 0xFF, 0xF0, 0x00, 0x07, 0xFF, 0xFF, 0xF0,
        0x00, 0x07, 0xFF, 0xFF, 0xE0, 0x00, 0x07, 0xFF, 0xFF, 0xE0, 0x00, 0x07, 0xFF, 0xFF, 0xC0, 0x00,
        0x07, 0xFF, 0xFF, 0x80, 0x00, 0x07, 0xFF, 0xFF, 0x00, 0x00, 0x07, 0xFF, 0xFE, 0x00, 0x00, 0x07,
        0xFF, 0xFC, 0x00, 0x00, 0x07, 0xFF, 0xF0, 0x00, 0x00, 0x00, 0xFF, 0xC0, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"3",3*/

        "4", 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xFF, 0xFF, 0xE0, 0x00,
        0x03, 0xFF, 0xFF, 0xE0, 0x00, 0x03, 0xFF, 0xFF, 0xE0, 0x00, 0x07, 0xFF, 0xFF, 0xE0, 0x00, 0x07,
        0xFF, 0xFF, 0xE0, 0x00, 0x07, 0xFF, 0xFF, 0xE0, 0x00, 0x07, 0xFF, 0xFF, 0xE0, 0x00, 0x0F, 0xFF,
        0xFF, 0xE0, 0x00, 0x0F, 0xFF, 0xFF, 0xE0, 0x00, 0x0F, 0xFF, 0xFF, 0xE0, 0x00, 0x1F, 0xFF, 0xFF,
        0xE0, 0x00, 0x1F, 0xFF, 0xFF, 0xE0, 0x00, 0x1F, 0xFF, 0xFF, 0xE0, 0x00, 0x1F, 0xFF, 0xFF, 0xE0,
        0x00, 0x3F, 0xFF, 0xFF, 0xE0, 0x00, 0x3F, 0xFF, 0xFF, 0xE0, 0x00, 0x3F, 0xFF, 0xFF, 0xE0, 0x00,
        0x7F, 0xFB, 0xFF, 0xE0, 0x00, 0x7F, 0xFB, 0xFF, 0xE0, 0x00, 0x7F, 0xFB, 0xFF, 0xE0, 0x00, 0xFF,
        0xF3, 0xFF, 0xE0, 0x00, 0xFF, 0xF3, 0xFF, 0xE0, 0x00, 0xFF, 0xF3, 0xFF, 0xE0, 0x00, 0xFF, 0xE3,
        0xFF, 0xE0, 0x01, 0xFF, 0xE3, 0xFF, 0xE0, 0x01, 0xFF, 0xC3, 0xFF, 0xE0, 0x01, 0xFF, 0xC3, 0xFF,
        0xE0, 0x03, 0xFF, 0xC3, 0xFF, 0xE0, 0x03, 0xFF, 0x83, 0xFF, 0xE0, 0x03, 0xFF, 0x83, 0xFF, 0xE0,
        0x03, 0xFF, 0x83, 0xFF, 0xE0, 0x07, 0xFF, 0x03, 0xFF, 0xE0, 0x07, 0xFF, 0xE3, 0xFF, 0xE0, 0x07,
        0xFF, 0xE3, 0xFF, 0xE0, 0x0F, 0xFF, 0xE3, 0xFF, 0xE0, 0x0F, 0xFF, 0xE3, 0xFF, 0xE0, 0x0F, 0xFF,
        0xE3, 0xFF, 0xE0, 0x0F, 0xFF, 0xE3, 0xFF, 0xE0, 0x1F, 0xFF, 0xE3, 0xFF, 0xE0, 0x1F, 0xFF, 0xE3,
        0xFF, 0xE0, 0x1F, 0xFF, 0xE3, 0xFF, 0xE0, 0x3F, 0xFF, 0xE3, 0xFF, 0xE0, 0x3F, 0xFF, 0xE3, 0xFF,
        0xE0, 0x3F, 0xFF, 0xE3, 0xFF, 0xE0, 0x7F, 0xFF, 0xE3, 0xFF, 0xE0, 0x7F, 0xFF, 0xE3, 0xFF, 0xE0,
        0x7F, 0xFF, 0xE3, 0xFF, 0xE0, 0x00, 0x00, 0x03, 0xFF, 0xE0, 0x00, 0x00, 0x03, 0xFF, 0xE0, 0x00,
        0x00, 0x03, 0xFF, 0xE0, 0x00, 0x00, 0x03, 0xFF, 0xE0, 0x00, 0x00, 0x03, 0xFF, 0xE0, 0x00, 0x00,
        0x03, 0xFF, 0xE0, 0x00, 0x00, 0x03, 0xFF, 0xE0, 0x00, 0x00, 0x03, 0xFF, 0xE0, 0x00, 0x00, 0x03,
        0xFF, 0xE0, 0x00, 0x00, 0x03, 0xFF, 0xE0, 0x00, 0x00, 0x03, 0xFF, 0xE0, 0x00, 0x00, 0x03, 0xFF,
        0xE0, 0x00, 0x00, 0x03, 0xFF, 0xE0, 0x00, 0x00, 0x03, 0xFF, 0xE0, 0x00, 0x00, 0x03, 0xFF, 0xE0,
        0x00, 0x00, 0x03, 0xFF, 0xE0, 0x00, 0x00, 0x03, 0xFF, 0xE0, 0x00, 0x00, 0x03, 0xFF, 0xE0, 0x00,
        0x00, 0x03, 0xFF, 0xE0, 0x00, 0x00, 0x03, 0xFF, 0xE0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"4",4*/

        "5", 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xFE, 0x00, 0x00,
        0x7F, 0xFF, 0xFE, 0x00, 0x00, 0x7F, 0xFF, 0xFE, 0x00, 0x00, 0xFF, 0xFF, 0xFE, 0x00, 0x00, 0xFF,
        0xFF, 0xFE, 0x00, 0x00, 0xFF, 0xFF, 0xFE, 0x00, 0x00, 0xFF, 0xFF, 0xFE, 0x00, 0x00, 0xFF, 0xFF,
        0xFE, 0x00, 0x00, 0xFF, 0xFF, 0xFE, 0x00, 0x00, 0xFF, 0xFF, 0xFE, 0x00, 0x01, 0xFF, 0xFF, 0xFE,
        0x00, 0x01, 0xFF, 0xFF, 0xFE, 0x00, 0x01, 0xFF, 0xFF, 0xFE, 0x00, 0x01, 0xFF, 0xFF, 0xFE, 0x00,
        0x01, 0xFF, 0xFF, 0xFE, 0x00, 0x01, 0xFF, 0xFF, 0xFE, 0x00, 0x03, 0xFF, 0xFF, 0xFE, 0x00, 0x03,
        0xFF, 0xFF, 0xFE, 0x00, 0x03, 0xFF, 0xFF, 0xFE, 0x00, 0x03, 0xFF, 0xE0, 0x00, 0x00, 0x03, 0xFF,
        0xE0, 0x00, 0x00, 0x03, 0xFF, 0xE0, 0x00, 0x00, 0x03, 0xFF, 0xE0, 0x00, 0x00, 0x07, 0xFF, 0xF8,
        0x00, 0x00, 0x07, 0xFF, 0xFE, 0x00, 0x00, 0x07, 0xFF, 0xFF, 0x00, 0x00, 0x07, 0xFF, 0xFF, 0x80,
        0x00, 0x07, 0xFF, 0xFF, 0xC0, 0x00, 0x07, 0xFF, 0xFF, 0xE0, 0x00, 0x07, 0xFF, 0xFF, 0xF0, 0x00,
        0x0F, 0xFF, 0xFF, 0xF0, 0x00, 0x0F, 0xFF, 0xFF, 0xF8, 0x00, 0x0F, 0xFF, 0xFF, 0xF8, 0x00, 0x0F,
        0xFF, 0xFF, 0xFC, 0x00, 0x00, 0x0F, 0xFF, 0xFC, 0x00, 0x00, 0x03, 0xFF, 0xFC, 0x00, 0x00, 0x01,
        0xFF, 0xFE, 0x00, 0x00, 0x00, 0xFF, 0xFE, 0x00, 0x00, 0x00, 0xFF, 0xFE, 0x00, 0x00, 0x00, 0x7F,
        0xFE, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0x00, 0x00, 0x00, 0x3F, 0xFF, 0x00, 0x00, 0x00, 0x3F, 0xFF,
        0x00, 0x00, 0x00, 0x3F, 0xFF, 0x00, 0x00, 0x00, 0x3F, 0xFF, 0x00, 0x00, 0x00, 0x3F, 0xFF, 0x00,
        0x00, 0x00, 0x7F, 0xFF, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0x00, 0x0C,
        0x01, 0xFF, 0xFF, 0x00, 0x0F, 0x83, 0xFF, 0xFF, 0x00, 0x0F, 0xFF, 0xFF, 0xFE, 0x00, 0x0F, 0xFF,
        0xFF, 0xFE, 0x00, 0x0F, 0xFF, 0xFF, 0xFE, 0x00, 0x0F, 0xFF, 0xFF, 0xFE, 0x00, 0x0F, 0xFF, 0xFF,
        0xFC, 0x00, 0x0F, 0xFF, 0xFF, 0xFC, 0x00, 0x0F, 0xFF, 0xFF, 0xF8, 0x00, 0x0F, 0xFF, 0xFF, 0xF8,
        0x00, 0x0F, 0xFF, 0xFF, 0xF0, 0x00, 0x0F, 0xFF, 0xFF, 0xF0, 0x00, 0x0F, 0xFF, 0xFF, 0xE0, 0x00,
        0x0F, 0xFF, 0xFF, 0xC0, 0x00, 0x0F, 0xFF, 0xFF, 0x80, 0x00, 0x0F, 0xFF, 0xFF, 0x00, 0x00, 0x07,
        0xFF, 0xFE, 0x00, 0x00, 0x01, 0xFF, 0xF8, 0x00, 0x00, 0x00, 0x3F, 0xE0, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"5",5*/

        "6", 0x00, 0x38, 0x00, 0x00, 0x00, 0x00, 0x3E, 0x00, 0x00, 0x00, 0x00, 0x3F, 0x80, 0x00, 0x00, 0x00,
        0x7F, 0xE0, 0x00, 0x00, 0x00, 0x7F, 0xF8, 0x00, 0x00, 0x00, 0x7F, 0xFE, 0x00, 0x00, 0x00, 0x7F,
        0xFC, 0x00, 0x00, 0x00, 0xFF, 0xFC, 0x00, 0x00, 0x00, 0xFF, 0xFC, 0x00, 0x00, 0x00, 0xFF, 0xFC,
        0x00, 0x00, 0x00, 0xFF, 0xF8, 0x00, 0x00, 0x01, 0xFF, 0xF8, 0x00, 0x00, 0x01, 0xFF, 0xF8, 0x00,
        0x00, 0x01, 0xFF, 0xF8, 0x00, 0x00, 0x01, 0xFF, 0xF0, 0x00, 0x00, 0x03, 0xFF, 0xF0, 0x00, 0x00,
        0x03, 0xFF, 0xF0, 0x00, 0x00, 0x03, 0xFF, 0xF0, 0x00, 0x00, 0x03, 0xFF, 0xE0, 0x00, 0x00, 0x07,
        0xFF, 0xE2, 0x00, 0x00, 0x07, 0xFF, 0xE3, 0x00, 0x00, 0x07, 0xFF, 0xE7, 0x80, 0x00, 0x07, 0xFF,
        0xC7, 0xE0, 0x00, 0x0F, 0xFF, 0xC7, 0xF0, 0x00, 0x0F, 0xFF, 0xC7, 0xF8, 0x00, 0x0F, 0xFF, 0xCF,
        0xFC, 0x00, 0x0F, 0xFF, 0xCF, 0xFE, 0x00, 0x1F, 0xFF, 0x8F, 0xFE, 0x00, 0x1F, 0xFF, 0x9F, 0xFF,
        0x00, 0x1F, 0xFF, 0x9F, 0xFF, 0x00, 0x1F, 0xFF, 0x9F, 0xFF, 0x80, 0x3F, 0xFF, 0x1F, 0xFF, 0x80,
        0x3F, 0xFF, 0x3F, 0xFF, 0xC0, 0x3F, 0xFF, 0x3F, 0xFF, 0xC0, 0x3F, 0xFF, 0x3F, 0xFF, 0xC0, 0x7F,
        0xFE, 0x7F, 0xFF, 0xE0, 0x7F, 0xFE, 0x7F, 0xFF, 0xE0, 0x7F, 0xFE, 0x7F, 0xFF, 0xE0, 0x7F, 0xFE,
        0x9F, 0xFF, 0xE0, 0x7F, 0xFC, 0x0F, 0xFF, 0xE0, 0xFF, 0xFC, 0x07, 0xFF, 0xE0, 0xFF, 0xFC, 0x03,
        0xFF, 0xF0, 0xFF, 0xFC, 0x03, 0xFF, 0xF0, 0xFF, 0xF8, 0x03, 0xFF, 0xF0, 0xFF, 0xF8, 0x03, 0xFF,
        0xF0, 0xFF, 0xF8, 0x03, 0xFF, 0xF0, 0xFF, 0xF8, 0x03, 0xFF, 0xF0, 0xFF, 0xFC, 0x03, 0xFF, 0xF0,
        0xFF, 0xFC, 0x03, 0xFF, 0xE0, 0xFF, 0xFC, 0x07, 0xFF, 0xE0, 0x7F, 0xFE, 0x0F, 0xFF, 0xE0, 0x7F,
        0xFF, 0x9F, 0xFF, 0xE0, 0x7F, 0xFF, 0xFF, 0xFF, 0xE0, 0x7F, 0xFF, 0xFF, 0xFF, 0xE0, 0x7F, 0xFF,
        0xFF, 0xFF, 0xC0, 0x3F, 0xFF, 0xFF, 0xFF, 0xC0, 0x3F, 0xFF, 0xFF, 0xFF, 0xC0, 0x1F, 0xFF, 0xFF,
        0xFF, 0x80, 0x1F, 0xFF, 0xFF, 0xFF, 0x80, 0x1F, 0xFF, 0xFF, 0xFF, 0x00, 0x0F, 0xFF, 0xFF, 0xFF,
        0x00, 0x07, 0xFF, 0xFF, 0xFE, 0x00, 0x07, 0xFF, 0xFF, 0xFE, 0x00, 0x03, 0xFF, 0xFF, 0xFC, 0x00,
        0x01, 0xFF, 0xFF, 0xF8, 0x00, 0x00, 0xFF, 0xFF, 0xF0, 0x00, 0x00, 0x7F, 0xFF, 0xE0, 0x00, 0x00,
        0x3F, 0xFF, 0xC0, 0x00, 0x00, 0x0F, 0xFF, 0x00, 0x00, 0x00, 0x03, 0xFC, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"6",6*/

        "7", 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xFF, 0xFF, 0xFF, 0x80, 0x3F,
        0xFF, 0xFF, 0xFF, 0x80, 0x3F, 0xFF, 0xFF, 0xFF, 0x80, 0x3F, 0xFF, 0xFF, 0xFF, 0x80, 0x3F, 0xFF,
        0xFF, 0xFF, 0x80, 0x3F, 0xFF, 0xFF, 0xFF, 0x00, 0x3F, 0xFF, 0xFF, 0xFF, 0x00, 0x3F, 0xFF, 0xFF,
        0xFF, 0x00, 0x3F, 0xFF, 0xFF, 0xFF, 0x00, 0x3F, 0xFF, 0xFF, 0xFF, 0x00, 0x3F, 0xFF, 0xFF, 0xFE,
        0x00, 0x3F, 0xFF, 0xFF, 0xFE, 0x00, 0x3F, 0xFF, 0xFF, 0xFE, 0x00, 0x3F, 0xFF, 0xFF, 0xFE, 0x00,
        0x3F, 0xFF, 0xFF, 0xFE, 0x00, 0x3F, 0xFF, 0xFF, 0xFC, 0x00, 0x3F, 0xFF, 0xFF, 0xFC, 0x00, 0x3F,
        0xFF, 0xFF, 0xFC, 0x00, 0x3F, 0xFF, 0xFF, 0xFC, 0x00, 0x00, 0x00, 0xFF, 0xFC, 0x00, 0x00, 0x00,
        0xFF, 0xF8, 0x00, 0x00, 0x00, 0xFF, 0xF8, 0x00, 0x00, 0x00, 0xFF, 0xF8, 0x00, 0x00, 0x01, 0xFF,
        0xF8, 0x00, 0x00, 0x01, 0xFF, 0xF8, 0x00, 0x00, 0x01, 0xFF, 0xF0, 0x00, 0x00, 0x01, 0xFF, 0xF0,
        0x00, 0x00, 0x01, 0xFF, 0xF0, 0x00, 0x00, 0x03, 0xFF, 0xF0, 0x00, 0x00, 0x03, 0xFF, 0xF0, 0x00,
        0x00, 0x03, 0xFF, 0xE0, 0x00, 0x00, 0x03, 0xFF, 0xE0, 0x00, 0x00, 0x03, 0xFF, 0xE0, 0x00, 0x00,
        0x07, 0xFF, 0xE0, 0x00, 0x00, 0x07, 0xFF, 0xE0, 0x00, 0x00, 0x07, 0xFF, 0xC0, 0x00, 0x00, 0x07,
        0xFF, 0xC0, 0x00, 0x00, 0x0F, 0xFF, 0xC0, 0x00, 0x00, 0x0F, 0xFF, 0xC0, 0x00, 0x00, 0x0F, 0xFF,
        0xC0, 0x00, 0x00, 0x0F, 0xFF, 0x80, 0x00, 0x00, 0x0F, 0xFF, 0x80, 0x00, 0x00, 0x1F, 0xFF, 0x80,
        0x00, 0x00, 0x1F, 0xFF, 0x80, 0x00, 0x00, 0x1F, 0xFF, 0x80, 0x00, 0x00, 0x1F, 0xFF, 0x00, 0x00,
        0x00, 0x3F, 0xFF, 0x00, 0x00, 0x00, 0x3F, 0xFF, 0x00, 0x00, 0x00, 0x3F, 0xFF, 0x00, 0x00, 0x00,
        0x3F, 0xFF, 0x00, 0x00, 0x00, 0x3F, 0xFE, 0x00, 0x00, 0x00, 0x7F, 0xFE, 0x00, 0x00, 0x00, 0x7F,
        0xFE, 0x00, 0x00, 0x00, 0x7F, 0xFE, 0x00, 0x00, 0x00, 0x7F, 0xFE, 0x00, 0x00, 0x00, 0xFF, 0xFC,
        0x00, 0x00, 0x00, 0xFF, 0xFC, 0x00, 0x00, 0x00, 0xFF, 0xFC, 0x00, 0x00, 0x00, 0xFF, 0xFC, 0x00,
        0x00, 0x00, 0xFF, 0xFC, 0x00, 0x00, 0x01, 0xFF, 0xF8, 0x00, 0x00, 0x01, 0xFF, 0xF8, 0x00, 0x00,
        0x01, 0xFF, 0xF8, 0x00, 0x00, 0x01, 0xFF, 0xF8, 0x00, 0x00, 0x03, 0xFF, 0xF8, 0x00, 0x00, 0x03,
        0xFF, 0xF0, 0x00, 0x00, 0x03, 0xFF, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"7",7*/

        "8", 0x00, 0x03, 0xF8, 0x00, 0x00, 0x00, 0x0F, 0xFF, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0x80, 0x00, 0x00,
        0x3F, 0xFF, 0xC0, 0x00, 0x00, 0x7F, 0xFF, 0xE0, 0x00, 0x00, 0xFF, 0xFF, 0xF0, 0x00, 0x01, 0xFF,
        0xFF, 0xF8, 0x00, 0x01, 0xFF, 0xFF, 0xF8, 0x00, 0x03, 0xFF, 0xFF, 0xFC, 0x00, 0x03, 0xFF, 0xFF,
        0xFC, 0x00, 0x07, 0xFF, 0xFF, 0xFC, 0x00, 0x07, 0xFF, 0xFF, 0xFE, 0x00, 0x07, 0xFF, 0xFF, 0xFE,
        0x00, 0x0F, 0xFF, 0xFF, 0xFE, 0x00, 0x0F, 0xFF, 0xBF, 0xFF, 0x00, 0x0F, 0xFE, 0x0F, 0xFF, 0x00,
        0x0F, 0xFE, 0x07, 0xFF, 0x00, 0x0F, 0xFC, 0x07, 0xFF, 0x00, 0x0F, 0xFC, 0x03, 0xFF, 0x00, 0x0F,
        0xFC, 0x03, 0xFF, 0x00, 0x0F, 0xFC, 0x03, 0xFF, 0x00, 0x0F, 0xFC, 0x03, 0xFF, 0x00, 0x0F, 0xFC,
        0x07, 0xFF, 0x00, 0x0F, 0xFE, 0x07, 0xFF, 0x00, 0x0F, 0xFE, 0x0F, 0xFE, 0x00, 0x07, 0xFF, 0x9F,
        0xFE, 0x00, 0x07, 0xFF, 0xFF, 0xFE, 0x00, 0x07, 0xFF, 0xFF, 0xFE, 0x00, 0x03, 0xFF, 0xFF, 0xFC,
        0x00, 0x03, 0xFF, 0xFF, 0xFC, 0x00, 0x03, 0xFF, 0xFF, 0xF8, 0x00, 0x03, 0xFF, 0xFF, 0xFC, 0x00,
        0x07, 0xFF, 0xFF, 0xFE, 0x00, 0x0F, 0xFF, 0xFF, 0xFE, 0x00, 0x0F, 0xFF, 0xFF, 0xFF, 0x00, 0x1F,
        0xFF, 0xFF, 0xFF, 0x00, 0x1F, 0xFF, 0xFF, 0xFF, 0x80, 0x1F, 0xFF, 0xFF, 0xFF, 0x80, 0x3F, 0xFF,
        0xFF, 0xFF, 0x80, 0x3F, 0xFF, 0x9F, 0xFF, 0xC0, 0x3F, 0xFE, 0x07, 0xFF, 0xC0, 0x3F, 0xFC, 0x03,
        0xFF, 0xC0, 0x3F, 0xF8, 0x03, 0xFF, 0xC0, 0x7F, 0xF8, 0x01, 0xFF, 0xC0, 0x7F, 0xF0, 0x01, 0xFF,
        0xC0, 0x7F, 0xF0, 0x01, 0xFF, 0xC0, 0x7F, 0xF0, 0x00, 0xFF, 0xC0, 0x7F, 0xF0, 0x00, 0xFF, 0xC0,
        0x7F, 0xF0, 0x00, 0xFF, 0xC0, 0x7F, 0xF0, 0x01, 0xFF, 0xC0, 0x7F, 0xF0, 0x01, 0xFF, 0xC0, 0x3F,
        0xF8, 0x01, 0xFF, 0xC0, 0x3F, 0xF8, 0x03, 0xFF, 0xC0, 0x3F, 0xFC, 0x03, 0xFF, 0xC0, 0x3F, 0xFE,
        0x07, 0xFF, 0xC0, 0x3F, 0xFF, 0x1F, 0xFF, 0x80, 0x1F, 0xFF, 0xFF, 0xFF, 0x80, 0x1F, 0xFF, 0xFF,
        0xFF, 0x80, 0x1F, 0xFF, 0xFF, 0xFF, 0x00, 0x0F, 0xFF, 0xFF, 0xFF, 0x00, 0x0F, 0xFF, 0xFF, 0xFF,
        0x00, 0x07, 0xFF, 0xFF, 0xFE, 0x00, 0x07, 0xFF, 0xFF, 0xFC, 0x00, 0x03, 0xFF, 0xFF, 0xFC, 0x00,
        0x01, 0xFF, 0xFF, 0xF8, 0x00, 0x00, 0xFF, 0xFF, 0xF0, 0x00, 0x00, 0x7F, 0xFF, 0xE0, 0x00, 0x00,
        0x3F, 0xFF, 0xC0, 0x00, 0x00, 0x1F, 0xFF, 0x00, 0x00, 0x00, 0x03, 0xFC, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"8",8*/

        "9", 0x00, 0x03, 0xF8, 0x00, 0x00, 0x00, 0x0F, 0xFF, 0x00, 0x00, 0x00, 0x3F, 0xFF, 0xC0, 0x00, 0x00,
        0x7F, 0xFF, 0xE0, 0x00, 0x00, 0xFF, 0xFF, 0xF0, 0x00, 0x01, 0xFF, 0xFF, 0xF8, 0x00, 0x03, 0xFF,
        0xFF, 0xFC, 0x00, 0x07, 0xFF, 0xFF, 0xFC, 0x00, 0x07, 0xFF, 0xFF, 0xFE, 0x00, 0x0F, 0xFF, 0xFF,
        0xFF, 0x00, 0x1F, 0xFF, 0xFF, 0xFF, 0x00, 0x1F, 0xFF, 0xFF, 0xFF, 0x80, 0x1F, 0xFF, 0xFF, 0xFF,
        0x80, 0x3F, 0xFF, 0xFF, 0xFF, 0xC0, 0x3F, 0xFF, 0xFF, 0xFF, 0xC0, 0x7F, 0xFF, 0xFF, 0xFF, 0xC0,
        0x7F, 0xFF, 0xFF, 0xFF, 0xE0, 0x7F, 0xFF, 0xFF, 0xFF, 0xE0, 0x7F, 0xFF, 0xFF, 0xFF, 0xE0, 0x7F,
        0xFF, 0x0F, 0xFF, 0xE0, 0xFF, 0xFE, 0x07, 0xFF, 0xE0, 0xFF, 0xFC, 0x03, 0xFF, 0xE0, 0xFF, 0xFC,
        0x03, 0xFF, 0xF0, 0xFF, 0xFC, 0x01, 0xFF, 0xF0, 0xFF, 0xF8, 0x01, 0xFF, 0xF0, 0xFF, 0xF8, 0x01,
        0xFF, 0xF0, 0xFF, 0xFC, 0x03, 0xFF, 0xF0, 0xFF, 0xFC, 0x03, 0xFF, 0xF0, 0xFF, 0xFC, 0x03, 0xFF,
        0xE0, 0xFF, 0xFC, 0x03, 0xFF, 0xE0, 0xFF, 0xFE, 0x03, 0xFF, 0xE0, 0xFF, 0xFF, 0x87, 0xFF, 0xE0,
        0x7F, 0xFF, 0xE7, 0xFF, 0xE0, 0x7F, 0xFF, 0xE7, 0xFF, 0xE0, 0x7F, 0xFF, 0xC7, 0xFF, 0xC0, 0x7F,
        0xFF, 0xCF, 0xFF, 0xC0, 0x3F, 0xFF, 0xCF, 0xFF, 0xC0, 0x3F, 0xFF, 0xCF, 0xFF, 0xC0, 0x3F, 0xFF,
        0x8F, 0xFF, 0x80, 0x1F, 0xFF, 0x9F, 0xFF, 0x80, 0x1F, 0xFF, 0x9F, 0xFF, 0x80, 0x0F, 0xFF, 0x1F,
        0xFF, 0x80, 0x0F, 0xFF, 0x1F, 0xFF, 0x80, 0x07, 0xFF, 0x3F, 0xFF, 0x00, 0x03, 0xFF, 0x3F, 0xFF,
        0x00, 0x01, 0xFE, 0x3F, 0xFF, 0x00, 0x00, 0xFE, 0x3F, 0xFE, 0x00, 0x00, 0x7E, 0x7F, 0xFE, 0x00,
        0x00, 0x3C, 0x7F, 0xFE, 0x00, 0x00, 0x1C, 0x7F, 0xFE, 0x00, 0x00, 0x04, 0x7F, 0xFC, 0x00, 0x00,
        0x00, 0xFF, 0xFC, 0x00, 0x00, 0x00, 0xFF, 0xFC, 0x00, 0x00, 0x00, 0xFF, 0xFC, 0x00, 0x00, 0x00,
        0xFF, 0xF8, 0x00, 0x00, 0x01, 0xFF, 0xF8, 0x00, 0x00, 0x01, 0xFF, 0xF8, 0x00, 0x00, 0x01, 0xFF,
        0xF8, 0x00, 0x00, 0x01, 0xFF, 0xF0, 0x00, 0x00, 0x03, 0xFF, 0xF0, 0x00, 0x00, 0x03, 0xFF, 0xF0,
        0x00, 0x00, 0x03, 0xFF, 0xF0, 0x00, 0x00, 0x03, 0xFF, 0xE0, 0x00, 0x00, 0x07, 0xFF, 0xE0, 0x00,
        0x00, 0x07, 0xFF, 0xE0, 0x00, 0x00, 0x01, 0xFF, 0xE0, 0x00, 0x00, 0x00, 0x7F, 0xC0, 0x00, 0x00,
        0x00, 0x1F, 0xC0, 0x00, 0x00, 0x00, 0x07, 0xC0, 0x00, 0x00, 0x00, 0x01, 0xC0, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"9",9*/

};

struct FONT72_CHINESE const Font72_Chinese[] =
    {

};

UI28_MATRIX_XX_QUAN_LIST(72)
