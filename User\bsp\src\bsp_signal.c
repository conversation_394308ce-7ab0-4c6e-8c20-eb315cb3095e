#include "bsp.h"
volatile signal_t t_signal[SIGNAL_ITEM_NUM] = {0}; /* 信号结构体 */
#if (SIGNAL_IO_DIRECT_DRIVE_SWITCH == 0)
static const uint8_t X_open_level[SIGNAL_ITEM_NUM] =/* 开路时的电平 */
    {
#define SIGNAL_DEBUG_X(name, gpio_, pin_, open, rise_d, fall_d) open,
        SIGNAL_ITEM_LIST
#undef SIGNAL_DEBUG_X
};
#if SIGNAL_DELAY_FILTERING_SWITCH
static const uint16_t X_rise_delay[SIGNAL_ITEM_NUM] = /* 上升沿延时 */
{
    #define SIGNAL_DEBUG_X(name, gpio_, pin_, open, rise_d, fall_d) rise_d,
            SIGNAL_ITEM_LIST
    #undef SIGNAL_DEBUG_X
    };
static const uint16_t X_fall_delay[SIGNAL_ITEM_NUM] = /* 下降沿延时 */
{
    #define SIGNAL_DEBUG_X(name, gpio_, pin_, open, rise_d, fall_d) fall_d,
            SIGNAL_ITEM_LIST
    #undef SIGNAL_DEBUG_X
    };
#endif
#else
/* 依次定义GPIO */
typedef struct
{
    GPIO_TypeDef *gpio;
    uint16_t pin;
    uint8_t open_level; /* 开路时的电平 */
#if SIGNAL_DELAY_FILTERING_SWITCH
    uint16_t rise_delay; /* 上升沿延时 */
    uint16_t fall_delay; /* 下降沿延时 */
#endif
} X_GPIO_T;
#if SIGNAL_DELAY_FILTERING_SWITCH
/* GPIO和PIN定义 */
static const X_GPIO_T s_gpio_list[SIGNAL_ITEM_NUM] =
    {
#define SIGNAL_DEBUG_X(name, gpio_, pin_, open, rise_d, fall_d) {gpio_, pin_, open, rise_d, fall_d},
        SIGNAL_ITEM_LIST
#undef SIGNAL_DEBUG_X
};
#else
/* GPIO和PIN定义 */
static const X_GPIO_T s_gpio_list[SIGNAL_ITEM_NUM] =
    {
#define SIGNAL_DEBUG_X(name, gpio_, pin_, open, rise_d, fall_d) {gpio_, pin_, open},
        SIGNAL_ITEM_LIST
#undef SIGNAL_DEBUG_X
};
#endif

/*
*********************************************************************************************************
*	函 数 名: bsp_InitSignalHard
*	形    参: 无
*	返 回 值: 无
*	功能说明: 配置信号对应的GPIO
*********************************************************************************************************
*/
void bsp_InitSignalHard(void)
{
    GPIO_InitTypeDef gpio_init;
    uint8_t i;

    /* 第1步：打开GPIO时钟 */
#define SIGNAL_DEBUG_X(name, gpio_, pin_, open, rise_d, fall_d) RCC_APB2PeriphClockCmd(RCC_APB2Periph_##gpio_, ENABLE);
    SIGNAL_ITEM_LIST
#undef SIGNAL_DEBUG_X

    /* 第2步：配置所有的按键GPIO为浮动输入模式(实际上CPU复位后就是输入状态) */
    gpio_init.GPIO_Mode = GPIO_Mode_IPD;     /* 设置上拉输入 */
    gpio_init.GPIO_Speed = GPIO_Speed_50MHz; /* GPIO速度等级 */

    for (i = 0; i < SIGNAL_ITEM_NUM; i++)
    {
        gpio_init.GPIO_Pin = s_gpio_list[i].pin;
        GPIO_Init(s_gpio_list[i].gpio, &gpio_init);
    }
}
#endif
/*
*********************************************************************************************************
*	函 数 名: IsSignalDownFunc
*	形    参: _id：信号ID
*	返 回 值: SIGNAL_OPEN：开路 SIGNAL_CLOSE：闭合
*	功能说明: 判断信号状态，非电平
*********************************************************************************************************
*/
uint8_t IsSignalDownFunc(uint8_t _id)
{
    uint8_t level;

#if (SIGNAL_IO_DIRECT_DRIVE_SWITCH == 0)
    if (IS_SIGNAL_DOWN_FUNC(_id))
#else
    if ((s_gpio_list[_id].gpio->IDR & s_gpio_list[_id].pin) != 0)
#endif
    {
        level = 1;
    }
    else
    {
        level = 0;
    }

#if (SIGNAL_IO_DIRECT_DRIVE_SWITCH == 0)
    if (level != X_open_level[_id])
#else
    if (level != t_signal[_id].open_level)
#endif
    {
        return SIGNAL_CLOSE;
    }
    else
    {
        return SIGNAL_OPEN;
    }
}

/*
*********************************************************************************************************
*	函 数 名: bsp_InitSignalVar
*	形    参: 无
*	返 回 值: 无
*	功能说明: 初始化信号变量
*********************************************************************************************************
*/
void bsp_InitSignalVar(void)
{
    uint8_t i;
#if (SIGNAL_IO_DIRECT_DRIVE_SWITCH == 0)
for (i = 0; i < SIGNAL_ITEM_NUM; i++)
{
    t_signal[i].open_level = X_open_level[i]; /* 开路时的电平 */
#if SIGNAL_DELAY_FILTERING_SWITCH
    t_signal[i].rise_delay = X_rise_delay[i]; /* 上升沿延时 */
    t_signal[i].fall_delay = X_fall_delay[i]; /* 下降沿延时 */
    t_signal[i].Count = X_fall_delay[i];         /* 延时计数器 */
#endif
    t_signal[i].sig = IsSignalDownFunc(i); /*  当前信号状态，非电平 */
#if SIGNAL_CUSTOM_FIFO_SWITCH
    t_signal[i].fifo_rise_task = 0; /* FIFO上升沿任务 */
    t_signal[i].fifo_rise_task = 0; /* FIFO上升沿任务 */
#endif
}
#else
    /* 给每个信号结构体成员变量赋一组缺省值 */
    for (i = 0; i < SIGNAL_ITEM_NUM; i++)
    {
        t_signal[i].open_level = s_gpio_list[i].open_level; /* 开路时的电平 */
#if SIGNAL_DELAY_FILTERING_SWITCH
        t_signal[i].rise_delay = s_gpio_list[i].rise_delay; /* 上升沿延时 */
        t_signal[i].fall_delay = s_gpio_list[i].fall_delay; /* 下降沿延时 */
        t_signal[i].Count = t_signal[i].fall_delay;         /* 延时计数器 */
#endif
        t_signal[i].sig = IsSignalDownFunc(i); /*  当前信号状态，非电平 */
#if SIGNAL_CUSTOM_FIFO_SWITCH
        t_signal[i].fifo_rise_task = 0; /* FIFO上升沿任务 */
        t_signal[i].fifo_rise_task = 0; /* FIFO上升沿任务 */
#endif
    }
#endif
}

#if SIGNAL_DELAY_FILTERING_SWITCH
/*
*********************************************************************************************************
*	函 数 名: bsp_SetSignalParam
*	形    参: _ID : 信号ID，从0开始
*	形    参: open_level : 开路时的电平
*	形    参: rise_delay : 上升沿延时
*	形    参: fall_delay : 下降沿延时
*	返 回 值: 无
*	功能说明: 设置信号参数
*********************************************************************************************************
*/
void bsp_SetSignalParam(uint8_t _ID, uint8_t open_level, uint8_t rise_delay, uint8_t fall_delay)
{
    t_signal[_ID].open_level = open_level; /* 开路时的电平 */
    t_signal[_ID].rise_delay = rise_delay; /* 上升沿延时 */
    t_signal[_ID].fall_delay = fall_delay; /* 下降沿延时 */
    t_signal[_ID].Count = fall_delay;      /* 延时计数器 */
}
#else
/*
*********************************************************************************************************
*	函 数 名: bsp_SetSignalParam
*	形    参: _ID : 信号ID，从0开始
*	形    参: open_level : 开路时的电平
*	返 回 值: 无
*	功能说明: 设置信号参数
*********************************************************************************************************
*/
void bsp_SetSignalParam(uint8_t _ID, uint8_t open_level)
{
    t_signal[_ID].open_level = open_level; /* 开路时的电平 */
}
#endif
#if SIGNAL_CUSTOM_FIFO_SWITCH
/*
*********************************************************************************************************
*	函 数 名: bsp_SetSignalfifotask
*	形    参: _ID : 信号ID，从0开始
*	形    参: fifo_rise_task : FIFO上升沿任务
*	形    参: fifo_fall_task : FIFO下降沿任务
*	返 回 值: 无
*	功能说明: 设置信号fifo任务
*********************************************************************************************************
*/
void bsp_SetSignalfifotask(uint8_t _ID, uint8_t fifo_rise_task, uint8_t fifo_fall_task)
{
    t_signal[_ID].fifo_rise_task = fifo_rise_task; /* FIFO上升沿任务 */
    t_signal[_ID].fifo_fall_task = fifo_fall_task; /* FIFO下降沿任务 */
}
#endif

/*
*********************************************************************************************************
*	函 数 名: bsp_DetectSignal
*	形    参: IO的id， 从0开始编码
*	返 回 值: 无
*	功能说明: 检测一个信号。非阻塞状态，必须被周期性的调用。
*********************************************************************************************************
*/
void bsp_DetectSignal(uint8_t i)
{
    volatile signal_t *pBtn;
    pBtn = &t_signal[i];                    /* 读取相应信号的结构体地址，程序里面每个信号都有自己的结构体 */
    if (IsSignalDownFunc(i) != SIGNAL_OPEN) /* 如果对应信号闭合 */
    {
#if SIGNAL_DELAY_FILTERING_SWITCH
        if (pBtn->Count < pBtn->fall_delay) /* 如果滤波器计数器小于设定的滤波时间 */
        {
            pBtn->Count = pBtn->fall_delay; /* 将滤波器计数器设置等于设定的滤波时间 */
        }
        else if (pBtn->Count < (pBtn->fall_delay + pBtn->rise_delay)) /* 如果滤波器计数器小于设定的总滤波时间 */
        {
            pBtn->Count++; /* 滤波器计数器+1 */
        }
        else /* 如果波器计数器大于总设定的滤波时间，滤波完成确认闭合 */
        {
#endif
            if (pBtn->sig != SIGNAL_CLOSE) /* 如果对应信号原来是开路 */
            {
                pBtn->sig = SIGNAL_CLOSE; /* 将对应的信号设定为闭合 */
#if SIGNAL_FIXED_FIFO_SWITCH
#if KEY_SWITCH
#if SEQUENTIAL_KEY_SWITCH
#if MULTIPLE_KEY_STROKES
                bsp_Putfifo((uint8_t)(2 * i + 4 * KEY_ITEM_NUM + KEY_SEQ_ITEM_NUM + KEY_MUL_ITEM_NUM + 1)); /* 固定信号任务放入FIFO */
#else
                bsp_Putfifo((uint8_t)(2 * i + 4 * KEY_ITEM_NUM + KEY_SEQ_ITEM_NUM + 1)); /* 固定信号任务放入FIFO */
#endif
#elif MULTIPLE_KEY_STROKES
                bsp_Putfifo((uint8_t)(2 * i + 4 * KEY_ITEM_NUM + KEY_MUL_ITEM_NUM + 1)); /* 键值多击按键放入按键FIFO */
#else
                bsp_Putfifo((uint8_t)(2 * i + 4 * KEY_ITEM_NUM + 1)); /* 键值多击按键放入按键FIFO */
#endif
#else
                bsp_Putfifo((uint8_t)(2 * i + 1)); /* 键值多击按键放入按键FIFO */
#endif
#endif

#if SIGNAL_CUSTOM_FIFO_SWITCH
                bsp_Putfifo(pBtn->fifo_rise_task); /* FIFO上升沿任务放入按键FIFO */
#endif
            }
#if SIGNAL_DELAY_FILTERING_SWITCH
        }
#endif
    }
    else /* 如果对应信号开路 */
    {
#if SIGNAL_DELAY_FILTERING_SWITCH
        if (pBtn->Count > pBtn->fall_delay) /* 如果滤波器计数器大于设定的滤波时间 */
        {
            pBtn->Count = pBtn->fall_delay; /* 将滤波器计数器设置等于设定的滤波时间 */
        }
        else if (pBtn->Count != 0) /* 如果滤波器计数器不等于0 */
        {
            pBtn->Count--; /* 将滤波器计数器-1 */
        }
        else /* 如果波器计数器等于0，滤波完成确认开路 */
        {
#endif
            if (pBtn->sig != SIGNAL_OPEN) /* 如果对应信号原来是闭合 */
            {
                pBtn->sig = SIGNAL_OPEN; /* 将对应的信号设定为开路 */
#if SIGNAL_FIXED_FIFO_SWITCH
#if KEY_SWITCH
#if SEQUENTIAL_KEY_SWITCH
#if MULTIPLE_KEY_STROKES
                bsp_Putfifo((uint8_t)(2 * i + 4 * KEY_ITEM_NUM + KEY_SEQ_ITEM_NUM + KEY_MUL_ITEM_NUM + 2)); /* 固定信号任务放入FIFO */
#else
                bsp_Putfifo((uint8_t)(2 * i + 4 * KEY_ITEM_NUM + KEY_SEQ_ITEM_NUM + 2)); /* 固定信号任务放入FIFO */
#endif
#elif MULTIPLE_KEY_STROKES
                bsp_Putfifo((uint8_t)(2 * i + 4 * KEY_ITEM_NUM + KEY_MUL_ITEM_NUM + 2)); /* 键值多击按键放入按键FIFO */
#else
                bsp_Putfifo((uint8_t)(2 * i + 4 * KEY_ITEM_NUM + 2)); /* 键值多击按键放入按键FIFO */
#endif
#else
                bsp_Putfifo((uint8_t)(2 * i + 2)); /* 键值多击按键放入按键FIFO */
#endif
#endif

#if SIGNAL_CUSTOM_FIFO_SWITCH
                bsp_Putfifo(pBtn->fifo_fall_task); /* FIFO下降沿任务放入按键FIFO */
#endif
            }
#if SIGNAL_DELAY_FILTERING_SWITCH
        }
#endif
    }
}

/*
*********************************************************************************************************
*	函 数 名: bsp_InitSignal
*	形    参: 无
*	返 回 值: 无
*	功能说明: 初始化信号
*********************************************************************************************************
*/
void bsp_InitSignal(void)
{
#if (SIGNAL_IO_DIRECT_DRIVE_SWITCH == 1)
    bsp_InitSignalHard(); /* 配置信号对应的GPIO */
#endif
    bsp_InitSignalVar(); /* 初始化信号变量 */
}

/*
*********************************************************************************************************
*	函 数 名: bsp_SignalScan10ms
*	形    参: 无
*	返 回 值: 无
*	功能说明: 扫描所有信号。非阻塞，被10ms周期性的调用
*********************************************************************************************************
*/
void bsp_SignalScan10ms(void)
{
    uint8_t i;
    for (i = 0; i < SIGNAL_ITEM_NUM; i++)
    {
        bsp_DetectSignal(i);
    }
}
