#ifndef _UI28_ARC_
#define _UI28_ARC_
#include "UI28.h"
#if (UI28_ARC_ENABLED == 1)
#define UI28_ARC_LOADING_SET_UP 0 /* 圆弧加载动画参数是否可以设置 */
#if (UI28_ARC_LOADING_SET_UP == 0)
#define UI28_ARC_LOADING_ITEM_LIST \
    UI28_ARC_LOADING_DEBUG_X(95 /* 左上X坐标 */, 20 /*左上Y坐标*/, 70 /* R角表地址(外) */, 55 /* R角表地址(内) */, DGREY /* 背景颜色 */, GREY /* 圆弧颜色 */)
#endif

extern volatile unsigned char ui28_arc_loading_display; /* 圆弧加载动画显示标志位 */
/* 绘制圆弧 */
/* usX:外圆左上角坐标X*/
/* usY:外圆左上角坐标Y*/
/* ucR_outside:R角表地址(外)*/
/* ucR_within:R角表地址(内)*/
/* start_degree:起始角度（0-359）*/
/* end_degree:终止角度（0-359）*/
/* start_shield_degree:起始屏蔽角度（0-359）*/
/* end_shield_degree:终止屏蔽角度（0-359）*/
/* usColor_0:背景颜色*/
/* usColor_1:圆弧颜色*/
void UI28_draw_arc(unsigned short usX, unsigned short usY, unsigned char *ucR_outside, unsigned char *ucR_within, unsigned short start_degree, unsigned short end_degree, unsigned short start_shield_degree, unsigned short end_shield_degree, unsigned short usColor_0, unsigned short usColor_1);
/* 圆弧加载动画 */
void UI28_arc_loading(void);
/* 圆弧加载动画显示开关（关，实际只是不刷新了） */
void UI28_display_onoff_arc_loading(unsigned char _onoff_);
#if (UI28_ARC_LOADING_SET_UP == 1)
/* 设置圆弧加载动画 */
void UI28_set_up_arc_loading(unsigned short usX, unsigned short usY, unsigned char *ucR_outside, unsigned char *ucR_within, unsigned short usColor_0, unsigned short usColor_1);
#endif
#endif
#endif
