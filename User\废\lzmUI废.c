#include "lzmUI.h"
/* 字高 */
const unsigned char lzm_matrix_heigh_list[LZM_MATRIX_ITEM_NUM] =
    {
#define LZM_MATRIX_DEBUG_X(heigh) heigh,
        LZM_MATRIX_ITEM_LIST
#undef LZM_MATRIX_DEBUG_X
};
/* ASCII个数 */
const unsigned int lzm_matrix_ascii_quan_list[LZM_MATRIX_ITEM_NUM] =
    {
#define LZM_MATRIX_DEBUG_X(heigh) (sizeof(Font##heigh##_Ascii) / sizeof(struct FONT##heigh##_ASCII)),
        LZM_MATRIX_ITEM_LIST
#undef LZM_MATRIX_DEBUG_X
};
/* CHINESE个数 */
const unsigned int lzm_matrix_chinese_quan_list[LZM_MATRIX_ITEM_NUM] =
    {
#define LZM_MATRIX_DEBUG_X(heigh) (sizeof(Font##heigh##_Chinese) / sizeof(struct FONT##heigh##_CHINESE)),
        LZM_MATRIX_ITEM_LIST
#undef LZM_MATRIX_DEBUG_X
};

#define LZM_UI_H 240                       // 屏幕高度
#define LZM_UI_W 320                       // 屏幕宽度
#define LZM_UI_W_Byte_count (LZM_UI_W / 8) // 屏幕宽度字节数

/* 二维像素页缓存 */
volatile unsigned char PixelPageCache2D[LZM_UI_H][LZM_UI_W_Byte_count];

/**
 * @brief  对二维像素页缓存-开窗
 * @param  usX ：在二维像素页缓存的起点X坐标
 * @param  usY ：在二维像素页缓存的起点Y坐标
 * @param  usWidth ：窗口的宽度
 * @param  usHeight ：窗口的高度
 * @param  usBit ：0或1
 * @retval 无
 */
void PixelPageCache2D_OpenWin(unsigned short usX, unsigned short usY, unsigned short usWidth, unsigned short usHeight, unsigned char usBit)
{
    unsigned short y, byte;
    unsigned short start_byte, end_byte;
    unsigned char start_bit, end_bit;

    // 计算当前行涉及的字节范围
    start_byte = usX >> 3;
    start_bit = usX & 7;
    byte = usX + usWidth - 1;
    end_byte = byte >> 3;
    end_bit = byte & 7;
    if (usBit)
    {
        // 逐行处理窗口区域
        for (y = usY; y < usY + usHeight; y++)
        {
            // 遍历当前行的每个字节
            for (byte = start_byte; byte <= end_byte; byte++)
            {
                if (start_byte == end_byte)
                {
                    // 情况1：窗口在同一个字节内
                    PixelPageCache2D[y][byte] |= ((0xFF >> start_bit) & (0xFF << (7 - end_bit)));
                }
                else if (byte == start_byte)
                {
                    // 情况2：处理起始字节的部分位
                    PixelPageCache2D[y][byte] |= (0xFF >> start_bit);
                }
                else if (byte == end_byte)
                {
                    // 情况3：处理结束字节的部分位
                    PixelPageCache2D[y][byte] |= (0xFF << (7 - end_bit));
                }
                else
                {
                    // 情况4：中间完整字节，直接清零
                    PixelPageCache2D[y][byte] = 0xFF;
                }
            }
        }
    }
    else
    {
        // 逐行处理窗口区域
        for (y = usY; y < usY + usHeight; y++)
        {
            // 遍历当前行的每个字节
            for (byte = start_byte; byte <= end_byte; byte++)
            {
                if (start_byte == end_byte)
                {
                    // 情况1：窗口在同一个字节内
                    PixelPageCache2D[y][byte] &= ~((0xFF >> start_bit) & (0xFF << (7 - end_bit)));
                }
                else if (byte == start_byte)
                {
                    // 情况2：处理起始字节的部分位
                    PixelPageCache2D[y][byte] &= ~(0xFF >> start_bit);
                }
                else if (byte == end_byte)
                {
                    // 情况3：处理结束字节的部分位
                    PixelPageCache2D[y][byte] &= ~(0xFF << (7 - end_bit));
                }
                else
                {
                    // 情况4：中间完整字节，直接清零
                    PixelPageCache2D[y][byte] = 0x00;
                }
            }
        }
    }
}

/**
 * @brief  对二维像素页缓存的某一窗口刷双色图
 * @param  usX ：在二维像素页缓存的起点X坐标
 * @param  usY ：在二维像素页缓存的起点Y坐标
 * @param  usWidth ：窗口的宽度
 * @param  usHeight ：窗口的高度
 * @param  usY_R ：Y轴允许范围坐标
 * @param  usH_R ：Y轴允许范围高度
 * @param  usPicture ：图地址
 * @retval 无
 */
void PixelPageCache2D_TwoColorChart(unsigned short usX, unsigned short usY, unsigned short usWidth, unsigned short usHeight, unsigned short usY_R, unsigned short usH_R, unsigned char *usTwoColor)
{
    unsigned short y, byte;
    unsigned short start_byte;
    unsigned char start_bit;
    unsigned char dev_byte, assist_byte1, assist_byte2;

    // 计算双手图一行的字节数
    dev_byte = (usWidth + 7) >> 3;
    // 计算当前行涉及的字节范围
    start_byte = usX >> 3;
    start_bit = usX & 0x07;
    // 逐行处理窗口区域
    for (y = 0; y < usHeight; y++)
    {
        if ((y + usY) >= usY_R && (y + usY) < (usY_R + usH_R))
        {
            // 遍历当前行的每个字节
            for (byte = 0; byte < dev_byte; byte++)
            {
                assist_byte1 = usTwoColor[dev_byte * y + byte] >> start_bit;
                assist_byte2 = usTwoColor[dev_byte * y + byte] << (8 - start_bit);
                PixelPageCache2D[y + usY][byte + start_byte] = (PixelPageCache2D[y + usY][byte + start_byte] & (0xFF << (8 - start_bit))) + assist_byte1;
                PixelPageCache2D[y + usY][byte + start_byte + 1] = assist_byte2;
            }
        }
    }
}

/**
 * @brief  对二维像素页缓存的某一窗口刷字符串
 * @param  usX ：在二维像素页缓存的起点X坐标
 * @param  usY ：在二维像素页缓存的起点Y坐标
 * @param  usHeight ：窗口的高度
 * @param  usY_R ：Y轴允许范围坐标
 * @param  usH_R ：Y轴允许范围高度
 * @param  usText ：图地址
 * @retval 无
 */
void PixelPageCache2D_Text(unsigned short usX, unsigned short usY, enum LZM_MATRIX_e_ FontSize, unsigned short usY_R, unsigned short usH_R, char *usText)
{
    if (usText == 0)
    {
        return;
    }

    while (*usText)
    {
        if ((unsigned char)*usText >= 0xE0)
        { // 全角字符（UTF-8三字节）
            PixelPageCache2D_TwoColorChart(usX, usY, lzm_matrix_heigh_list[FontSize], lzm_matrix_heigh_list[FontSize], usY_R, usH_R, LZM_FontAddressAcquisition(usText, FontSize));
            usX += lzm_matrix_heigh_list[FontSize];
            usText += 3; // 跳过全角字符的后续字节
        }
        else
        { // 半角字符
            PixelPageCache2D_TwoColorChart(usX, usY, (lzm_matrix_heigh_list[FontSize] >> 1), lzm_matrix_heigh_list[FontSize], usY_R, usH_R, LZM_FontAddressAcquisition(usText, FontSize));
            usX += (lzm_matrix_heigh_list[FontSize] >> 1);
            usText++;
        }
    }
}
