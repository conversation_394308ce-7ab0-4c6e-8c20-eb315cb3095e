#include "bsp.h"
volatile Dishwashing_PRO_T DIS_P_T = {DIS_S_STOP, DIS_P_WASH, 0}; /* 洗碗结构体 */
volatile uint8_t pro_lock = NO;                                   // 锁机标志位（是否锁机）

/* 锁机状态管理（非阻塞轮询1秒） */
void LockStateManagement(void)
{
}

/* 洗碗业务运行管理（非阻塞轮询1秒） */
void DishwashingBusinessOperation(void)
{
    switch (DIS_P_T.DIS_S_) /* 洗碗状态 */
    {
    case DIS_S_STOP: /* 停 */
        if (YES == pro_lock /* 锁机 */)
        {
            page_goto(page_standby); /* 界面切换：待机菜单 */
        }
        break;
    case DIS_S_RUN: /* 动 */
        /* 计数管理 */
        switch (DIS_P_T.DIS_P_) /* 洗碗进程 */
        {
        case DIS_P_WASH: /* 洗涤 */
        {
            int Total_time = 0;           /* 总时间 */
            switch (PV(int, USR_mode, 0)) /* 洗碗模式(节能、标准、强力) */
            {
            case Wash_Mode_1:                                               /* 节能 */
                Total_time = PV(int, USR_mill_17, PV(int, USR_set_tad, 0)); /* 总时间=节能时间 */
                break;
            case Wash_Mode_2:                                               /* 标准 */
                Total_time = PV(int, USR_mill_18, PV(int, USR_set_tad, 0)); /* 总时间=标准时间 */
                break;
            case Wash_Mode_3:                                               /* 强力 */
                Total_time = PV(int, USR_mill_19, PV(int, USR_set_tad, 0)); /* 总时间=强力时间 */
                break;
            default:
                break;
            }

            if (DIS_P_T.count < Total_time /* 洗涤时间计数 */)
            {
                DIS_P_T.count++;
            }
            else
            {
                /* 洗涤时间达到就跳转到停顿 */
                DIS_P_T.count = 0;           /* 计数归零 */
                DIS_P_T.DIS_P_ = DIS_P_STOP; /* 切换到停顿 */
            }
        }
        break;
        case DIS_P_STOP: /* 停顿 */
            if (DIS_P_T.count < PV(int, USR_mill_23, PV(int, USR_set_tad, 0)) /* 停顿时间计数 */)
            {
                DIS_P_T.count++;
            }
            else
            {
                /* 停顿时间达到就跳转到漂洗 */
                DIS_P_T.count = 0;            /* 计数归零 */
                DIS_P_T.DIS_P_ = DIS_P_RINSE; /* 切换到漂洗 */
            }
            break;
        case DIS_P_RINSE: /* 漂洗 */
            if (DIS_P_T.count < PV(int, USR_user_13, 0) /* 漂洗时间计数 */)
            {
                DIS_P_T.count++;
            }
            else
            {
                /* 漂洗时间达到就跳转到完成 */
                DIS_P_T.count = 0;             /* 计数归零 */
                DIS_P_T.DIS_P_ = DIS_P_FINISH; /* 切换到完成 */
                DIS_P_T.DIS_S_ = DIS_S_STOP;   /* 洗碗状态=停 */

                bsp_Putfifo(B4_BEEP); /* 压入任务：慢响3声 */
            }
            break;
        case DIS_P_FINISH:               /* 完成 */
            DIS_P_T.DIS_P_ = DIS_P_WASH; /* 切换到洗涤 */
            break;
        default:
            break;
        }
        break;
    default:
        break;
    }
}

/* 洗碗关闭业务 */
void DishwashingCloseBusiness(void)
{
    DIS_P_T.DIS_S_ = DIS_S_STOP; /* 停 */
    DIS_P_T.DIS_P_ = DIS_P_WASH; /* 切换到洗涤 */
    DIS_P_T.count = 0;           /* 洗碗计数 */
}

/* 洗碗暂停业务 */
void DishwashingSuspendBusiness(void)
{
    DIS_P_T.DIS_S_ = DIS_S_STOP; /* 停 */
}

/* 水满检查函数 */
uint8_t Full_water_check_function(void)
{
    switch (PV(int, USR_mill_1, PV(int, USR_set_tad, 0)) /* 水满判断 */)
    {
        /* 仅洗高 */
    case water_full_1:
        if (PRO_SW_SIG_OBTAIN(PSST1 /* 洗高 */) /* 业务用 开关信号获取 */ == SIGNAL_CLOSE /* 闭合 */)
        {
            return TRUE; /* 返回真 */
        }
        break;
        /* 洗高与漂高 */
    case water_full_2:
        if (PRO_SW_SIG_OBTAIN(PSST1 /* 洗高 */) /* 业务用 开关信号获取 */ == SIGNAL_CLOSE /* 闭合 */ && PRO_SW_SIG_OBTAIN(PSST3 /* 漂高 */) /* 业务用 开关信号获取 */ == SIGNAL_CLOSE /* 闭合 */)
        {
            return TRUE; /* 返回真 */
        }
        break;
    default:
        break;
    }

    return FALSE; /* 返回假 */
}

/**********************************************************************************************************************************************/
volatile uint8_t Full_water_level_once = FALSE; /*上电水位满过一次标志位*/
/**********************************************************************************************************************************************/
/* 上电水满一次检测函数（非阻塞轮询1秒） */
void Power_on_water_full_once_detection_function(void)
{
    if (Full_water_check_function() == TRUE) /* 水满检查为真 */
    {
        Full_water_level_once = TRUE; /*上电水位满过一次为真*/
    }
}

/* 运行条件判断 */
uint8_t DishwashingDeterminationOfOperatingConditions(void)
{
    if (PV(int, USR_mill_5, PV(int, USR_set_tad, 0)) /* 机门未关能否运行 */ == NO /* 不能 */)
    {
        if (PRO_SW_SIG_OBTAIN(PSST5 /* 机门 */) /* 业务用 开关信号获取 */ == SIGNAL_OPEN /* 开路 */)
        {
            return DIS_C_DOOR; /* 返回假 */
        }
    }

    if (Full_water_level_once == FALSE) /* 没有上电水满过一次 */
    {
        return DIS_C_WATER; /* 返回假 */
    }

    if (PV(int, USR_user_5, 0) /* 洗涤门槛温度 */ != PMIN(int, USR_user_5) /*获取参数下限*/)
    {
        if (PRO_TEMP_SEE_OBTAIN(PTT1 /* 洗涤温度 */) /* 业务用 温度信号(显示)获取 */ < PV(int, USR_user_5, 0) /* 洗涤门槛温度 */)
        {
            return DIS_C_TEMP; /* 返回假 */
        }
    }

    if (PV(int, USR_mill_6, PV(int, USR_set_tad, 0)) /* 缺水能否启动运行 */ == NO /* 不能 */)
    {
        if (Full_water_check_function() == FALSE) /* 水没满 */
        {
            return DIS_C_WATER; /* 返回假 */
        }
    }

    switch (PV(int, USR_mill_7, PV(int, USR_set_tad, 0)) /* 运行中缺水是否暂停 */)
    {
    case UP_w_s_s_0 /* 不暂停 */:
        break;
    case UP_w_s_s_1 /* 洗高暂停 */:
        if (PRO_SW_SIG_OBTAIN(PSST1 /* 洗高 */) /* 业务用 开关信号获取 */ == SIGNAL_OPEN /* 开路 */)
        {
            return DIS_C_WATER; /* 返回假 */
        }
        break;
    case UP_w_s_s_2 /* 洗低暂停 */:
        if (PRO_SW_SIG_OBTAIN(PSST2 /* 洗低 */) /* 业务用 开关信号获取 */ == SIGNAL_OPEN /* 开路 */)
        {
            return DIS_C_WATER; /* 返回假 */
        }
        break;
    case UP_w_s_s_3 /* 漂高暂停 */:
        if (PRO_SW_SIG_OBTAIN(PSST3 /* 漂高 */) /* 业务用 开关信号获取 */ == SIGNAL_OPEN /* 开路 */)
        {
            return DIS_C_WATER; /* 返回假 */
        }
        break;
    case UP_w_s_s_4 /* 漂低暂停 */:
        if (PRO_SW_SIG_OBTAIN(PSST4 /* 漂低 */) /* 业务用 开关信号获取 */ == SIGNAL_OPEN /* 开路 */)
        {
            return DIS_C_WATER; /* 返回假 */
        }
        break;
    default:
        break;
    }

    return DIS_C_SATISFY; /* 返回 满足 */
}

/* 从暂停业务到启动 */
/* 返回0：条件没达到 1：启动成功 2：已经在运行中 */
uint8_t From_suspending_business_to_starting(void)
{
    if (DishwashingDeterminationOfOperatingConditions() != DIS_C_SATISFY)
    {
        DIS_P_T.DIS_S_ = DIS_S_STOP; /* 洗碗状态=停 */
        /* 返回假 */
        return 0;
    }

    if (DIS_P_T.DIS_S_ == DIS_S_RUN) /* 洗碗状态==动 */
    {
        /* 已经在运行中 */
        return 2;
    }
    else
    {
        DIS_P_T.DIS_S_ = DIS_S_RUN; /* 洗碗状态=动 */
        /* 返回真 */
        return 1;
    }
}

/* 缺水停止运行管理（非阻塞轮询1秒） */
void Stop_operation_management_due_to_water_shortage(void)
{
    if (PV(int, USR_mill_7, PV(int, USR_set_tad, 0)) /* 运行中缺水是否暂停 */ == UP_w_s_s_0 /* 不暂停 */)
    {
        return;
    }

    if (DIS_P_T.DIS_S_ == DIS_S_STOP) /* 洗碗状态==停 */
    {
        if (DIS_P_T.DIS_P_ == DIS_P_FINISH) /* 洗碗进程==完成 */
        {
            return;
        }

        if (DIS_P_T.DIS_P_ == DIS_P_WASH /* 洗碗进程==洗涤 */ && DIS_P_T.count == 0 /* 洗碗计数==0 */)
        {
            return;
        }

        From_suspending_business_to_starting(); /* 从暂停业务到启动 */
    }
    else /* 洗碗状态==动 */
    {
        switch (PV(int, USR_mill_7, PV(int, USR_set_tad, 0)) /* 运行中缺水是否暂停 */)
        {
        case UP_w_s_s_0 /* 不暂停 */:
            return;
            break;
        case UP_w_s_s_1 /* 洗高暂停 */:
            if (PRO_SW_SIG_OBTAIN(PSST1 /* 洗高 */) /* 业务用 开关信号获取 */ != SIGNAL_OPEN /* 开路 */)
            {
                return;
            }
            break;
        case UP_w_s_s_2 /* 洗低暂停 */:
            if (PRO_SW_SIG_OBTAIN(PSST2 /* 洗低 */) /* 业务用 开关信号获取 */ != SIGNAL_OPEN /* 开路 */)
            {
                return;
            }
            break;
        case UP_w_s_s_3 /* 漂高暂停 */:
            if (PRO_SW_SIG_OBTAIN(PSST3 /* 漂高 */) /* 业务用 开关信号获取 */ != SIGNAL_OPEN /* 开路 */)
            {
                return;
            }
            break;
        case UP_w_s_s_4 /* 漂低暂停 */:
            if (PRO_SW_SIG_OBTAIN(PSST4 /* 漂低 */) /* 业务用 开关信号获取 */ != SIGNAL_OPEN /* 开路 */)
            {
                return;
            }
            break;
        default:
            return;
            break;
        }
        DishwashingSuspendBusiness(); /* 洗碗暂停业务 */
    }
}

/**********************************************************************************************************************************************/
#define STARTUP_SCREEN_TIME 4 //(1S/4S)
/**********************************************************************************************************************************************/
/* 开机画面停留时间（非阻塞轮询1秒） */
void Power_on_screen_dwell_time(void)
{
    static uint8_t Startup_screen_time_count = 0; /* 开机画面时间计数 */

    if (pa_m_tVar.current_page /* 当前交互界面 */ == page_start /* 开机画面 */)
    {
        if (++Startup_screen_time_count > STARTUP_SCREEN_TIME)
        {
            page_goto(page_standby); /*切换页面：待机菜单*/
        }
    }
}

/**********************************************************************************************************************************************/
volatile uint8_t interface_settings_No_Action_reset_count = 0; // 设置界面无操作无操作计数（1S\60S）
/**********************************************************************************************************************************************/
/* 设置界面无操作倒计时处理（非阻塞轮询1S秒） */
void Interface_no_operation_countdown_processing(void)
{
    if (pa_m_tVar.current_page /* 当前交互界面 */ == page_statistics /* 统计查看 */)
    {
        if (++interface_settings_No_Action_reset_count > 60)
        {
            page_goto(page_home); /*切换页面：返回根界面*/
        }
    }

    if (pa_m_tVar.current_page /* 当前交互界面 */ == page_check /* 状态查看 */)
    {
        if (++interface_settings_No_Action_reset_count > 20)
        {
            page_goto(page_home); /*切换页面：返回根界面*/
        }
    }
}

/* 运行界面无运行操作倒计时处理（非阻塞轮询1S秒） */
void Countdown_processing_for_no_running_operation_on_the_running_interface(void)
{
    static uint8_t Auxiliary_Counting = 0;                   /*辅助计数*/
    static int No_operation_count_for_running_interface = 0; /*运行界面无运行操作计数*/

    if (pa_m_tVar.root_page /* 当前根的界面 */ != page_run /* 运行 */)
    {
        No_operation_count_for_running_interface = 0; /*运行界面无运行操作计数*/
        return;
    }

    if (Alarm_Flag != warn_empty /* 报警标志 */) /* 如果发生报警就不退出了 */
    {
        No_operation_count_for_running_interface = 0; /*运行界面无运行操作计数*/
        return;
    }

    if (PV(int, USR_user_14, 0) /*获取参数值*/ == PMIN(int, USR_user_14) /*获取参数下限*/) /* 自动关机延时 */
    {
        No_operation_count_for_running_interface = 0; /*运行界面无运行操作计数*/
        return;
    }

    if (DIS_P_T.DIS_S_ /* 洗碗状态 */ == DIS_S_RUN /* 动 */)
    {
        No_operation_count_for_running_interface = 0; // 运行界面无运行操作计数（1S\30S）
        return;
    }

    if (++Auxiliary_Counting /*辅助计数*/ > 60)
    {
        Auxiliary_Counting = 0;
        if (++No_operation_count_for_running_interface > PV(int, USR_user_14, 0)) /* 自动关机延时 */
        {
            page_goto(page_standby); /*切换页面：待机菜单*/
        }
    }
}

/* 洗碗机用量统计（非阻塞轮询1S秒） */
void Dishwasher_usage_statistics(void)
{
    if (PRO_RELAY_OBTAIN(PRELT1 /* 洗涤剂 */) /* 业务用 继电器控制状态获取 */ == ON /* 开 */)
    {
        parame_adj(USR_stat_1 /*洗涤剂工作时长 */, 0, PARAME_INC /* 增加 */, PARAME_S_STEP /* 单步 */);
        parame_adj(USR_Ostat_1 /*总洗涤剂工作时长 */, 0, PARAME_INC /* 增加 */, PARAME_S_STEP /* 单步 */);
    }
    if (PRO_RELAY_OBTAIN(PRELT2 /* 干燥剂 */) /* 业务用 继电器控制状态获取 */ == ON /* 开 */)
    {
        parame_adj(USR_stat_2 /*干燥剂工作时长 */, 0, PARAME_INC /* 增加 */, PARAME_S_STEP /* 单步 */);
        parame_adj(USR_Ostat_2 /*总干燥剂工作时长 */, 0, PARAME_INC /* 增加 */, PARAME_S_STEP /* 单步 */);
    }
    if (PRO_RELAY_OBTAIN(PRELT5 /* 进水阀 */) /* 业务用 继电器控制状态获取 */ == ON /* 开 */)
    {
        parame_adj(USR_stat_3 /*进水阀工作时长 */, 0, PARAME_INC /* 增加 */, PARAME_S_STEP /* 单步 */);
        parame_adj(USR_Ostat_3 /*总进水阀工作时长 */, 0, PARAME_INC /* 增加 */, PARAME_S_STEP /* 单步 */);
    }
    if (PRO_RELAY_OBTAIN(PRELT4 /* 洗加热 */) /* 业务用 继电器控制状态获取 */ == ON /* 开 */)
    {
        parame_adj(USR_stat_4 /*洗加热工作时长 */, 0, PARAME_INC /* 增加 */, PARAME_S_STEP /* 单步 */);
        parame_adj(USR_Ostat_4 /*总洗加热工作时长 */, 0, PARAME_INC /* 增加 */, PARAME_S_STEP /* 单步 */);
    }
    if (PRO_RELAY_OBTAIN(PRELT3 /* 漂加热 */) /* 业务用 继电器控制状态获取 */ == ON /* 开 */)
    {
        parame_adj(USR_stat_5 /*漂加热工作时长 */, 0, PARAME_INC /* 增加 */, PARAME_S_STEP /* 单步 */);
        parame_adj(USR_Ostat_5 /*总漂加热工作时长 */, 0, PARAME_INC /* 增加 */, PARAME_S_STEP /* 单步 */);
    }
}

static int Business_ee_management_count = 0; /*业务ee管理计数*/
/*  业务ee立刻存储存储管理 */
void Business_EE_immediate_storage_and_storage_management(void)
{
    Business_ee_management_count = 0; /*业务ee管理计数*/
    bsp_Putfifo(EE_SA /* EE保存 */);  // 发送命令消息
}
/*  业务ee半小时存储管理（非阻塞轮询1S秒） */
void Business_EE_half_hour_storage_management(void)
{
    if (++Business_ee_management_count > 1800)
    {
        bsp_Putfifo(EE_SA /* EE保存 */); // 发送命令消息
    }
}

/* 洗涤剂输出控制（非阻塞轮询1秒） */
void Detergent_output_control(void)
{
    if (pa_m_tVar.current_page /* 当前交互界面 */ == page_debug /* 调试 */)
    {
        /* 保持状态 */
        return;
    }

    if (pa_m_tVar.root_page /* 当前根界面 */ != page_run /* 运行 */)
    {
        PRO_PWM_CONTROL(PPWMT1 /* 洗涤剂 */, 0 /* 百分比 */); /* 业务用 pwm控制 */
        PRO_RELAY_CONTROL(PRELT1 /* 洗涤剂 */, OFF);          /* 业务用 继电器控制 */
        return;
    }

    if (Alarm_Flag != warn_empty /* 报警标志 */) /* 如果发生报警 */
    {
        PRO_PWM_CONTROL(PPWMT1 /* 洗涤剂 */, 0 /* 百分比 */); /* 业务用 pwm控制 */
        PRO_RELAY_CONTROL(PRELT1 /* 洗涤剂 */, OFF);          /* 业务用 继电器控制 */
        return;
    }

    if (DIS_P_T.DIS_S_ /* 洗碗状态 */ != DIS_S_RUN /* =动 */)
    {
        PRO_PWM_CONTROL(PPWMT1 /* 洗涤剂 */, 0 /* 百分比 */); /* 业务用 pwm控制 */
        PRO_RELAY_CONTROL(PRELT1 /* 洗涤剂 */, OFF);          /* 业务用 继电器控制 */
        return;
    }

    if (DIS_P_T.DIS_P_ /* 洗碗进程 */ != DIS_P_WASH /* 洗涤 */)
    {
        PRO_PWM_CONTROL(PPWMT1 /* 洗涤剂 */, 0 /* 百分比 */); /* 业务用 pwm控制 */
        PRO_RELAY_CONTROL(PRELT1 /* 洗涤剂 */, OFF);          /* 业务用 继电器控制 */
        return;
    }

    if (DIS_P_T.count /* 洗碗计数 */ <= PV(int, USR_user_9, 0) /* 洗涤剂工作时间 */)
    {
        PRO_PWM_CONTROL(PPWMT1 /* 洗涤剂 */, PV(int, USR_user_10, 0) /* 洗涤剂强度 */ /* 百分比 */); /* 业务用 pwm控制 */
        PRO_RELAY_CONTROL(PRELT1 /* 洗涤剂 */, ON);                                                  /* 业务用 继电器控制 */
    }
    else
    {
        PRO_PWM_CONTROL(PPWMT1 /* 洗涤剂 */, 0 /* 百分比 */); /* 业务用 pwm控制 */
        PRO_RELAY_CONTROL(PRELT1 /* 洗涤剂 */, OFF);          /* 业务用 继电器控制 */
    }
}

/* 干燥剂输出控制（非阻塞轮询1秒） */
void Desiccant_output_control(void)
{
    if (pa_m_tVar.current_page /* 当前交互界面 */ == page_debug /* 调试 */)
    {
        /* 保持状态 */
        return;
    }

    if (pa_m_tVar.root_page /* 当前根界面 */ != page_run /* 运行 */)
    {
        PRO_PWM_CONTROL(PPWMT2 /* 干燥机 */, 0 /* 百分比 */); /* 业务用 pwm控制 */
        PRO_RELAY_CONTROL(PRELT2 /* 干燥机 */, OFF);          /* 业务用 继电器控制 */
        return;
    }

    if (Alarm_Flag != warn_empty /* 报警标志 */) /* 如果发生报警 */
    {
        PRO_PWM_CONTROL(PPWMT2 /* 干燥机 */, 0 /* 百分比 */); /* 业务用 pwm控制 */
        PRO_RELAY_CONTROL(PRELT2 /* 干燥机 */, OFF);          /* 业务用 继电器控制 */
        return;
    }

    if (DIS_P_T.DIS_S_ /* 洗碗状态 */ != DIS_S_RUN /* =动 */)
    {
        PRO_PWM_CONTROL(PPWMT2 /* 干燥机 */, 0 /* 百分比 */); /* 业务用 pwm控制 */
        PRO_RELAY_CONTROL(PRELT2 /* 干燥机 */, OFF);          /* 业务用 继电器控制 */
        return;
    }

    if (DIS_P_T.DIS_P_ /* 洗碗进程 */ != DIS_P_RINSE /* 漂洗 */)
    {
        PRO_PWM_CONTROL(PPWMT2 /* 干燥机 */, 0 /* 百分比 */); /* 业务用 pwm控制 */
        PRO_RELAY_CONTROL(PRELT2 /* 干燥机 */, OFF);          /* 业务用 继电器控制 */
        return;
    }

    if (DIS_P_T.count /* 洗碗计数 */ <= PV(int, USR_user_11, 0) /* 干燥剂工作时间 */)
    {
        PRO_PWM_CONTROL(PPWMT2 /* 干燥机 */, PV(int, USR_user_12, 0) /* 干燥剂强度 */ /* 百分比 */); /* 业务用 pwm控制 */
        PRO_RELAY_CONTROL(PRELT2 /* 干燥机 */, ON);                                                  /* 业务用 继电器控制 */
    }
    else
    {
        PRO_PWM_CONTROL(PPWMT2 /* 干燥机 */, 0 /* 百分比 */); /* 业务用 pwm控制 */
        PRO_RELAY_CONTROL(PRELT2 /* 干燥机 */, OFF);          /* 业务用 继电器控制 */
    }
}

volatile int Water_inlet_delay_count = 0; /* 进水延时计数（1秒） */
/* 进水阀输出控制（非阻塞轮询1秒） */
void Inlet_valve_output_control(void)
{
    static uint8_t Difference_flag = ON;                    /* 差值标志 */
    static int Forced_water_inlet_count_of_inlet_valve = 0; /* 进水阀强制进水计数（1秒） */

    if (pa_m_tVar.current_page /* 当前交互界面 */ == page_debug /* 调试 */)
    {
        /* 保持状态 */
        return;
    }

    if (pa_m_tVar.root_page /* 当前根界面 */ != page_run /* 运行 */)
    {
        Difference_flag = ON;                        /* 差值标志 */
        PRO_RELAY_CONTROL(PRELT5 /* 进水阀 */, OFF); /* 业务用 继电器控制 */
        Water_inlet_delay_count = 0;                 /* 进水延时计数（0.1秒） */
        return;
    }

    if (Alarm_Flag != warn_empty /* 报警标志 */) /* 如果发生报警 */
    {
        PRO_RELAY_CONTROL(PRELT5 /* 进水阀 */, OFF); /* 业务用 继电器控制 */
        return;
    }

    if (PV(int, USR_mill_8, PV(int, USR_set_tad, 0)) /*门开进水是否停止 */ == YES /* 暂停 */)
    {
        if (PRO_SW_SIG_OBTAIN(PSST5 /* 机门 */) /* 业务用 开关信号获取 */ == SIGNAL_OPEN /* 开路 */)
        {
            PRO_RELAY_CONTROL(PRELT5 /* 进水阀 */, OFF); /* 业务用 继电器控制 */
            return;
        }
    }

    if (PRO_TEMP_SEE_OBTAIN(PTT2 /* 漂洗温度 */) /* 业务用 温度信号(显示)获取 */ >= PV(int, USR_user_7, 0) /*漂洗进水降温温度 */)
    {
        Forced_water_inlet_count_of_inlet_valve = PV(int, USR_user_8, 0); /* 进水阀强制进水计数=进水降温时间 */
    }

    if (Forced_water_inlet_count_of_inlet_valve > 0) /* 强制进水 */
    {
        Forced_water_inlet_count_of_inlet_valve--;
        PRO_RELAY_CONTROL(PRELT5 /* 进水阀 */, ON); /* 业务用 继电器控制 */
        return;
    }

    if (DIS_P_T.DIS_S_ /* 洗碗状态 */ == DIS_S_RUN /* 动 */)
    {
        if (PV(int, USR_mill_10, PV(int, USR_set_tad, 0)) /* 洗涤时缺水能否补水*/ == NO)
        {
            if (DIS_P_T.DIS_P_ /* 洗碗进程 */ == DIS_P_WASH /* 洗涤 */ || DIS_P_T.DIS_P_ /* 洗碗进程 */ == DIS_P_STOP /* 停顿 */)
            {
                PRO_RELAY_CONTROL(PRELT5 /* 进水阀 */, OFF); /* 业务用 继电器控制 */
                return;
            }
        }

        if (DIS_P_T.DIS_P_ /* 洗碗进程 */ == DIS_P_RINSE /* 漂洗 */)
        {
            PRO_RELAY_CONTROL(PRELT5 /* 进水阀 */, ON); /* 业务用 继电器控制 */
            return;
        }
    }

    if (Full_water_check_function() == TRUE) /* 水满检查为真 */
    {
        if (Water_inlet_delay_count > 0) /* 进水延时计数 */
        {
            Water_inlet_delay_count--;
            PRO_RELAY_CONTROL(PRELT5 /* 进水阀 */, ON); /* 业务用 继电器控制 */
        }
        else
        {
            PRO_RELAY_CONTROL(PRELT5 /* 进水阀 */, OFF); /* 业务用 继电器控制 */
        }
    }
    else
    {
        Water_inlet_delay_count = PV(int, USR_user_6, 0); /* 进水阀延时进水时间 */
        if (PV(int, USR_mill_4, PV(int, USR_set_tad, 0)) /* 进水保温回差*/ != PMAX(int, USR_user_14) /*获取参数上限*/)
        {
            if (PV(int, USR_mill_1, PV(int, USR_set_tad, 0)) /* 水满判断 */ == water_full_2 /* 洗高与漂高 */)
            {
                if (PRO_SW_SIG_OBTAIN(PSST1 /* 洗高 */) /* 业务用 开关信号获取 */ == SIGNAL_OPEN /* 开路 */ && PRO_SW_SIG_OBTAIN(PSST3 /* 漂高 */) /* 业务用 开关信号获取 */ == SIGNAL_CLOSE /* 闭合 */)
                {
                    if (PRO_TEMP_SEE_OBTAIN(PTT2 /* 漂洗温度 */) /* 业务用 温度信号(显示)获取 */ < (PV(int, USR_user_2, 0) /* 漂洗控温温度*/ - PV(int, USR_mill_4, PV(int, USR_set_tad, 0)) /* 进水保温回差*/))
                    {
                        Difference_flag = OFF; /* 差值标志 */
                    }

                    if (PRO_TEMP_SEE_OBTAIN(PTT2 /* 漂洗温度 */) /* 业务用 温度信号(显示)获取 */ >= PV(int, USR_user_2, 0) /* 漂洗控温温度*/)
                    {
                        Difference_flag = ON; /* 差值标志 */
                    }

                    PRO_RELAY_CONTROL(PRELT5 /* 进水阀 */, Difference_flag); /* 业务用 继电器控制 */
                    return;
                }
            }
        }
        PRO_RELAY_CONTROL(PRELT5 /* 进水阀 */, ON); /* 业务用 继电器控制 */
    }
}

/* 漂洗加热输出控制（非阻塞轮询1秒） */
void Rinse_heating_output_control(void)
{
    static uint8_t Difference_flag = ON; /* 差值标志 */
    static int Auxiliary_count = 0;      /* 辅助计数（1秒） */
    int Rinse_target_temperature;        /* 漂洗目标温度 */

    if (pa_m_tVar.current_page /* 当前交互界面 */ == page_debug /* 调试 */)
    {
        /* 保持状态 */
        return;
    }

    if (pa_m_tVar.root_page /* 当前根界面 */ != page_run /* 运行 */)
    {
        Difference_flag = ON;                        /* 差值标志 */
        Auxiliary_count = 0;                         /* 辅助计数（1秒） */
        PRO_RELAY_CONTROL(PRELT3 /* 漂加热 */, OFF); /* 业务用 继电器控制 */
        return;
    }

    if (Alarm_Flag != warn_empty /* 报警标志 */) /* 如果发生报警 */
    {
        PRO_RELAY_CONTROL(PRELT3 /* 漂加热 */, OFF); /* 业务用 继电器控制 */
        return;
    }

    if (PRO_TEMP_USE_OBTAIN(PTT2 /* 漂洗温度 */) /* 业务用 温度信号(使用)获取 */ >= PV(int, USR_user_7, 0) /* 漂洗进水降温温度 */)
    {
        PRO_RELAY_CONTROL(PRELT3 /* 漂加热 */, OFF); /* 业务用 继电器控制 */
        return;
    }

    if (PV(int, USR_mill_11, PV(int, USR_set_tad, 0)) /*漂洗暂停时漂加热是否关闭 */ == YES /* 是 */)
    {
        if (DIS_P_T.DIS_S_ /* 洗碗状态 */ == DIS_S_STOP /* 停 */ && DIS_P_T.DIS_P_ /* 洗碗进程 */ == DIS_P_RINSE /* 漂洗 */)
        {
            PRO_RELAY_CONTROL(PRELT3 /* 漂加热 */, OFF); /* 业务用 继电器控制 */
            return;
        }
    }

    if (PV(int, USR_mill_21, PV(int, USR_set_tad, 0)) /*延时进水中是否开启加热 */ == NO /* 不开启 */)
    {
        if (PV(int, USR_mill_2, PV(int, USR_set_tad, 0)) /* 漂洗控温开启条件 */ == R_t_c_o_c_1 /* 仅洗高 */)
        {
            if (Water_inlet_delay_count > 0) /* 进水延时中 */
            {
                PRO_RELAY_CONTROL(PRELT3 /* 漂加热 */, OFF); /* 业务用 继电器控制 */
                return;
            }
        }
    }

    switch (PV(int, USR_mill_2, PV(int, USR_set_tad, 0)) /* 漂洗控温开启条件 */)
    {
    case R_t_c_o_c_1:
        /* 仅洗高 */
        if (PRO_SW_SIG_OBTAIN(PSST1 /* 洗高 */) /* 业务用 开关信号获取 */ == SIGNAL_OPEN /* 开路 */)
        {
            PRO_RELAY_CONTROL(PRELT3 /* 漂加热 */, OFF); /* 业务用 继电器控制 */
            return;
        }
        break;
    case R_t_c_o_c_2:
        /* 仅洗低 */
        if (PRO_SW_SIG_OBTAIN(PSST2 /* 洗低 */) /* 业务用 开关信号获取 */ == SIGNAL_OPEN /* 开路 */)
        {
            PRO_RELAY_CONTROL(PRELT3 /* 漂加热 */, OFF); /* 业务用 继电器控制 */
            return;
        }
        break;
    case R_t_c_o_c_3:
        /* 仅漂高 */
        if (PRO_SW_SIG_OBTAIN(PSST3 /* 漂高 */) /* 业务用 开关信号获取 */ == SIGNAL_OPEN /* 开路 */)
        {
            PRO_RELAY_CONTROL(PRELT3 /* 漂加热 */, OFF); /* 业务用 继电器控制 */
            return;
        }
        break;
    case R_t_c_o_c_4:
        /* 仅漂低 */
        if (PRO_SW_SIG_OBTAIN(PSST4 /* 漂低 */) /* 业务用 开关信号获取 */ == SIGNAL_OPEN /* 开路 */)
        {
            PRO_RELAY_CONTROL(PRELT3 /* 漂加热 */, OFF); /* 业务用 继电器控制 */
            return;
        }
        break;
    case R_t_c_o_c_5:
        /* 洗高与漂高 */
        if ((PRO_SW_SIG_OBTAIN(PSST1 /* 洗高 */) /* 业务用 开关信号获取 */ == SIGNAL_OPEN /* 开路 */) || (PRO_SW_SIG_OBTAIN(PSST3 /* 漂高 */) /* 业务用 开关信号获取 */ == SIGNAL_OPEN /* 开路 */))
        {
            PRO_RELAY_CONTROL(PRELT3 /* 漂加热 */, OFF); /* 业务用 继电器控制 */
            return;
        }
        break;
    default:
        break;
    }

    if (PV(int, USR_mill_27, PV(int, USR_set_tad, 0)) /*漂洗探头故障能否运行 */ == YES /* 能 */)
    {
        if (PRO_TEMP_ANO_OBTAIN(PTT2 /* 漂洗温度 */) /* 业务用 温度探头异常获取 */ > 0)
        {
            // 周期性开停
            if (PV(int, USR_mill_28, PV(int, USR_set_tad, 0)) /*探头故障漂洗加热开周期 */ == 0)
            {
                PRO_RELAY_CONTROL(PRELT3 /* 漂加热 */, OFF); /* 业务用 继电器控制 */
                return;
            }
            else
            {
                if (PV(int, USR_mill_29, PV(int, USR_set_tad, 0)) /*探头故障漂洗加热关周期 */ == 0)
                {
                    PRO_RELAY_CONTROL(PRELT3 /* 漂加热 */, ON); /* 业务用 继电器控制 */
                    return;
                }
                else
                {
                    if (++Auxiliary_count > (PV(int, USR_mill_28, PV(int, USR_set_tad, 0)) + PV(int, USR_mill_29, PV(int, USR_set_tad, 0))))
                    {
                        Auxiliary_count = 0;
                    }

                    if (Auxiliary_count < PV(int, USR_mill_28, PV(int, USR_set_tad, 0)) /*探头故障漂洗加热开周期 */)
                    {
                        /* 开 */
                        PRO_RELAY_CONTROL(PRELT3 /* 漂加热 */, OFF); /* 业务用 继电器控制 */
                        return;
                    }
                    else
                    {
                        /* 关 */
                        PRO_RELAY_CONTROL(PRELT3 /* 漂加热 */, ON); /* 业务用 继电器控制 */
                        return;
                    }
                }
            }
            return;
        }
    }

    if (PRO_SW_SIG_OBTAIN(PSST5 /* 机门 */) /* 业务用 开关信号获取 */ == SIGNAL_OPEN /* 开路 */)
    {
        /* 漂洗目标温度 */
        Rinse_target_temperature = PRO_TEMP_SEE_OBTAIN(PTT2 /* 漂洗温度 */) /* 业务用 温度信号(显示)获取 */ - PV(int, USR_mill_22, PV(int, USR_set_tad, 0)) /* 机门打开漂洗目标下降温度 */;
    }
    else
    {
        /* 漂洗目标温度 */
        Rinse_target_temperature = PRO_TEMP_SEE_OBTAIN(PTT2 /* 漂洗温度 */) /* 业务用 温度信号(显示)获取 */;
    }

    if (Rinse_target_temperature /* 漂洗目标温度 */ >= PV(int, USR_user_2, 0) /* 洗涤控温温度 */)
    {
        Difference_flag = OFF; /* 差值标志 */
    }
    if (Rinse_target_temperature /* 漂洗目标温度 */ < (PV(int, USR_user_2, 0) /* 洗涤控温温度 */ - PV(int, USR_user_4, 0) /* 洗涤控温回差温度 */))
    {
        Difference_flag = ON; /* 差值标志 */
    }
    PRO_RELAY_CONTROL(PRELT3 /* 漂加热 */, Difference_flag); /* 业务用 继电器控制 */
}

/* 洗涤加热输出控制（非阻塞轮询1秒） */
void Washing_and_heating_output_control(void)
{
    static uint8_t Difference_flag = ON; /* 差值标志 */
    static int Auxiliary_count = 0;      /* 辅助计数（1秒） */

    if (pa_m_tVar.current_page /* 当前交互界面 */ == page_debug /* 调试 */)
    {
        /* 保持状态 */
        return;
    }

    if (pa_m_tVar.root_page /* 当前根界面 */ != page_run /* 运行 */)
    {
        Difference_flag = ON;                        /* 差值标志 */
        Auxiliary_count = 0;                         /* 辅助计数（1秒） */
        PRO_RELAY_CONTROL(PRELT4 /* 洗加热 */, OFF); /* 业务用 继电器控制 */
        return;
    }

    if (Alarm_Flag != warn_empty /* 报警标志 */) /* 如果发生报警 */
    {
        PRO_RELAY_CONTROL(PRELT4 /* 洗加热 */, OFF); /* 业务用 继电器控制 */
        return;
    }

    if (PV(int, USR_mill_16, PV(int, USR_set_tad, 0)) /*是否错峰加热 */ == YES /* 是 */)
    {
        if (PRO_RELAY_OBTAIN(PRELT3 /* 漂加热 */) /* 业务用 继电器控制状态获取 */ == ON /* 开 */)
        {
            PRO_RELAY_CONTROL(PRELT4 /* 洗加热 */, OFF); /* 业务用 继电器控制 */
            return;
        }
    }

    if (PV(int, USR_mill_21, PV(int, USR_set_tad, 0)) /*延时进水中是否开启加热 */ == NO /* 不开启 */)
    {
        if (PV(int, USR_mill_3, PV(int, USR_set_tad, 0)) /*洗涤控温开启条件 */ == W_t_c_o_c_1 /* 仅洗高 */)
        {
            if (Water_inlet_delay_count > 0) /* 进水延时中 */
            {
                PRO_RELAY_CONTROL(PRELT4 /* 洗加热 */, OFF); /* 业务用 继电器控制 */
                return;
            }
        }
    }

    switch (PV(int, USR_mill_3, PV(int, USR_set_tad, 0)) /* 洗涤控温开启条件 */)
    {
    case W_t_c_o_c_1:
        /* 仅洗高 */
        if (PRO_SW_SIG_OBTAIN(PSST1 /* 洗高 */) /* 业务用 开关信号获取 */ == SIGNAL_OPEN /* 开路 */)
        {
            PRO_RELAY_CONTROL(PRELT4 /* 洗加热 */, OFF); /* 业务用 继电器控制 */
            return;
        }
        break;
    case W_t_c_o_c_2:
        /* 仅洗低 */
        if (PRO_SW_SIG_OBTAIN(PSST2 /* 洗低 */) /* 业务用 开关信号获取 */ == SIGNAL_OPEN /* 开路 */)
        {
            PRO_RELAY_CONTROL(PRELT4 /* 洗加热 */, OFF); /* 业务用 继电器控制 */
            return;
        }
        break;
    default:
        break;
    }

    if (PV(int, USR_mill_24, PV(int, USR_set_tad, 0)) /*洗涤探头故障能否运行 */ == YES /* 能 */)
    {
        if (PRO_TEMP_ANO_OBTAIN(PTT1 /* 洗涤温度 */) /* 业务用 温度探头异常获取 */ > 0)
        {
            // 周期性开停
            if (PV(int, USR_mill_25, PV(int, USR_set_tad, 0)) /*探头故障洗涤加热开周期 */ == 0)
            {
                PRO_RELAY_CONTROL(PRELT4 /* 洗加热 */, OFF); /* 业务用 继电器控制 */
                return;
            }
            else
            {
                if (PV(int, USR_mill_26, PV(int, USR_set_tad, 0)) /*探头故障洗涤加热关周期 */ == 0)
                {
                    PRO_RELAY_CONTROL(PRELT4 /* 洗加热 */, ON); /* 业务用 继电器控制 */
                    return;
                }
                else
                {
                    if (++Auxiliary_count > (PV(int, USR_mill_25, PV(int, USR_set_tad, 0)) + PV(int, USR_mill_26, PV(int, USR_set_tad, 0))))
                    {
                        Auxiliary_count = 0;
                    }

                    if (Auxiliary_count < PV(int, USR_mill_25, PV(int, USR_set_tad, 0)) /*探头故障洗涤加热开周期 */)
                    {
                        /* 开 */
                        PRO_RELAY_CONTROL(PRELT4 /* 洗加热 */, OFF); /* 业务用 继电器控制 */
                        return;
                    }
                    else
                    {
                        /* 关 */
                        PRO_RELAY_CONTROL(PRELT4 /* 洗加热 */, ON); /* 业务用 继电器控制 */
                        return;
                    }
                }
            }
            return;
        }
    }

    if (PRO_TEMP_SEE_OBTAIN(PTT1 /* 洗涤温度 */) /* 业务用 温度信号(显示)获取 */ >= PV(int, USR_user_1, 0) /* 洗涤控温温度 */)
    {
        Difference_flag = OFF; /* 差值标志 */
    }
    if (PRO_TEMP_SEE_OBTAIN(PTT1 /* 洗涤温度 */) /* 业务用 温度信号(显示)获取 */ < (PV(int, USR_user_1, 0) /* 洗涤控温温度 */ - PV(int, USR_user_3, 0) /* 洗涤控温回差温度 */))
    {
        Difference_flag = ON; /* 差值标志 */
    }
    PRO_RELAY_CONTROL(PRELT4 /* 洗加热 */, Difference_flag); /* 业务用 继电器控制 */
}

/* 漂洗泵输出控制（非阻塞轮询1秒） */
void Rinse_pump_output_control(void)
{
    static uint8_t Rinse_pump_non_stop_sign = 0; /* 漂洗泵不停标志 */
    static int Water_delay_count = 0;            /* 泵抽水延时计数（0.1秒） */

    if (PRO_SW_SIG_OBTAIN(PSST3 /* 漂高 */) /* 业务用 开关信号获取 */ == SIGNAL_CLOSE /* 闭合 */)
    {
        Water_delay_count = 0; /* 泵抽水延时计数（1秒） */
    }
    else
    {
        if (Water_delay_count <= PV(int, USR_mill_13, PV(int, USR_set_tad, 0)) /*漂洗泵打水延时 */)
        {
            Water_delay_count++; /* 泵抽水延时计数（1秒） */
        }
    }

    if (pa_m_tVar.current_page /* 当前交互界面 */ == page_debug /* 调试 */)
    {
        /* 保持状态 */
        return;
    }

    if (pa_m_tVar.root_page /* 当前根界面 */ != page_run /* 运行 */)
    {
        PRO_RELAY_CONTROL(PRELT6 /* 漂洗泵 */, OFF); /* 业务用 继电器控制 */
        return;
    }

    if (Alarm_Flag != warn_empty /* 报警标志 */) /* 如果发生报警 */
    {
        PRO_RELAY_CONTROL(PRELT6 /* 漂洗泵 */, OFF); /* 业务用 继电器控制 */
        return;
    }

    if (PRO_SW_SIG_OBTAIN(PSST5 /* 机门 */) /* 业务用 开关信号获取 */ == SIGNAL_OPEN /* 开路 */)
    {
        PRO_RELAY_CONTROL(PRELT6 /* 漂洗泵 */, OFF); /* 业务用 继电器控制 */
        return;
    }

    if (DIS_P_T.DIS_S_ /* 洗碗状态 */ == DIS_S_RUN /* 动 */)
    {
        if (DIS_P_T.DIS_P_ /* 洗碗进程 */ == DIS_P_RINSE /* 漂洗 */)
        {
            PRO_RELAY_CONTROL(PRELT6 /* 漂洗泵 */, ON); /* 业务用 继电器控制 */
            return;
        }

        if (PV(int, USR_mill_15, PV(int, USR_set_tad, 0)) /*洗涤时漂洗泵是否强制关闭 */ == YES /* 是 */)
        {
            if (DIS_P_T.DIS_P_ /* 洗碗进程 */ == DIS_P_WASH /* 洗涤 */ || DIS_P_T.DIS_P_ /* 洗碗进程 */ == DIS_P_STOP /* 停顿 */)
            {
                PRO_RELAY_CONTROL(PRELT6 /* 漂洗泵 */, OFF); /* 业务用 继电器控制 */
                return;
            }
        }
    }

    if (PV(int, USR_mill_14, PV(int, USR_set_tad, 0)) /*进水阀与漂洗泵联动开关 */ == YES /* 是 */)
    {
        if (PRO_RELAY_OBTAIN(PRELT5 /* 进水阀 */) /* 业务用 继电器控制状态获取 */ == ON /* 开 */)
        {
            PRO_RELAY_CONTROL(PRELT6 /* 漂洗泵 */, ON); /* 业务用 继电器控制 */
            return;
        }
    }

    if (PV(int, USR_mill_12, PV(int, USR_set_tad, 0)) /*漂洗泵辅助进水开关 */ == YES /* 是 */)
    {
        if (PRO_SW_SIG_OBTAIN(PSST1 /* 洗高 */) /* 业务用 开关信号获取 */ == SIGNAL_OPEN /* 开路 */ && PRO_SW_SIG_OBTAIN(PSST3 /* 漂高 */) /* 业务用 开关信号获取 */ == SIGNAL_CLOSE /* 闭合 */ && (PRO_RELAY_OBTAIN(PRELT5 /* 进水阀 */) /* 业务用 继电器控制状态获取 */ == ON /* 开 */))
        {
            PRO_RELAY_CONTROL(PRELT6 /* 漂洗泵 */, ON); /* 业务用 继电器控制 */
            return;
        }
    }

    if (PRO_SW_SIG_OBTAIN(PSST1 /* 洗高 */) /* 业务用 开关信号获取 */ == SIGNAL_CLOSE /* 闭合 */)
    {
        PRO_RELAY_CONTROL(PRELT6 /* 漂洗泵 */, OFF); /* 业务用 继电器控制 */
        Rinse_pump_non_stop_sign = 0;                /* 漂洗泵不停标志 */
    }
    else
    {
        if ((PRO_RELAY_OBTAIN(PRELT5 /* 进水阀 */) /* 业务用 继电器控制状态获取 */ == OFF /* 关 */))
        {
            PRO_RELAY_CONTROL(PRELT6 /* 漂洗泵 */, OFF); /* 业务用 继电器控制 */
            return;
        }

        if (PV(int, USR_mill_13, PV(int, USR_set_tad, 0)) /*漂洗泵打水延时 */ == PMAX(int, USR_mill_13) /* 达到上限显示“不停止” */ /*获取参数上限*/)
        {
            if (PRO_SW_SIG_OBTAIN(PSST3 /* 漂高 */) /* 业务用 开关信号获取 */ == SIGNAL_CLOSE /* 闭合 */)
            {
                PRO_RELAY_CONTROL(PRELT6 /* 漂洗泵 */, ON); /* 业务用 继电器控制 */
                Rinse_pump_non_stop_sign = 1;               // 漂洗泵不停标志
            }
            else
            {
                if (Rinse_pump_non_stop_sign == 1)
                {
                    PRO_RELAY_CONTROL(PRELT6 /* 漂洗泵 */, ON); /* 业务用 继电器控制 */
                }
                else
                {
                    PRO_RELAY_CONTROL(PRELT6 /* 漂洗泵 */, OFF); /* 业务用 继电器控制 */
                }
            }
        }
        else if (PV(int, USR_mill_13, PV(int, USR_set_tad, 0)) /*漂洗泵打水延时 */ != PMIN(int, USR_mill_13) /* 达到下限显示“不打水” */ /*获取参数下限*/)
        {
            if (PRO_SW_SIG_OBTAIN(PSST3 /* 漂高 */) /* 业务用 开关信号获取 */ == SIGNAL_CLOSE /* 闭合 */)
            {
                PRO_RELAY_CONTROL(PRELT6 /* 漂洗泵 */, ON); /* 业务用 继电器控制 */
            }
            else
            {
                if (Water_delay_count <= PV(int, USR_mill_13, PV(int, USR_set_tad, 0)) /*漂洗泵打水延时 */)
                {
                    PRO_RELAY_CONTROL(PRELT6 /* 漂洗泵 */, ON); /* 业务用 继电器控制 */
                }
                else
                {
                    PRO_RELAY_CONTROL(PRELT6 /* 漂洗泵 */, OFF); /* 业务用 继电器控制 */
                }
            }
        }
        else
        {
            PRO_RELAY_CONTROL(PRELT6 /* 漂洗泵 */, OFF); /* 业务用 继电器控制 */
        }
    }
}

/* 洗涤泵输出控制（非阻塞轮询1秒） */
void Washing_pump_output_control(void)
{
    if (pa_m_tVar.current_page /* 当前交互界面 */ == page_debug /* 调试 */)
    {
        /* 保持状态 */
        return;
    }

    if (pa_m_tVar.root_page /* 当前根界面 */ != page_run /* 运行 */)
    {
        PRO_RELAY_CONTROL(PRELT7 /* 洗涤泵 */, OFF); /* 业务用 继电器控制 */
        return;
    }

    if (Alarm_Flag != warn_empty /* 报警标志 */) /* 如果发生报警 */
    {
        PRO_RELAY_CONTROL(PRELT7 /* 洗涤泵 */, OFF); /* 业务用 继电器控制 */
        return;
    }

    if (PRO_SW_SIG_OBTAIN(PSST5 /* 机门 */) /* 业务用 开关信号获取 */ == SIGNAL_OPEN /* 开路 */)
    {
        PRO_RELAY_CONTROL(PRELT7 /* 洗涤泵 */, OFF); /* 业务用 继电器控制 */
        return;
    }

    if (DIS_P_T.DIS_S_ /* 洗碗状态 */ == DIS_S_RUN /* 动 */)
    {
        if (DIS_P_T.DIS_P_ /* 洗碗进程 */ == DIS_P_WASH /* 洗涤 */)
        {
            PRO_RELAY_CONTROL(PRELT7 /* 洗涤泵 */, ON); /* 业务用 继电器控制 */
            return;
        }
    }

    PRO_RELAY_CONTROL(PRELT7 /* 洗涤泵 */, OFF); /* 业务用 继电器控制 */
}

/* 调试界面开关信号按键音 */
void Debugging_interface_switch_signal_button_sound(void)
{
    switch (pa_m_tVar.current_page) /* 当前交互界面 */
    {
    case page_debug:
        /* 调试 */
        bsp_Putfifo(B1_BEEP); /* 压入任务：短按键音 */
        break;
    default:
        /* 其它值不处理 */
        break;
    }
}

volatile uint8_t Door_closing_delay_count = 0; /* 关门延时计数（1秒） */
/* 关门延时启动函数（非阻塞轮询1秒） */
void Door_closing_delay_start_function(void)
{
    if (PRO_SW_SIG_OBTAIN(PSST5 /* 机门 */) /* 业务用 开关信号获取 */ == SIGNAL_OPEN /* 开路 */)
    {
        Door_closing_delay_count = 0; /* 关门延时计数（1秒） */
        return;
    }

    if (DIS_P_T.DIS_S_ /* 洗碗状态 */ == DIS_S_RUN /* 动 */)
    {
        Door_closing_delay_count = 0; /* 关门延时计数（1秒） */
        return;
    }

    if (Door_closing_delay_count > 0)
    {
        Door_closing_delay_count--;
        if (Door_closing_delay_count == 0)
        {
            if (From_suspending_business_to_starting() /* 从暂停业务到启动 */ == 0 /* 返回0：条件没达到 1：启动成功 2：已经在运行中 */)
            {
                bsp_Putfifo(B3_BEEP); /* 压入任务：急促3声(无效提示) */
            }
            else
            {
                bsp_Putfifo(B1_BEEP); /* 压入任务：短按键音 */
            }
        }
    }
}

/* 洗高从开到关触发一次 */
void Wa_H_from_open_to_closed(void)
{
    Debugging_interface_switch_signal_button_sound();
}
/* 洗高从关到开触发一次 */
void Wa_H_from_closed_to_open(void)
{
    Debugging_interface_switch_signal_button_sound();
}
/* 洗低从开到关触发一次 */
void Wa_L_from_open_to_closed(void)
{
    Debugging_interface_switch_signal_button_sound();
}
/* 洗低从关到开触发一次 */
void Wa_L_from_closed_to_open(void)
{
    Debugging_interface_switch_signal_button_sound();
}
/* 漂高从开到关触发一次 */
void RI_H_from_open_to_closed(void)
{
    Debugging_interface_switch_signal_button_sound();
}
/* 漂高从关到开触发一次 */
void RI_H_from_closed_to_open(void)
{
    Debugging_interface_switch_signal_button_sound();
}
/* 漂低从开到关触发一次 */
void RI_L_from_open_to_closed(void)
{
    Debugging_interface_switch_signal_button_sound();
}
/* 漂低从关到开触发一次 */
void RI_L_from_closed_to_open(void)
{
    Debugging_interface_switch_signal_button_sound();
}
/* 机门从开到关触发一次 */
void Door_from_open_to_closed(void)
{
    switch (pa_m_tVar.current_page) /* 当前交互界面 */
    {
    case page_run:
        /* 运行 */
        Door_closing_delay_count = PV(int, USR_mill_9, PV(int, USR_set_tad, 0)) /* 门关后延时启动时间 */ + 1; /* 关门延时计数（1秒） */
        return;
        break;
    default:
        /* 其它值不处理 */
        break;
    }
    Debugging_interface_switch_signal_button_sound();
}
/* 机门从关到开触发一次 */
void Door_from_closed_to_open(void)
{
    switch (pa_m_tVar.current_page) /* 当前交互界面 */
    {
    case page_run:
        /* 运行 */
        if (PV(int, USR_mill_5, PV(int, USR_set_tad, 0)) /* 机门未关能否运行 */ == NO /* 不能 */)
        {
            DishwashingSuspendBusiness(); /* 洗碗暂停业务 */
            bsp_Putfifo(B1_BEEP);         /* 压入任务：短按键音 */
        }
        break;
    default:
        /* 其它值不处理 */
        break;
    }
    Debugging_interface_switch_signal_button_sound();
}