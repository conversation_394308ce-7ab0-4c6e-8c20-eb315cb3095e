#ifndef _BSP_CRC_H_
#define _BSP_CRC_H_
#include <stdlib.h>
#include <string.h>
#include <stdio.h>
#include "air32f10x.h"
/* CRC8 校验和计算 */
unsigned char CalCrc8_Check(unsigned char L_pt[], unsigned long ucLengthTmp);
/**
 * @brief CRC16 校验和计算
 * @param {uint8_t} L_pt:需要进行CRC校验的数组
 * @param {uint16_t} INIT_:初始校验值（无初始校验，填0xFFFF）
 * @param {uint16_t} ucLengthTmp：数组长度
 * @return {校验结果}
 */
uint16_t CalCrc16_Check(uint8_t *L_pt,uint16_t INIT_, uint16_t ucLengthTmp);
/* 使能CRC时钟 */
#define InitCRC() RCC_AHBPeriphClockCmd(RCC_AHBPeriph_CRC,ENABLE)

#endif
