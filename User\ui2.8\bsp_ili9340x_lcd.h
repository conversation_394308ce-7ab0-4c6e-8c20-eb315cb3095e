#ifndef _BSP_ILI9340X_LCD_H_
#define _BSP_ILI9340X_LCD_H_
#include "bsp.h"
/******************************* ILI9340X 显示屏8位8080通讯引脚定义 ***************************/
/******控制信号线******/
/* 片选 */
#define _ILI_CS_PORT_ A
#define _ILI_CS_PIN_ 4
/* RS(D/I)引脚 */
#define _ILI_RS_PORT_ A
#define _ILI_RS_PIN_ 5
/* 写使能 */
#define _ILI_WR_PORT_ A
#define _ILI_WR_PIN_ 6
/* 读使能 */
#define _ILI_RD_PORT_ A
#define _ILI_RD_PIN_ 7
/* 复位 */
#define _ILI_RST_PORT_ A
#define _ILI_RST_PIN_ 15
/* 背光引脚 */
#define _ILI_BK_PORT_ A
#define _ILI_BK_PIN_ 8
/* 数据信号线 */
#define _ILI_DATA_PORT_ B

/********信号线控制相关的宏***************/
// 中间宏：强制展开参数
#define _EXPAND_CLK(port) RCC_APB2Periph_GPIO##port
#define _EXPAND_PORT(port) GPIO##port
#define _EXPAND_PIN(pin) GPIO_Pin_##pin

// 最终宏：调用中间宏
#define EXPAND_CLK(port) _EXPAND_CLK(port)
#define EXPAND_PORT(port) _EXPAND_PORT(port)
#define EXPAND_PIN(pin) _EXPAND_PIN(pin)

#define ILI9340X_CS_SET EXPAND_PORT(_ILI_CS_PORT_)->BSRR = EXPAND_PIN(_ILI_CS_PIN_) // 片选端口
#define ILI9340X_RS_SET EXPAND_PORT(_ILI_RS_PORT_)->BSRR = EXPAND_PIN(_ILI_RS_PIN_) // 数据/命令
#define ILI9340X_WR_SET EXPAND_PORT(_ILI_WR_PORT_)->BSRR = EXPAND_PIN(_ILI_WR_PIN_) // 写数据
#define ILI9340X_RD_SET EXPAND_PORT(_ILI_RD_PORT_)->BSRR = EXPAND_PIN(_ILI_RD_PIN_) // 读数据

#define ILI9340X_CS_CLR EXPAND_PORT(_ILI_CS_PORT_)->BRR = EXPAND_PIN(_ILI_CS_PIN_) // 片选端口
#define ILI9340X_RS_CLR EXPAND_PORT(_ILI_RS_PORT_)->BRR = EXPAND_PIN(_ILI_RS_PIN_) // 数据/命令
#define ILI9340X_WR_CLR EXPAND_PORT(_ILI_WR_PORT_)->BRR = EXPAND_PIN(_ILI_WR_PIN_) // 写数据
#define ILI9340X_RD_CLR EXPAND_PORT(_ILI_RD_PORT_)->BRR = EXPAND_PIN(_ILI_RD_PIN_) // 读数据

// 数据线输入输出
#define ILI9340X_DATAOUT(x)                          \
    do                                               \
    {                                                \
        EXPAND_PORT(_ILI_DATA_PORT_)->ODR &= 0xFF00; \
        EXPAND_PORT(_ILI_DATA_PORT_)->ODR += x;      \
    } while (0) // 数据输出
#define ILI9340X_DATAIN ((EXPAND_PORT(_ILI_DATA_PORT_)->IDR) & 0x00FF) // 数据输入

/*************************************** 调试预用 ******************************************/
#define ILI9340X_DEBUG_DELAY() // bsp_DelayMS(10)
/***************************** ILI934 显示区域的起始坐标和总行列数 ***************************/
#define ILI9340X_DispWindow_X_Star 0 // 起始点的X坐标
#define ILI9340X_DispWindow_Y_Star 0 // 起始点的Y坐标

#define ILI9340X_LESS_PIXEL 240 // 液晶屏较短方向的像素宽度
#define ILI9340X_MORE_PIXEL 320 // 液晶屏较长方向的像素宽度

/******************************* 定义 ILI9340X 显示屏常用颜色 ********************************/
#define BACKGROUND BLACK // 默认背景颜色

#define WHITE 0xFFFF   // 白色
#define BLACK 0x0000   // 黑色
#define DGREY 0x39C7   // 深灰
#define DGREY2 0x7BEF   // 深灰2
#define GREY 0xCE79    // 灰色
#define BLUE 0x001F    // 蓝色
#define BLUE2 0x051F   // 浅蓝色
#define BLUE3 0xDFFD   // 淡蓝
#define BLUE4 0xA7DF   // 淡蓝2
#define BLUE5 0x47F   // 蓝5
#define RED 0xF800     // 红色
#define RED2 0xFA8B     // 红色2
#define MAGENTA 0xF81F // 红紫色，洋红色
#define GREEN 0x07E0   // 绿色
#define GREEN3 0x7569   // 深绿
#define CYAN 0x7FFF    // 蓝绿色，青色
#define YELLOW 0xFFE0  // 黄色
#define YELLOW2 0xF7FB  // 淡黄
#define YELLOW3 0xFFF2  // 淡黄2
#define ORANGE 0xFCE0  // 橙
#define BRED 0xF81F
#define GRED 0xFFE0
#define GBLUE 0x07FF

/******************************* 定义 ILI9340X 常用命令 ********************************/
#define CMD_SetCoordinateX 0x2A // 设置X坐标
#define CMD_SetCoordinateY 0x2B // 设置Y坐标
#define CMD_SetPixel 0x2C       // 填充像素

void ILI9340X_Init(void);                                                                                                                                                                            // ILI9340X初始化函数，如果要用到lcd，一定要调用这个函数
void ILI9340X_BackLed_Control(FunctionalState enumState);                                                                                                                                            // ILI9340X背光LED控制
void ILI9340X_Clear(uint16_t usX, uint16_t usY, uint16_t usWidth, uint16_t usHeight, uint16_t usColors);                                                                                             // 对ILI9340X显示器的某一窗口以某种颜色进行清屏
void ILI9340X_Picture(uint16_t usX, uint16_t usY, uint16_t usWidth, uint16_t usHeight, uint16_t *usPicture);                                                                                         // 对ILI9340X显示器的某一窗口刷图
void ILI9340X_TwoColorChart(uint16_t usX, uint16_t usY, uint16_t usWidth, uint16_t usHeight, uint16_t usColor_0, uint16_t usColor_1, uint8_t *usTwoColor);                                           // 对ILI9340X显示器的某一窗口刷双色图
void ILI9340X_DrawRectangle(uint16_t usX_Start, uint16_t usY_Start, uint16_t usWidth, uint16_t usHeight, uint16_t usColor_0, uint16_t usColor_1, uint8_t *ucR_angle);
void ILI9340X_TwoColorChart_XYReversed(uint16_t usX, uint16_t usY, uint16_t usWidth, uint16_t usHeight, uint16_t usX_R, uint16_t usW_R, uint16_t usY_R, uint16_t usH_R, uint8_t *ucR_angle, uint16_t usColor_0, uint16_t usColor_1, uint8_t *usTwoColor);
#endif
