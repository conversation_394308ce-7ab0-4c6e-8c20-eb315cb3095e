#include "page_start.h"

/*
*********************************************************************************************************
*	函 数 名: page_root_load
*	形    参: 无
*	返 回 值: 无
*	功能说明: 根界面切换时调用
*********************************************************************************************************
*/
void page_start_root_load(void)
{
}

/*
*********************************************************************************************************
*	函 数 名: page_view_load
*	形    参: 无
*	返 回 值: 无
*	功能说明: 交互界面切换时调用
*********************************************************************************************************
*/
void page_start_view_load(void)
{
    UI28_Clear_Screen();               /*2.8寸屏幕清屏，并关闭所有模块，一般切换界面时调用*/
    UI28_display_onoff_arc_loading(1); /* 圆弧加载动画显示开关（关，实际只是不刷新了） */

    UI28_set_up_text_gradient_add(ui28_T1_tg, "系统正在运行..."); /* 设置文本地址 */
    UI28_display_onoff_text_gradient(ui28_T1_tg, 1);              /* 文本渐变显示开关（关实际只是不刷新了） */
}
/*
*********************************************************************************************************
*	函 数 名: page_update
*	形    参: 无
*	返 回 值: 无
*	功能说明: 为交互界面时，会周期调用
*********************************************************************************************************
*/
void page_start_update(void)
{
    PixelPageCache2D_Text_customize(140, 20, 0, 0, ui28_char_20, (char *)&_sys_parames.VERSION[0]); /*对二维像素页缓存的某一窗口刷字符串(自定义)*/
    ILI9340X_TwoColorChart(0, 220, 140, 20, BLACK, WHITE, (unsigned char *)&PixelPageCache2D[0][0]);  /*对ILI9340X显示器的某一窗口刷双色图*/
}
