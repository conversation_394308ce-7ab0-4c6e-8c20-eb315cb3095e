#ifndef _UI28_PROGRESS_BAR_
#define _UI28_PROGRESS_BAR_
#include "UI28.h"
#if (UI28_PROGRESS_BAR_ENABLED == 1)

/*创建进度条表*/
#define UI28_PROGRESS_ITEM_LIST \
	UI28_PROGRESS_DEBUG_X(1 /*名字*/, WHITE /*背景颜色(右)*/, BLACK /*进度条颜色(左)*/, 0 /* 进度条X坐标 */, 116 /*进度条Y坐标*/, 270 /*进度条宽*/, 8 /*进度条高*/)

/* 进度条ID */
enum UI28_PROGRESS_e_
{
#define UI28_PROGRESS_DEBUG_X(Lname, COLOR_0_, COLOR_1_, Lx, Ly, Lw, Lh) ui28_##Lname##_progress,
	UI28_PROGRESS_ITEM_LIST
#undef UI28_PROGRESS_DEBUG_X
		UI28_PROGRESS_ITEM_NUM,
};
// #if (UI28_PROGRESS_ITEM_NUM > 64)
// #error "相册表不能超过64"
// #elif (UI28_PROGRESS_ITEM_NUM > 32)
// typedef unsigned long long uint_progressDis_t;
// #elif (UI28_PROGRESS_ITEM_NUM > 16)
// typedef unsigned int uint_progressDis_t;
// #elif (UI28_PROGRESS_ITEM_NUM > 8)
// typedef unsigned short uint_progressDis_t;
// #else
typedef unsigned char uint_progressDis_t;
// #ends

extern volatile uint_progressDis_t ui28_ProgressBar_display; /* 进度条显示标志位 */

/* 进度条显示开关（关实际只是不刷新了） */
void UI28_display_onoff_ProgressBar(enum UI28_PROGRESS_e_ ALBUM_, unsigned char _onoff_);
/* 设置进度条位置 */
/* speed:(1：增量=差值/2)(2：增量=差值/4)(3：增量=差值/8)(4：增量=差值/16)(5：增量=差值/32)..... */
void UI28_change_cursor_value_ProgressBar(enum UI28_PROGRESS_e_ ALBUM_, unsigned char cur_val, unsigned char speed);

/* 根据目标进度更新当前 */
void Update_current_status_based_on_target_progressBar(enum UI28_PROGRESS_e_ ALBUM_);
/* 更新进度条 */
void UI28_update_ProgressBar(enum UI28_PROGRESS_e_ ALBUM_);
/* 工程初始化进度条（在系统上电执行一次） */
void ui28_Init_ProgressBar_Engineering(void);
/* 初始化进度条 */
void ui28_Init_ProgressBar(enum UI28_PROGRESS_e_ ALBUM_);

#endif
#endif
