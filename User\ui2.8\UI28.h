#ifndef _UI28_H_
#define _UI28_H_

#define UI28_SCREEN_ENABLED 1		 /* 启用2.8寸屏幕 开关 */
#define UI28_MENU_ENABLED 1			 /* 启用菜单 开关 */
#define UI28_ROLLING_ALBUM_ENABLED 1 /* 滚动相册 开关 */
#define UI28_ARC_ENABLED 1			 /* 圆弧 开关 */
#define UI28_TEXT_GRADIENT_ENABLED 1 /* 文本渐变 开关 */
#define UI28_ANIMATION_ENABLED 1	 /* 动画 开关 */
#define UI28_PROGRESS_BAR_ENABLED 1	 /* 进度条 开关 */

#define UI28_INIT_COLOUR BLACK /* 2.8寸初始化颜色 */

enum UI28_STEP_
{
	ui28_up = 0, /* 单步调节（上） */
	ui28_down	 /* 单步调节（下） */
};

#include "UI28_auxiliary_table.h"
#include "bsp_ili9340x_lcd.h"

#include "UI28_font_16.h"
#include "UI28_font_20.h"
#include "UI28_font_32.h"
#include "UI28_font_72.h"
#include "UI28_font_28.h"
#include "UI28_font_36.h"
#include "UI28_font_24.h"
#include "UI28_font_18.h"
#include "UI28_font_14.h"

#include "UI28_pic_120x120.h"
#include "UI28_pic_32x32.h"
#include "UI28_pic_22x30.h"
#include "UI28_pic_14x18.h"
#include "UI28_pic_48x88.h"
#include "UI28_pic_36x36.h"
#include "UI28_pic_45x45.h"
#include "UI28_pic_40x20.h"
#include "UI28_pic_70x70.h"

#define UI28_MATRIX_XX_ASCII__(heigh) (sizeof(Font##heigh##_Ascii) / sizeof(struct FONT##heigh##_ASCII))
#define UI28_MATRIX_XX_CHINESE__(heigh) (sizeof(Font##heigh##_Chinese) / sizeof(struct FONT##heigh##_CHINESE))
#define UI28_MATRIX_XX_ASCII(heigh) UI28_MATRIX_XX_ASCII__(heigh)
#define UI28_MATRIX_XX_CHINESE(heigh) UI28_MATRIX_XX_CHINESE__(heigh)
#define UI28_MATRIX_XX_QUAN_LIST__(heigh)                                                   \
	/* ASCII个数 */                                                                         \
	const unsigned int ui28_matrix_ascii_##heigh##_quan_list = UI28_MATRIX_XX_ASCII(heigh); \
	/* CHINESE个数 */                                                                       \
	const unsigned int ui28_matrix_chinese_##heigh##_quan_list = UI28_MATRIX_XX_CHINESE(heigh);
#define UI28_MATRIX_XX_QUAN_LIST(heigh) UI28_MATRIX_XX_QUAN_LIST__(heigh)

/* 字模 */
#define UI28_MATRIX_ITEM_LIST          \
	UI28_MATRIX_DEBUG_X(16 /* 字高 */) \
	UI28_MATRIX_DEBUG_X(20 /* 字高 */) \
	UI28_MATRIX_DEBUG_X(32 /* 字高 */) \
	UI28_MATRIX_DEBUG_X(72 /* 字高 */) \
	UI28_MATRIX_DEBUG_X(28 /* 字高 */) \
	UI28_MATRIX_DEBUG_X(36 /* 字高 */) \
	UI28_MATRIX_DEBUG_X(24 /* 字高 */) \
	UI28_MATRIX_DEBUG_X(18 /* 字高 */) \
	UI28_MATRIX_DEBUG_X(14 /* 字高 */)

/* 字模ID */
enum UI28_MATRIX_e_
{
#define UI28_MATRIX_DEBUG_X(heigh) ui28_char_##heigh,
	UI28_MATRIX_ITEM_LIST
#undef UI28_MATRIX_DEBUG_X
		UI28_MATRIX_ITEM_NUM,
};

/* 图模 */
#define UI28_PIC_MATRIX_ITEM_LIST                       \
	UI28_PIC_MATRIX_DEBUG_X(120 /* 宽 */, 120 /* 高 */) \
	UI28_PIC_MATRIX_DEBUG_X(32 /* 宽 */, 32 /* 高 */)   \
	UI28_PIC_MATRIX_DEBUG_X(22 /* 宽 */, 30 /* 高 */)   \
	UI28_PIC_MATRIX_DEBUG_X(14 /* 宽 */, 18 /* 高 */)   \
	UI28_PIC_MATRIX_DEBUG_X(48 /* 宽 */, 88 /* 高 */)   \
	UI28_PIC_MATRIX_DEBUG_X(36 /* 宽 */, 36 /* 高 */)   \
	UI28_PIC_MATRIX_DEBUG_X(45 /* 宽 */, 45 /* 高 */)   \
	UI28_PIC_MATRIX_DEBUG_X(40 /* 宽 */, 20 /* 高 */)   \
	UI28_PIC_MATRIX_DEBUG_X(70 /* 宽 */, 70 /* 高 */)

/* 图模ID */
enum UI28_PIC_MATRIX_e_
{
#define UI28_PIC_MATRIX_DEBUG_X(wide, heigh) ui28_pic_##wide##x##heigh,
	UI28_PIC_MATRIX_ITEM_LIST
#undef UI28_PIC_MATRIX_DEBUG_X
		UI28_PIC_MATRIX_ITEM_NUM,
};

#include "UI28_matrix.h"
#include "UI28_menu.h"
#include "UI28_rolling_album.h"
#include "UI28_arc.h"
#include "UI28_animation.h"
#include "UI28_progress_bar.h"

#define UI28_MATRIX_DEBUG_X(heigh)                                     \
	extern struct FONT##heigh##_ASCII const Font##heigh##_Ascii[];     \
	extern struct FONT##heigh##_CHINESE const Font##heigh##_Chinese[]; \
	extern const unsigned int ui28_matrix_ascii_##heigh##_quan_list;   \
	extern const unsigned int ui28_matrix_chinese_##heigh##_quan_list;
UI28_MATRIX_ITEM_LIST
#undef UI28_MATRIX_DEBUG_X

#define UI28_PIC_MATRIX_DEBUG_X(wide, heigh) \
	extern struct PIC_##wide##_##heigh const Pic_##wide##x##heigh[];
UI28_PIC_MATRIX_ITEM_LIST
#undef UI28_PIC_MATRIX_DEBUG_X

#define UI28_UI_H ILI9340X_LESS_PIXEL		 // 屏幕高度
#define UI28_UI_W ILI9340X_MORE_PIXEL		 // 屏幕宽度
#define UI28_UI_W_Byte_count (UI28_UI_W / 8) // 屏幕宽度字节数

extern const unsigned char ui28_matrix_heigh_list[UI28_MATRIX_ITEM_NUM];

extern volatile unsigned char PixelPageCache2D[UI28_UI_H][UI28_UI_W_Byte_count];

/*对二维像素页缓存-开窗*/
void PixelPageCache2D_OpenWin(unsigned short usX, unsigned short usY, unsigned short usWidth, unsigned short usHeight, unsigned char usBit);
/*对二维像素页缓存的某一窗口刷双色图*/
void PixelPageCache2D_TwoColorChart(unsigned short usX, unsigned short usY, unsigned short usWidth, unsigned short usHeight, unsigned short usY_R, unsigned short usH_R, unsigned char *usTwoColor);
/*对二维像素页缓存的某一窗口刷字符串*/
void PixelPageCache2D_Text(unsigned short usX, unsigned short usY, enum UI28_MATRIX_e_ FontSize, unsigned short usY_R, unsigned short usH_R, char *usText);

/*对二维像素页缓存-开窗(自定义)*/
void PixelPageCache2D_OpenWin_customize(unsigned short usWidth, unsigned short usHeight, unsigned char usBit);
/*对二维像素页缓存的某一窗口刷双色图(自定义)*/
void PixelPageCache2D_TwoColorChart_customize(unsigned short usWidthWin, unsigned short usHeightWin, short usX, short usY, unsigned short usWidth, unsigned short usHeight, unsigned char *usTwoColor);
/*对二维像素页缓存的某一窗口刷字符串(自定义)*/
void PixelPageCache2D_Text_customize(unsigned short usWidthWin, unsigned short usHeightWin, short usX, short usY, enum UI28_MATRIX_e_ FontSize, char *usText);

#if (UI28_SCREEN_ENABLED == 1)
void UI28_Init(void);		  /*2.8寸屏幕初始化*/
void UI28_Clear_Screen(void); /*2.8寸屏幕清屏，并关闭所有模块，一般切换界面时调用*/
void UI28_Polling(void);	  /*2.8寸屏幕轮询10ms*/

/**
 * @brief  字符像素宽计算
 * @param  usText ：字符串地址
 * @param  FontSize ：字符高度
 * @retval 宽度
 */
unsigned short UI28_character_pixel_width_calculation(enum UI28_MATRIX_e_ FontSize, char *usText);
// 通用动画插值函数(渐近动画)
// a:当前值地址
// a_trg:目标值地址
// n:(1：增量=差值/2)(2：增量=差值/4)(3：增量=差值/8)(4：增量=差值/16)(5：增量=差值/32).....
void UI28_anima(int *a, int *a_trg, int n);
// 0-128范围的透明度混合函数
// bg:背景色
// fg:前景色
// alpha：透明度0-128
// 返回：混合后的颜色
unsigned short alpha_blend_rgb565(unsigned short bg, unsigned short fg, unsigned char alpha);
// // 变化——降
// void ui28_change_fall(unsigned char max, unsigned char min, unsigned char price);
// // 变化——升
// void ui28_change_rise(unsigned char max, unsigned char min, unsigned char price);

#endif
#endif
