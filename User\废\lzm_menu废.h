#ifndef _LZM_MENU_
#define _LZM_MENU_

/*菜单项文本结构体*/
typedef struct MENU_TEXT_
{
	const char *name; /*菜单显示文本*/
	char *value;	  /*菜单数值文本*/
} MENU_TEXT;


#define MENU_CHAR_1        \
	{                      \
		{"1.菜单1", 0}, \
		{"2.菜单1", 0}, \
		{"3.菜单1", 0}, \
		{"4.菜单1", 0}  \
	}
#define MENU_CHAR_2        \
	{                      \
		{"1.菜单2", 0}, \
		{"2.菜单2", 0}, \
		{"3.菜单2", 0}, \
		{"4.菜单2", 0}, \
		{"5.菜单2", 0}, \
		{"6.菜单2", 0}, \
		{"7.菜单2", 0}, \
		{"8.菜单2", 0}, \
		{"9.菜单2", 0}  \
	}

	/*注意：默认光标R角为边距*/
#define LZM_MENU_ITEM_LIST                                                                                                                                                                                   \
	LZM_MENU_DEBUG_X(1 /*名字*/, 20 /*菜单左上角X坐标*/, 30 /*菜单左上角Y坐标*/, 280 /*菜单宽*/, 20 /*字高*/, 5 /*边距*/, 6 /*显示行数*/, 220 /*数值位置X坐标*/, 5 /*滚动条宽*/, MENU_CHAR_1 /* 菜单表 */) \
	LZM_MENU_DEBUG_X(2 /*名字*/, 20 /*菜单左上角X坐标*/, 30 /*菜单左上角Y坐标*/, 280 /*菜单宽*/, 20 /*字高*/, 5 /*边距*/, 6 /*显示行数*/, 220 /*数值位置X坐标*/, 5 /*滚动条宽*/, MENU_CHAR_2 /* 菜单表 */)

/* 菜单ID */
enum LZM_MENU_e_
{
#define LZM_MENU_DEBUG_X(Lname, Lx, Ly, bLW, LH, LS, LR, Lvx, Lb, ...) lzm_##Lname##_menu,
	LZM_MENU_ITEM_LIST
#undef LZM_MENU_DEBUG_X
		LZM_MENU_ITEM_NUM,
};



#endif
