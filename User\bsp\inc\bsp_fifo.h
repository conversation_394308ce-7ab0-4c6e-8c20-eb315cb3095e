#ifndef __BSP_FIFO_H
#define __BSP_FIFO_H
#include "bsp.h"

#define FIFOADD_ITEM_LIST          \
	FIFOADD_DEBUG_X(EE_SA /* FIFO任务码名称 */, SaveParameData() /* 函数 */) /* EE保存 */ \
	FIFOADD_DEBUG_X(EE_BA /* FIFO任务码名称 */, BackupsParameData() /* 函数 */) /* EE备份 */ \
	FIFOADD_DEBUG_X(Wa_H_SWR /* FIFO任务码名称 */, Wa_H_from_open_to_closed() /* 函数 */) /* 洗高从开到关触发一次 */ \
	FIFOADD_DEBUG_X(Wa_H_SWF /* FIFO任务码名称 */, Wa_H_from_closed_to_open() /* 函数 */) /* 洗高从关到开触发一次 */ \
	FIFOADD_DEBUG_X(Wa_L_SWR /* FIFO任务码名称 */, Wa_L_from_open_to_closed() /* 函数 */) /* 洗低从开到关触发一次 */ \
	FIFOADD_DEBUG_X(Wa_L_SWF /* FIFO任务码名称 */, Wa_L_from_closed_to_open() /* 函数 */) /* 洗低从关到开触发一次 */ \
	FIFOADD_DEBUG_X(RI_H_SWR /* FIFO任务码名称 */, RI_H_from_open_to_closed() /* 函数 */) /* 漂高从开到关触发一次 */ \
	FIFOADD_DEBUG_X(RI_H_SWF /* FIFO任务码名称 */, RI_H_from_closed_to_open() /* 函数 */) /* 漂高从关到开触发一次 */ \
	FIFOADD_DEBUG_X(RI_L_SWR /* FIFO任务码名称 */, RI_L_from_open_to_closed() /* 函数 */) /* 漂低从开到关触发一次 */ \
	FIFOADD_DEBUG_X(RI_L_SWF /* FIFO任务码名称 */, RI_L_from_closed_to_open() /* 函数 */) /* 漂低从关到开触发一次 */ \
	FIFOADD_DEBUG_X(DOOR_SWR /* FIFO任务码名称 */, Door_from_open_to_closed() /* 函数 */) /* 机门从开到关触发一次 */ \
	FIFOADD_DEBUG_X(DOOR_SWF /* FIFO任务码名称 */, Door_from_closed_to_open() /* 函数 */) /* 机门从关到开触发一次 */
/*
	推荐使用enum, 不用#define，原因：
	(1) 便于新增键值,方便调整顺序，使代码看起来舒服点
	(2) 编译器可帮我们避免键值重复。
*/
enum FIFO_ENUM
{
	FIFO_NONE = 0, /* 0 表示任务事件 */
#if KEY_SWITCH
#define KEY_DEBUG_X(name, help, LTime, RSpeed) name##_DOWN, /* 键按下 */ name##_UP, /* 键弹起 */ name##_REPEAT, /* 键连发 */ name##_LONG, /* 键长按 */
	KEY_ITEM_LIST
#undef KEY_DEBUG_X
#endif
	
#if SEQUENTIAL_KEY_SWITCH
#define KEY_SEQ_DEBUG_X(name, ...) name##_SEQ,
		KEY_SEQ_ITEM_LIST
#undef KEY_SEQ_DEBUG_X
#endif

#if MULTIPLE_KEY_STROKES
#define KEY_MUL_DEBUG_X(name, KID_K_, NUM_) name##_MUL,
			KEY_MUL_ITEM_LIST
#undef KEY_MUL_DEBUG_X
#endif

#if SIGNAL_FIXED_FIFO_SWITCH
#define SIGNAL_DEBUG_X(name, gpio_, pin_, open, rise_d, fall_d) name##_SIG_RI, name##_SIG_FA,
				SIGNAL_ITEM_LIST
#undef SIGNAL_DEBUG_X
#endif

#if BEEP_SWITCH
#define BEEP_DEBUG_X(name, Cycle, ...) name##_BEEP,
					BEEP_ITEM_LIST
#undef BEEP_DEBUG_X
#endif

#define FIFOADD_DEBUG_X(name, ...) name,
						FIFOADD_ITEM_LIST
#undef FIFOADD_DEBUG_X

};

/* FIFO用到变量 */
#define FIFO_SIZE 30
typedef struct
{
	uint8_t Buf[FIFO_SIZE]; /* 键值缓冲区 */
	uint8_t Read;			/* 缓冲区读指针1 */
	uint8_t Write;			/* 缓冲区写指针 */
							// uint8_t Read2;				/* 缓冲区读指针2 */
} bsp_fifo_t;

/* 将1个任务压入FIFO缓冲区 */
void bsp_Putfifo(uint8_t _Code);
/* 从FIFO缓冲区读取一个任务 */
uint8_t bsp_Getfifo(void);
/* 确认任务在FIFO缓冲区是否已存在 */
uint8_t bsp_Queryfifo(uint8_t _Code);
/* 清空FIFO缓冲区 */
void bsp_Clearfifo(void);
/* 处理任务事件。非阻塞，被s1ms周期性的调用 */
void Handling_fifo_Events1ms(void);
#endif
