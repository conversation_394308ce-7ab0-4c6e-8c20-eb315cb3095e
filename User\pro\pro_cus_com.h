#ifndef _PRO_CUS_COM_H
#define _PRO_CUS_COM_H

#define QR_CODE_ADDRESS 0x0803E800 /*二维码地址*/

extern volatile uint8_t Not_receiving_correct_data_count; // 正常是大于0的，等于0表示通讯异常但前板有电
extern volatile uint8_t GearOutput[2];                    // 0:洗涤剂 1:干燥剂
extern volatile uint32_t InformationOutput;               // 开关量输出

extern volatile uint8_t Dis_type;           // 洗碗机类型
extern volatile int16_t InformationTemp[2]; // 0:洗涤温度 1:漂洗温度
extern volatile uint16_t InformationSignal; // 开关量信号

extern volatile uint8_t g_rfid_recv_lst_len; // 队列数据长度

/* 洗碗机类型 */
enum Dis_type_e_
{
    Dis_t_1,//5继电器，2调速（催干剂 洗涤剂）
    Dis_t_2,//7继电器，无调速
};

// 后板信息数据流解析，非阻塞
void Backboard_data_receive(void);
/*二维码字节流处理*/
void QR_code_byte_stream_processing(uint8_t _data);

#endif
