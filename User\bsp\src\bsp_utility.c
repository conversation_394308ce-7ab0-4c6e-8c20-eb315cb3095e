#include "bsp.h"
/*
*********************************************************************************************************
*	函 数 名: util_bcd2dec
*	形    参: 十进制值
*	返 回 值: BCD值
*	功能说明: 十进制转BCD
*********************************************************************************************************
*/
unsigned char DEC_TO_BCD(unsigned char dec)
{
     unsigned char bcd = 0;
     bcd = (dec / 10) << 4;
     bcd |= dec % 10;
     return bcd;
}

/*
*********************************************************************************************************
*	函 数 名: BCD_TO_DEC
*	形    参: BCD值
*	返 回 值: 十进制值
*	功能说明: BCD转十进制
*********************************************************************************************************
*/
unsigned char BCD_TO_DEC(unsigned char bcd)
{
     unsigned char dec = 0;
     dec = ((bcd >> 4) & 0x0f) * 10;
     dec += bcd & 0x0f;
     return dec;
}

/*
*********************************************************************************************************
*	函 数 名: Binary_search_asc
*	形    参: arr:需要进行查找的数组
*	形    参: len:数组长度
*	形    参: value:要查找的值
*	返 回 值: 数组位置(插入点的前一个位置),若比第一个值还小返回-1
*	功能说明: 二分查找 升序
*********************************************************************************************************
*/
short Binary_search_asc(unsigned short const *arr, unsigned short len, unsigned short value)
{
     short low = 0;
     short high = len - 1;
     short mid;
     short result = -1; /* 初始化前一个下标为-1 */

     while (low <= high)
     {
          mid = low + (high - low) / 2; /* 避免整数溢出 */

          if (arr[mid] == value)
          {
               return mid; /* 找到目标值，返回下标 */
          }
          else if (arr[mid] < value)
          {
               low = mid + 1; /* 目标值在mid右侧 */
               result = mid;  /* 更新前一个下标 */
          }
          else
          {
               high = mid - 1; /* 目标值在mid左侧 */
          }
     }

     /* 未找到目标值，返回前一个下标 */
     /* 此时result指向最后一个小于value的位置（如果存在） */
     return result;
}

/*
*********************************************************************************************************
*	函 数 名: Binary_search_desc
*	形    参: arr:需要进行查找的数组
*	形    参: len:数组长度
*	形    参: value:要查找的值
*	返 回 值: 数组位置(插入点的前一个位置),若比第一个值还大返回-1
*	功能说明: 二分查找 降序
*********************************************************************************************************
*/
short Binary_search_desc(unsigned short const *arr, unsigned short len, unsigned short value)
{
     short low = 0;
     short high = len - 1;
     short mid;
     short result = -1; /* 初始化结果为-1，表示未找到 */

     while (low <= high)
     {
          mid = low + (high - low) / 2; /* 防止整数溢出 */

          if (arr[mid] == value)
          {
               return mid; /* 找到目标值，返回下标 */
          }
          else if (arr[mid] < value)
          {
               high = mid - 1; /* 目标值在mid的左侧，因为数组是降序的 */
          }
          else
          {
               low = mid + 1; /* 目标值在mid的右侧或就是mid的后一个位置，因为数组是降序的 */
               result = mid;  /* 更新插入点的前一个下标 */
          }
     }

     /* 循环结束时，low > high，表示未找到目标值 */
     /* 此时result保存的是应该插入目标值的前一个下标 */
     return result;
}

// 卡尔曼增益计算
#define Kalman_Gain_Calculation(E_est, E_mea) ((E_est) / ((E_est) + (E_mea)))

/*
*********************************************************************************************************
*	函 数 名: Kalman_X_K_Calculation
*	形    参: kalman_Structure:卡尔曼结构体
*	形    参: measured_value:估计初始值
*	形    参: E_est:初始化估计误差（初始化不可为 0 ）
*	形    参: E_mea:初始化测量误差（传感器误差+AD误差）
*	返 回 值: 数组位置(插入点的前一个位置),若比第一个值还大返回-1
*	功能说明: 初始化卡尔曼结构体
*********************************************************************************************************
*/
void Kaleman_Parameter_Init(Kalman_InitDef *kalman_Structure, float measured_value, float E_mea, float E_est)
{
     kalman_Structure->X_k = measured_value; // 将第一个测量值赋值给估计
     kalman_Structure->E_mea = E_mea;
     kalman_Structure->E_est = E_est;
}

/*
*********************************************************************************************************
*	函 数 名: Kalman_X_K_Calculation
*	形    参: kalman_Structure:卡尔曼结构体
*	形    参: measured_value:估计值
*	返 回 值: 数组位置(插入点的前一个位置),若比第一个值还大返回-1
*	功能说明: 水平一维卡尔曼估计值计算
*********************************************************************************************************
*/
float Kalman_X_K_Calculation(Kalman_InitDef *kalman_Structure, float measured_value)
{
     float Kalman_Gain; // 卡尔曼增益

     // 迭代更新增益
     Kalman_Gain = Kalman_Gain_Calculation(kalman_Structure->E_est, kalman_Structure->E_mea);
     // 迭代更新估计误差（逐渐趋于 0 ）
     kalman_Structure->E_est = (1.0F - Kalman_Gain) * kalman_Structure->E_est;
     // 迭代更新估计值
     kalman_Structure->X_k = kalman_Structure->X_k + Kalman_Gain * (measured_value - kalman_Structure->X_k);

     return kalman_Structure->X_k;
}

/*
*********************************************************************************************************
*	函 数 名: SmoothingFilter
*	形    参: filter:窗口平滑滤波结构体
*	形    参: _num:传入的数值
*	返 回 值: 平均滤波后的值
*	功能说明: 窗口平滑滤波
*********************************************************************************************************
*/
short SmoothingFilter(FilterObjectType *filter, short _num)
{
     unsigned char i;
     int result = 0;
     filter->buffer[filter->position] = _num;
     if (filter->position > filter->bufCount) // 更新轮询数值
     {
          filter->position = 0;
     }
     else
     {
          filter->position++;
     }

     for (i = 0; i < filter->bufCount; i++)
     {
          result += filter->buffer[i];
     }

     result /= filter->bufCount;

     return (short)result;
}

/*
*********************************************************************************************************
*	函 数 名: filter1Step
*	形    参: a:上一个值
*	形    参: b:下一个值
*	形    参: sens:降低的权重
*	返 回 值: 滤波后的值
*	功能说明: 降权重滤波
*********************************************************************************************************
*/
short filter1Step(short a, short b, short sens)
{
     return a + (b / sens) - (a / sens);
}
