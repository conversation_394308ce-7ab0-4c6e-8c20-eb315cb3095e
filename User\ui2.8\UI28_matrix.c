#include "UI28_matrix.h"

// 根据字模ID与所在位置获取 ASCII字符
char *UI28_RetrieveASCIIcharacters(enum UI28_MATRIX_e_ FontSize, unsigned int mid)
{
    switch (FontSize)
    {
#define UI28_MATRIX_DEBUG_X(heigh) \
    case ui28_char_##heigh:        \
        return (char *)Font##heigh##_Ascii[mid].txt;
        UI28_MATRIX_ITEM_LIST
#undef UI28_MATRIX_DEBUG_X
    default:
        return 0;
    }
}
// 根据字模ID与所在位置获取 汉语字符
char *UI28_RetrieveCHINESEcharacters(enum UI28_MATRIX_e_ FontSize, unsigned int mid)
{
    switch (FontSize)
    {
#define UI28_MATRIX_DEBUG_X(heigh) \
    case ui28_char_##heigh:        \
        return (char *)Font##heigh##_Chinese[mid].txt;
        UI28_MATRIX_ITEM_LIST
#undef UI28_MATRIX_DEBUG_X
    default:
        return 0;
    }
}
// 获取字模地址
unsigned char *UI28_FontAddressAcquisition(char *character_, enum UI28_MATRIX_e_ FontSize)
{
    if (character_ == 0)
    {
        /* 字符串地址为空 */
        return 0;
    }

    if (character_[0] == 0)
    {
        /* 字符结尾 */
        return 0;
    }

    if (FontSize >= UI28_MATRIX_ITEM_NUM)
    {
        /* 字高不在范围，返回空地址 */
        return 0;
    }

    int left = 0;
    int right = 0;
    char *dat_;
    int mid = 0;
    int cmp = 0;

    if ((character_[0] & 0x80) == 0) // UTF-8 ASCII判断
    {
        // 在ASCII数组中二分查找
        switch (FontSize)
        {
#define UI28_MATRIX_DEBUG_X(heigh)                         \
    case ui28_char_##heigh:                                \
        right = ui28_matrix_ascii_##heigh##_quan_list - 1; \
        break;
            UI28_MATRIX_ITEM_LIST
#undef UI28_MATRIX_DEBUG_X
        default:
            break;
        }

        while (left <= right)
        {
            mid = left + ((right - left) >> 1);
            dat_ = UI28_RetrieveASCIIcharacters(FontSize, mid);
            cmp = strncmp(dat_, character_, 1);

            if (cmp < 0)
            {
                left = mid + 1;
            }
            else if (cmp > 0)
            {
                right = mid - 1;
            }
            else
            {
                return (unsigned char *)(dat_ + 1);
            }
        }
    }
    else
    {
        // 在汉字数组中二分查找
        switch (FontSize)
        {
#define UI28_MATRIX_DEBUG_X(heigh)                           \
    case ui28_char_##heigh:                                  \
        right = ui28_matrix_chinese_##heigh##_quan_list - 1; \
        break;
            UI28_MATRIX_ITEM_LIST
#undef UI28_MATRIX_DEBUG_X
        default:
            break;
        }

        while (left <= right)
        {
            mid = left + ((right - left) >> 1);
            dat_ = UI28_RetrieveCHINESEcharacters(FontSize, mid);
            cmp = memcmp(dat_, character_, 3);

            if (cmp < 0)
            {
                left = mid + 1;
            }
            else if (cmp > 0)
            {
                right = mid - 1;
            }
            else
            {
                return (unsigned char *)(dat_ + 3);
            }
        }
    }
    return 0; // 未找到返回空指针
}

#if (UI28_TEXT_GRADIENT_ENABLED == 1)
volatile uint_Gradient_t ui28_Text_Gradient_display = 0;      /* 文本渐变显示标志位 */
volatile uint_Gradient_t ui28_Text_Gradient_Synchronized = 0; /* 文本渐变同步标志位 */

/* 字符串地址表 */
volatile char *ui28_Text_Gradient_add_list[UI28_TEXT_GRADIENT_ITEM_NUM] =
    {
#define UI28_TEXT_GRADIENT_DEBUG_X(Tname, Tx, Ty, Tw, Th, T0, T1, Tmin, Tmax, Tc) 0,
        UI28_TEXT_GRADIENT_ITEM_LIST
#undef UI28_TEXT_GRADIENT_DEBUG_X
};

/* X坐标表 */
const unsigned short ui28_Text_Gradient_Tx_list[UI28_TEXT_GRADIENT_ITEM_NUM] =
    {
#define UI28_TEXT_GRADIENT_DEBUG_X(Tname, Tx, Ty, Tw, Th, T0, T1, Tmin, Tmax, Tc) Tx,
        UI28_TEXT_GRADIENT_ITEM_LIST
#undef UI28_TEXT_GRADIENT_DEBUG_X
};

/* Y坐标表 */
const unsigned short ui28_Text_Gradient_Ty_list[UI28_TEXT_GRADIENT_ITEM_NUM] =
    {
#define UI28_TEXT_GRADIENT_DEBUG_X(Tname, Tx, Ty, Tw, Th, T0, T1, Tmin, Tmax, Tc) Ty,
        UI28_TEXT_GRADIENT_ITEM_LIST
#undef UI28_TEXT_GRADIENT_DEBUG_X
};

/* 窗口宽度 */
const unsigned short ui28_Text_Gradient_Tw_list[UI28_TEXT_GRADIENT_ITEM_NUM] =
    {
#define UI28_TEXT_GRADIENT_DEBUG_X(Tname, Tx, Ty, Tw, Th, T0, T1, Tmin, Tmax, Tc) Tw,
        UI28_TEXT_GRADIENT_ITEM_LIST
#undef UI28_TEXT_GRADIENT_DEBUG_X
};

/* 字高 */
const unsigned char ui28_Text_Gradient_Th_list[UI28_TEXT_GRADIENT_ITEM_NUM] =
    {
#define UI28_TEXT_GRADIENT_DEBUG_X(Tname, Tx, Ty, Tw, Th, T0, T1, Tmin, Tmax, Tc) Th,
        UI28_TEXT_GRADIENT_ITEM_LIST
#undef UI28_TEXT_GRADIENT_DEBUG_X
};

/* 字模ID */
const unsigned char ui28_Text_Gradient_Tm_list[UI28_TEXT_GRADIENT_ITEM_NUM] =
    {
#define UI28_TEXT_GRADIENT_DEBUG_X(Tname, Tx, Ty, Tw, Th, T0, T1, Tmin, Tmax, Tc) ui28_char_##Th,
        UI28_TEXT_GRADIENT_ITEM_LIST
#undef UI28_TEXT_GRADIENT_DEBUG_X
};

/* 背景颜色 */
#if (UI28_TEXT_GRADIENT_VARIABLE_COLOR_ENABLED == 1)
volatile unsigned short ui28_Text_Gradient_T0_list[UI28_TEXT_GRADIENT_ITEM_NUM] =
#else
const unsigned short ui28_Text_Gradient_T0_list[UI28_TEXT_GRADIENT_ITEM_NUM] =
#endif
    {
#define UI28_TEXT_GRADIENT_DEBUG_X(Tname, Tx, Ty, Tw, Th, T0, T1, Tmin, Tmax, Tc) T0,
        UI28_TEXT_GRADIENT_ITEM_LIST
#undef UI28_TEXT_GRADIENT_DEBUG_X
};

/* 字颜色 */
#if (UI28_TEXT_GRADIENT_VARIABLE_COLOR_ENABLED == 1)
volatile unsigned short ui28_Text_Gradient_T1_list[UI28_TEXT_GRADIENT_ITEM_NUM] =
#else
const unsigned short ui28_Text_Gradient_T0_list[UI28_TEXT_GRADIENT_ITEM_NUM] =
#endif
    {
#define UI28_TEXT_GRADIENT_DEBUG_X(Tname, Tx, Ty, Tw, Th, T0, T1, Tmin, Tmax, Tc) T1,
        UI28_TEXT_GRADIENT_ITEM_LIST
#undef UI28_TEXT_GRADIENT_DEBUG_X
};

/* 渐变最小值 */
const unsigned char ui28_Text_Gradient_Tmin_list[UI28_TEXT_GRADIENT_ITEM_NUM] =
    {
#define UI28_TEXT_GRADIENT_DEBUG_X(Tname, Tx, Ty, Tw, Th, T0, T1, Tmin, Tmax, Tc) Tmin,
        UI28_TEXT_GRADIENT_ITEM_LIST
#undef UI28_TEXT_GRADIENT_DEBUG_X
};

/* 渐变最大值 */
const unsigned char ui28_Text_Gradient_Tmax_list[UI28_TEXT_GRADIENT_ITEM_NUM] =
    {
#define UI28_TEXT_GRADIENT_DEBUG_X(Tname, Tx, Ty, Tw, Th, T0, T1, Tmin, Tmax, Tc) Tmax,
        UI28_TEXT_GRADIENT_ITEM_LIST
#undef UI28_TEXT_GRADIENT_DEBUG_X
};

/* 渐变方式 */
const unsigned char ui28_Text_Gradient_Tc_list[UI28_TEXT_GRADIENT_ITEM_NUM] =
    {
#define UI28_TEXT_GRADIENT_DEBUG_X(Tname, Tx, Ty, Tw, Th, T0, T1, Tmin, Tmax, Tc) Tc,
        UI28_TEXT_GRADIENT_ITEM_LIST
#undef UI28_TEXT_GRADIENT_DEBUG_X
};

/* 渐变值 */
volatile unsigned char ui28_Text_Gradient_alpha_list[UI28_TEXT_GRADIENT_ITEM_NUM] =
    {
#define UI28_TEXT_GRADIENT_DEBUG_X(Tname, Tx, Ty, Tw, Th, T0, T1, Tmin, Tmax, Tc) Tmax,
        UI28_TEXT_GRADIENT_ITEM_LIST
#undef UI28_TEXT_GRADIENT_DEBUG_X
};

/* 渐变步 */
volatile unsigned char ui28_Text_Gradient_steps_list[UI28_TEXT_GRADIENT_ITEM_NUM] =
    {
#define UI28_TEXT_GRADIENT_DEBUG_X(Tname, Tx, Ty, Tw, Th, T0, T1, Tmin, Tmax, Tc) 0,
        UI28_TEXT_GRADIENT_ITEM_LIST
#undef UI28_TEXT_GRADIENT_DEBUG_X
};

/* 文本渐变显示开关（关实际只是不刷新了） */
void UI28_display_onoff_text_gradient(enum UI28_TEXT_GRADIENT_e_ GRADIENT_, unsigned char _onoff_)
{
    if (_onoff_ == 0)
    {
        ui28_Text_Gradient_display &= ~(1UL << GRADIENT_);
    }
    else
    {
        ui28_Text_Gradient_display |= (1UL << GRADIENT_);
    }
}

/* 设置文本地址 */
void UI28_set_up_text_gradient_add(enum UI28_TEXT_GRADIENT_e_ GRADIENT_, volatile char *txet_add)
{
    ui28_Text_Gradient_add_list[GRADIENT_] = txet_add;
}

/* 初始化渐变值 */
void UI28_init_text_gradient_alpha(enum UI28_TEXT_GRADIENT_e_ GRADIENT_)
{
    ui28_Text_Gradient_alpha_list[GRADIENT_] = ui28_Text_Gradient_Tmax_list[GRADIENT_];
    ui28_Text_Gradient_steps_list[GRADIENT_] = 0;
}

/* 更新文本渐变 */
void UI28_update_text_gradient(enum UI28_TEXT_GRADIENT_e_ GRADIENT_)
{
    /*对二维像素页缓存-开窗(自定义)（窗口内bit全为0）*/
    PixelPageCache2D_OpenWin_customize(ui28_Text_Gradient_Tw_list[GRADIENT_], ui28_Text_Gradient_Th_list[GRADIENT_], 0);
    // 对二维像素页缓存的某一窗口刷字符串(自定义)
    PixelPageCache2D_Text_customize(ui28_Text_Gradient_Tw_list[GRADIENT_], ui28_Text_Gradient_Th_list[GRADIENT_], 0, 0, ui28_Text_Gradient_Tm_list[GRADIENT_], (char *)ui28_Text_Gradient_add_list[GRADIENT_]);

    switch (ui28_Text_Gradient_Tc_list[GRADIENT_])
    {
    case ui28_fall:
        /* 降 */
        if (ui28_Text_Gradient_alpha_list[GRADIENT_] > ui28_Text_Gradient_Tmin_list[GRADIENT_])
        {
            ui28_Text_Gradient_alpha_list[GRADIENT_]--;
        }
        else
        {
            ui28_Text_Gradient_alpha_list[GRADIENT_] = ui28_Text_Gradient_Tmax_list[GRADIENT_];
        }
        break;
    case ui28_rise:
        /* 升 */
        if (ui28_Text_Gradient_alpha_list[GRADIENT_] < ui28_Text_Gradient_Tmax_list[GRADIENT_])
        {
            ui28_Text_Gradient_alpha_list[GRADIENT_]++;
        }
        else
        {
            ui28_Text_Gradient_alpha_list[GRADIENT_] = ui28_Text_Gradient_Tmin_list[GRADIENT_];
        }
        break;
    case ui28_cycle:
        /* 循环 */
        if (ui28_Text_Gradient_steps_list[GRADIENT_] == 0) // 递减
        {
            if (ui28_Text_Gradient_alpha_list[GRADIENT_] > ui28_Text_Gradient_Tmin_list[GRADIENT_])
            {
                ui28_Text_Gradient_alpha_list[GRADIENT_]--;
            }
            else
            {
                ui28_Text_Gradient_steps_list[GRADIENT_] = 1;
            }
        }
        else // 递增
        {
            if (ui28_Text_Gradient_alpha_list[GRADIENT_] < ui28_Text_Gradient_Tmax_list[GRADIENT_])
            {
                ui28_Text_Gradient_alpha_list[GRADIENT_]++;
            }
            else
            {
                ui28_Text_Gradient_steps_list[GRADIENT_] = 0;
            }
        }
        break;
    default:
        break;
    }

    if (ui28_Text_Gradient_Synchronized /* 文本渐变同步标志位 */ & (1 << GRADIENT_))
    {
        for (size_t i = 0; i < UI28_TEXT_GRADIENT_ITEM_NUM; i++)
        {
            if (ui28_Text_Gradient_Synchronized /* 文本渐变同步标志位 */ & (1 << i) && ui28_Text_Gradient_display /* 文本渐变显示标志位 */ & (1 << i))
            {
                ui28_Text_Gradient_alpha_list[i] = ui28_Text_Gradient_alpha_list[GRADIENT_];
            }
        }
    }

    // 对ILI9340X显示器的某一窗口刷双色图
    ILI9340X_TwoColorChart(ui28_Text_Gradient_Tx_list[GRADIENT_], ui28_Text_Gradient_Ty_list[GRADIENT_], ui28_Text_Gradient_Tw_list[GRADIENT_], ui28_Text_Gradient_Th_list[GRADIENT_], ui28_Text_Gradient_T0_list[GRADIENT_], alpha_blend_rgb565(ui28_Text_Gradient_T0_list[GRADIENT_], ui28_Text_Gradient_T1_list[GRADIENT_], ui28_Text_Gradient_alpha_list[GRADIENT_]), (unsigned char *)&PixelPageCache2D[0][0]);
}

/* 设置文本同步渐变 */
void UI28_set_synchronous_gradient_text_gradient(enum UI28_TEXT_GRADIENT_e_ GRADIENT_, unsigned char _onoff_)
{
    if (_onoff_ == 0)
    {
        ui28_Text_Gradient_Synchronized &= ~(1UL << GRADIENT_);
    }
    else
    {
        ui28_Text_Gradient_Synchronized |= (1UL << GRADIENT_);
    }
}

#if (UI28_TEXT_GRADIENT_VARIABLE_COLOR_ENABLED == 1)
/* 更改颜色 */
void UI28_change_color_album_text_gradient(enum UI28_TEXT_GRADIENT_e_ GRADIENT_, unsigned short color_0, unsigned short color_1)
{
    /* 背景颜色 */
    ui28_Text_Gradient_T0_list[GRADIENT_] = color_0;
    /* 字颜色 */
    ui28_Text_Gradient_T1_list[GRADIENT_] = color_1;
}
#endif
#endif
