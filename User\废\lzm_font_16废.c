#include "lzmUI.h"
struct FONT16_ASCII
{
    char txt[1];
    unsigned char dat[40];
};

struct FONT16_ASCII const Font16_Ascii[] =
{
  "!",0x00,0x00,0x00,0x00,0x0c,0x00,0x0c,0x00,0x0c,0x00,0x0c,0x00,0x0c,0x00,0x0c,0x00, /* 0 */
       0x0c,0x00,0x0c,0x00,0x0c,0x00,0x08,0x00,0x08,0x00,0x00,0x00,0x0c,0x00,0x0c,0x00,
       0x0c,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
  "\"",0x00,0x00,0x00,0x00,0x00,0x00,0x12,0x00,0x12,0x00,0x12,0x00,0x12,0x00,0x00,0x00, /* 1 */
       0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
       0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
  "#",0x00,0x00,0x00,0x00,0x00,0x00,0x11,0x00,0x11,0x00,0x11,0x00,0x11,0x00,0xff,0x80, /* 2 */
       0x13,0x00,0x32,0x00,0x32,0x00,0x22,0x00,0xff,0x80,0x22,0x00,0x22,0x00,0x26,0x00,
       0x66,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
  "$",0x00,0x00,0x00,0x00,0x08,0x00,0x1e,0x00,0x3f,0x00,0x6b,0x00,0x69,0x00,0x68,0x00, /* 3 */
       0x38,0x00,0x1c,0x00,0x0f,0x00,0x0b,0x00,0x69,0x00,0x69,0x00,0x69,0x00,0x3f,0x00,
       0x1e,0x00,0x08,0x00,0x08,0x00,0x00,0x00,
};

struct FONT16_CHINESE
{
    char txt[3];
    unsigned char dat[60];
};

struct FONT16_CHINESE const Font16_Chinese[] =
{
  "打",0x00,0x00,0x00,0x0c,0x00,0x00,0x0c,0x00,0x00,0x0c,0x7f,0xe0,0x0c,0x02,0x00,0x7f, /* 0 */
       0x82,0x00,0x0c,0x02,0x00,0x0c,0x02,0x00,0x0c,0x02,0x00,0x0d,0x82,0x00,0x0f,0x02,
       0x00,0x7c,0x02,0x00,0x4c,0x02,0x00,0x0c,0x02,0x00,0x0c,0x02,0x00,0x0c,0x02,0x00,
       0x0c,0x06,0x00,0x3c,0x1e,0x00,0x10,0x0c,0x00,0x00,0x00,0x00,
  "鹅",0x00,0x00,0x00,0x03,0x06,0x00,0x0b,0x04,0x00,0x7f,0xdf,0xc0,0x13,0x58,0x40,0x13, /* 1 */
       0x7e,0x40,0x13,0x1b,0x40,0x7f,0xfa,0x40,0x13,0x19,0xc0,0x13,0x19,0x80,0x11,0x78,
       0x00,0x1d,0x5f,0xe0,0x71,0xc0,0x20,0x51,0x80,0x20,0x11,0xbf,0x60,0x13,0xc0,0x60,
       0x16,0xf0,0x60,0x70,0x61,0xe0,0x20,0x01,0xc0,0x00,0x00,0x00,
  "发",0x00,0x00,0x00,0x00,0x44,0x00,0x0c,0x46,0x00,0x08,0x43,0x00,0x08,0xc1,0x00,0x18, /* 2 */
       0xc0,0x00,0x1f,0xff,0xc0,0x00,0x80,0x00,0x01,0x80,0x00,0x01,0xff,0x80,0x03,0xff,
       0x00,0x07,0x83,0x00,0x06,0xc6,0x00,0x0c,0x44,0x00,0x18,0x68,0x00,0x70,0x30,0x00,
       0x20,0xfc,0x00,0x07,0x87,0xe0,0x0e,0x00,0xc0,0x00,0x00,0x00,
  "否",0x00,0x00,0x00,0x00,0x00,0x00,0x3f,0xff,0xc0,0x00,0x70,0x00,0x00,0xe0,0x00,0x01, /* 3 */
       0xc0,0x00,0x03,0xce,0x00,0x0e,0x47,0x80,0x3c,0x41,0xe0,0x70,0x40,0x60,0x00,0x40,
       0x00,0x0f,0xff,0x00,0x08,0x01,0x00,0x08,0x01,0x00,0x08,0x01,0x00,0x08,0x01,0x00,
       0x0f,0xff,0x00,0x08,0x01,0x00,0x08,0x01,0x00,0x00,0x00,0x00,
};
