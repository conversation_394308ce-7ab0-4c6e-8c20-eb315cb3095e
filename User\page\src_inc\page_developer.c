#include "page_developer.h"

/*
*********************************************************************************************************
*	函 数 名: page_root_load
*	形    参: 无
*	返 回 值: 无
*	功能说明: 根界面切换时调用
*********************************************************************************************************
*/
void page_developer_root_load(void)
{
}
/*
*********************************************************************************************************
*	函 数 名: page_view_load
*	形    参: 无
*	返 回 值: 无
*	功能说明: 交互界面切换时调用
*********************************************************************************************************
*/
void page_developer_view_load(void)
{
    UI28_Clear_Screen();                   /*2.8寸屏幕清屏，并关闭所有模块，一般切换界面时调用*/
    ILI9340X_Clear(0, 0, 320, 240, WHITE); /*对ILI9340X显示器的某一窗口以某种颜色进行清屏*/

    PixelPageCache2D_Text_customize(140, 20, 0, 0, ui28_char_20, (char *)&_sys_parames.VERSION[0]); /*对二维像素页缓存的某一窗口刷字符串(自定义)*/
    ILI9340X_TwoColorChart(0, 50, 140, 20, WHITE, BLACK, (unsigned char *)&PixelPageCache2D[0][0]); /*对ILI9340X显示器的某一窗口刷双色图*/

    PixelPageCache2D_Text_customize(60, 20, 0, 0, ui28_char_20, (char *)&_sys_parames.DATE[0]);    /*对二维像素页缓存的某一窗口刷字符串(自定义)*/
    ILI9340X_TwoColorChart(0, 75, 60, 20, WHITE, BLACK, (unsigned char *)&PixelPageCache2D[0][0]); /*对ILI9340X显示器的某一窗口刷双色图*/
}
/*
*********************************************************************************************************
*	函 数 名: page_update
*	形    参: 无
*	返 回 值: 无
*	功能说明: 为交互界面时，会周期调用
*********************************************************************************************************
*/
void page_developer_update(void)
{
}
