/*******************************************************************************
 * 业务参数管理器
 ******************************************************************************/
#include "bsp.h"

/* 单位文本 */
volatile char Unit_Text1[]="秒";
volatile char Unit_Text2[]="分";
volatile char Unit_Text3[]="时";
volatile char Unit_Text4[]="℃";
volatile char Unit_Text5[]="℉";
volatile char Unit_Text6[]="次";
volatile char Unit_Text7[]="%";
volatile char Unit_Text8[]="‰";
volatile char Unit_Text9[]="#";

/* 用户参数配置文件 */
/* 初始值 */
const parame_t parames_config_init =
    {
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) __VA_ARGS__,
        PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
};
/* 下限 */
const parame_l_t parames_config_min =
    {
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) min,
        PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
};
/* 上限 */
const parame_l_t parames_config_max =
    {
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) max,
        PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
};
/* 单次调节步进 */
const parame_l_t parames_config_S_step =
    {
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) S_step,
        PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
};
/* 连续调节步进 */
const parame_l_t parames_config_C_step =
    {
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) S_step,
        PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
};
/* 个数 */
const uint8_t parames_config_num[] =
    {
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) num,
        PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
};
/* 单位 */
const uint8_t parames_config_unit[] =
    {
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) unit,
        PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
};
/* 调节模式 */
const uint8_t parames_config_mode[] =
    {
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) mode,
        PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
};
/* 初始化等级 */
const uint8_t parames_config_level[] =
    {
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) level,
        PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
};
/* 限制上限的参数ID */
const uint8_t parames_config_max_id[] =
    {
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) max_id,
        PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
};
/* 限制下限的参数ID */
const uint8_t parames_config_min_id[] =
    {
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) min_id,
        PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
};
/* 参数更新回调，调用期间已经是更新后的数据 */
void (*const parames_config_update[])(void) =
    {
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) call,
        PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
};
/*用户参数数值*/
volatile parame_f_t _sys_parames = {{0}, {PARAM_VERSION}, {PARAM_LAST_UPDATED}};
/*用户上一次参数数值*/
volatile parame_t _sys_last_parames = {0};
/*参数查询结构体*/
const parame_query_t PQuery_t[] =
    {
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) {(void *)&_sys_parames.sys.name[0], (void *)&_sys_last_parames.name[0], (void *)&parames_config_init.name[0], (void *)&parames_config_min.name, (void *)&parames_config_max.name, (void *)&parames_config_S_step.name, (void *)&parames_config_C_step.name},
        PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
};

/*获取初始值地址*/
#define PIp(idx) (PQuery_t[idx].init)
/*获取单次调节步进地址*/
#define PSp_step(idx) (PQuery_t[idx].adj_S_step)
/*获取连续调节步进地址*/
#define PCp_step(idx) (PQuery_t[idx].adj_C_step)

/*获取获取初始值*/
#define PI(type, idx, idd) (((type *)PIp(idx))[idd])
/*获取单次调节步进*/
#define PS_step(type, idx) (*(type *)PSp_step(idx))
/*获取连续调节步进*/
#define PC_step(type, idx) (*(type *)PCp_step(idx))
/*获取调节模式*/
#define PADGM(idx) (parames_config_mode[idx])
/*获取限制下限的参数ID*/
#define PMIN_ID(idx) (parames_config_min_id[idx])
/*获取限制上限的参数ID*/
#define PMAX_ID(idx) (parames_config_max_id[idx])

/*
*********************************************************************************************************
*	函 数 名: parame_socpe_obt
*	形    参: idx：参数ID
*	形    参: idd：参数深度
*	形    参: limitMAX：上限地址
*	形    参: limitMIN：下线地址
*	返 回 值: 无
*	功能说明: 获取用户参数范围
*********************************************************************************************************
*/
void parame_socpe_obt(uint8_t idx, uint8_t idd, void *limitMAX, void *limitMIN)
{
    switch (idx)
    {
#if (ValueUnion_int8 == 1 || ValueUnion_char == 1)
#define int8_t_SELECT(id) case USR_##id:
#define int16_t_SELECT(id)
#define int32_t_SELECT(id)
#define int64_t_SELECT(id)
#define uint8_t_SELECT(id)
#define uint16_t_SELECT(id)
#define uint32_t_SELECT(id)
#define uint64_t_SELECT(id)
#define char_SELECT(id) case USR_##id:
#define int_SELECT(id)
#define float_SELECT(id)
#define double_SELECT(id)
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) type##_SELECT(name)
        PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
#undef int8_t_SELECT
#undef int16_t_SELECT
#undef int32_t_SELECT
#undef int64_t_SELECT
#undef uint8_t_SELECT
#undef uint16_t_SELECT
#undef uint32_t_SELECT
#undef uint64_t_SELECT
#undef char_SELECT
#undef int_SELECT
#undef float_SELECT
#undef double_SELECT
        /*确定上限*/
        if (0xFF == PMAX_ID(idx))
        { /*无参数限制的上限*/
            *(int8_t *)limitMAX = PMAX(int8_t, idx);
        }
        else
        { /*有参数限制的上限*/
            if (PMAX(int8_t, idx) < PV(int8_t, PMAX_ID(idx), idd))
            {
                *(int8_t *)limitMAX = PMAX(int8_t, idx);
            }
            else
            {
                *(int8_t *)limitMAX = PV(int8_t, PMAX_ID(idx), idd) - PS_step(int8_t, idx);
            }
        }
        /*确定下限*/
        if (0xFF == PMIN_ID(idx))
        { /*无参数限制的下限*/
            *(int8_t *)limitMIN = PMIN(int8_t, idx);
        }
        else
        { /*有参数限制的下限*/
            if (PMIN(int8_t, idx) > PV(int8_t, PMIN_ID(idx), idd))
            {
                *(int8_t *)limitMIN = PMIN(int8_t, idx);
            }
            else
            {
                *(int8_t *)limitMIN = PV(int8_t, PMIN_ID(idx), idd) + PS_step(int8_t, idx);
            }
        }
        break;
#endif

#if (ValueUnion_int16 == 1)
#define int8_t_SELECT(id)
#define int16_t_SELECT(id) case USR_##id:
#define int32_t_SELECT(id)
#define int64_t_SELECT(id)
#define uint8_t_SELECT(id)
#define uint16_t_SELECT(id)
#define uint32_t_SELECT(id)
#define uint64_t_SELECT(id)
#define char_SELECT(id)
#define int_SELECT(id)
#define float_SELECT(id)
#define double_SELECT(id)
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) type##_SELECT(name)
        PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
#undef int8_t_SELECT
#undef int16_t_SELECT
#undef int32_t_SELECT
#undef int64_t_SELECT
#undef uint8_t_SELECT
#undef uint16_t_SELECT
#undef uint32_t_SELECT
#undef uint64_t_SELECT
#undef char_SELECT
#undef int_SELECT
#undef float_SELECT
#undef double_SELECT
        /*确定上限*/
        if (0xFF == PMAX_ID(idx))
        { /*无参数限制的上限*/
            *(int16_t *)limitMAX = PMAX(int16_t, idx);
        }
        else
        { /*有参数限制的上限*/
            if (PMAX(int16_t, idx) < PV(int16_t, PMAX_ID(idx), idd))
            {
                *(int16_t *)limitMAX = PMAX(int16_t, idx);
            }
            else
            {
                *(int16_t *)limitMAX = PV(int16_t, PMAX_ID(idx), idd) - PS_step(int16_t, idx);
            }
        }
        /*确定下限*/
        if (0xFF == PMIN_ID(idx))
        { /*无参数限制的下限*/
            *(int16_t *)limitMIN = PMIN(int16_t, idx);
        }
        else
        { /*有参数限制的下限*/
            if (PMIN(int16_t, idx) > PV(int16_t, PMIN_ID(idx), idd))
            {
                *(int16_t *)limitMIN = PMIN(int16_t, idx);
            }
            else
            {
                *(int16_t *)limitMIN = PV(int16_t, PMIN_ID(idx), idd) + PS_step(int16_t, idx);
            }
        }
        break;
#endif

#if (ValueUnion_int32 == 1)
#define int8_t_SELECT(id)
#define int16_t_SELECT(id)
#define int32_t_SELECT(id) case USR_##id:
#define int64_t_SELECT(id)
#define uint8_t_SELECT(id)
#define uint16_t_SELECT(id)
#define uint32_t_SELECT(id)
#define uint64_t_SELECT(id)
#define char_SELECT(id)
#define int_SELECT(id)
#define float_SELECT(id)
#define double_SELECT(id)
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) type##_SELECT(name)
        PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
#undef int8_t_SELECT
#undef int16_t_SELECT
#undef int32_t_SELECT
#undef int64_t_SELECT
#undef uint8_t_SELECT
#undef uint16_t_SELECT
#undef uint32_t_SELECT
#undef uint64_t_SELECT
#undef char_SELECT
#undef int_SELECT
#undef float_SELECT
#undef double_SELECT
        /*确定上限*/
        if (0xFF == PMAX_ID(idx))
        { /*无参数限制的上限*/
            *(int32_t *)limitMAX = PMAX(int32_t, idx);
        }
        else
        { /*有参数限制的上限*/
            if (PMAX(int32_t, idx) < PV(int32_t, PMAX_ID(idx), idd))
            {
                *(int32_t *)limitMAX = PMAX(int32_t, idx);
            }
            else
            {
                *(int32_t *)limitMAX = PV(int32_t, PMAX_ID(idx), idd) - PS_step(int32_t, idx);
            }
        }
        /*确定下限*/
        if (0xFF == PMIN_ID(idx))
        { /*无参数限制的下限*/
            *(int32_t *)limitMIN = PMIN(int32_t, idx);
        }
        else
        { /*有参数限制的下限*/
            if (PMIN(int32_t, idx) > PV(int32_t, PMIN_ID(idx), idd))
            {
                *(int32_t *)limitMIN = PMIN(int32_t, idx);
            }
            else
            {
                *(int32_t *)limitMIN = PV(int32_t, PMIN_ID(idx), idd) + PS_step(int32_t, idx);
            }
        }
        break;
#endif

#if (ValueUnion_int64 == 1)
#define int8_t_SELECT(id)
#define int16_t_SELECT(id)
#define int32_t_SELECT(id)
#define int64_t_SELECT(id) case USR_##id:
#define uint8_t_SELECT(id)
#define uint16_t_SELECT(id)
#define uint32_t_SELECT(id)
#define uint64_t_SELECT(id)
#define char_SELECT(id)
#define int_SELECT(id)
#define float_SELECT(id)
#define double_SELECT(id)
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) type##_SELECT(name)
        PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
#undef int8_t_SELECT
#undef int16_t_SELECT
#undef int32_t_SELECT
#undef int64_t_SELECT
#undef uint8_t_SELECT
#undef uint16_t_SELECT
#undef uint32_t_SELECT
#undef uint64_t_SELECT
#undef char_SELECT
#undef int_SELECT
#undef float_SELECT
#undef double_SELECT
        /*确定上限*/
        if (0xFF == PMAX_ID(idx))
        { /*无参数限制的上限*/
            *(int64_t *)limitMAX = PMAX(int64_t, idx);
        }
        else
        { /*有参数限制的上限*/
            if (PMAX(int64_t, idx) < PV(int64_t, PMAX_ID(idx), idd))
            {
                *(int64_t *)limitMAX = PMAX(int64_t, idx);
            }
            else
            {
                *(int64_t *)limitMAX = PV(int64_t, PMAX_ID(idx), idd) - PS_step(int64_t, idx);
            }
        }
        /*确定下限*/
        if (0xFF == PMIN_ID(idx))
        { /*无参数限制的下限*/
            *(int64_t *)limitMIN = PMIN(int64_t, idx);
        }
        else
        { /*有参数限制的下限*/
            if (PMIN(int64_t, idx) > PV(int64_t, PMIN_ID(idx), idd))
            {
                *(int64_t *)limitMIN = PMIN(int64_t, idx);
            }
            else
            {
                *(int64_t *)limitMIN = PV(int64_t, PMIN_ID(idx), idd) + PS_step(int64_t, idx);
            }
        }
        break;
#endif

#if (ValueUnion_uint8 == 1)
#define int8_t_SELECT(id)
#define int16_t_SELECT(id)
#define int32_t_SELECT(id)
#define int64_t_SELECT(id)
#define uint8_t_SELECT(id) case USR_##id:
#define uint16_t_SELECT(id)
#define uint32_t_SELECT(id)
#define uint64_t_SELECT(id)
#define char_SELECT(id)
#define int_SELECT(id)
#define float_SELECT(id)
#define double_SELECT(id)
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) type##_SELECT(name)
        PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
#undef int8_t_SELECT
#undef int16_t_SELECT
#undef int32_t_SELECT
#undef int64_t_SELECT
#undef uint8_t_SELECT
#undef uint16_t_SELECT
#undef uint32_t_SELECT
#undef uint64_t_SELECT
#undef char_SELECT
#undef int_SELECT
#undef float_SELECT
#undef double_SELECT
        /*确定上限*/
        if (0xFF == PMAX_ID(idx))
        { /*无参数限制的上限*/
            *(uint8_t *)limitMAX = PMAX(uint8_t, idx);
        }
        else
        { /*有参数限制的上限*/
            if (PMAX(uint8_t, idx) < PV(uint8_t, PMAX_ID(idx), idd))
            {
                *(uint8_t *)limitMAX = PMAX(uint8_t, idx);
            }
            else
            {
                *(uint8_t *)limitMAX = PV(uint8_t, PMAX_ID(idx), idd) - PS_step(uint8_t, idx);
            }
        }
        /*确定下限*/
        if (0xFF == PMIN_ID(idx))
        { /*无参数限制的下限*/
            *(uint8_t *)limitMIN = PMIN(uint8_t, idx);
        }
        else
        { /*有参数限制的下限*/
            if (PMIN(uint8_t, idx) > PV(uint8_t, PMIN_ID(idx), idd))
            {
                *(uint8_t *)limitMIN = PMIN(uint8_t, idx);
            }
            else
            {
                *(uint8_t *)limitMIN = PV(uint8_t, PMIN_ID(idx), idd) + PS_step(uint8_t, idx);
            }
        }
        break;
#endif

#if (ValueUnion_uint16 == 1)
#define int8_t_SELECT(id)
#define int16_t_SELECT(id)
#define int32_t_SELECT(id)
#define int64_t_SELECT(id)
#define uint8_t_SELECT(id)
#define uint16_t_SELECT(id) case USR_##id:
#define uint32_t_SELECT(id)
#define uint64_t_SELECT(id)
#define char_SELECT(id)
#define int_SELECT(id)
#define float_SELECT(id)
#define double_SELECT(id)
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) type##_SELECT(name)
        PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
#undef int8_t_SELECT
#undef int16_t_SELECT
#undef int32_t_SELECT
#undef int64_t_SELECT
#undef uint8_t_SELECT
#undef uint16_t_SELECT
#undef uint32_t_SELECT
#undef uint64_t_SELECT
#undef char_SELECT
#undef int_SELECT
#undef float_SELECT
#undef double_SELECT
        /*确定上限*/
        if (0xFF == PMAX_ID(idx))
        { /*无参数限制的上限*/
            *(uint16_t *)limitMAX = PMAX(uint16_t, idx);
        }
        else
        { /*有参数限制的上限*/
            if (PMAX(uint16_t, idx) < PV(uint16_t, PMAX_ID(idx), idd))
            {
                *(uint16_t *)limitMAX = PMAX(uint16_t, idx);
            }
            else
            {
                *(uint16_t *)limitMAX = PV(uint16_t, PMAX_ID(idx), idd) - PS_step(uint16_t, idx);
            }
        }
        /*确定下限*/
        if (0xFF == PMIN_ID(idx))
        { /*无参数限制的下限*/
            *(uint16_t *)limitMIN = PMIN(uint16_t, idx);
        }
        else
        { /*有参数限制的下限*/
            if (PMIN(uint16_t, idx) > PV(uint16_t, PMIN_ID(idx), idd))
            {
                *(uint16_t *)limitMIN = PMIN(uint16_t, idx);
            }
            else
            {
                *(uint16_t *)limitMIN = PV(uint16_t, PMIN_ID(idx), idd) + PS_step(uint16_t, idx);
            }
        }
        break;
#endif

#if (ValueUnion_uint32 == 1)
#define int8_t_SELECT(id)
#define int16_t_SELECT(id)
#define int32_t_SELECT(id)
#define int64_t_SELECT(id)
#define uint8_t_SELECT(id)
#define uint16_t_SELECT(id)
#define uint32_t_SELECT(id) case USR_##id:
#define uint64_t_SELECT(id)
#define char_SELECT(id)
#define int_SELECT(id)
#define float_SELECT(id)
#define double_SELECT(id)
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) type##_SELECT(name)
        PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
#undef int8_t_SELECT
#undef int16_t_SELECT
#undef int32_t_SELECT
#undef int64_t_SELECT
#undef uint8_t_SELECT
#undef uint16_t_SELECT
#undef uint32_t_SELECT
#undef uint64_t_SELECT
#undef char_SELECT
#undef int_SELECT
#undef float_SELECT
#undef double_SELECT
        /*确定上限*/
        if (0xFF == PMAX_ID(idx))
        { /*无参数限制的上限*/
            *(uint32_t *)limitMAX = PMAX(uint32_t, idx);
        }
        else
        { /*有参数限制的上限*/
            if (PMAX(uint32_t, idx) < PV(uint32_t, PMAX_ID(idx), idd))
            {
                *(uint32_t *)limitMAX = PMAX(uint32_t, idx);
            }
            else
            {
                *(uint32_t *)limitMAX = PV(uint32_t, PMAX_ID(idx), idd) - PS_step(uint32_t, idx);
            }
        }
        /*确定下限*/
        if (0xFF == PMIN_ID(idx))
        { /*无参数限制的下限*/
            *(uint32_t *)limitMIN = PMIN(uint32_t, idx);
        }
        else
        { /*有参数限制的下限*/
            if (PMIN(uint32_t, idx) > PV(uint32_t, PMIN_ID(idx), idd))
            {
                *(uint32_t *)limitMIN = PMIN(uint32_t, idx);
            }
            else
            {
                *(uint32_t *)limitMIN = PV(uint32_t, PMIN_ID(idx), idd) + PS_step(uint32_t, idx);
            }
        }
        break;
#endif

#if (ValueUnion_uint64 == 1)
#define int8_t_SELECT(id)
#define int16_t_SELECT(id)
#define int32_t_SELECT(id)
#define int64_t_SELECT(id)
#define uint8_t_SELECT(id)
#define uint16_t_SELECT(id)
#define uint32_t_SELECT(id)
#define uint64_t_SELECT(id) case USR_##id:
#define char_SELECT(id)
#define int_SELECT(id)
#define float_SELECT(id)
#define double_SELECT(id)
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) type##_SELECT(name)
        PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
#undef int8_t_SELECT
#undef int16_t_SELECT
#undef int32_t_SELECT
#undef int64_t_SELECT
#undef uint8_t_SELECT
#undef uint16_t_SELECT
#undef uint32_t_SELECT
#undef uint64_t_SELECT
#undef char_SELECT
#undef int_SELECT
#undef float_SELECT
#undef double_SELECT
        /*确定上限*/
        if (0xFF == PMAX_ID(idx))
        { /*无参数限制的上限*/
            *(uint64_t *)limitMAX = PMAX(uint64_t, idx);
        }
        else
        { /*有参数限制的上限*/
            if (PMAX(uint64_t, idx) < PV(uint64_t, PMAX_ID(idx), idd))
            {
                *(uint64_t *)limitMAX = PMAX(uint64_t, idx);
            }
            else
            {
                *(uint64_t *)limitMAX = PV(uint64_t, PMAX_ID(idx), idd) - PS_step(uint64_t, idx);
            }
        }
        /*确定下限*/
        if (0xFF == PMIN_ID(idx))
        { /*无参数限制的下限*/
            *(uint64_t *)limitMIN = PMIN(uint64_t, idx);
        }
        else
        { /*有参数限制的下限*/
            if (PMIN(uint64_t, idx) > PV(uint64_t, PMIN_ID(idx), idd))
            {
                *(uint64_t *)limitMIN = PMIN(uint64_t, idx);
            }
            else
            {
                *(uint64_t *)limitMIN = PV(uint64_t, PMIN_ID(idx), idd) + PS_step(uint64_t, idx);
            }
        }
        break;
#endif

#if (ValueUnion_int == 1)
#define int8_t_SELECT(id)
#define int16_t_SELECT(id)
#define int32_t_SELECT(id)
#define int64_t_SELECT(id)
#define uint8_t_SELECT(id)
#define uint16_t_SELECT(id)
#define uint32_t_SELECT(id)
#define uint64_t_SELECT(id)
#define char_SELECT(id)
#define int_SELECT(id) case USR_##id:
#define float_SELECT(id)
#define double_SELECT(id)
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) type##_SELECT(name)
        PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
#undef int8_t_SELECT
#undef int16_t_SELECT
#undef int32_t_SELECT
#undef int64_t_SELECT
#undef uint8_t_SELECT
#undef uint16_t_SELECT
#undef uint32_t_SELECT
#undef uint64_t_SELECT
#undef char_SELECT
#undef int_SELECT
#undef float_SELECT
#undef double_SELECT
        /*确定上限*/
        if (0xFF == PMAX_ID(idx))
        { /*无参数限制的上限*/
            *(int *)limitMAX = PMAX(int, idx);
        }
        else
        { /*有参数限制的上限*/
            if (PMAX(int, idx) < PV(int, PMAX_ID(idx), idd))
            {
                *(int *)limitMAX = PMAX(int, idx);
            }
            else
            {
                *(int *)limitMAX = PV(int, PMAX_ID(idx), idd) - PS_step(int, idx);
            }
        }
        /*确定下限*/
        if (0xFF == PMIN_ID(idx))
        { /*无参数限制的下限*/
            *(int *)limitMIN = PMIN(int, idx);
        }
        else
        { /*有参数限制的下限*/
            if (PMIN(int, idx) > PV(int, PMIN_ID(idx), idd))
            {
                *(int *)limitMIN = PMIN(int, idx);
            }
            else
            {
                *(int *)limitMIN = PV(int, PMIN_ID(idx), idd) + PS_step(int, idx);
            }
        }
        break;
#endif

#if (ValueUnion_float == 1)
#define int8_t_SELECT(id)
#define int16_t_SELECT(id)
#define int32_t_SELECT(id)
#define int64_t_SELECT(id)
#define uint8_t_SELECT(id)
#define uint16_t_SELECT(id)
#define uint32_t_SELECT(id)
#define uint64_t_SELECT(id)
#define char_SELECT(id)
#define int_SELECT(id)
#define float_SELECT(id) case USR_##id:
#define double_SELECT(id)
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) type##_SELECT(name)
        PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
#undef int8_t_SELECT
#undef int16_t_SELECT
#undef int32_t_SELECT
#undef int64_t_SELECT
#undef uint8_t_SELECT
#undef uint16_t_SELECT
#undef uint32_t_SELECT
#undef uint64_t_SELECT
#undef char_SELECT
#undef int_SELECT
#undef float_SELECT
#undef double_SELECT
        /*确定上限*/
        if (0xFF == PMAX_ID(idx))
        { /*无参数限制的上限*/
            *(float *)limitMAX = PMAX(float, idx);
        }
        else
        { /*有参数限制的上限*/
            if (PMAX(float, idx) < PV(float, PMAX_ID(idx), idd))
            {
                *(float *)limitMAX = PMAX(float, idx);
            }
            else
            {
                *(float *)limitMAX = PV(float, PMAX_ID(idx), idd) - PS_step(float, idx);
            }
        }
        /*确定下限*/
        if (0xFF == PMIN_ID(idx))
        { /*无参数限制的下限*/
            *(float *)limitMIN = PMIN(float, idx);
        }
        else
        { /*有参数限制的下限*/
            if (PMIN(float, idx) > PV(float, PMIN_ID(idx), idd))
            {
                *(float *)limitMIN = PMIN(float, idx);
            }
            else
            {
                *(float *)limitMIN = PV(float, PMIN_ID(idx), idd) + PS_step(float, idx);
            }
        }
        break;
#endif

#if (ValueUnion_double == 1)
#define int8_t_SELECT(id)
#define int16_t_SELECT(id)
#define int32_t_SELECT(id)
#define int64_t_SELECT(id)
#define uint8_t_SELECT(id)
#define uint16_t_SELECT(id)
#define uint32_t_SELECT(id)
#define uint64_t_SELECT(id)
#define char_SELECT(id)
#define int_SELECT(id)
#define float_SELECT(id)
#define double_SELECT(id) case USR_##id:
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) type##_SELECT(name)
        PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
#undef int8_t_SELECT
#undef int16_t_SELECT
#undef int32_t_SELECT
#undef int64_t_SELECT
#undef uint8_t_SELECT
#undef uint16_t_SELECT
#undef uint32_t_SELECT
#undef uint64_t_SELECT
#undef char_SELECT
#undef int_SELECT
#undef float_SELECT
#undef double_SELECT
        /*确定上限*/
        if (0xFF == PMAX_ID(idx))
        { /*无参数限制的上限*/
            *(double *)limitMAX = PMAX(double, idx);
        }
        else
        { /*有参数限制的上限*/
            if (PMAX(double, idx) < PV(double, PMAX_ID(idx), idd))
            {
                *(double *)limitMAX = PMAX(double, idx);
            }
            else
            {
                *(double *)limitMAX = PV(double, PMAX_ID(idx), idd) - PS_step(double, idx);
            }
        }
        /*确定下限*/
        if (0xFF == PMIN_ID(idx))
        { /*无参数限制的下限*/
            *(double *)limitMIN = PMIN(double, idx);
        }
        else
        { /*有参数限制的下限*/
            if (PMIN(double, idx) > PV(double, PMIN_ID(idx), idd))
            {
                *(double *)limitMIN = PMIN(double, idx);
            }
            else
            {
                *(double *)limitMIN = PV(double, PMIN_ID(idx), idd) + PS_step(double, idx);
            }
        }
        break;
#endif
    default:
        break;
    }
}

/*
*********************************************************************************************************
*	函 数 名: parame_adj
*	形    参: idx：参数ID
*	形    参: idd：参数深度
*	形    参: op：调整方向（增加or减少）
*	形    参: step：布进类型（单步or连步）
*	返 回 值: 无
*	功能说明: 用户可调参数调整
*********************************************************************************************************
*/
void parame_adj(uint8_t idx, uint8_t idd, PARAME_ADJ_DIRECTION op, PARAME_S_OR_D_STEP step)
{
    switch (idx)
    {
#if (ValueUnion_int8 == 1 || ValueUnion_char == 1)
#define int8_t_SELECT(id) case USR_##id:
#define int16_t_SELECT(id)
#define int32_t_SELECT(id)
#define int64_t_SELECT(id)
#define uint8_t_SELECT(id)
#define uint16_t_SELECT(id)
#define uint32_t_SELECT(id)
#define uint64_t_SELECT(id)
#define char_SELECT(id) case USR_##id:
#define int_SELECT(id)
#define float_SELECT(id)
#define double_SELECT(id)
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) type##_SELECT(name)
        PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
#undef int8_t_SELECT
#undef int16_t_SELECT
#undef int32_t_SELECT
#undef int64_t_SELECT
#undef uint8_t_SELECT
#undef uint16_t_SELECT
#undef uint32_t_SELECT
#undef uint64_t_SELECT
#undef char_SELECT
#undef int_SELECT
#undef float_SELECT
#undef double_SELECT
        {
            int8_t limitMAX, limitMIN;                                        /*限幅缓存*/
            parame_socpe_obt(idx, idd, (void *)&limitMAX, (void *)&limitMIN); /*获取用户参数范围*/
            if (PARAME_INC == op)
            {
                /*+*/
                if (PARAME_S_STEP == step)
                {
                    /*单次调节*/
                    PV(int8_t, idx, idd) += PS_step(int8_t, idx);
                }
                else if (PARAME_D_STEP == step)
                {
                    /*连续调节*/
                    PV(int8_t, idx, idd) += PC_step(int8_t, idx);
                }
                if (PV(int8_t, idx, idd) > limitMAX)
                {
                    if (ADJ_MODE_AMP == PADGM(idx))
                    {
                        /*限幅调节*/
                        PV(int8_t, idx, idd) = limitMAX;
                    }
                    else
                    {
                        /*循环调节*/
                        PV(int8_t, idx, idd) = limitMIN;
                    }
                }
            }
            else if (PARAME_DEC == op)
            {
                /*-*/
                if (PARAME_S_STEP == step)
                {
                    /*单次调节*/
                    PV(int8_t, idx, idd) -= PS_step(int8_t, idx);
                }
                else if (PARAME_D_STEP == step)
                {
                    /*连续调节*/
                    PV(int8_t, idx, idd) -= PC_step(int8_t, idx);
                }
                if (PV(int8_t, idx, idd) < limitMIN)
                {
                    if (ADJ_MODE_AMP == PADGM(idx))
                    {
                        /*限幅调节*/
                        PV(int8_t, idx, idd) = limitMIN;
                    }
                    else
                    {
                        /*循环调节*/
                        PV(int8_t, idx, idd) = limitMAX;
                    }
                }
            }
        }
        break;
#endif

#if (ValueUnion_int16 == 1)
#define int8_t_SELECT(id)
#define int16_t_SELECT(id) case USR_##id:
#define int32_t_SELECT(id)
#define int64_t_SELECT(id)
#define uint8_t_SELECT(id)
#define uint16_t_SELECT(id)
#define uint32_t_SELECT(id)
#define uint64_t_SELECT(id)
#define char_SELECT(id)
#define int_SELECT(id)
#define float_SELECT(id)
#define double_SELECT(id)
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) type##_SELECT(name)
        PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
#undef int8_t_SELECT
#undef int16_t_SELECT
#undef int32_t_SELECT
#undef int64_t_SELECT
#undef uint8_t_SELECT
#undef uint16_t_SELECT
#undef uint32_t_SELECT
#undef uint64_t_SELECT
#undef char_SELECT
#undef int_SELECT
#undef float_SELECT
#undef double_SELECT
        {
            int16_t limitMAX, limitMIN;                                       /*限幅缓存*/
            parame_socpe_obt(idx, idd, (void *)&limitMAX, (void *)&limitMIN); /*获取用户参数范围*/
            if (PARAME_INC == op)
            {
                /*+*/
                if (PARAME_S_STEP == step)
                {
                    /*单次调节*/
                    PV(int16_t, idx, idd) += PS_step(int16_t, idx);
                }
                else if (PARAME_D_STEP == step)
                {
                    /*连续调节*/
                    PV(int16_t, idx, idd) += PC_step(int16_t, idx);
                }
                if (PV(int16_t, idx, idd) > limitMAX)
                {
                    if (ADJ_MODE_AMP == PADGM(idx))
                    {
                        /*限幅调节*/
                        PV(int16_t, idx, idd) = limitMAX;
                    }
                    else
                    {
                        /*循环调节*/
                        PV(int16_t, idx, idd) = limitMIN;
                    }
                }
            }
            else if (PARAME_DEC == op)
            {
                /*-*/
                if (PARAME_S_STEP == step)
                {
                    /*单次调节*/
                    PV(int16_t, idx, idd) -= PS_step(int16_t, idx);
                }
                else if (PARAME_D_STEP == step)
                {
                    /*连续调节*/
                    PV(int16_t, idx, idd) -= PC_step(int16_t, idx);
                }
                if (PV(int16_t, idx, idd) < limitMIN)
                {
                    if (ADJ_MODE_AMP == PADGM(idx))
                    {
                        /*限幅调节*/
                        PV(int16_t, idx, idd) = limitMIN;
                    }
                    else
                    {
                        /*循环调节*/
                        PV(int16_t, idx, idd) = limitMAX;
                    }
                }
            }
        }
        break;
#endif

#if (ValueUnion_int32 == 1)
#define int8_t_SELECT(id)
#define int16_t_SELECT(id)
#define int32_t_SELECT(id) case USR_##id:
#define int64_t_SELECT(id)
#define uint8_t_SELECT(id)
#define uint16_t_SELECT(id)
#define uint32_t_SELECT(id)
#define uint64_t_SELECT(id)
#define char_SELECT(id)
#define int_SELECT(id)
#define float_SELECT(id)
#define double_SELECT(id)
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) type##_SELECT(name)
        PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
#undef int8_t_SELECT
#undef int16_t_SELECT
#undef int32_t_SELECT
#undef int64_t_SELECT
#undef uint8_t_SELECT
#undef uint16_t_SELECT
#undef uint32_t_SELECT
#undef uint64_t_SELECT
#undef char_SELECT
#undef int_SELECT
#undef float_SELECT
#undef double_SELECT
        {
            int32_t limitMAX, limitMIN;                                       /*限幅缓存*/
            parame_socpe_obt(idx, idd, (void *)&limitMAX, (void *)&limitMIN); /*获取用户参数范围*/
            if (PARAME_INC == op)
            {
                /*+*/
                if (PARAME_S_STEP == step)
                {
                    /*单次调节*/
                    PV(int32_t, idx, idd) += PS_step(int32_t, idx);
                }
                else if (PARAME_D_STEP == step)
                {
                    /*连续调节*/
                    PV(int32_t, idx, idd) += PC_step(int32_t, idx);
                }
                if (PV(int32_t, idx, idd) > limitMAX)
                {
                    if (ADJ_MODE_AMP == PADGM(idx))
                    {
                        /*限幅调节*/
                        PV(int32_t, idx, idd) = limitMAX;
                    }
                    else
                    {
                        /*循环调节*/
                        PV(int32_t, idx, idd) = limitMIN;
                    }
                }
            }
            else if (PARAME_DEC == op)
            {
                /*-*/
                if (PARAME_S_STEP == step)
                {
                    /*单次调节*/
                    PV(int32_t, idx, idd) -= PS_step(int32_t, idx);
                }
                else if (PARAME_D_STEP == step)
                {
                    /*连续调节*/
                    PV(int32_t, idx, idd) -= PC_step(int32_t, idx);
                }
                if (PV(int32_t, idx, idd) < limitMIN)
                {
                    if (ADJ_MODE_AMP == PADGM(idx))
                    {
                        /*限幅调节*/
                        PV(int32_t, idx, idd) = limitMIN;
                    }
                    else
                    {
                        /*循环调节*/
                        PV(int32_t, idx, idd) = limitMAX;
                    }
                }
            }
        }
        break;
#endif

#if (ValueUnion_int64 == 1)
#define int8_t_SELECT(id)
#define int16_t_SELECT(id)
#define int32_t_SELECT(id)
#define int64_t_SELECT(id) case USR_##id:
#define uint8_t_SELECT(id)
#define uint16_t_SELECT(id)
#define uint32_t_SELECT(id)
#define uint64_t_SELECT(id)
#define char_SELECT(id)
#define int_SELECT(id)
#define float_SELECT(id)
#define double_SELECT(id)
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) type##_SELECT(name)
        PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
#undef int8_t_SELECT
#undef int16_t_SELECT
#undef int32_t_SELECT
#undef int64_t_SELECT
#undef uint8_t_SELECT
#undef uint16_t_SELECT
#undef uint32_t_SELECT
#undef uint64_t_SELECT
#undef char_SELECT
#undef int_SELECT
#undef float_SELECT
#undef double_SELECT
        {
            int64_t limitMAX, limitMIN;                                       /*限幅缓存*/
            parame_socpe_obt(idx, idd, (void *)&limitMAX, (void *)&limitMIN); /*获取用户参数范围*/
            if (PARAME_INC == op)
            {
                /*+*/
                if (PARAME_S_STEP == step)
                {
                    /*单次调节*/
                    PV(int64_t, idx, idd) += PS_step(int64_t, idx);
                }
                else if (PARAME_D_STEP == step)
                {
                    /*连续调节*/
                    PV(int64_t, idx, idd) += PC_step(int64_t, idx);
                }
                if (PV(int64_t, idx, idd) > limitMAX)
                {
                    if (ADJ_MODE_AMP == PADGM(idx))
                    {
                        /*限幅调节*/
                        PV(int64_t, idx, idd) = limitMAX;
                    }
                    else
                    {
                        /*循环调节*/
                        PV(int64_t, idx, idd) = limitMIN;
                    }
                }
            }
            else if (PARAME_DEC == op)
            {
                /*-*/
                if (PARAME_S_STEP == step)
                {
                    /*单次调节*/
                    PV(int64_t, idx, idd) -= PS_step(int64_t, idx);
                }
                else if (PARAME_D_STEP == step)
                {
                    /*连续调节*/
                    PV(int64_t, idx, idd) -= PC_step(int64_t, idx);
                }
                if (PV(int64_t, idx, idd) < limitMIN)
                {
                    if (ADJ_MODE_AMP == PADGM(idx))
                    {
                        /*限幅调节*/
                        PV(int64_t, idx, idd) = limitMIN;
                    }
                    else
                    {
                        /*循环调节*/
                        PV(int64_t, idx, idd) = limitMAX;
                    }
                }
            }
        }
        break;
#endif

#if (ValueUnion_uint8 == 1)
#define int8_t_SELECT(id)
#define int16_t_SELECT(id)
#define int32_t_SELECT(id)
#define int64_t_SELECT(id)
#define uint8_t_SELECT(id) case USR_##id:
#define uint16_t_SELECT(id)
#define uint32_t_SELECT(id)
#define uint64_t_SELECT(id)
#define char_SELECT(id)
#define int_SELECT(id)
#define float_SELECT(id)
#define double_SELECT(id)
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) type##_SELECT(name)
        PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
#undef int8_t_SELECT
#undef int16_t_SELECT
#undef int32_t_SELECT
#undef int64_t_SELECT
#undef uint8_t_SELECT
#undef uint16_t_SELECT
#undef uint32_t_SELECT
#undef uint64_t_SELECT
#undef char_SELECT
#undef int_SELECT
#undef float_SELECT
#undef double_SELECT
        {
            uint8_t limitMAX, limitMIN;                                       /*限幅缓存*/
            parame_socpe_obt(idx, idd, (void *)&limitMAX, (void *)&limitMIN); /*获取用户参数范围*/
            if (PARAME_INC == op)
            {
                /*+*/
                if (PARAME_S_STEP == step)
                {
                    /*单次调节*/
                    PV(uint8_t, idx, idd) += PS_step(uint8_t, idx);
                }
                else if (PARAME_D_STEP == step)
                {
                    /*连续调节*/
                    PV(uint8_t, idx, idd) += PC_step(uint8_t, idx);
                }
                if (PV(uint8_t, idx, idd) > limitMAX)
                {
                    if (ADJ_MODE_AMP == PADGM(idx))
                    {
                        /*限幅调节*/
                        PV(uint8_t, idx, idd) = limitMAX;
                    }
                    else
                    {
                        /*循环调节*/
                        PV(uint8_t, idx, idd) = limitMIN;
                    }
                }
            }
            else if (PARAME_DEC == op)
            {
                /*-*/
                if (PARAME_S_STEP == step)
                {
                    /*单次调节*/
                    PV(uint8_t, idx, idd) -= PS_step(uint8_t, idx);
                }
                else if (PARAME_D_STEP == step)
                {
                    /*连续调节*/
                    PV(uint8_t, idx, idd) -= PC_step(uint8_t, idx);
                }
                if (PV(uint8_t, idx, idd) < limitMIN)
                {
                    if (ADJ_MODE_AMP == PADGM(idx))
                    {
                        /*限幅调节*/
                        PV(uint8_t, idx, idd) = limitMIN;
                    }
                    else
                    {
                        /*循环调节*/
                        PV(uint8_t, idx, idd) = limitMAX;
                    }
                }
            }
        }
        break;
#endif

#if (ValueUnion_uint16 == 1)
#define int8_t_SELECT(id)
#define int16_t_SELECT(id)
#define int32_t_SELECT(id)
#define int64_t_SELECT(id)
#define uint8_t_SELECT(id)
#define uint16_t_SELECT(id) case USR_##id:
#define uint32_t_SELECT(id)
#define uint64_t_SELECT(id)
#define char_SELECT(id)
#define int_SELECT(id)
#define float_SELECT(id)
#define double_SELECT(id)
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) type##_SELECT(name)
        PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
#undef int8_t_SELECT
#undef int16_t_SELECT
#undef int32_t_SELECT
#undef int64_t_SELECT
#undef uint8_t_SELECT
#undef uint16_t_SELECT
#undef uint32_t_SELECT
#undef uint64_t_SELECT
#undef char_SELECT
#undef int_SELECT
#undef float_SELECT
#undef double_SELECT
        {
            uint16_t limitMAX, limitMIN;                                      /*限幅缓存*/
            parame_socpe_obt(idx, idd, (void *)&limitMAX, (void *)&limitMIN); /*获取用户参数范围*/
            if (PARAME_INC == op)
            {
                /*+*/
                if (PARAME_S_STEP == step)
                {
                    /*单次调节*/
                    PV(uint16_t, idx, idd) += PS_step(uint16_t, idx);
                }
                else if (PARAME_D_STEP == step)
                {
                    /*连续调节*/
                    PV(uint16_t, idx, idd) += PC_step(uint16_t, idx);
                }
                if (PV(uint16_t, idx, idd) > limitMAX)
                {
                    if (ADJ_MODE_AMP == PADGM(idx))
                    {
                        /*限幅调节*/
                        PV(uint16_t, idx, idd) = limitMAX;
                    }
                    else
                    {
                        /*循环调节*/
                        PV(uint16_t, idx, idd) = limitMIN;
                    }
                }
            }
            else if (PARAME_DEC == op)
            {
                /*-*/
                if (PARAME_S_STEP == step)
                {
                    /*单次调节*/
                    PV(uint16_t, idx, idd) -= PS_step(uint16_t, idx);
                }
                else if (PARAME_D_STEP == step)
                {
                    /*连续调节*/
                    PV(uint16_t, idx, idd) -= PC_step(uint16_t, idx);
                }
                if (PV(uint16_t, idx, idd) < limitMIN)
                {
                    if (ADJ_MODE_AMP == PADGM(idx))
                    {
                        /*限幅调节*/
                        PV(uint16_t, idx, idd) = limitMIN;
                    }
                    else
                    {
                        /*循环调节*/
                        PV(uint16_t, idx, idd) = limitMAX;
                    }
                }
            }
        }
        break;
#endif

#if (ValueUnion_uint32 == 1)
#define int8_t_SELECT(id)
#define int16_t_SELECT(id)
#define int32_t_SELECT(id)
#define int64_t_SELECT(id)
#define uint8_t_SELECT(id)
#define uint16_t_SELECT(id)
#define uint32_t_SELECT(id) case USR_##id:
#define uint64_t_SELECT(id)
#define char_SELECT(id)
#define int_SELECT(id)
#define float_SELECT(id)
#define double_SELECT(id)
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) type##_SELECT(name)
        PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
#undef int8_t_SELECT
#undef int16_t_SELECT
#undef int32_t_SELECT
#undef int64_t_SELECT
#undef uint8_t_SELECT
#undef uint16_t_SELECT
#undef uint32_t_SELECT
#undef uint64_t_SELECT
#undef char_SELECT
#undef int_SELECT
#undef float_SELECT
#undef double_SELECT
        {
            uint32_t limitMAX, limitMIN;                                      /*限幅缓存*/
            parame_socpe_obt(idx, idd, (void *)&limitMAX, (void *)&limitMIN); /*获取用户参数范围*/
            if (PARAME_INC == op)
            {
                /*+*/
                if (PARAME_S_STEP == step)
                {
                    /*单次调节*/
                    PV(uint32_t, idx, idd) += PS_step(uint32_t, idx);
                }
                else if (PARAME_D_STEP == step)
                {
                    /*连续调节*/
                    PV(uint32_t, idx, idd) += PC_step(uint32_t, idx);
                }
                if (PV(uint32_t, idx, idd) > limitMAX)
                {
                    if (ADJ_MODE_AMP == PADGM(idx))
                    {
                        /*限幅调节*/
                        PV(uint32_t, idx, idd) = limitMAX;
                    }
                    else
                    {
                        /*循环调节*/
                        PV(uint32_t, idx, idd) = limitMIN;
                    }
                }
            }
            else if (PARAME_DEC == op)
            {
                /*-*/
                if (PARAME_S_STEP == step)
                {
                    /*单次调节*/
                    PV(uint32_t, idx, idd) -= PS_step(uint32_t, idx);
                }
                else if (PARAME_D_STEP == step)
                {
                    /*连续调节*/
                    PV(uint32_t, idx, idd) -= PC_step(uint32_t, idx);
                }
                if (PV(uint32_t, idx, idd) < limitMIN)
                {
                    if (ADJ_MODE_AMP == PADGM(idx))
                    {
                        /*限幅调节*/
                        PV(uint32_t, idx, idd) = limitMIN;
                    }
                    else
                    {
                        /*循环调节*/
                        PV(uint32_t, idx, idd) = limitMAX;
                    }
                }
            }
        }
        break;
#endif

#if (ValueUnion_uint64 == 1)
#define int8_t_SELECT(id)
#define int16_t_SELECT(id)
#define int32_t_SELECT(id)
#define int64_t_SELECT(id)
#define uint8_t_SELECT(id)
#define uint16_t_SELECT(id)
#define uint32_t_SELECT(id)
#define uint64_t_SELECT(id) case USR_##id:
#define char_SELECT(id)
#define int_SELECT(id)
#define float_SELECT(id)
#define double_SELECT(id)
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) type##_SELECT(name)
        PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
#undef int8_t_SELECT
#undef int16_t_SELECT
#undef int32_t_SELECT
#undef int64_t_SELECT
#undef uint8_t_SELECT
#undef uint16_t_SELECT
#undef uint32_t_SELECT
#undef uint64_t_SELECT
#undef char_SELECT
#undef int_SELECT
#undef float_SELECT
#undef double_SELECT
        {
            uint64_t limitMAX, limitMIN;                                      /*限幅缓存*/
            parame_socpe_obt(idx, idd, (void *)&limitMAX, (void *)&limitMIN); /*获取用户参数范围*/
            if (PARAME_INC == op)
            {
                /*+*/
                if (PARAME_S_STEP == step)
                {
                    /*单次调节*/
                    PV(uint64_t, idx, idd) += PS_step(uint64_t, idx);
                }
                else if (PARAME_D_STEP == step)
                {
                    /*连续调节*/
                    PV(uint64_t, idx, idd) += PC_step(uint64_t, idx);
                }
                if (PV(uint64_t, idx, idd) > limitMAX)
                {
                    if (ADJ_MODE_AMP == PADGM(idx))
                    {
                        /*限幅调节*/
                        PV(uint64_t, idx, idd) = limitMAX;
                    }
                    else
                    {
                        /*循环调节*/
                        PV(uint64_t, idx, idd) = limitMIN;
                    }
                }
            }
            else if (PARAME_DEC == op)
            {
                /*-*/
                if (PARAME_S_STEP == step)
                {
                    /*单次调节*/
                    PV(uint64_t, idx, idd) -= PS_step(uint64_t, idx);
                }
                else if (PARAME_D_STEP == step)
                {
                    /*连续调节*/
                    PV(uint64_t, idx, idd) -= PC_step(uint64_t, idx);
                }
                if (PV(uint64_t, idx, idd) < limitMIN)
                {
                    if (ADJ_MODE_AMP == PADGM(idx))
                    {
                        /*限幅调节*/
                        PV(uint64_t, idx, idd) = limitMIN;
                    }
                    else
                    {
                        /*循环调节*/
                        PV(uint64_t, idx, idd) = limitMAX;
                    }
                }
            }
        }
        break;
#endif

#if (ValueUnion_int == 1)
#define int8_t_SELECT(id)
#define int16_t_SELECT(id)
#define int32_t_SELECT(id)
#define int64_t_SELECT(id)
#define uint8_t_SELECT(id)
#define uint16_t_SELECT(id)
#define uint32_t_SELECT(id)
#define uint64_t_SELECT(id)
#define char_SELECT(id)
#define int_SELECT(id) case USR_##id:
#define float_SELECT(id)
#define double_SELECT(id)
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) type##_SELECT(name)
        PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
#undef int8_t_SELECT
#undef int16_t_SELECT
#undef int32_t_SELECT
#undef int64_t_SELECT
#undef uint8_t_SELECT
#undef uint16_t_SELECT
#undef uint32_t_SELECT
#undef uint64_t_SELECT
#undef char_SELECT
#undef int_SELECT
#undef float_SELECT
#undef double_SELECT
        {
            int limitMAX, limitMIN;                                           /*限幅缓存*/
            parame_socpe_obt(idx, idd, (void *)&limitMAX, (void *)&limitMIN); /*获取用户参数范围*/
            if (PARAME_INC == op)
            {
                /*+*/
                if (PARAME_S_STEP == step)
                {
                    /*单次调节*/
                    PV(int, idx, idd) += PS_step(int, idx);
                }
                else if (PARAME_D_STEP == step)
                {
                    /*连续调节*/
                    PV(int, idx, idd) += PC_step(int, idx);
                }
                if (PV(int, idx, idd) > limitMAX)
                {
                    if (ADJ_MODE_AMP == PADGM(idx))
                    {
                        /*限幅调节*/
                        PV(int, idx, idd) = limitMAX;
                    }
                    else
                    {
                        /*循环调节*/
                        PV(int, idx, idd) = limitMIN;
                    }
                }
            }
            else if (PARAME_DEC == op)
            {
                /*-*/
                if (PARAME_S_STEP == step)
                {
                    /*单次调节*/
                    PV(int, idx, idd) -= PS_step(int, idx);
                }
                else if (PARAME_D_STEP == step)
                {
                    /*连续调节*/
                    PV(int, idx, idd) -= PC_step(int, idx);
                }
                if (PV(int, idx, idd) < limitMIN)
                {
                    if (ADJ_MODE_AMP == PADGM(idx))
                    {
                        /*限幅调节*/
                        PV(int, idx, idd) = limitMIN;
                    }
                    else
                    {
                        /*循环调节*/
                        PV(int, idx, idd) = limitMAX;
                    }
                }
            }
        }
        break;
#endif

#if (ValueUnion_float == 1)
#define int8_t_SELECT(id)
#define int16_t_SELECT(id)
#define int32_t_SELECT(id)
#define int64_t_SELECT(id)
#define uint8_t_SELECT(id)
#define uint16_t_SELECT(id)
#define uint32_t_SELECT(id)
#define uint64_t_SELECT(id)
#define char_SELECT(id)
#define int_SELECT(id)
#define float_SELECT(id) case USR_##id:
#define double_SELECT(id)
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) type##_SELECT(name)
        PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
#undef int8_t_SELECT
#undef int16_t_SELECT
#undef int32_t_SELECT
#undef int64_t_SELECT
#undef uint8_t_SELECT
#undef uint16_t_SELECT
#undef uint32_t_SELECT
#undef uint64_t_SELECT
#undef char_SELECT
#undef int_SELECT
#undef float_SELECT
#undef double_SELECT
        {
            float limitMAX, limitMIN;                                         /*限幅缓存*/
            parame_socpe_obt(idx, idd, (void *)&limitMAX, (void *)&limitMIN); /*获取用户参数范围*/
            if (PARAME_INC == op)
            {
                /*+*/
                if (PARAME_S_STEP == step)
                {
                    /*单次调节*/
                    PV(float, idx, idd) += PS_step(float, idx);
                }
                else if (PARAME_D_STEP == step)
                {
                    /*连续调节*/
                    PV(float, idx, idd) += PC_step(float, idx);
                }
                if (PV(float, idx, idd) > limitMAX)
                {
                    if (ADJ_MODE_AMP == PADGM(idx))
                    {
                        /*限幅调节*/
                        PV(float, idx, idd) = limitMAX;
                    }
                    else
                    {
                        /*循环调节*/
                        PV(float, idx, idd) = limitMIN;
                    }
                }
            }
            else if (PARAME_DEC == op)
            {
                /*-*/
                if (PARAME_S_STEP == step)
                {
                    /*单次调节*/
                    PV(float, idx, idd) -= PS_step(float, idx);
                }
                else if (PARAME_D_STEP == step)
                {
                    /*连续调节*/
                    PV(float, idx, idd) -= PC_step(float, idx);
                }
                if (PV(float, idx, idd) < limitMIN)
                {
                    if (ADJ_MODE_AMP == PADGM(idx))
                    {
                        /*限幅调节*/
                        PV(float, idx, idd) = limitMIN;
                    }
                    else
                    {
                        /*循环调节*/
                        PV(float, idx, idd) = limitMAX;
                    }
                }
            }
        }
        break;
#endif

#if (ValueUnion_double == 1)
#define int8_t_SELECT(id)
#define int16_t_SELECT(id)
#define int32_t_SELECT(id)
#define int64_t_SELECT(id)
#define uint8_t_SELECT(id)
#define uint16_t_SELECT(id)
#define uint32_t_SELECT(id)
#define uint64_t_SELECT(id)
#define char_SELECT(id)
#define int_SELECT(id)
#define float_SELECT(id)
#define double_SELECT(id) case USR_##id:
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) type##_SELECT(name)
        PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
#undef int8_t_SELECT
#undef int16_t_SELECT
#undef int32_t_SELECT
#undef int64_t_SELECT
#undef uint8_t_SELECT
#undef uint16_t_SELECT
#undef uint32_t_SELECT
#undef uint64_t_SELECT
#undef char_SELECT
#undef int_SELECT
#undef float_SELECT
#undef double_SELECT
        {
            double limitMAX, limitMIN;                                        /*限幅缓存*/
            parame_socpe_obt(idx, idd, (void *)&limitMAX, (void *)&limitMIN); /*获取用户参数范围*/
            if (PARAME_INC == op)
            {
                /*+*/
                if (PARAME_S_STEP == step)
                {
                    /*单次调节*/
                    PV(double, idx, idd) += PS_step(double, idx);
                }
                else if (PARAME_D_STEP == step)
                {
                    /*连续调节*/
                    PV(double, idx, idd) += PC_step(double, idx);
                }
                if (PV(double, idx, idd) > limitMAX)
                {
                    if (ADJ_MODE_AMP == PADGM(idx))
                    {
                        /*限幅调节*/
                        PV(double, idx, idd) = limitMAX;
                    }
                    else
                    {
                        /*循环调节*/
                        PV(double, idx, idd) = limitMIN;
                    }
                }
            }
            else if (PARAME_DEC == op)
            {
                /*-*/
                if (PARAME_S_STEP == step)
                {
                    /*单次调节*/
                    PV(double, idx, idd) -= PS_step(double, idx);
                }
                else if (PARAME_D_STEP == step)
                {
                    /*连续调节*/
                    PV(double, idx, idd) -= PC_step(double, idx);
                }
                if (PV(double, idx, idd) < limitMIN)
                {
                    if (ADJ_MODE_AMP == PADGM(idx))
                    {
                        /*限幅调节*/
                        PV(double, idx, idd) = limitMIN;
                    }
                    else
                    {
                        /*循环调节*/
                        PV(double, idx, idd) = limitMAX;
                    }
                }
            }
        }
        break;
#endif
    default:
        break;
    }
}

/*
*********************************************************************************************************
*	函 数 名: parame_set_pv
*	形    参: idx：参数ID
*	形    参: idd：参数深度
*	形    参: value：修改的参数值地址
*	返 回 值: TRUE：修改成功 FALSE：修改失败
*	功能说明: 修改参数
*********************************************************************************************************
*/
uint8_t parame_set_pv(uint8_t idx, uint8_t idd, void *value)
{
    switch (idx)
    {
#if (ValueUnion_int8 == 1 || ValueUnion_char == 1)
#define int8_t_SELECT(id) case USR_##id:
#define int16_t_SELECT(id)
#define int32_t_SELECT(id)
#define int64_t_SELECT(id)
#define uint8_t_SELECT(id)
#define uint16_t_SELECT(id)
#define uint32_t_SELECT(id)
#define uint64_t_SELECT(id)
#define char_SELECT(id) case USR_##id:
#define int_SELECT(id)
#define float_SELECT(id)
#define double_SELECT(id)
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) type##_SELECT(name)
        PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
#undef int8_t_SELECT
#undef int16_t_SELECT
#undef int32_t_SELECT
#undef int64_t_SELECT
#undef uint8_t_SELECT
#undef uint16_t_SELECT
#undef uint32_t_SELECT
#undef uint64_t_SELECT
#undef char_SELECT
#undef int_SELECT
#undef float_SELECT
#undef double_SELECT
        {
            int8_t limitMAX, limitMIN;                                        /*限幅缓存*/
            parame_socpe_obt(idx, idd, (void *)&limitMAX, (void *)&limitMIN); /*获取用户参数范围*/
            if (*(int8_t *)value >= limitMIN || *(int8_t *)value <= limitMAX)
            {
                PV(int8_t, idx, idd) = *(int8_t *)value;
                return TRUE;
            }
        }
        break;
#endif

#if (ValueUnion_int16 == 1)
#define int8_t_SELECT(id)
#define int16_t_SELECT(id) case USR_##id:
#define int32_t_SELECT(id)
#define int64_t_SELECT(id)
#define uint8_t_SELECT(id)
#define uint16_t_SELECT(id)
#define uint32_t_SELECT(id)
#define uint64_t_SELECT(id)
#define char_SELECT(id)
#define int_SELECT(id)
#define float_SELECT(id)
#define double_SELECT(id)
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) type##_SELECT(name)
        PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
#undef int8_t_SELECT
#undef int16_t_SELECT
#undef int32_t_SELECT
#undef int64_t_SELECT
#undef uint8_t_SELECT
#undef uint16_t_SELECT
#undef uint32_t_SELECT
#undef uint64_t_SELECT
#undef char_SELECT
#undef int_SELECT
#undef float_SELECT
#undef double_SELECT
        {
            int16_t limitMAX, limitMIN;                                       /*限幅缓存*/
            parame_socpe_obt(idx, idd, (void *)&limitMAX, (void *)&limitMIN); /*获取用户参数范围*/
            if (*(int16_t *)value >= limitMIN || *(int16_t *)value <= limitMAX)
            {
                PV(int16_t, idx, idd) = *(int16_t *)value;
                return TRUE;
            }
        }
        break;
#endif

#if (ValueUnion_int32 == 1)
#define int8_t_SELECT(id)
#define int16_t_SELECT(id)
#define int32_t_SELECT(id) case USR_##id:
#define int64_t_SELECT(id)
#define uint8_t_SELECT(id)
#define uint16_t_SELECT(id)
#define uint32_t_SELECT(id)
#define uint64_t_SELECT(id)
#define char_SELECT(id)
#define int_SELECT(id)
#define float_SELECT(id)
#define double_SELECT(id)
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) type##_SELECT(name)
        PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
#undef int8_t_SELECT
#undef int16_t_SELECT
#undef int32_t_SELECT
#undef int64_t_SELECT
#undef uint8_t_SELECT
#undef uint16_t_SELECT
#undef uint32_t_SELECT
#undef uint64_t_SELECT
#undef char_SELECT
#undef int_SELECT
#undef float_SELECT
#undef double_SELECT
        {
            int32_t limitMAX, limitMIN;                                       /*限幅缓存*/
            parame_socpe_obt(idx, idd, (void *)&limitMAX, (void *)&limitMIN); /*获取用户参数范围*/
            if (*(int32_t *)value >= limitMIN || *(int32_t *)value <= limitMAX)
            {
                PV(int32_t, idx, idd) = *(int32_t *)value;
                return TRUE;
            }
        }
        break;
#endif

#if (ValueUnion_int64 == 1)
#define int8_t_SELECT(id)
#define int16_t_SELECT(id)
#define int32_t_SELECT(id)
#define int64_t_SELECT(id) case USR_##id:
#define uint8_t_SELECT(id)
#define uint16_t_SELECT(id)
#define uint32_t_SELECT(id)
#define uint64_t_SELECT(id)
#define char_SELECT(id)
#define int_SELECT(id)
#define float_SELECT(id)
#define double_SELECT(id)
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) type##_SELECT(name)
        PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
#undef int8_t_SELECT
#undef int16_t_SELECT
#undef int32_t_SELECT
#undef int64_t_SELECT
#undef uint8_t_SELECT
#undef uint16_t_SELECT
#undef uint32_t_SELECT
#undef uint64_t_SELECT
#undef char_SELECT
#undef int_SELECT
#undef float_SELECT
#undef double_SELECT
        {
            int64_t limitMAX, limitMIN;                                       /*限幅缓存*/
            parame_socpe_obt(idx, idd, (void *)&limitMAX, (void *)&limitMIN); /*获取用户参数范围*/
            if (*(int64_t *)value >= limitMIN || *(int64_t *)value <= limitMAX)
            {
                PV(int64_t, idx, idd) = *(int64_t *)value;
                return TRUE;
            }
        }
        break;
#endif

#if (ValueUnion_uint8 == 1)
#define int8_t_SELECT(id)
#define int16_t_SELECT(id)
#define int32_t_SELECT(id)
#define int64_t_SELECT(id)
#define uint8_t_SELECT(id) case USR_##id:
#define uint16_t_SELECT(id)
#define uint32_t_SELECT(id)
#define uint64_t_SELECT(id)
#define char_SELECT(id)
#define int_SELECT(id)
#define float_SELECT(id)
#define double_SELECT(id)
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) type##_SELECT(name)
        PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
#undef int8_t_SELECT
#undef int16_t_SELECT
#undef int32_t_SELECT
#undef int64_t_SELECT
#undef uint8_t_SELECT
#undef uint16_t_SELECT
#undef uint32_t_SELECT
#undef uint64_t_SELECT
#undef char_SELECT
#undef int_SELECT
#undef float_SELECT
#undef double_SELECT
        {
            uint8_t limitMAX, limitMIN;                                       /*限幅缓存*/
            parame_socpe_obt(idx, idd, (void *)&limitMAX, (void *)&limitMIN); /*获取用户参数范围*/
            if (*(uint8_t *)value >= limitMIN || *(uint8_t *)value <= limitMAX)
            {
                PV(uint8_t, idx, idd) = *(uint8_t *)value;
                return TRUE;
            }
        }
        break;
#endif

#if (ValueUnion_uint16 == 1)
#define int8_t_SELECT(id)
#define int16_t_SELECT(id)
#define int32_t_SELECT(id)
#define int64_t_SELECT(id)
#define uint8_t_SELECT(id)
#define uint16_t_SELECT(id) case USR_##id:
#define uint32_t_SELECT(id)
#define uint64_t_SELECT(id)
#define char_SELECT(id)
#define int_SELECT(id)
#define float_SELECT(id)
#define double_SELECT(id)
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) type##_SELECT(name)
        PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
#undef int8_t_SELECT
#undef int16_t_SELECT
#undef int32_t_SELECT
#undef int64_t_SELECT
#undef uint8_t_SELECT
#undef uint16_t_SELECT
#undef uint32_t_SELECT
#undef uint64_t_SELECT
#undef char_SELECT
#undef int_SELECT
#undef float_SELECT
#undef double_SELECT
        {
            uint16_t limitMAX, limitMIN;                                      /*限幅缓存*/
            parame_socpe_obt(idx, idd, (void *)&limitMAX, (void *)&limitMIN); /*获取用户参数范围*/
            if (*(uint16_t *)value >= limitMIN || *(uint16_t *)value <= limitMAX)
            {
                PV(uint16_t, idx, idd) = *(uint16_t *)value;
                return TRUE;
            }
        }
        break;
#endif

#if (ValueUnion_uint32 == 1)
#define int8_t_SELECT(id)
#define int16_t_SELECT(id)
#define int32_t_SELECT(id)
#define int64_t_SELECT(id)
#define uint8_t_SELECT(id)
#define uint16_t_SELECT(id)
#define uint32_t_SELECT(id) case USR_##id:
#define uint64_t_SELECT(id)
#define char_SELECT(id)
#define int_SELECT(id)
#define float_SELECT(id)
#define double_SELECT(id)
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) type##_SELECT(name)
        PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
#undef int8_t_SELECT
#undef int16_t_SELECT
#undef int32_t_SELECT
#undef int64_t_SELECT
#undef uint8_t_SELECT
#undef uint16_t_SELECT
#undef uint32_t_SELECT
#undef uint64_t_SELECT
#undef char_SELECT
#undef int_SELECT
#undef float_SELECT
#undef double_SELECT
        {
            uint32_t limitMAX, limitMIN;                                      /*限幅缓存*/
            parame_socpe_obt(idx, idd, (void *)&limitMAX, (void *)&limitMIN); /*获取用户参数范围*/
            if (*(uint32_t *)value >= limitMIN || *(uint32_t *)value <= limitMAX)
            {
                PV(uint32_t, idx, idd) = *(uint32_t *)value;
                return TRUE;
            }
        }
        break;
#endif

#if (ValueUnion_uint64 == 1)
#define int8_t_SELECT(id)
#define int16_t_SELECT(id)
#define int32_t_SELECT(id)
#define int64_t_SELECT(id)
#define uint8_t_SELECT(id)
#define uint16_t_SELECT(id)
#define uint32_t_SELECT(id)
#define uint64_t_SELECT(id) case USR_##id:
#define char_SELECT(id)
#define int_SELECT(id)
#define float_SELECT(id)
#define double_SELECT(id)
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) type##_SELECT(name)
        PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
#undef int8_t_SELECT
#undef int16_t_SELECT
#undef int32_t_SELECT
#undef int64_t_SELECT
#undef uint8_t_SELECT
#undef uint16_t_SELECT
#undef uint32_t_SELECT
#undef uint64_t_SELECT
#undef char_SELECT
#undef int_SELECT
#undef float_SELECT
#undef double_SELECT
        {
            uint64_t limitMAX, limitMIN;                                      /*限幅缓存*/
            parame_socpe_obt(idx, idd, (void *)&limitMAX, (void *)&limitMIN); /*获取用户参数范围*/
            if (*(uint64_t *)value >= limitMIN || *(uint64_t *)value <= limitMAX)
            {
                PV(uint64_t, idx, idd) = *(uint64_t *)value;
                return TRUE;
            }
        }
        break;
#endif

#if (ValueUnion_int == 1)
#define int8_t_SELECT(id)
#define int16_t_SELECT(id)
#define int32_t_SELECT(id)
#define int64_t_SELECT(id)
#define uint8_t_SELECT(id)
#define uint16_t_SELECT(id)
#define uint32_t_SELECT(id)
#define uint64_t_SELECT(id)
#define char_SELECT(id)
#define int_SELECT(id) case USR_##id:
#define float_SELECT(id)
#define double_SELECT(id)
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) type##_SELECT(name)
        PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
#undef int8_t_SELECT
#undef int16_t_SELECT
#undef int32_t_SELECT
#undef int64_t_SELECT
#undef uint8_t_SELECT
#undef uint16_t_SELECT
#undef uint32_t_SELECT
#undef uint64_t_SELECT
#undef char_SELECT
#undef int_SELECT
#undef float_SELECT
#undef double_SELECT
        {
            int limitMAX, limitMIN;                                           /*限幅缓存*/
            parame_socpe_obt(idx, idd, (void *)&limitMAX, (void *)&limitMIN); /*获取用户参数范围*/
            if (*(int *)value >= limitMIN || *(int *)value <= limitMAX)
            {
                PV(int, idx, idd) = *(int *)value;
                return TRUE;
            }
        }
        break;
#endif

#if (ValueUnion_float == 1)
#define int8_t_SELECT(id)
#define int16_t_SELECT(id)
#define int32_t_SELECT(id)
#define int64_t_SELECT(id)
#define uint8_t_SELECT(id)
#define uint16_t_SELECT(id)
#define uint32_t_SELECT(id)
#define uint64_t_SELECT(id)
#define char_SELECT(id)
#define int_SELECT(id)
#define float_SELECT(id) case USR_##id:
#define double_SELECT(id)
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) type##_SELECT(name)
        PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
#undef int8_t_SELECT
#undef int16_t_SELECT
#undef int32_t_SELECT
#undef int64_t_SELECT
#undef uint8_t_SELECT
#undef uint16_t_SELECT
#undef uint32_t_SELECT
#undef uint64_t_SELECT
#undef char_SELECT
#undef int_SELECT
#undef float_SELECT
#undef double_SELECT
        {
            float limitMAX, limitMIN;                                         /*限幅缓存*/
            parame_socpe_obt(idx, idd, (void *)&limitMAX, (void *)&limitMIN); /*获取用户参数范围*/
            if (*(float *)value >= limitMIN || *(float *)value <= limitMAX)
            {
                PV(float, idx, idd) = *(float *)value;
                return TRUE;
            }
        }
        break;
#endif

#if (ValueUnion_double == 1)
#define int8_t_SELECT(id)
#define int16_t_SELECT(id)
#define int32_t_SELECT(id)
#define int64_t_SELECT(id)
#define uint8_t_SELECT(id)
#define uint16_t_SELECT(id)
#define uint32_t_SELECT(id)
#define uint64_t_SELECT(id)
#define char_SELECT(id)
#define int_SELECT(id)
#define float_SELECT(id)
#define double_SELECT(id) case USR_##id:
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) type##_SELECT(name)
        PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
#undef int8_t_SELECT
#undef int16_t_SELECT
#undef int32_t_SELECT
#undef int64_t_SELECT
#undef uint8_t_SELECT
#undef uint16_t_SELECT
#undef uint32_t_SELECT
#undef uint64_t_SELECT
#undef char_SELECT
#undef int_SELECT
#undef float_SELECT
#undef double_SELECT
        {
            double limitMAX, limitMIN;                                        /*限幅缓存*/
            parame_socpe_obt(idx, idd, (void *)&limitMAX, (void *)&limitMIN); /*获取用户参数范围*/
            if (*(double *)value >= limitMIN || *(double *)value <= limitMAX)
            {
                PV(double, idx, idd) = *(double *)value;
                return TRUE;
            }
        }
        break;
#endif
    default:
        break;
    }
    return FALSE;
}

/*
*********************************************************************************************************
*	函 数 名: ScanDataChanges10ms
*	形    参: 无
*	返 回 值: 无
*	功能说明: 扫描数据变化。非阻塞，被10ms周期性的调用
*********************************************************************************************************
*/
void ScanDataChanges10ms(void)
{
    uint8_t idx, idd;
    for (idx = 0; idx < PARAM_ITEM_NUM; idx++)
    {
        switch (idx)
        {
#if (ValueUnion_int8 == 1 || ValueUnion_char == 1)
#define int8_t_SELECT(id) case USR_##id:
#define int16_t_SELECT(id)
#define int32_t_SELECT(id)
#define int64_t_SELECT(id)
#define uint8_t_SELECT(id)
#define uint16_t_SELECT(id)
#define uint32_t_SELECT(id)
#define uint64_t_SELECT(id)
#define char_SELECT(id) case USR_##id:
#define int_SELECT(id)
#define float_SELECT(id)
#define double_SELECT(id)
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) type##_SELECT(name)
            PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
#undef int8_t_SELECT
#undef int16_t_SELECT
#undef int32_t_SELECT
#undef int64_t_SELECT
#undef uint8_t_SELECT
#undef uint16_t_SELECT
#undef uint32_t_SELECT
#undef uint64_t_SELECT
#undef char_SELECT
#undef int_SELECT
#undef float_SELECT
#undef double_SELECT
            for (idd = 0; idd < parames_config_num[idx]; idd++)
            {
                if (PV(int8_t, idx, idd) != PLV(int8_t, idx, idd))
                {
                    PLV(int8_t, idx, idd) = PV(int8_t, idx, idd);
                    if (parames_config_update[idx] != NULL)
                    {
                        parames_config_update[idx]();
                    }
                }
            }
            break;
#endif

#if (ValueUnion_int16 == 1)
#define int8_t_SELECT(id)
#define int16_t_SELECT(id) case USR_##id:
#define int32_t_SELECT(id)
#define int64_t_SELECT(id)
#define uint8_t_SELECT(id)
#define uint16_t_SELECT(id)
#define uint32_t_SELECT(id)
#define uint64_t_SELECT(id)
#define char_SELECT(id)
#define int_SELECT(id)
#define float_SELECT(id)
#define double_SELECT(id)
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) type##_SELECT(name)
            PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
#undef int8_t_SELECT
#undef int16_t_SELECT
#undef int32_t_SELECT
#undef int64_t_SELECT
#undef uint8_t_SELECT
#undef uint16_t_SELECT
#undef uint32_t_SELECT
#undef uint64_t_SELECT
#undef char_SELECT
#undef int_SELECT
#undef float_SELECT
#undef double_SELECT
            for (idd = 0; idd < parames_config_num[idx]; idd++)
            {
                if (PV(int16_t, idx, idd) != PLV(int16_t, idx, idd))
                {
                    PLV(int16_t, idx, idd) = PV(int16_t, idx, idd);
                    if (parames_config_update[idx] != NULL)
                    {
                        parames_config_update[idx]();
                    }
                }
            }
            break;
#endif

#if (ValueUnion_int32 == 1)
#define int8_t_SELECT(id)
#define int16_t_SELECT(id)
#define int32_t_SELECT(id) case USR_##id:
#define int64_t_SELECT(id)
#define uint8_t_SELECT(id)
#define uint16_t_SELECT(id)
#define uint32_t_SELECT(id)
#define uint64_t_SELECT(id)
#define char_SELECT(id)
#define int_SELECT(id)
#define float_SELECT(id)
#define double_SELECT(id)
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) type##_SELECT(name)
            PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
#undef int8_t_SELECT
#undef int16_t_SELECT
#undef int32_t_SELECT
#undef int64_t_SELECT
#undef uint8_t_SELECT
#undef uint16_t_SELECT
#undef uint32_t_SELECT
#undef uint64_t_SELECT
#undef char_SELECT
#undef int_SELECT
#undef float_SELECT
#undef double_SELECT
            for (idd = 0; idd < parames_config_num[idx]; idd++)
            {
                if (PV(int32_t, idx, idd) != PLV(int32_t, idx, idd))
                {
                    PLV(int32_t, idx, idd) = PV(int32_t, idx, idd);
                    if (parames_config_update[idx] != NULL)
                    {
                        parames_config_update[idx]();
                    }
                }
            }
            break;
#endif

#if (ValueUnion_int64 == 1)
#define int8_t_SELECT(id)
#define int16_t_SELECT(id)
#define int32_t_SELECT(id)
#define int64_t_SELECT(id) case USR_##id:
#define uint8_t_SELECT(id)
#define uint16_t_SELECT(id)
#define uint32_t_SELECT(id)
#define uint64_t_SELECT(id)
#define char_SELECT(id)
#define int_SELECT(id)
#define float_SELECT(id)
#define double_SELECT(id)
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) type##_SELECT(name)
            PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
#undef int8_t_SELECT
#undef int16_t_SELECT
#undef int32_t_SELECT
#undef int64_t_SELECT
#undef uint8_t_SELECT
#undef uint16_t_SELECT
#undef uint32_t_SELECT
#undef uint64_t_SELECT
#undef char_SELECT
#undef int_SELECT
#undef float_SELECT
#undef double_SELECT
            for (idd = 0; idd < parames_config_num[idx]; idd++)
            {
                if (PV(int64_t, idx, idd) != PLV(int64_t, idx, idd))
                {
                    PLV(int64_t, idx, idd) = PV(int64_t, idx, idd);
                    if (parames_config_update[idx] != NULL)
                    {
                        parames_config_update[idx]();
                    }
                }
            }
            break;
#endif

#if (ValueUnion_uint8 == 1)
#define int8_t_SELECT(id)
#define int16_t_SELECT(id)
#define int32_t_SELECT(id)
#define int64_t_SELECT(id)
#define uint8_t_SELECT(id) case USR_##id:
#define uint16_t_SELECT(id)
#define uint32_t_SELECT(id)
#define uint64_t_SELECT(id)
#define char_SELECT(id)
#define int_SELECT(id)
#define float_SELECT(id)
#define double_SELECT(id)
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) type##_SELECT(name)
            PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
#undef int8_t_SELECT
#undef int16_t_SELECT
#undef int32_t_SELECT
#undef int64_t_SELECT
#undef uint8_t_SELECT
#undef uint16_t_SELECT
#undef uint32_t_SELECT
#undef uint64_t_SELECT
#undef char_SELECT
#undef int_SELECT
#undef float_SELECT
#undef double_SELECT
            for (idd = 0; idd < parames_config_num[idx]; idd++)
            {
                if (PV(uint8_t, idx, idd) != PLV(uint8_t, idx, idd))
                {
                    PLV(uint8_t, idx, idd) = PV(uint8_t, idx, idd);
                    if (parames_config_update[idx] != NULL)
                    {
                        parames_config_update[idx]();
                    }
                }
            }
            break;
#endif

#if (ValueUnion_uint16 == 1)
#define int8_t_SELECT(id)
#define int16_t_SELECT(id)
#define int32_t_SELECT(id)
#define int64_t_SELECT(id)
#define uint8_t_SELECT(id)
#define uint16_t_SELECT(id) case USR_##id:
#define uint32_t_SELECT(id)
#define uint64_t_SELECT(id)
#define char_SELECT(id)
#define int_SELECT(id)
#define float_SELECT(id)
#define double_SELECT(id)
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) type##_SELECT(name)
            PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
#undef int8_t_SELECT
#undef int16_t_SELECT
#undef int32_t_SELECT
#undef int64_t_SELECT
#undef uint8_t_SELECT
#undef uint16_t_SELECT
#undef uint32_t_SELECT
#undef uint64_t_SELECT
#undef char_SELECT
#undef int_SELECT
#undef float_SELECT
#undef double_SELECT
            for (idd = 0; idd < parames_config_num[idx]; idd++)
            {
                if (PV(uint16_t, idx, idd) != PLV(uint16_t, idx, idd))
                {
                    PLV(uint16_t, idx, idd) = PV(uint16_t, idx, idd);
                    if (parames_config_update[idx] != NULL)
                    {
                        parames_config_update[idx]();
                    }
                }
            }
            break;
#endif

#if (ValueUnion_uint32 == 1)
#define int8_t_SELECT(id)
#define int16_t_SELECT(id)
#define int32_t_SELECT(id)
#define int64_t_SELECT(id)
#define uint8_t_SELECT(id)
#define uint16_t_SELECT(id)
#define uint32_t_SELECT(id) case USR_##id:
#define uint64_t_SELECT(id)
#define char_SELECT(id)
#define int_SELECT(id)
#define float_SELECT(id)
#define double_SELECT(id)
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) type##_SELECT(name)
            PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
#undef int8_t_SELECT
#undef int16_t_SELECT
#undef int32_t_SELECT
#undef int64_t_SELECT
#undef uint8_t_SELECT
#undef uint16_t_SELECT
#undef uint32_t_SELECT
#undef uint64_t_SELECT
#undef char_SELECT
#undef int_SELECT
#undef float_SELECT
#undef double_SELECT
            for (idd = 0; idd < parames_config_num[idx]; idd++)
            {
                if (PV(uint32_t, idx, idd) != PLV(uint32_t, idx, idd))
                {
                    PLV(uint32_t, idx, idd) = PV(uint32_t, idx, idd);
                    if (parames_config_update[idx] != NULL)
                    {
                        parames_config_update[idx]();
                    }
                }
            }
            break;
#endif

#if (ValueUnion_uint64 == 1)
#define int8_t_SELECT(id)
#define int16_t_SELECT(id)
#define int32_t_SELECT(id)
#define int64_t_SELECT(id)
#define uint8_t_SELECT(id)
#define uint16_t_SELECT(id)
#define uint32_t_SELECT(id)
#define uint64_t_SELECT(id) case USR_##id:
#define char_SELECT(id)
#define int_SELECT(id)
#define float_SELECT(id)
#define double_SELECT(id)
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) type##_SELECT(name)
            PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
#undef int8_t_SELECT
#undef int16_t_SELECT
#undef int32_t_SELECT
#undef int64_t_SELECT
#undef uint8_t_SELECT
#undef uint16_t_SELECT
#undef uint32_t_SELECT
#undef uint64_t_SELECT
#undef char_SELECT
#undef int_SELECT
#undef float_SELECT
#undef double_SELECT
            for (idd = 0; idd < parames_config_num[idx]; idd++)
            {
                if (PV(uint64_t, idx, idd) != PLV(uint64_t, idx, idd))
                {
                    PLV(uint64_t, idx, idd) = PV(uint64_t, idx, idd);
                    if (parames_config_update[idx] != NULL)
                    {
                        parames_config_update[idx]();
                    }
                }
            }
            break;
#endif

#if (ValueUnion_int == 1)
#define int8_t_SELECT(id)
#define int16_t_SELECT(id)
#define int32_t_SELECT(id)
#define int64_t_SELECT(id)
#define uint8_t_SELECT(id)
#define uint16_t_SELECT(id)
#define uint32_t_SELECT(id)
#define uint64_t_SELECT(id)
#define char_SELECT(id)
#define int_SELECT(id) case USR_##id:
#define float_SELECT(id)
#define double_SELECT(id)
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) type##_SELECT(name)
            PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
#undef int8_t_SELECT
#undef int16_t_SELECT
#undef int32_t_SELECT
#undef int64_t_SELECT
#undef uint8_t_SELECT
#undef uint16_t_SELECT
#undef uint32_t_SELECT
#undef uint64_t_SELECT
#undef char_SELECT
#undef int_SELECT
#undef float_SELECT
#undef double_SELECT
            for (idd = 0; idd < parames_config_num[idx]; idd++)
            {
                if (PV(int, idx, idd) != PLV(int, idx, idd))
                {
                    PLV(int, idx, idd) = PV(int, idx, idd);
                    if (parames_config_update[idx] != NULL)
                    {
                        parames_config_update[idx]();
                    }
                }
            }
            break;
#endif

#if (ValueUnion_float == 1)
#define int8_t_SELECT(id)
#define int16_t_SELECT(id)
#define int32_t_SELECT(id)
#define int64_t_SELECT(id)
#define uint8_t_SELECT(id)
#define uint16_t_SELECT(id)
#define uint32_t_SELECT(id)
#define uint64_t_SELECT(id)
#define char_SELECT(id)
#define int_SELECT(id)
#define float_SELECT(id) case USR_##id:
#define double_SELECT(id)
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) type##_SELECT(name)
            PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
#undef int8_t_SELECT
#undef int16_t_SELECT
#undef int32_t_SELECT
#undef int64_t_SELECT
#undef uint8_t_SELECT
#undef uint16_t_SELECT
#undef uint32_t_SELECT
#undef uint64_t_SELECT
#undef char_SELECT
#undef int_SELECT
#undef float_SELECT
#undef double_SELECT
            for (idd = 0; idd < parames_config_num[idx]; idd++)
            {
                if (PV(float, idx, idd) != PLV(float, idx, idd))
                {
                    PLV(float, idx, idd) = PV(float, idx, idd);
                    if (parames_config_update[idx] != NULL)
                    {
                        parames_config_update[idx]();
                    }
                }
            }
            break;
#endif

#if (ValueUnion_double == 1)
#define int8_t_SELECT(id)
#define int16_t_SELECT(id)
#define int32_t_SELECT(id)
#define int64_t_SELECT(id)
#define uint8_t_SELECT(id)
#define uint16_t_SELECT(id)
#define uint32_t_SELECT(id)
#define uint64_t_SELECT(id)
#define char_SELECT(id)
#define int_SELECT(id)
#define float_SELECT(id)
#define double_SELECT(id) case USR_##id:
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) type##_SELECT(name)
            PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
#undef int8_t_SELECT
#undef int16_t_SELECT
#undef int32_t_SELECT
#undef int64_t_SELECT
#undef uint8_t_SELECT
#undef uint16_t_SELECT
#undef uint32_t_SELECT
#undef uint64_t_SELECT
#undef char_SELECT
#undef int_SELECT
#undef float_SELECT
#undef double_SELECT
            for (idd = 0; idd < parames_config_num[idx]; idd++)
            {
                if (PV(double, idx, idd) != PLV(double, idx, idd))
                {
                    PLV(double, idx, idd) = PV(double, idx, idd);
                    if (parames_config_update[idx] != NULL)
                    {
                        parames_config_update[idx]();
                    }
                }
            }
            break;
#endif
        default:
            break;
        }
    }
}

/*
*********************************************************************************************************
*	函 数 名: Init_parame_level
*	形    参: level_:等于等级的参数都会被初始化
*	形    参: idd_：参数深度(0xFF:全部初始化)
*	返 回 值: 无
*	功能说明: 按等级初始化参数
*********************************************************************************************************
*/
void Init_parame_level(PARAME_RET_LEVEL level_, uint8_t idd_)
{
    uint8_t idx, idd;
    for (idx = 0; idx < PARAM_ITEM_NUM; idx++)
    {
        switch (idx)
        {
#if (ValueUnion_int8 == 1 || ValueUnion_char == 1)
#define int8_t_SELECT(id) case USR_##id:
#define int16_t_SELECT(id)
#define int32_t_SELECT(id)
#define int64_t_SELECT(id)
#define uint8_t_SELECT(id)
#define uint16_t_SELECT(id)
#define uint32_t_SELECT(id)
#define uint64_t_SELECT(id)
#define char_SELECT(id) case USR_##id:
#define int_SELECT(id)
#define float_SELECT(id)
#define double_SELECT(id)
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) type##_SELECT(name)
            PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
#undef int8_t_SELECT
#undef int16_t_SELECT
#undef int32_t_SELECT
#undef int64_t_SELECT
#undef uint8_t_SELECT
#undef uint16_t_SELECT
#undef uint32_t_SELECT
#undef uint64_t_SELECT
#undef char_SELECT
#undef int_SELECT
#undef float_SELECT
#undef double_SELECT
            if (idd_ == 0xFF)
            {
                for (idd = 0; idd < parames_config_num[idx]; idd++)
                {
                    if (parames_config_level[idx] == level_)
                    {
                        PV(int8_t, idx, idd) = PI(int8_t, idx, idd);
                    }
                }
            }
            else
            {
                if (parames_config_level[idx] == level_)
                {
                    PV(int8_t, idx, idd_) = PI(int8_t, idx, idd_);
                }
            }
            break;
#endif

#if (ValueUnion_int16 == 1)
#define int8_t_SELECT(id)
#define int16_t_SELECT(id) case USR_##id:
#define int32_t_SELECT(id)
#define int64_t_SELECT(id)
#define uint8_t_SELECT(id)
#define uint16_t_SELECT(id)
#define uint32_t_SELECT(id)
#define uint64_t_SELECT(id)
#define char_SELECT(id)
#define int_SELECT(id)
#define float_SELECT(id)
#define double_SELECT(id)
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) type##_SELECT(name)
            PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
#undef int8_t_SELECT
#undef int16_t_SELECT
#undef int32_t_SELECT
#undef int64_t_SELECT
#undef uint8_t_SELECT
#undef uint16_t_SELECT
#undef uint32_t_SELECT
#undef uint64_t_SELECT
#undef char_SELECT
#undef int_SELECT
#undef float_SELECT
#undef double_SELECT
            if (idd_ == 0xFF)
            {
                for (idd = 0; idd < parames_config_num[idx]; idd++)
                {
                    if (parames_config_level[idx] == level_)
                    {
                        PV(int16_t, idx, idd) = PI(int16_t, idx, idd);
                    }
                }
            }
            else
            {
                if (parames_config_level[idx] == level_)
                {
                    PV(int16_t, idx, idd_) = PI(int16_t, idx, idd_);
                }
            }
            break;
#endif

#if (ValueUnion_int32 == 1)
#define int8_t_SELECT(id)
#define int16_t_SELECT(id)
#define int32_t_SELECT(id) case USR_##id:
#define int64_t_SELECT(id)
#define uint8_t_SELECT(id)
#define uint16_t_SELECT(id)
#define uint32_t_SELECT(id)
#define uint64_t_SELECT(id)
#define char_SELECT(id)
#define int_SELECT(id)
#define float_SELECT(id)
#define double_SELECT(id)
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) type##_SELECT(name)
            PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
#undef int8_t_SELECT
#undef int16_t_SELECT
#undef int32_t_SELECT
#undef int64_t_SELECT
#undef uint8_t_SELECT
#undef uint16_t_SELECT
#undef uint32_t_SELECT
#undef uint64_t_SELECT
#undef char_SELECT
#undef int_SELECT
#undef float_SELECT
#undef double_SELECT
            if (idd_ == 0xFF)
            {
                for (idd = 0; idd < parames_config_num[idx]; idd++)
                {
                    if (parames_config_level[idx] == level_)
                    {
                        PV(int32_t, idx, idd) = PI(int32_t, idx, idd);
                    }
                }
            }
            else
            {
                if (parames_config_level[idx] == level_)
                {
                    PV(int32_t, idx, idd_) = PI(int32_t, idx, idd_);
                }
            }
            break;
#endif

#if (ValueUnion_int64 == 1)
#define int8_t_SELECT(id)
#define int16_t_SELECT(id)
#define int32_t_SELECT(id)
#define int64_t_SELECT(id) case USR_##id:
#define uint8_t_SELECT(id)
#define uint16_t_SELECT(id)
#define uint32_t_SELECT(id)
#define uint64_t_SELECT(id)
#define char_SELECT(id)
#define int_SELECT(id)
#define float_SELECT(id)
#define double_SELECT(id)
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) type##_SELECT(name)
            PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
#undef int8_t_SELECT
#undef int16_t_SELECT
#undef int32_t_SELECT
#undef int64_t_SELECT
#undef uint8_t_SELECT
#undef uint16_t_SELECT
#undef uint32_t_SELECT
#undef uint64_t_SELECT
#undef char_SELECT
#undef int_SELECT
#undef float_SELECT
#undef double_SELECT
            if (idd_ == 0xFF)
            {
                for (idd = 0; idd < parames_config_num[idx]; idd++)
                {
                    if (parames_config_level[idx] == level_)
                    {
                        PV(int64_t, idx, idd) = PI(int64_t, idx, idd);
                    }
                }
            }
            else
            {
                if (parames_config_level[idx] == level_)
                {
                    PV(int64_t, idx, idd_) = PI(int64_t, idx, idd_);
                }
            }
            break;
#endif

#if (ValueUnion_uint8 == 1)
#define int8_t_SELECT(id)
#define int16_t_SELECT(id)
#define int32_t_SELECT(id)
#define int64_t_SELECT(id)
#define uint8_t_SELECT(id) case USR_##id:
#define uint16_t_SELECT(id)
#define uint32_t_SELECT(id)
#define uint64_t_SELECT(id)
#define char_SELECT(id)
#define int_SELECT(id)
#define float_SELECT(id)
#define double_SELECT(id)
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) type##_SELECT(name)
            PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
#undef int8_t_SELECT
#undef int16_t_SELECT
#undef int32_t_SELECT
#undef int64_t_SELECT
#undef uint8_t_SELECT
#undef uint16_t_SELECT
#undef uint32_t_SELECT
#undef uint64_t_SELECT
#undef char_SELECT
#undef int_SELECT
#undef float_SELECT
#undef double_SELECT
            if (idd_ == 0xFF)
            {
                for (idd = 0; idd < parames_config_num[idx]; idd++)
                {
                    if (parames_config_level[idx] == level_)
                    {
                        PV(uint8_t, idx, idd) = PI(uint8_t, idx, idd);
                    }
                }
            }
            else
            {
                if (parames_config_level[idx] == level_)
                {
                    PV(uint8_t, idx, idd_) = PI(uint8_t, idx, idd_);
                }
            }
            break;
#endif

#if (ValueUnion_uint16 == 1)
#define int8_t_SELECT(id)
#define int16_t_SELECT(id)
#define int32_t_SELECT(id)
#define int64_t_SELECT(id)
#define uint8_t_SELECT(id)
#define uint16_t_SELECT(id) case USR_##id:
#define uint32_t_SELECT(id)
#define uint64_t_SELECT(id)
#define char_SELECT(id)
#define int_SELECT(id)
#define float_SELECT(id)
#define double_SELECT(id)
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) type##_SELECT(name)
            PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
#undef int8_t_SELECT
#undef int16_t_SELECT
#undef int32_t_SELECT
#undef int64_t_SELECT
#undef uint8_t_SELECT
#undef uint16_t_SELECT
#undef uint32_t_SELECT
#undef uint64_t_SELECT
#undef char_SELECT
#undef int_SELECT
#undef float_SELECT
#undef double_SELECT
            if (idd_ == 0xFF)
            {
                for (idd = 0; idd < parames_config_num[idx]; idd++)
                {
                    if (parames_config_level[idx] == level_)
                    {
                        PV(uint16_t, idx, idd) = PI(uint16_t, idx, idd);
                    }
                }
            }
            else
            {
                if (parames_config_level[idx] == level_)
                {
                    PV(uint16_t, idx, idd_) = PI(uint16_t, idx, idd_);
                }
            }
            break;
#endif

#if (ValueUnion_uint32 == 1)
#define int8_t_SELECT(id)
#define int16_t_SELECT(id)
#define int32_t_SELECT(id)
#define int64_t_SELECT(id)
#define uint8_t_SELECT(id)
#define uint16_t_SELECT(id)
#define uint32_t_SELECT(id) case USR_##id:
#define uint64_t_SELECT(id)
#define char_SELECT(id)
#define int_SELECT(id)
#define float_SELECT(id)
#define double_SELECT(id)
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) type##_SELECT(name)
            PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
#undef int8_t_SELECT
#undef int16_t_SELECT
#undef int32_t_SELECT
#undef int64_t_SELECT
#undef uint8_t_SELECT
#undef uint16_t_SELECT
#undef uint32_t_SELECT
#undef uint64_t_SELECT
#undef char_SELECT
#undef int_SELECT
#undef float_SELECT
#undef double_SELECT
            if (idd_ == 0xFF)
            {
                for (idd = 0; idd < parames_config_num[idx]; idd++)
                {
                    if (parames_config_level[idx] == level_)
                    {
                        PV(uint32_t, idx, idd) = PI(uint32_t, idx, idd);
                    }
                }
            }
            else
            {
                if (parames_config_level[idx] == level_)
                {
                    PV(uint32_t, idx, idd_) = PI(uint32_t, idx, idd_);
                }
            }
            break;
#endif

#if (ValueUnion_uint64 == 1)
#define int8_t_SELECT(id)
#define int16_t_SELECT(id)
#define int32_t_SELECT(id)
#define int64_t_SELECT(id)
#define uint8_t_SELECT(id)
#define uint16_t_SELECT(id)
#define uint32_t_SELECT(id)
#define uint64_t_SELECT(id) case USR_##id:
#define char_SELECT(id)
#define int_SELECT(id)
#define float_SELECT(id)
#define double_SELECT(id)
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) type##_SELECT(name)
            PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
#undef int8_t_SELECT
#undef int16_t_SELECT
#undef int32_t_SELECT
#undef int64_t_SELECT
#undef uint8_t_SELECT
#undef uint16_t_SELECT
#undef uint32_t_SELECT
#undef uint64_t_SELECT
#undef char_SELECT
#undef int_SELECT
#undef float_SELECT
#undef double_SELECT
            if (idd_ == 0xFF)
            {
                for (idd = 0; idd < parames_config_num[idx]; idd++)
                {
                    if (parames_config_level[idx] == level_)
                    {
                        PV(uint64_t, idx, idd) = PI(uint64_t, idx, idd);
                    }
                }
            }
            else
            {
                if (parames_config_level[idx] == level_)
                {
                    PV(uint64_t, idx, idd_) = PI(uint64_t, idx, idd_);
                }
            }
            break;
#endif

#if (ValueUnion_int == 1)
#define int8_t_SELECT(id)
#define int16_t_SELECT(id)
#define int32_t_SELECT(id)
#define int64_t_SELECT(id)
#define uint8_t_SELECT(id)
#define uint16_t_SELECT(id)
#define uint32_t_SELECT(id)
#define uint64_t_SELECT(id)
#define char_SELECT(id)
#define int_SELECT(id) case USR_##id:
#define float_SELECT(id)
#define double_SELECT(id)
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) type##_SELECT(name)
            PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
#undef int8_t_SELECT
#undef int16_t_SELECT
#undef int32_t_SELECT
#undef int64_t_SELECT
#undef uint8_t_SELECT
#undef uint16_t_SELECT
#undef uint32_t_SELECT
#undef uint64_t_SELECT
#undef char_SELECT
#undef int_SELECT
#undef float_SELECT
#undef double_SELECT
            if (idd_ == 0xFF)
            {
                for (idd = 0; idd < parames_config_num[idx]; idd++)
                {
                    if (parames_config_level[idx] == level_)
                    {
                        PV(int, idx, idd) = PI(int, idx, idd);
                    }
                }
            }
            else
            {
                if (parames_config_level[idx] == level_)
                {
                    PV(int, idx, idd_) = PI(int, idx, idd_);
                }
            }
            break;
#endif

#if (ValueUnion_float == 1)
#define int8_t_SELECT(id)
#define int16_t_SELECT(id)
#define int32_t_SELECT(id)
#define int64_t_SELECT(id)
#define uint8_t_SELECT(id)
#define uint16_t_SELECT(id)
#define uint32_t_SELECT(id)
#define uint64_t_SELECT(id)
#define char_SELECT(id)
#define int_SELECT(id)
#define float_SELECT(id) case USR_##id:
#define double_SELECT(id)
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) type##_SELECT(name)
            PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
#undef int8_t_SELECT
#undef int16_t_SELECT
#undef int32_t_SELECT
#undef int64_t_SELECT
#undef uint8_t_SELECT
#undef uint16_t_SELECT
#undef uint32_t_SELECT
#undef uint64_t_SELECT
#undef char_SELECT
#undef int_SELECT
#undef float_SELECT
#undef double_SELECT
            if (idd_ == 0xFF)
            {
                for (idd = 0; idd < parames_config_num[idx]; idd++)
                {
                    if (parames_config_level[idx] == level_)
                    {
                        PV(float, idx, idd) = PI(float, idx, idd);
                    }
                }
            }
            else
            {
                if (parames_config_level[idx] == level_)
                {
                    PV(float, idx, idd_) = PI(float, idx, idd_);
                }
            }
            break;
#endif

#if (ValueUnion_double == 1)
#define int8_t_SELECT(id)
#define int16_t_SELECT(id)
#define int32_t_SELECT(id)
#define int64_t_SELECT(id)
#define uint8_t_SELECT(id)
#define uint16_t_SELECT(id)
#define uint32_t_SELECT(id)
#define uint64_t_SELECT(id)
#define char_SELECT(id)
#define int_SELECT(id)
#define float_SELECT(id)
#define double_SELECT(id) case USR_##id:
#define PARAM_DEBUG_X(name, type, S_step, C_step, unit, mode, level, max_id, min_id, call, num, min, max, ...) type##_SELECT(name)
            PARAM_ITEM_LIST
#undef PARAM_DEBUG_X
#undef int8_t_SELECT
#undef int16_t_SELECT
#undef int32_t_SELECT
#undef int64_t_SELECT
#undef uint8_t_SELECT
#undef uint16_t_SELECT
#undef uint32_t_SELECT
#undef uint64_t_SELECT
#undef char_SELECT
#undef int_SELECT
#undef float_SELECT
#undef double_SELECT
            if (idd_ == 0xFF)
            {
                for (idd = 0; idd < parames_config_num[idx]; idd++)
                {
                    if (parames_config_level[idx] == level_)
                    {
                        PV(double, idx, idd) = PI(double, idx, idd);
                    }
                }
            }
            else
            {
                if (parames_config_level[idx] == level_)
                {
                    PV(double, idx, idd_) = PI(double, idx, idd_);
                }
            }
            break;
#endif
        default:
            break;
        }
    }
}
