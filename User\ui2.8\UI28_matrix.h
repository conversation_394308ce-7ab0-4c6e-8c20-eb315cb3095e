#ifndef _UI28_MATRIX_H_
#define _UI28_MATRIX_H_
#include "UI28.h"
// 获取字模地址
unsigned char *UI28_FontAddressAcquisition(char *character_, enum UI28_MATRIX_e_ FontSize);

#if (UI28_TEXT_GRADIENT_ENABLED == 1)
#define UI28_TEXT_GRADIENT_VARIABLE_COLOR_ENABLED 1 /* 启用渐变文字可变颜色 开关 */

/*创建文本渐变表*/
#define UI28_TEXT_GRADIENT_ITEM_LIST                                                                                                                                                                                  \
    UI28_TEXT_GRADIENT_DEBUG_X(T1 /*名字*/, 45 /*窗口X坐标*/, 180 /*窗口Y坐标*/, 240 /*窗口宽度*/, 32 /*字高*/, BLACK /*背景颜色*/, WHITE /*字颜色*/, 40 /*渐变最小值*/, 128 /*渐变最大值*/, ui28_cycle /*渐变方式*/) \
    UI28_TEXT_GRADIENT_DEBUG_X(T2 /*名字*/, 95 /*窗口X坐标*/, 190 /*窗口Y坐标*/, 128 /*窗口宽度*/, 32 /*字高*/, BLACK /*背景颜色*/, WHITE /*字颜色*/, 40 /*渐变最小值*/, 128 /*渐变最大值*/, ui28_cycle /*渐变方式*/) \
    UI28_TEXT_GRADIENT_DEBUG_X(T3 /*名字*/, 22 /*窗口X坐标*/, 12 /*窗口Y坐标*/, 28 /*窗口宽度*/, 28 /*字高*/, BLACK /*背景颜色*/, GREY /*字颜色*/, 40 /*渐变最小值*/, 128 /*渐变最大值*/, ui28_cycle /*渐变方式*/)    \
    UI28_TEXT_GRADIENT_DEBUG_X(T4 /*名字*/, 22 /*窗口X坐标*/, 42 /*窗口Y坐标*/, 28 /*窗口宽度*/, 28 /*字高*/, BLACK /*背景颜色*/, GREY /*字颜色*/, 40 /*渐变最小值*/, 128 /*渐变最大值*/, ui28_cycle /*渐变方式*/)    \
    UI28_TEXT_GRADIENT_DEBUG_X(T5 /*名字*/, 22 /*窗口X坐标*/, 171 /*窗口Y坐标*/, 28 /*窗口宽度*/, 28 /*字高*/, BLACK /*背景颜色*/, GREY /*字颜色*/, 40 /*渐变最小值*/, 128 /*渐变最大值*/, ui28_cycle /*渐变方式*/)   \
    UI28_TEXT_GRADIENT_DEBUG_X(T6 /*名字*/, 22 /*窗口X坐标*/, 201 /*窗口Y坐标*/, 28 /*窗口宽度*/, 28 /*字高*/, BLACK /*背景颜色*/, GREY /*字颜色*/, 40 /*渐变最小值*/, 128 /*渐变最大值*/, ui28_cycle /*渐变方式*/)   \
    UI28_TEXT_GRADIENT_DEBUG_X(T7 /*名字*/, 232 /*窗口X坐标*/, 65 /*窗口Y坐标*/, 72 /*窗口宽度*/, 36 /*字高*/, GREEN3 /*背景颜色*/, WHITE /*字颜色*/, 40 /*渐变最小值*/, 128 /*渐变最大值*/, ui28_cycle /*渐变方式*/) \
    UI28_TEXT_GRADIENT_DEBUG_X(T8 /*名字*/, 260 /*窗口X坐标*/, 15 /*窗口Y坐标*/, 48 /*窗口宽度*/, 24 /*字高*/, BLACK /*背景颜色*/, GREY /*字颜色*/, 40 /*渐变最小值*/, 128 /*渐变最大值*/, ui28_cycle /*渐变方式*/)\
    UI28_TEXT_GRADIENT_DEBUG_X(T9 /*名字*/, 28 /*窗口X坐标*/, 83 /*窗口Y坐标*/, 32 /*窗口宽度*/, 16 /*字高*/, BLUE3 /*背景颜色*/, BLACK /*字颜色*/, 40 /*渐变最小值*/, 128 /*渐变最大值*/, ui28_cycle /*渐变方式*/)\
    UI28_TEXT_GRADIENT_DEBUG_X(T10 /*名字*/, 28 /*窗口X坐标*/, 142 /*窗口Y坐标*/, 32 /*窗口宽度*/, 16 /*字高*/, YELLOW2 /*背景颜色*/, BLACK /*字颜色*/, 40 /*渐变最小值*/, 128 /*渐变最大值*/, ui28_cycle /*渐变方式*/)\
    UI28_TEXT_GRADIENT_DEBUG_X(T11 /*名字*/, 75 /*窗口X坐标*/, 90 /*窗口Y坐标*/, 150 /*窗口宽度*/, 20 /*字高*/, BLACK /*背景颜色*/, BLUE4 /*字颜色*/, 40 /*渐变最小值*/, 128 /*渐变最大值*/, ui28_cycle /*渐变方式*/)\
    UI28_TEXT_GRADIENT_DEBUG_X(T12 /*名字*/, 75 /*窗口X坐标*/, 130 /*窗口Y坐标*/, 170 /*窗口宽度*/, 20 /*字高*/, BLACK /*背景颜色*/, YELLOW3 /*字颜色*/, 40 /*渐变最小值*/, 128 /*渐变最大值*/, ui28_cycle /*渐变方式*/)\
    UI28_TEXT_GRADIENT_DEBUG_X(T13 /*名字*/, 281 /*窗口X坐标*/, 113 /*窗口Y坐标*/, 35 /*窗口宽度*/, 14 /*字高*/, WHITE /*背景颜色*/, BLACK /*字颜色*/, 40 /*渐变最小值*/, 128 /*渐变最大值*/, ui28_cycle /*渐变方式*/) \
    UI28_TEXT_GRADIENT_DEBUG_X(T14 /*名字*/, 135 /*窗口X坐标*/, 6 /*窗口Y坐标*/, 128 /*窗口宽度*/, 32 /*字高*/, BLACK /*背景颜色*/, WHITE /*字颜色*/, 40 /*渐变最小值*/, 128 /*渐变最大值*/, ui28_cycle /*渐变方式*/) \
    UI28_TEXT_GRADIENT_DEBUG_X(T15 /*名字*/, 95 /*窗口X坐标*/, 102 /*窗口Y坐标*/, 225 /*窗口宽度*/, 36 /*字高*/, RED /*背景颜色*/, YELLOW /*字颜色*/, 40 /*渐变最小值*/, 128 /*渐变最大值*/, ui28_cycle /*渐变方式*/) \
    UI28_TEXT_GRADIENT_DEBUG_X(T16 /*名字*/, 119 /*窗口X坐标*/, 2 /*窗口Y坐标*/, 144 /*窗口宽度*/, 18 /*字高*/, BLACK /*背景颜色*/, WHITE /*字颜色*/, 40 /*渐变最小值*/, 128 /*渐变最大值*/, ui28_cycle /*渐变方式*/) \
    UI28_TEXT_GRADIENT_DEBUG_X(T17 /*名字*/, 119 /*窗口X坐标*/, 25 /*窗口Y坐标*/, 144 /*窗口宽度*/, 18 /*字高*/, BLACK /*背景颜色*/, WHITE /*字颜色*/, 40 /*渐变最小值*/, 128 /*渐变最大值*/, ui28_cycle /*渐变方式*/)

    /* 文本渐变ID */
enum UI28_TEXT_GRADIENT_e_
{
#define UI28_TEXT_GRADIENT_DEBUG_X(Tname, Tx, Ty, Tw, Th, T0, T1, Tmin, Tmax, Tc) ui28_##Tname##_tg,
    UI28_TEXT_GRADIENT_ITEM_LIST
#undef UI28_TEXT_GRADIENT_DEBUG_X
        UI28_TEXT_GRADIENT_ITEM_NUM,
};

enum UI28_CHANGE_
{
    ui28_fall = 0, /* 降 */
    ui28_rise,     /* 升 */
    ui28_cycle     /* 循环 */
};


// #if (UI28_TEXT_GRADIENT_ITEM_NUM > 64)
// #error "相册表不能超过64"
// #elif (UI28_TEXT_GRADIENT_ITEM_NUM > 32)
// typedef unsigned long long uint_Gradient_t;
// #elif (UI28_TEXT_GRADIENT_ITEM_NUM > 16)
typedef unsigned int uint_Gradient_t;
// #elif (UI28_TEXT_GRADIENT_ITEM_NUM > 8)
// typedef unsigned short uint_Gradient_t;
// #else
// typedef unsigned char uint_Gradient_t;
// #endif

extern volatile uint_Gradient_t ui28_Text_Gradient_display; /* 文本渐变显示标志位 */
extern volatile uint_Gradient_t ui28_Text_Gradient_Synchronized; /* 文本渐变同步标志位 */

/* 文本渐变显示开关（关实际只是不刷新了） */
void UI28_display_onoff_text_gradient(enum UI28_TEXT_GRADIENT_e_ GRADIENT_, unsigned char _onoff_);
/* 设置文本地址 */
void UI28_set_up_text_gradient_add(enum UI28_TEXT_GRADIENT_e_ GRADIENT_, volatile char *txet_add);
/* 初始化渐变值 */
void UI28_init_text_gradient_alpha(enum UI28_TEXT_GRADIENT_e_ GRADIENT_);
/* 更新文本渐变 */
void UI28_update_text_gradient(enum UI28_TEXT_GRADIENT_e_ GRADIENT_);
/* 设置文本同步渐变 */
void UI28_set_synchronous_gradient_text_gradient(enum UI28_TEXT_GRADIENT_e_ GRADIENT_, unsigned char _onoff_);
#if (UI28_TEXT_GRADIENT_VARIABLE_COLOR_ENABLED == 1)
/* 更改颜色 */
void UI28_change_color_album_text_gradient(enum UI28_TEXT_GRADIENT_e_ GRADIENT_, unsigned short color0, unsigned short color_1);
#endif
#endif
#endif
