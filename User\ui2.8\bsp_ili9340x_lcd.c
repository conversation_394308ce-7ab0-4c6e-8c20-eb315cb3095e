#include "bsp.h"

/**
 * @brief  向ILI9340X写入命令
 * @param  usCmd :要写入的命令（表寄存器地址）
 * @retval 无
 */
void ILI9340X_Write_Cmd(uint8_t usCmd)
{
	ILI9340X_CS_CLR;		 // 开始片选
	ILI9340X_RS_CLR;		 // 写命令
	ILI9340X_RD_SET;		 // 禁止读
	ILI9340X_DATAOUT(usCmd); // 输出命令
	ILI9340X_WR_CLR;		 // 写入开始
	ILI9340X_WR_SET;		 // 写入结束
	ILI9340X_CS_SET;		 // 结束片选
}

/**
 * @brief  向ILI9340X写入数据
 * @param  usData :要写入的数据
 * @retval 无
 */
void ILI9340X_Write_Data(uint8_t *usData, int num)
{
	ILI9340X_CS_CLR; // 开始片选
	ILI9340X_RS_SET; // 写数据
	ILI9340X_RD_SET; // 禁止读
	for (int i = 0; i < num; i++)
	{
		ILI9340X_DATAOUT(usData[i]); // 输出数据
		ILI9340X_WR_CLR;			 // 写入开始
		ILI9340X_WR_SET;			 // 写入结束
	}
	ILI9340X_CS_SET; // 结束片选
}

/**
 * @brief  向ILI9340X写入数据16位
 * @param  usData :要写入的数据
 * @retval 无
 */
void ILI9340X_Write_Data16bit(uint16_t *usData, int num)
{
	ILI9340X_CS_CLR; // 开始片选
	ILI9340X_RS_SET; // 写数据
	ILI9340X_RD_SET; // 禁止读
	for (int i = 0; i < num; i++)
	{
		ILI9340X_DATAOUT(usData[i] >> 8);	  // 输出数据
		ILI9340X_WR_CLR;					  // 写入开始
		ILI9340X_WR_SET;					  // 写入结束
		ILI9340X_DATAOUT((uint8_t)usData[i]); // 输出数据
		ILI9340X_WR_CLR;					  // 写入开始
		ILI9340X_WR_SET;					  // 写入结束
	}
	ILI9340X_CS_SET; // 结束片选
}

/**
 * @brief  从ILI9340X读取数据
 * @param  无
 * @retval 读取到的数据
 */
uint8_t ILI9340X_Read_Data(void)
{
	uint32_t a;
	uint8_t data;
	a = (EXPAND_PORT(_ILI_DATA_PORT_)->CRL) & 0XFFFF0000 + 0X00008888;
	EXPAND_PORT(_ILI_DATA_PORT_)->CRL = a; // 上拉输入
	a = (EXPAND_PORT(_ILI_DATA_PORT_)->CRH) & 0XFFFF0000 + 0X00008888;
	EXPAND_PORT(_ILI_DATA_PORT_)->CRH = a;		 // 上拉输入
	EXPAND_PORT(_ILI_DATA_PORT_)->ODR &= 0XFF00; // 全部输出0

	ILI9340X_RS_SET;
	ILI9340X_WR_SET;

	ILI9340X_CS_CLR;
	// 读取数据
	ILI9340X_RD_CLR;

	data = ILI9340X_DATAIN;
	ILI9340X_RD_SET;
	ILI9340X_CS_SET;

	a = (EXPAND_PORT(_ILI_DATA_PORT_)->CRL) & 0XFFFF0000 + 0X00003333;
	EXPAND_PORT(_ILI_DATA_PORT_)->CRL = a; // 上拉输出
	a = (EXPAND_PORT(_ILI_DATA_PORT_)->CRH) & 0XFFFF0000 + 0X00003333;
	EXPAND_PORT(_ILI_DATA_PORT_)->CRH = a;		 // 上拉输出
	EXPAND_PORT(_ILI_DATA_PORT_)->ODR |= 0X00FF; // 全部输出高
	return data;
}

/**
 * @brief  初始化ILI9340X的IO引脚
 * @param  无
 * @retval 无
 */
static void ILI9340X_GPIO_Config(void)
{
	GPIO_InitTypeDef GPIO_InitStructure;

	/* 使能复用IO时钟*/
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_AFIO, ENABLE);
	// 开启SWD，失能JTAG (部分PB引脚用在了jtag接口，改成SWD接口就不会有干扰)
	GPIO_PinRemapConfig(GPIO_Remap_SWJ_JTAGDisable, ENABLE);

	/* 使能对应相应管脚时钟*/
	RCC_APB2PeriphClockCmd(
		/*控制信号*/
		EXPAND_CLK(_ILI_CS_PORT_) | EXPAND_CLK(_ILI_RS_PORT_) | EXPAND_CLK(_ILI_WR_PORT_) |
			EXPAND_CLK(_ILI_RD_PORT_) | EXPAND_CLK(_ILI_BK_PORT_) |
			/*复位信号*/
			EXPAND_CLK(_ILI_RST_PORT_) |
			/*数据信号*/
			EXPAND_CLK(_ILI_DATA_PORT_),
		ENABLE);

	/* 配置液晶相对应的数据线,PORT-D0~D15 */
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP;

	GPIO_InitStructure.GPIO_Pin = 0x00FF;
	GPIO_Init(EXPAND_PORT(_ILI_DATA_PORT_), &GPIO_InitStructure);

	/* 配置液晶相对应的控制线
	 * 读        :LCD-RD
	 * 写        :LCD-WR
	 * 片选       :LCD-CS
	 * 数据/命令  :LCD-DC
	 */
	GPIO_InitStructure.GPIO_Pin = EXPAND_PIN(_ILI_RD_PIN_);
	GPIO_Init(EXPAND_PORT(_ILI_RD_PORT_), &GPIO_InitStructure);

	GPIO_InitStructure.GPIO_Pin = EXPAND_PIN(_ILI_WR_PIN_);
	GPIO_Init(EXPAND_PORT(_ILI_WR_PORT_), &GPIO_InitStructure);

	GPIO_InitStructure.GPIO_Pin = EXPAND_PIN(_ILI_CS_PIN_);
	GPIO_Init(EXPAND_PORT(_ILI_CS_PORT_), &GPIO_InitStructure);

	GPIO_InitStructure.GPIO_Pin = EXPAND_PIN(_ILI_RS_PIN_);
	GPIO_Init(EXPAND_PORT(_ILI_RS_PORT_), &GPIO_InitStructure);

	/* 配置LCD背光控制管脚BK*/
	GPIO_InitStructure.GPIO_Pin = EXPAND_PIN(_ILI_BK_PIN_);
	GPIO_Init(EXPAND_PORT(_ILI_BK_PORT_), &GPIO_InitStructure);
}

/**
 * @brief  初始化ILI9340X寄存器
 * @param  无
 * @retval 无
 */
void ILI9340X_REG_Config(void)
{
	ILI9340X_DEBUG_DELAY();
	ILI9340X_Write_Cmd(0xB4); // 显示反转控制
	__I uint8_t lcd_buff1[1] = {0x80};
	ILI9340X_Write_Data((uint8_t *)&lcd_buff1, 1);

	ILI9340X_DEBUG_DELAY();
	ILI9340X_Write_Cmd(0xB7);
	__I uint8_t lcd_buff2[7] = {0xFF, 0x44, 0x04, 0x44, 0x04, 0x02, 0x04};
	ILI9340X_Write_Data((uint8_t *)&lcd_buff2, 7);

	ILI9340X_DEBUG_DELAY();
	ILI9340X_Write_Cmd(0xBA); // 电源控制
	__I uint8_t lcd_buff3[3] = {0x1F, 0x11, 0x20};
	ILI9340X_Write_Data((uint8_t *)&lcd_buff3, 3);

	ILI9340X_DEBUG_DELAY();
	ILI9340X_Write_Cmd(0xBB); // 电源控制 2
	__I uint8_t lcd_buff4[3] = {0x74, 0x77, 0x33};
	ILI9340X_Write_Data((uint8_t *)&lcd_buff4, 3);

	ILI9340X_DEBUG_DELAY();
	ILI9340X_Write_Cmd(0xCD);
	__I uint8_t lcd_buff5[3] = {0x26, 0x26, 0x00};
	ILI9340X_Write_Data((uint8_t *)&lcd_buff5, 3);

	ILI9340X_DEBUG_DELAY();
	ILI9340X_Write_Cmd(0xE8);
	__I uint8_t lcd_buff6[5] = {0x11, 0x11, 0x33, 0x11, 0x55};
	ILI9340X_Write_Data((uint8_t *)&lcd_buff6, 5);

	ILI9340X_DEBUG_DELAY();
	ILI9340X_Write_Cmd(0xE9);
	__I uint8_t lcd_buff7[9] = {0x40, 0x84, 0x65, 0x30, 0xC0, 0x00, 0xFF, 0x00, 0x88};
	ILI9340X_Write_Data((uint8_t *)&lcd_buff7, 9);

	ILI9340X_DEBUG_DELAY();
	ILI9340X_Write_Cmd(0xEA);
	__I uint8_t lcd_buff8[7] = {0x03, 0x22, 0x18, 0xE2, 0x04, 0x00, 0x00};
	ILI9340X_Write_Data((uint8_t *)&lcd_buff8, 7);

	ILI9340X_DEBUG_DELAY();
	ILI9340X_Write_Cmd(0xEC);
	__I uint8_t lcd_buff9[1] = {0x48};
	ILI9340X_Write_Data((uint8_t *)&lcd_buff9, 1);

	ILI9340X_DEBUG_DELAY();
	ILI9340X_Write_Cmd(0xF2);
	__I uint8_t lcd_buff10[2] = {0x00, 0x00};
	ILI9340X_Write_Data((uint8_t *)&lcd_buff10, 2);

	ILI9340X_DEBUG_DELAY();
	ILI9340X_Write_Cmd(0xF5);
	__I uint8_t lcd_buff11[1] = {0xBB};
	ILI9340X_Write_Data((uint8_t *)&lcd_buff11, 1);

	ILI9340X_DEBUG_DELAY();
	ILI9340X_Write_Cmd(0x36); // 内存访问控制（控制屏幕方向）
	__I uint8_t lcd_buff12[1] = {0x60};
	ILI9340X_Write_Data((uint8_t *)&lcd_buff12, 1);

	ILI9340X_DEBUG_DELAY();
	ILI9340X_Write_Cmd(0x3a); // COLMOD：像素格式集
	__I uint8_t lcd_buff13[1] = {0x05};
	ILI9340X_Write_Data((uint8_t *)&lcd_buff13, 1);

	ILI9340X_DEBUG_DELAY();
	ILI9340X_Write_Cmd(0xE4);
	__I uint8_t lcd_buff14[15] = {0x00, 0x0B, 0x11, 0x03, 0x10, 0x06, 0x37, 0x36, 0x4C, 0x02, 0x0B, 0x0A, 0x2D, 0x34, 0x0d};
	ILI9340X_Write_Data((uint8_t *)&lcd_buff14, 15);

	ILI9340X_DEBUG_DELAY();
	ILI9340X_Write_Cmd(0xE5);
	__I uint8_t lcd_buff15[15] = {0x00, 0x0B, 0x11, 0x04, 0x0F, 0x06, 0x37, 0x45, 0x4C, 0x02, 0x0A, 0x0A, 0x2D, 0x34, 0x0d};
	ILI9340X_Write_Data((uint8_t *)&lcd_buff15, 15);

	ILI9340X_DEBUG_DELAY();
	ILI9340X_Write_Cmd(0x11);

	bsp_DelayMS(120);
	ILI9340X_Write_Cmd(0x29);
	ILI9340X_DEBUG_DELAY();
	ILI9340X_Write_Cmd(0x2c);
}

/**
 * @brief  ILI9340X初始化函数，如果要用到lcd，一定要调用这个函数
 * @param  无
 * @retval 无
 */
void ILI9340X_Init(void)
{
	ILI9340X_GPIO_Config();
	
	ILI9340X_BackLed_Control ( ENABLE );      //点亮LCD背光灯

	GPIO_SetBits(EXPAND_PORT(_ILI_RST_PORT_), EXPAND_PIN(_ILI_RST_PIN_)); // 复位
	
	ILI9340X_REG_Config();
}

/**
 * @brief  ILI9340X背光LED控制
 * @param  enumState ：决定是否使能背光LED
 *   该参数为以下值之一：
 *     @arg ENABLE :使能背光LED
 *     @arg DISABLE :禁用背光LED
 * @retval 无
 */
void ILI9340X_BackLed_Control(FunctionalState enumState)
{
	if (enumState)
		GPIO_SetBits(EXPAND_PORT(_ILI_BK_PORT_), EXPAND_PIN(_ILI_BK_PIN_));
	else
		GPIO_ResetBits(EXPAND_PORT(_ILI_BK_PORT_), EXPAND_PIN(_ILI_BK_PIN_));
}

/**
 * @brief  在ILI9340X显示器上开辟一个窗口
 * @param  usX ：在特定扫描方向下窗口的起点X坐标
 * @param  usY ：在特定扫描方向下窗口的起点Y坐标
 * @param  usWidth ：窗口的宽度
 * @param  usHeight ：窗口的高度
 * @retval 无
 */
void ILI9340X_OpenWindow(uint16_t usX, uint16_t usY, uint16_t usWidth, uint16_t usHeight)
{
	uint16_t _Data;
	ILI9340X_Write_Cmd(CMD_SetCoordinateX); /* 设置X坐标 */
	ILI9340X_Write_Data16bit(&usX, 1);
	_Data = usX + usWidth - 1;
	ILI9340X_Write_Data16bit(&_Data, 1);

	ILI9340X_Write_Cmd(CMD_SetCoordinateY); /* 设置Y坐标*/
	ILI9340X_Write_Data16bit(&usY, 1);
	_Data = usY + usHeight - 1;
	ILI9340X_Write_Data16bit(&_Data, 1);
}

/**
 * @brief  设定ILI9340X的光标坐标
 * @param  usX ：在特定扫描方向下光标的X坐标
 * @param  usY ：在特定扫描方向下光标的Y坐标
 * @retval 无
 */
static void ILI9340X_SetCursor(uint16_t usX, uint16_t usY)
{
	ILI9340X_OpenWindow(usX, usY, 1, 1);
}

/**
 * @brief  在ILI9340X显示器上以某一颜色填充像素点
 * @param  ulAmout_Point ：要填充颜色的像素点的总数目
 * @param  usColor ：颜色
 * @retval 无
 */
void ILI9340X_FillColor(uint32_t ulAmout_Point, uint16_t usColor)
{
	uint32_t i = 0;
	/* memory write */
	ILI9340X_Write_Cmd(CMD_SetPixel);

	for (i = 0; i < ulAmout_Point; i++)
	{
		ILI9340X_Write_Data16bit(&usColor, 1);
	}
}

/**
 * @brief  在ILI9340X显示器上填充像素点
 * @param  ulAmout_Point ：要填充颜色的像素点的总数目
 * @param  Picture_ ：图地址
 * @retval 无
 */
void ILI9340X_FillPicture(uint32_t ulAmout_Point, uint16_t *Picture_)
{
	uint32_t i = 0;
	/* memory write */
	ILI9340X_Write_Cmd(CMD_SetPixel);

	for (i = 0; i < ulAmout_Point; i++)
	{
		ILI9340X_Write_Data16bit(&Picture_[i], 1);
	}
}

/**
 * @brief  对ILI9340X显示器的某一窗口以某种颜色进行清屏
 * @param  usX ：在特定扫描方向下窗口的起点X坐标
 * @param  usY ：在特定扫描方向下窗口的起点Y坐标
 * @param  usWidth ：窗口的宽度
 * @param  usHeight ：窗口的高度
 * @param  usColors ：设置颜色
 * @retval 无
 */
void ILI9340X_Clear(uint16_t usX, uint16_t usY, uint16_t usWidth, uint16_t usHeight, uint16_t usColors)
{
	ILI9340X_OpenWindow(usX, usY, usWidth, usHeight);
	ILI9340X_FillColor(usWidth * usHeight, usColors);
}

/**
 * @brief  对ILI9340X显示器的某一窗口刷图
 * @param  usX ：在特定扫描方向下窗口的起点X坐标
 * @param  usY ：在特定扫描方向下窗口的起点Y坐标
 * @param  usWidth ：窗口的宽度
 * @param  usHeight ：窗口的高度
 * @param  usPicture ：图地址
 * @retval 无
 */
void ILI9340X_Picture(uint16_t usX, uint16_t usY, uint16_t usWidth, uint16_t usHeight, uint16_t *usPicture)
{
	ILI9340X_OpenWindow(usX, usY, usWidth, usHeight);
	ILI9340X_FillPicture(usWidth * usHeight, usPicture);
}

/**
 * @brief  对ILI9340X显示器的某一窗口刷双色图
 * @param  usX ：在特定扫描方向下窗口的起点X坐标
 * @param  usY ：在特定扫描方向下窗口的起点Y坐标
 * @param  usWidth ：窗口的宽度
 * @param  usHeight ：窗口的高度
 * @param  usColor_0 ：0bit颜色
 * @param  usColor_1 ：1bit颜色
 * @param  usPicture ：图地址
 * @retval 无
 */
void ILI9340X_TwoColorChart(uint16_t usX, uint16_t usY, uint16_t usWidth, uint16_t usHeight, uint16_t usColor_0, uint16_t usColor_1, uint8_t *usTwoColor)
{
	int usW, usH;
	uint16_t i = 0;
	uint8_t j = 0;

	ILI9340X_OpenWindow(usX, usY, usWidth, usHeight);
	/* memory write */
	ILI9340X_Write_Cmd(CMD_SetPixel);

	// 一行一行处理要显示的颜色
	for (usH = 0; usH < usHeight; usH++)
	{
		// 如果上一行的一个字节有多余没处理完，直接跳到下一个字节
		if (j > 0)
		{
			j = 0;
			i++;
		}
		// 一位一位处理要显示的颜色
		for (usW = 0; usW < usWidth; usW++)
		{
			if (usTwoColor[i] & (0x80 >> j))
			{
				ILI9340X_Write_Data16bit(&usColor_1, 1);
			}
			else
			{
				ILI9340X_Write_Data16bit(&usColor_0, 1);
			}

			// 如果超过一个字节就跳到下一个字节
			if (++j > 7)
			{
				j = 0;
				i++;
			}
		}
	}
}

/**
 * @brief  对ILI9340X显示器的某一窗口刷双色图（带反色框）
 * @param  usX ：在特定扫描方向下窗口的起点X坐标
 * @param  usY ：在特定扫描方向下窗口的起点Y坐标
 * @param  usWidth ：窗口的宽度
 * @param  usHeight ：窗口的高度
 * @param  usX_R ：X轴反色的坐标
 * @param  usW_R ：X轴反色的宽度
 * @param  usY_R ：Y轴反色的坐标
 * @param  usH_R ：Y轴反色的高度
 * @param  ucR_angle ：反色框的R角表地址
 * @param  usColor_0 ：0bit颜色
 * @param  usColor_1 ：1bit颜色
 * @param  usPicture ：图地址
 * @retval 无
 */
void ILI9340X_TwoColorChart_XYReversed(uint16_t usX, uint16_t usY, uint16_t usWidth, uint16_t usHeight, uint16_t usX_R, uint16_t usW_R, uint16_t usY_R, uint16_t usH_R, uint8_t *ucR_angle, uint16_t usColor_0, uint16_t usColor_1, uint8_t *usTwoColor)
{
	int usW, usH;
	uint16_t i = 0;
	uint8_t j = 0;
	uint16_t Color_1, Color_0;

	ILI9340X_OpenWindow(usX, usY, usWidth, usHeight);
	/* memory write */
	ILI9340X_Write_Cmd(CMD_SetPixel);

	// 一行一行处理要显示的颜色
	for (usH = 0; usH < usHeight; usH++)
	{
		// 如果上一行的一个字节有多余没处理完，直接跳到下一个字节
		if (j > 0)
		{
			j = 0;
			i++;
		}

		// 一位一位处理要显示的颜色
		for (usW = 0; usW < usWidth; usW++)
		{
			// 根据反色范围确定颜色
			if ((usY + usH) >= usY_R && (usY + usH) < (usY_R + usH_R))
			{
				// 给不需要反色的位置做调整
				if ((usX + usW) < usX_R || (usX + usW) >= (usX_R + usW_R))
				{
					Color_1 = usColor_1;
					Color_0 = usColor_0;
				}
				else
				{
					Color_1 = usColor_0;
					Color_0 = usColor_1;

					if ((usY + usH - usY_R) < (ucR_angle[0] - 2))
					{
						if ((usX + usW - usX_R) < ucR_angle[usY + usH +1 - usY_R] || (usX + usW - usX_R) >= (usW_R - ucR_angle[usY + usH +1 - usY_R]))
						{
							/* 有R角的行才能进入 */
							Color_1 = usColor_1;
							Color_0 = usColor_0;
						}
					}
					else if ((usY + usH - usY_R) >= (usH_R - ucR_angle[0]))
					{
						if ((usX + usW - usX_R) < ucR_angle[usH_R + usY_R -usY - usH] || (usX + usW - usX_R) >= (usW_R - ucR_angle[usH_R + usY_R -usY - usH]))
						{
							/* 有R角的行才能进入 */
							Color_1 = usColor_1;
							Color_0 = usColor_0;
						}
					}
				}
			}
			else
			{
				Color_1 = usColor_1;
				Color_0 = usColor_0;
			}

			if (usTwoColor[i] & (0x80 >> j))
			{
				ILI9340X_Write_Data16bit(&Color_1, 1);
			}
			else
			{
				ILI9340X_Write_Data16bit(&Color_0, 1);
			}

			// 如果超过一个字节就跳到下一个字节
			if (++j > 7)
			{
				j = 0;
				i++;
			}
		}
	}
}

/**
 * @brief  在 ILI9340X 显示器上画一个矩形
 * @param  usX_Start ：在特定扫描方向下矩形的起始点X坐标
 * @param  usY_Start ：在特定扫描方向下矩形的起始点Y坐标
 * @param  usWidth：矩形的宽度（单位：像素）
 * @param  usHeight：矩形的高度（单位：像素）
 * @param  usColor_0 ：0bit颜色
 * @param  usColor_1 ：1bit颜色
 * @param  ucR_angle ：像素R角表地址
 * @retval 无
 */
void ILI9340X_DrawRectangle(uint16_t usX_Start, uint16_t usY_Start, uint16_t usWidth, uint16_t usHeight, uint16_t usColor_0, uint16_t usColor_1, uint8_t *ucR_angle)
{
	int usW, usH;

	if (ucR_angle[0] > (usWidth >> 1) || ucR_angle[0] > (usHeight >> 1))
	{
		return; /* R角过大，就返回 */
	}

	ILI9340X_OpenWindow(usX_Start, usY_Start, usWidth, usHeight);
	/* memory write */
	ILI9340X_Write_Cmd(CMD_SetPixel);
	// 一行一行处理要显示的颜色
	// 上面R角行
	for (usH = 1; usH <= ucR_angle[0]; usH++)
	{
		for (usW = 0; usW < usWidth; usW++)
		{
			if (usW < ucR_angle[usH] || (usWidth - usW) <= ucR_angle[usH])
			{
				ILI9340X_Write_Data16bit(&usColor_0, 1);
			}
			else
			{
				ILI9340X_Write_Data16bit(&usColor_1, 1);
			}
		}
	}
	// 中间行
	for (; usH <= usHeight - ucR_angle[0]; usH++)
	{
		for (usW = 0; usW < usWidth; usW++)
		{
			ILI9340X_Write_Data16bit(&usColor_1, 1);
		}
	}
	// 下面R角行
	for (; usH <= usHeight; usH++)
	{
		for (usW = 0; usW < usWidth; usW++)
		{
			if (usW < ucR_angle[usHeight - usH + 1] || (usWidth - usW) <= ucR_angle[usHeight - usH + 1])
			{
				ILI9340X_Write_Data16bit(&usColor_0, 1);
			}
			else
			{
				ILI9340X_Write_Data16bit(&usColor_1, 1);
			}
		}
	}
}
