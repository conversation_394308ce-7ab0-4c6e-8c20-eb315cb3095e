#include "bsp_boot.h"
/* 初始化一个与APP共用的全局变量 */
uint32_t g_JumpInit __attribute__((section(".bss.NoInit")));
/* 升级握手表 */
__I uint8_t Handshake[4] = {0xD1, 0x2E, 0xE2, 0x1D};
/* 超时计数 */
// #define MAX_LENG 4//最大长度
__IO uint8_t Queur_Leng = 0; // 队列长度
//__IO uint8_t Queur_Cache[MAX_LENG]={0};//队列缓存区
/*
*********************************************************************************************************
*	函 数 名: BootAppReceiveDataProcessing
*	功能说明: BOOTAPP接收数据处理
*	形    参: _byte-接收的字节流
*	返 回 值: 无
*********************************************************************************************************
*/
void BootAppReceiveDataProcessing(uint8_t _data)
{
	//	if ((Queur_Leng!=0&&(_data==Handshake[Queur_Leng]&&Handshake[Queur_Leng-1]==Queur_Cache[Queur_Leng-1]))||(Queur_Leng==0&&_data==Handshake[0]))
	//	{
	//		Queur_Cache[Queur_Leng++]=_data;
	if (_data == Handshake[Queur_Leng])
	{
		Queur_Leng++;
		if (Queur_Leng == 4)
		{
			if ((BOOT_MISJUDGMENT_CONDITIONS) != 0)
			{
				Queur_Leng = 0;
			}
			else
			{
				// 握手成功
				g_JumpInit = 0x11111111;
				NVIC_SystemReset(); /* 复位CPU */
			}
		}
	}
	else
	{
		Queur_Leng = 0; // 队列长度
	}
}
