#include "bsp.h"
void RCC_ClkConfiguration(void);
/*
*********************************************************************************************************
*	函 数 名: bsp_Init
*	功能说明: 初始化硬件设备。只需要调用一次。该函数配置CPU寄存器和外设的寄存器并初始化一些全局变量。
*			 全局变量。
*	形    参：无
*	返 回 值: 无
*********************************************************************************************************
*/
void bsp_Init(void)
{
	/* AIR32F103 内置的 PLL 可输出 216MHz 时钟 */
	RCC_ClkConfiguration(); /* 配置时钟 */

	/* 优先级分组设置为4 */
	NVIC_PriorityGroupConfig(NVIC_PriorityGroup_4);

	/* 初始化系统滴答定时器 (此函数会开中断) */
	bsp_InitTimer();

	InitCRC(); /* 使能CRC时钟 */

	/*页面管理器 初始化*/
	page_mgr_init();

	/* 初始化各种驱动 */
#if (UI28_SCREEN_ENABLED == 1)
	UI28_Init(); /*2.8寸屏幕初始化*/
#endif

#if KEY_SWITCH
	bsp_InitKey(); /* 初始化按键 */
#endif

#if SIGNAL_SWITCH
	bsp_InitSignal(); /* 初始化信号 */
#endif

#if BEEP_SWITCH
	BEEP_InitHard(); /* 初始化蜂鸣器硬件 */
#endif
}

/*
*********************************************************************************************************
*	函 数 名: RCC_ClkConfiguration
*	功能说明: 配置时钟函数，使用外部晶振8Mhz,
*           SYSCLK=216MHz
*           AHB=216MHz
*           APB1=108MHz
*           APB2=216MHz
*	形    参：无
*	返 回 值: 无
*********************************************************************************************************
*/
void RCC_ClkConfiguration(void)
{
	RCC_DeInit(); /* 复位RCC寄存器 */

	RCC_HSEConfig(RCC_HSE_ON); /* 使能HSE */
	while (RCC_GetFlagStatus(RCC_FLAG_HSERDY) == RESET)
		/* 等待HSE就绪 */;

	RCC_PLLCmd(DISABLE);												   /* 关闭PLL */
	AIR_RCC_PLLConfig(RCC_PLLSource_HSE_Div1, RCC_PLLMul_27, FLASH_Div_2); /* 配置PLL,8*27=216MHz */

	RCC_PLLCmd(ENABLE); /* 使能PLL */
	while (RCC_GetFlagStatus(RCC_FLAG_PLLRDY) == RESET)
		/* 等待PLL就绪 */;

	RCC_SYSCLKConfig(RCC_SYSCLKSource_PLLCLK); /* 选择PLL作为系统时钟 */

	RCC_HCLKConfig(RCC_SYSCLK_Div1); /* 配置AHB时钟 */
	RCC_PCLK1Config(RCC_HCLK_Div2);	 /* 配置APB1时钟 */
	RCC_PCLK2Config(RCC_HCLK_Div1);	 /* 配置APB2时钟 */

	RCC_LSICmd(ENABLE); /* 使能内部低速时钟 */
	while (RCC_GetFlagStatus(RCC_FLAG_LSIRDY) == RESET)
		/* 等待LSI就绪 */;
	RCC_HSICmd(ENABLE); /* 使能内部高速时钟 */
	while (RCC_GetFlagStatus(RCC_FLAG_HSIRDY) == RESET)
		/* 等待HSI就绪 */;
}

/*
*********************************************************************************************************
*	函 数 名: bsp_RunPer10ms
*	功能说明: 该函数每隔10ms被Systick中断调用1次。详见 bsp_timer.c的定时中断服务程序。一些处理时间要求不严格的
*			任务可以放在此函数。比如：按键扫描、蜂鸣器鸣叫控制等。
*	形    参: 无
*	返 回 值: 无
*********************************************************************************************************
*/
void bsp_RunPer10ms(void)
{
#if KEY_SWITCH
	bsp_KeyScan10ms(); /* 扫描所有按键。非阻塞，被10ms周期性的调用 */
#endif

#if SIGNAL_SWITCH
	bsp_SignalScan10ms(); /* 扫描所有信号。非阻塞，被10ms周期性的调用 */
#endif

#if BEEP_SWITCH
	BEEP_Pro10ms(); /* 每隔10ms调用1次该函数，用于控制蜂鸣器发声 */
#endif
}
