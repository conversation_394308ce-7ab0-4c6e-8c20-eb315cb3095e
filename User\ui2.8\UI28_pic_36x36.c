#include "UI28.h"
struct PIC_36_36 const Pic_36x36[] =
{
  {0x00,0x07,0xfc,0x00,0x00,0x00,0x3f,0xff,0x80,0x00,0x00,0xff,0xff,0xf0,0x00,0x01,0xf8,0x03,0xf8,0x00,0x07,0xe0,0x00,0x7c,0x00,0x0f,0x80,0x00,0x1e,0x00,0x0f,0x00,0x00,0x0f,0x00,0x1e,0x00,0x00,0x07,0x80,0x3c,0x00,0x00,0x03,0x80,0x38,0x00,0x00,0x01,0xc0,0x78,0x00,0x00,0x01,0xc0,0x70,0x00,0x00,0x00,0xe0,0x70,0x00,0x00,0x00,0xe0,0x60,0x00,0x00,0x00,0x60,0xe0,0x00,0x00,0x00,0x70,0xe0,0x38,0xe0,0x30,0x70,0xf0,0x7f,0xf0,0x7e,0x70,0xf8,0xff,0xfd,0xff,0xf0,0xff,0xff,0xff,0xff,0xf0,0xff,0xff,0xff,0xff,0xf0,0xfe,0xff,0xff,0xf9,0xf0,0xfc,0x7f,0xff,0xf1,0xf0,0x78,0x7f,0xff,0xf9,0xe0,0x7c,0xff,0xff,0xff,0xe0,0x7f,0xff,0xff,0xff,0xe0,0x3f,0xff,0xff,0xff,0xe0,0x3f,0xff,0xff,0xff,0xc0,0x1f,0xff,0x3f,0xff,0xc0,0x1f,0xfe,0x1f,0xff,0x80,0x0f,0xff,0x3f,0xff,0x00,0x07,0xff,0xff,0xff,0x00,0x03,0xff,0xff,0xfe,0x00,0x01,0xff,0xff,0xf8,0x00,0x00,0xff,0xff,0xf0,0x00,0x00,0x3f,0xff,0xc0,0x00,0x00,0x03,0xfc,0x00,0x00},\
  {0x00,0x07,0xfc,0x00,0x00,0x00,0x3f,0xff,0x80,0x00,0x00,0xff,0xff,0xf0,0x00,0x01,0xf8,0x03,0xf8,0x00,0x07,0xe0,0x00,0x7c,0x00,0x0f,0x80,0x00,0x1e,0x00,0x0f,0x00,0x00,0x0f,0x00,0x1e,0x00,0x00,0x07,0x80,0x3c,0x00,0x00,0x03,0x80,0x38,0x00,0x00,0x01,0xc0,0x78,0x00,0x00,0x01,0xc0,0x70,0x00,0x00,0x00,0xe0,0x70,0x00,0x00,0x00,0xe0,0x60,0x00,0x00,0x00,0x60,0xe0,0x00,0x00,0x00,0x70,0xe0,0x00,0x00,0x00,0x70,0xe0,0x01,0x02,0x00,0x70,0xff,0x03,0xc7,0x03,0xf0,0xff,0x83,0xff,0x83,0xf0,0xff,0xff,0xff,0xff,0xf0,0xff,0xff,0xff,0xff,0xf0,0xff,0xff,0xff,0xff,0xf0,0x7f,0x9f,0xff,0xf3,0xe0,0x7f,0x0f,0xff,0xe1,0xe0,0x7f,0x0f,0xff,0xf3,0xe0,0x3f,0x9f,0xff,0xff,0xe0,0x3f,0xff,0xff,0xff,0xc0,0x1f,0xff,0xff,0xff,0xc0,0x1f,0xff,0xef,0xff,0x80,0x0f,0xff,0xc7,0xff,0x00,0x07,0xff,0x87,0xff,0x00,0x03,0xff,0xcf,0xfe,0x00,0x01,0xff,0xff,0xf8,0x00,0x00,0xff,0xff,0xf0,0x00,0x00,0x3f,0xff,0xc0,0x00,0x00,0x03,0xfc,0x00,0x00},\
  {0x00,0x07,0xfc,0x00,0x00,0x00,0x3f,0xff,0x80,0x00,0x00,0xff,0xff,0xf0,0x00,0x01,0xf8,0x03,0xf8,0x00,0x07,0xe0,0x00,0x7c,0x00,0x0f,0x80,0x00,0x1e,0x00,0x0f,0x00,0x00,0x0f,0x00,0x1e,0x00,0x00,0x07,0x80,0x3c,0x00,0x00,0x03,0x80,0x38,0x00,0x00,0x01,0xc0,0x78,0x00,0x00,0x01,0xc0,0x70,0x00,0x00,0x00,0xe0,0x70,0x00,0x00,0x00,0xe0,0x60,0x00,0x00,0x00,0x60,0xe0,0x00,0x00,0x00,0x70,0xe0,0x00,0x00,0x00,0x70,0xe0,0x00,0x00,0x00,0x70,0xe0,0x00,0x00,0x00,0x70,0xf8,0x00,0x00,0x01,0xf0,0xfc,0x03,0x06,0x03,0xf0,0xfe,0x07,0xbf,0x07,0xf0,0xff,0x0f,0xff,0x8f,0xf0,0x7f,0xff,0xff,0xff,0xe0,0x7f,0xff,0xff,0xff,0xe0,0x7f,0x9f,0xff,0xf1,0xe0,0x3f,0x0f,0xff,0xe0,0xe0,0x3f,0x0f,0xff,0xe0,0xc0,0x1f,0x9f,0xe7,0xf1,0xc0,0x1f,0xff,0xc3,0xff,0x80,0x0f,0xff,0xc3,0xff,0x00,0x07,0xff,0xe7,0xff,0x00,0x03,0xff,0xff,0xfe,0x00,0x01,0xff,0xff,0xf8,0x00,0x00,0xff,0xff,0xf0,0x00,0x00,0x3f,0xff,0xc0,0x00,0x00,0x03,0xfc,0x00,0x00},\
  {0x00,0x07,0xfc,0x00,0x00,0x00,0x3f,0xff,0x80,0x00,0x00,0xff,0xff,0xf0,0x00,0x01,0xf8,0x03,0xf8,0x00,0x07,0xe0,0x00,0x7c,0x00,0x0f,0x80,0x00,0x1e,0x00,0x0f,0x00,0x00,0x0f,0x00,0x1e,0x00,0x00,0x07,0x80,0x3c,0x00,0x00,0x03,0x80,0x38,0x00,0x00,0x01,0xc0,0x78,0x00,0x00,0x01,0xc0,0x70,0x00,0x00,0x00,0xe0,0x70,0x00,0x00,0x00,0xe0,0x60,0x00,0x00,0x00,0x60,0xe0,0x00,0x00,0x40,0x70,0xe1,0xc0,0x70,0xe0,0x70,0xe7,0xe0,0x7f,0xf0,0x70,0xff,0xf9,0xff,0xfd,0xf0,0xff,0xff,0xff,0xff,0xf0,0xff,0xff,0xff,0xff,0xf0,0xff,0xff,0xff,0xff,0xf0,0xfe,0x7f,0xff,0xf3,0xf0,0x7c,0x3f,0xff,0xe1,0xe0,0x7c,0x3f,0xff,0xe1,0xe0,0x7c,0x3f,0xff,0xf3,0xe0,0x3e,0x7f,0xff,0xff,0xe0,0x3f,0xff,0xff,0xff,0xc0,0x1f,0xff,0xff,0xff,0xc0,0x1f,0xff,0xcf,0xff,0x80,0x0f,0xff,0x87,0xff,0x00,0x07,0xff,0x87,0xff,0x00,0x03,0xff,0xcf,0xfe,0x00,0x01,0xff,0xff,0xf8,0x00,0x00,0xff,0xff,0xf0,0x00,0x00,0x3f,0xff,0xc0,0x00,0x00,0x03,0xfc,0x00,0x00},\
};
