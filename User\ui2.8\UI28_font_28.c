#include "UI28.h"
struct FONT28_ASCII const Font28_Ascii[] =
{
     " ",0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
     0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
     0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
     0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,  /* 0 */ 
};

struct FONT28_CHINESE const Font28_Chinese[] =
{
     "洗",0x10,0x00,0xF0,0x00,0x38,0x3C,0xF0,0x00,0x7C,0x3C,0xF0,0x00,0x3E,0x78,0xF0,0x00,
    0x1E,0x7F,0xFF,0xC0,0x0C,0x7F,0xFF,0xC0,0x00,0xFF,0xFF,0xC0,0x00,0xF0,0xF0,0x00,
    0x31,0xE0,0xF0,0x00,0x7D,0xE0,0xF0,0x00,0x7E,0x00,0xF0,0x00,0x3E,0xFF,0xFF,0xE0,
    0x1C,0xFF,0xFF,0xE0,0x08,0xFF,0xFF,0xE0,0x00,0xFF,0xFF,0xE0,0x18,0x0F,0x38,0x00,
    0x1E,0x0E,0x38,0x00,0x1E,0x1E,0x38,0x00,0x3C,0x1E,0x38,0x80,0x3C,0x1E,0x38,0xE0,
    0x3C,0x3E,0x38,0xF0,0x3C,0x3C,0x38,0xE0,0x78,0x7C,0x3C,0xE0,0x79,0xF8,0x3F,0xE0,
    0x79,0xF0,0x3F,0xE0,0x78,0xE0,0x1F,0xC0,0x00,0x40,0x00,0x00,0x00,0x00,0x00,0x00,/*"洗",0*/
     
     "涤",0x10,0x07,0x00,0x00,0x38,0x0F,0x00,0x00,0x7E,0x1F,0xFF,0x80,0x1F,0x3F,0xFF,0x80,
    0x0E,0x7F,0xFF,0x80,0x04,0xFC,0x0F,0x80,0x01,0xFE,0x1F,0x00,0x20,0xEF,0xBE,0x00,
    0x78,0x47,0xFC,0x00,0x7C,0x07,0xFE,0x00,0x7E,0xFF,0xFF,0xE0,0x3D,0xFF,0xBF,0xE0,
    0x08,0xFC,0x07,0xE0,0x00,0xE1,0xE0,0x40,0x00,0x01,0xE0,0x00,0x18,0xFF,0xFF,0xC0,
    0x1E,0xFF,0xFF,0xC0,0x1E,0xFF,0xFF,0xC0,0x1E,0x11,0xE2,0x00,0x3C,0x39,0xE7,0x00,
    0x3C,0x7D,0xEF,0x80,0x3C,0xF9,0xE7,0xC0,0x3D,0xF1,0xE3,0xE0,0x79,0xFF,0xE1,0xE0,
    0x79,0xCF,0xE0,0xC0,0x38,0x0F,0xC0,0x00,0x00,0x0F,0x00,0x00,0x00,0x00,0x00,0x00,/*"涤",1*/
     
     "漂",0x10,0x00,0x00,0x00,0x39,0xFF,0xFF,0xE0,0x7D,0xFF,0xFF,0xE0,0x3F,0xFF,0xFF,0xE0,
    0x1C,0x07,0x38,0x00,0x08,0xFF,0xFF,0xC0,0x00,0xFF,0xFF,0xE0,0x00,0xFF,0xFF,0xE0,
    0x30,0xE7,0x38,0xE0,0x7C,0xE7,0x38,0xE0,0x7E,0xFF,0xFF,0xE0,0x3E,0xFF,0xFF,0xE0,
    0x1C,0xFF,0xFF,0xE0,0x08,0x00,0x00,0x00,0x00,0x7F,0xFF,0x80,0x18,0x7F,0xFF,0x80,
    0x1E,0x7F,0xFF,0x80,0x1E,0x00,0x00,0x00,0x3D,0xFF,0xFF,0xE0,0x3D,0xFF,0xFF,0xE0,
    0x3D,0xFF,0xFF,0xE0,0x3C,0x39,0xE6,0x00,0x78,0xF9,0xE7,0xC0,0x7B,0xF9,0xE7,0xE0,
    0x7B,0xCF,0xE1,0xE0,0x79,0x0F,0xE0,0x60,0x00,0x07,0x80,0x00,0x00,0x00,0x00,0x00,/*"漂",2*/
};

UI28_MATRIX_XX_QUAN_LIST(28)

