#ifndef _PAGE_PARAMETER_H
#define _PAGE_PARAMETER_H
#include "bsp.h"

extern char value_user_1[7];	  /*菜单数值文本*/
extern char value_user_2[7];	  /*菜单数值文本*/
extern char value_user_3[7];	  /*菜单数值文本*/
extern char value_user_4[7];	  /*菜单数值文本*/
extern char value_user_5[7];	  /*菜单数值文本*/
extern char value_user_6[10];	  /*菜单数值文本*/
extern char value_user_7[7];	  /*菜单数值文本*/
extern char value_user_8[7];	  /*菜单数值文本*/
extern char value_user_9[7];	  /*菜单数值文本*/
extern char value_user_10[7];	  /*菜单数值文本*/
extern char value_user_11[7];	  /*菜单数值文本*/
extern char value_user_12[7];	  /*菜单数值文本*/
extern char value_user_13[7];	  /*菜单数值文本*/
extern char value_user_14[10];	  /*菜单数值文本*/
extern char value_user_15[10];	  /*菜单数值文本*/

extern char value_set_tad[4];	  /*菜单数值文本*/
extern char value_mill_1[16];	  /*菜单数值文本*/
extern char value_mill_2[16];	  /*菜单数值文本*/
extern char value_mill_3[10];	  /*菜单数值文本*/
extern char value_mill_4[10];	  /*菜单数值文本*/
extern char value_mill_5[13];	  /*菜单数值文本*/
extern char value_mill_6[13];	  /*菜单数值文本*/
extern char value_mill_7[13];	  /*菜单数值文本*/
extern char value_mill_8[13];	  /*菜单数值文本*/
extern char value_mill_9[10];	  /*菜单数值文本*/
extern char value_mill_10[13];	  /*菜单数值文本*/
extern char value_mill_11[13];	  /*菜单数值文本*/
extern char value_mill_12[13];	  /*菜单数值文本*/
extern char value_mill_13[13];	  /*菜单数值文本*/
extern char value_mill_14[13];	  /*菜单数值文本*/
extern char value_mill_15[13];	  /*菜单数值文本*/
extern char value_mill_16[13];	  /*菜单数值文本*/
extern char value_mill_17[7];	  /*菜单数值文本*/
extern char value_mill_18[7];	  /*菜单数值文本*/
extern char value_mill_19[7];	  /*菜单数值文本*/
extern char value_mill_20[10];	  /*菜单数值文本*/
extern char value_mill_21[10];	  /*菜单数值文本*/
extern char value_mill_22[6];	  /*菜单数值文本*/
extern char value_mill_23[7];	  /*菜单数值文本*/
extern char value_mill_24[13]; /*菜单数值文本*/
extern char value_mill_25[7];  /*菜单数值文本*/
extern char value_mill_26[7];  /*菜单数值文本*/
extern char value_mill_27[13]; /*菜单数值文本*/
extern char value_mill_28[7];  /*菜单数值文本*/
extern char value_mill_29[7];  /*菜单数值文本*/

extern char value_relay_[7][10]; /*菜单数值文本*/

/* 根界面切换时调用 */
void page_parameter_root_load(void);
/* 交互界面切换时调用 */
void page_parameter_view_load(void);
/* 为交互界面时，会周期调用 */
void page_parameter_update(void);

#endif
