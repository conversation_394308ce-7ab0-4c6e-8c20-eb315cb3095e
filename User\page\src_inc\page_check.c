#include "page_check.h"

/*
*********************************************************************************************************
*	函 数 名: page_root_load
*	形    参: 无
*	返 回 值: 无
*	功能说明: 根界面切换时调用
*********************************************************************************************************
*/
void page_check_root_load(void)
{
}
/*
*********************************************************************************************************
*	函 数 名: page_view_load
*	形    参: 无
*	返 回 值: 无
*	功能说明: 交互界面切换时调用
*********************************************************************************************************
*/
void page_check_view_load(void)
{
    int i;
    bsp_SetKeyParam(KID_K1, 100, 0); // 支持长按1秒，无连发
    bsp_SetKeyParam(KID_K2, 100, 0); // 支持长按1秒，无连发
    bsp_SetKeyParam(KID_K3, 100, 0); // 支持长按1秒，无连发
    bsp_SetKeyParam(KID_K4, 100, 0); // 支持长按1秒，无连发
    bsp_SetKeyParam(KID_K5, 100, 0); // 支持长按1秒，无连发

    UI28_Clear_Screen();                                                                            /*2.8寸屏幕清屏，并关闭所有模块，一般切换界面时调用*/
    PixelPageCache2D_Text_customize(72, 16, 0, 0, ui28_char_16, "洗涤温度:");                       /*对二维像素页缓存的某一窗口刷字符串(自定义)*/
    ILI9340X_TwoColorChart(20, 3, 72, 16, BLACK, WHITE, (unsigned char *)&PixelPageCache2D[0][0]);  /*对ILI9340X显示器的某一窗口刷双色图*/
    PixelPageCache2D_Text_customize(72, 16, 0, 0, ui28_char_16, "漂洗温度:");                       /*对二维像素页缓存的某一窗口刷字符串(自定义)*/
    ILI9340X_TwoColorChart(180, 3, 72, 16, BLACK, WHITE, (unsigned char *)&PixelPageCache2D[0][0]); /*对ILI9340X显示器的某一窗口刷双色图*/
    ILI9340X_Clear(0, 23, 320, 1, WHITE);                                                           /*对ILI9340X显示器的某一窗口以某种颜色进行清屏*/

    PixelPageCache2D_Text_customize(32, 16, 0, 0, ui28_char_16, "洗高");                             /*对二维像素页缓存的某一窗口刷字符串(自定义)*/
    ILI9340X_TwoColorChart(16, 27, 32, 16, BLACK, WHITE, (unsigned char *)&PixelPageCache2D[0][0]);  /*对ILI9340X显示器的某一窗口刷双色图*/
    PixelPageCache2D_Text_customize(32, 16, 0, 0, ui28_char_16, "洗低:");                            /*对二维像素页缓存的某一窗口刷字符串(自定义)*/
    ILI9340X_TwoColorChart(80, 27, 32, 16, BLACK, WHITE, (unsigned char *)&PixelPageCache2D[0][0]);  /*对ILI9340X显示器的某一窗口刷双色图*/
    PixelPageCache2D_Text_customize(32, 16, 0, 0, ui28_char_16, "漂高");                             /*对二维像素页缓存的某一窗口刷字符串(自定义)*/
    ILI9340X_TwoColorChart(144, 27, 32, 16, BLACK, WHITE, (unsigned char *)&PixelPageCache2D[0][0]); /*对ILI9340X显示器的某一窗口刷双色图*/
    PixelPageCache2D_Text_customize(32, 16, 0, 0, ui28_char_16, "漂低:");                            /*对二维像素页缓存的某一窗口刷字符串(自定义)*/
    ILI9340X_TwoColorChart(208, 27, 32, 16, BLACK, WHITE, (unsigned char *)&PixelPageCache2D[0][0]); /*对ILI9340X显示器的某一窗口刷双色图*/
    PixelPageCache2D_Text_customize(32, 16, 0, 0, ui28_char_16, "机门:");                            /*对二维像素页缓存的某一窗口刷字符串(自定义)*/
    ILI9340X_TwoColorChart(272, 27, 32, 16, BLACK, WHITE, (unsigned char *)&PixelPageCache2D[0][0]); /*对ILI9340X显示器的某一窗口刷双色图*/
    UI28_draw_arc(24, 47, GET_R_ANGLE_BY_ROW(8), GET_R_ANGLE_BY_ROW(6), 0, 0, 0, 0, BLACK, WHITE);
    UI28_draw_arc(88, 47, GET_R_ANGLE_BY_ROW(8), GET_R_ANGLE_BY_ROW(6), 0, 0, 0, 0, BLACK, WHITE);
    UI28_draw_arc(152, 47, GET_R_ANGLE_BY_ROW(8), GET_R_ANGLE_BY_ROW(6), 0, 0, 0, 0, BLACK, WHITE);
    UI28_draw_arc(216, 47, GET_R_ANGLE_BY_ROW(8), GET_R_ANGLE_BY_ROW(6), 0, 0, 0, 0, BLACK, WHITE);
    UI28_draw_arc(280, 47, GET_R_ANGLE_BY_ROW(8), GET_R_ANGLE_BY_ROW(6), 0, 0, 0, 0, BLACK, WHITE);
    ILI9340X_Clear(0, 66, 320, 1, WHITE); /*对ILI9340X显示器的某一窗口以某种颜色进行清屏*/

    PixelPageCache2D_Text_customize(16, 16, 0, 0, ui28_char_16, "#1");                               /*对二维像素页缓存的某一窗口刷字符串(自定义)*/
    ILI9340X_TwoColorChart(32, 70, 16, 16, BLACK, WHITE, (unsigned char *)&PixelPageCache2D[0][0]);  /*对ILI9340X显示器的某一窗口刷双色图*/
    PixelPageCache2D_Text_customize(16, 16, 0, 0, ui28_char_16, "#2");                               /*对二维像素页缓存的某一窗口刷字符串(自定义)*/
    ILI9340X_TwoColorChart(112, 70, 16, 16, BLACK, WHITE, (unsigned char *)&PixelPageCache2D[0][0]); /*对ILI9340X显示器的某一窗口刷双色图*/
    PixelPageCache2D_Text_customize(16, 16, 0, 0, ui28_char_16, "#3");                               /*对二维像素页缓存的某一窗口刷字符串(自定义)*/
    ILI9340X_TwoColorChart(192, 70, 16, 16, BLACK, WHITE, (unsigned char *)&PixelPageCache2D[0][0]); /*对ILI9340X显示器的某一窗口刷双色图*/
    PixelPageCache2D_Text_customize(16, 16, 0, 0, ui28_char_16, "#4");                               /*对二维像素页缓存的某一窗口刷字符串(自定义)*/
    ILI9340X_TwoColorChart(272, 70, 16, 16, BLACK, WHITE, (unsigned char *)&PixelPageCache2D[0][0]); /*对ILI9340X显示器的某一窗口刷双色图*/
    PixelPageCache2D_Text_customize(16, 16, 0, 0, ui28_char_16, "#5");                               /*对二维像素页缓存的某一窗口刷字符串(自定义)*/
    ILI9340X_TwoColorChart(32, 143, 16, 16, BLACK, WHITE, (unsigned char *)&PixelPageCache2D[0][0]); /*对ILI9340X显示器的某一窗口刷双色图*/
    switch (Dis_type /*洗碗机类型*/)
    {
    case Dis_t_1 /*5继电器，2调速（催干剂 洗涤剂）*/:
        break;
    case Dis_t_2 /*7继电器，无调速*/:
        PixelPageCache2D_Text_customize(16, 16, 0, 0, ui28_char_16, "#6");                                /*对二维像素页缓存的某一窗口刷字符串(自定义)*/
        ILI9340X_TwoColorChart(112, 143, 16, 16, BLACK, WHITE, (unsigned char *)&PixelPageCache2D[0][0]); /*对ILI9340X显示器的某一窗口刷双色图*/
        PixelPageCache2D_Text_customize(16, 16, 0, 0, ui28_char_16, "#7");                                /*对二维像素页缓存的某一窗口刷字符串(自定义)*/
        ILI9340X_TwoColorChart(192, 143, 16, 16, BLACK, WHITE, (unsigned char *)&PixelPageCache2D[0][0]); /*对ILI9340X显示器的某一窗口刷双色图*/
        break;
    default:
        break;
    }

    for (i = USR_relay_1; i <= USR_relay_7; i++)
    {
        switch (PV(int, i, 0) /*获取参数值*/)
        {
        case HA_RELAY_empty /* 闲置 */:
            PixelPageCache2D_Text_customize(48, 16, 0, 0, ui28_char_16, " 闲置 "); /*对二维像素页缓存的某一窗口刷字符串(自定义)*/
            break;
        case HA_RELAY_DET /* 洗涤剂 */:
            PixelPageCache2D_Text_customize(48, 16, 0, 0, ui28_char_16, "洗涤剂"); /*对二维像素页缓存的某一窗口刷字符串(自定义)*/
            break;
        case HA_RELAY_DRY /* 干燥机 */:
            PixelPageCache2D_Text_customize(48, 16, 0, 0, ui28_char_16, "干燥机"); /*对二维像素页缓存的某一窗口刷字符串(自定义)*/
            break;
        case HA_RELAY_RI_HE /* 漂加热 */:
            PixelPageCache2D_Text_customize(48, 16, 0, 0, ui28_char_16, "漂加热"); /*对二维像素页缓存的某一窗口刷字符串(自定义)*/
            break;
        case HA_RELAY_WA_HE /* 洗加热 */:
            PixelPageCache2D_Text_customize(48, 16, 0, 0, ui28_char_16, "洗加热"); /*对二维像素页缓存的某一窗口刷字符串(自定义)*/
            break;
        case HA_RELAY_WAT /* 进水阀 */:
            PixelPageCache2D_Text_customize(48, 16, 0, 0, ui28_char_16, "进水阀"); /*对二维像素页缓存的某一窗口刷字符串(自定义)*/
            break;
        case HA_RELAY_RI_P /* 漂洗泵 */:
            PixelPageCache2D_Text_customize(48, 16, 0, 0, ui28_char_16, "漂洗泵"); /*对二维像素页缓存的某一窗口刷字符串(自定义)*/
            break;
        case HA_RELAY_WA_P /* 洗涤泵 */:
            PixelPageCache2D_Text_customize(48, 16, 0, 0, ui28_char_16, "洗涤泵"); /*对二维像素页缓存的某一窗口刷字符串(自定义)*/
            break;
        default:
            break;
        }

        switch (i)
        {
        case USR_relay_1:
            ILI9340X_TwoColorChart(16, 89, 48, 16, BLACK, WHITE, (unsigned char *)&PixelPageCache2D[0][0]); /*对ILI9340X显示器的某一窗口刷双色图*/
            break;
        case USR_relay_2:
            ILI9340X_TwoColorChart(96, 89, 48, 16, BLACK, WHITE, (unsigned char *)&PixelPageCache2D[0][0]); /*对ILI9340X显示器的某一窗口刷双色图*/
            break;
        case USR_relay_3:
            ILI9340X_TwoColorChart(176, 89, 48, 16, BLACK, WHITE, (unsigned char *)&PixelPageCache2D[0][0]); /*对ILI9340X显示器的某一窗口刷双色图*/
            break;
        case USR_relay_4:
            ILI9340X_TwoColorChart(256, 89, 48, 16, BLACK, WHITE, (unsigned char *)&PixelPageCache2D[0][0]); /*对ILI9340X显示器的某一窗口刷双色图*/
            break;
        case USR_relay_5:
            ILI9340X_TwoColorChart(16, 162, 48, 16, BLACK, WHITE, (unsigned char *)&PixelPageCache2D[0][0]); /*对ILI9340X显示器的某一窗口刷双色图*/
            break;
        case USR_relay_6:
            ILI9340X_TwoColorChart(96, 162, 48, 16, BLACK, WHITE, (unsigned char *)&PixelPageCache2D[0][0]); /*对ILI9340X显示器的某一窗口刷双色图*/
            break;
        case USR_relay_7:
            ILI9340X_TwoColorChart(176, 162, 48, 16, BLACK, WHITE, (unsigned char *)&PixelPageCache2D[0][0]); /*对ILI9340X显示器的某一窗口刷双色图*/
            break;
        default:
            break;
        }

        if (Dis_type /*洗碗机类型*/ == Dis_t_1 /*5继电器，2调速（催干剂 洗涤剂）*/)
        {
            if (i == USR_relay_5)
            {
                break;
            }
        }
        else if (Dis_type /*洗碗机类型*/ == Dis_t_2 /*7继电器，无调速*/)
        {
            /* code */
        }
    }
    UI28_draw_arc(32, 113, GET_R_ANGLE_BY_ROW(8), GET_R_ANGLE_BY_ROW(6), 0, 0, 0, 0, BLACK, WHITE);
    UI28_draw_arc(112, 113, GET_R_ANGLE_BY_ROW(8), GET_R_ANGLE_BY_ROW(6), 0, 0, 0, 0, BLACK, WHITE);
    UI28_draw_arc(192, 113, GET_R_ANGLE_BY_ROW(8), GET_R_ANGLE_BY_ROW(6), 0, 0, 0, 0, BLACK, WHITE);
    UI28_draw_arc(272, 113, GET_R_ANGLE_BY_ROW(8), GET_R_ANGLE_BY_ROW(6), 0, 0, 0, 0, BLACK, WHITE);
    UI28_draw_arc(32, 186, GET_R_ANGLE_BY_ROW(8), GET_R_ANGLE_BY_ROW(6), 0, 0, 0, 0, BLACK, WHITE);

    switch (Dis_type /*洗碗机类型*/)
    {
    case Dis_t_1 /*5继电器，2调速（催干剂 洗涤剂）*/:
        break;
    case Dis_t_2 /*7继电器，无调速*/:
        UI28_draw_arc(112, 186, GET_R_ANGLE_BY_ROW(8), GET_R_ANGLE_BY_ROW(6), 0, 0, 0, 0, BLACK, WHITE);
        UI28_draw_arc(192, 186, GET_R_ANGLE_BY_ROW(8), GET_R_ANGLE_BY_ROW(6), 0, 0, 0, 0, BLACK, WHITE);
        break;
    default:
        break;
    }

    ILI9340X_Clear(0, 209, 320, 1, WHITE); /*对ILI9340X显示器的某一窗口以某种颜色进行清屏*/

    switch (Dis_type /*洗碗机类型*/)
    {
    case Dis_t_1 /*5继电器，2调速（催干剂 洗涤剂）*/:
        PixelPageCache2D_Text_customize(88, 16, 0, 0, ui28_char_16, "调速洗涤剂:");                       /*对二维像素页缓存的某一窗口刷字符串(自定义)*/
        ILI9340X_TwoColorChart(17, 217, 88, 16, BLACK, WHITE, (unsigned char *)&PixelPageCache2D[0][0]);  /*对ILI9340X显示器的某一窗口刷双色图*/
        PixelPageCache2D_Text_customize(88, 16, 0, 0, ui28_char_16, "调速干燥机:");                       /*对二维像素页缓存的某一窗口刷字符串(自定义)*/
        ILI9340X_TwoColorChart(177, 217, 88, 16, BLACK, WHITE, (unsigned char *)&PixelPageCache2D[0][0]); /*对ILI9340X显示器的某一窗口刷双色图*/
        break;
    case Dis_t_2 /*7继电器，无调速*/:
        break;
    default:
        break;
    }
}
/*
*********************************************************************************************************
*	函 数 名: page_update
*	形    参: 无
*	返 回 值: 无
*	功能说明: 为交互界面时，会周期调用
*********************************************************************************************************
*/
void page_check_update(void)
{
    uint16_t colour;
    int len;
    char txet_[7];

    if (temp_e_t[PTT1 /* 洗涤温度 */].probe[0].abnormal > 0)
    {
        sprintf(&txet_[0], "%s", " ");
    }
    else
    {
        len = sprintf(&txet_[0], "%d", (temp_e_t[PTT1 /* 洗涤温度 */].Displaytemp / 10));
        sprintf(&txet_[len], "%s", &Unit_Text4[0]);
    }
    PixelPageCache2D_OpenWin_customize(40, 16, 0);                                                /*对二维像素页缓存-开窗(自定义)（窗口内bit全为0）*/
    PixelPageCache2D_Text_customize(40, 16, 0, 0, ui28_char_16, &txet_[0]);                       /*对二维像素页缓存的某一窗口刷字符串(自定义)*/
    ILI9340X_TwoColorChart(100, 3, 40, 16, BLACK, RED, (unsigned char *)&PixelPageCache2D[0][0]); /*对ILI9340X显示器的某一窗口刷双色图*/

    if (temp_e_t[PTT2 /* 漂洗温度 */].probe[0].abnormal > 0)
    {
        sprintf(&txet_[0], "%s", " ");
    }
    else
    {
        len = sprintf(&txet_[0], "%d", (temp_e_t[PTT2 /* 漂洗温度 */].Displaytemp / 10));
        sprintf(&txet_[len], "%s", &Unit_Text4[0]);
    }
    PixelPageCache2D_OpenWin_customize(40, 16, 0);                                                /*对二维像素页缓存-开窗(自定义)（窗口内bit全为0）*/
    PixelPageCache2D_Text_customize(40, 16, 0, 0, ui28_char_16, &txet_[0]);                       /*对二维像素页缓存的某一窗口刷字符串(自定义)*/
    ILI9340X_TwoColorChart(260, 3, 40, 16, BLACK, RED, (unsigned char *)&PixelPageCache2D[0][0]); /*对ILI9340X显示器的某一窗口刷双色图*/

    if (t_signal[PSST1 /* 洗高 */].sig == SIGNAL_CLOSE /* 闭合 */)
    {
        UI28_draw_arc(26, 49, GET_R_ANGLE_BY_ROW(6), 0, 0, 0, 0, 0, BLACK, RED);
    }
    else
    {
        UI28_draw_arc(26, 49, GET_R_ANGLE_BY_ROW(6), 0, 0, 0, 0, 0, BLACK, BLACK);
    }

    if (t_signal[PSST2 /* 洗低 */].sig == SIGNAL_CLOSE /* 闭合 */)
    {
        UI28_draw_arc(90, 49, GET_R_ANGLE_BY_ROW(6), 0, 0, 0, 0, 0, BLACK, RED);
    }
    else
    {
        UI28_draw_arc(90, 49, GET_R_ANGLE_BY_ROW(6), 0, 0, 0, 0, 0, BLACK, BLACK);
    }

    if (t_signal[PSST3 /* 漂高 */].sig == SIGNAL_CLOSE /* 闭合 */)
    {
        UI28_draw_arc(154, 49, GET_R_ANGLE_BY_ROW(6), 0, 0, 0, 0, 0, BLACK, RED);
    }
    else
    {
        UI28_draw_arc(154, 49, GET_R_ANGLE_BY_ROW(6), 0, 0, 0, 0, 0, BLACK, BLACK);
    }

    if (t_signal[PSST4 /* 漂低 */].sig == SIGNAL_CLOSE /* 闭合 */)
    {
        UI28_draw_arc(218, 49, GET_R_ANGLE_BY_ROW(6), 0, 0, 0, 0, 0, BLACK, RED);
    }
    else
    {
        UI28_draw_arc(218, 49, GET_R_ANGLE_BY_ROW(6), 0, 0, 0, 0, 0, BLACK, BLACK);
    }

    if (t_signal[PSST5 /* 机门 */].sig == SIGNAL_CLOSE /* 闭合 */)
    {
        UI28_draw_arc(282, 49, GET_R_ANGLE_BY_ROW(6), 0, 0, 0, 0, 0, BLACK, RED);
    }
    else
    {
        UI28_draw_arc(282, 49, GET_R_ANGLE_BY_ROW(6), 0, 0, 0, 0, 0, BLACK, BLACK);
    }

    for (len = 0; len < 7; len++)
    {
        if (InformationOutput /* 开关量输出*/ & (1 << len))
        {
            colour = RED;
        }
        else
        {
            colour = BLACK;
        }

        switch (len)
        {
        case 0:
            if (colour == RED)
            {
                UI28_draw_arc(34, 115, GET_R_ANGLE_BY_ROW(6), 0, 0, 0, 0, 0, BLACK, colour);
            }
            else
            {
                UI28_draw_arc(34, 115, GET_R_ANGLE_BY_ROW(6), 0, 0, 0, 0, 0, BLACK, colour);
            }
            break;
        case 1:
            if (colour == RED)
            {
                UI28_draw_arc(114, 115, GET_R_ANGLE_BY_ROW(6), 0, 0, 0, 0, 0, BLACK, colour);
            }
            else
            {
                UI28_draw_arc(114, 115, GET_R_ANGLE_BY_ROW(6), 0, 0, 0, 0, 0, BLACK, colour);
            }
            break;
        case 2:
            if (colour == RED)
            {
                UI28_draw_arc(194, 115, GET_R_ANGLE_BY_ROW(6), 0, 0, 0, 0, 0, BLACK, colour);
            }
            else
            {
                UI28_draw_arc(194, 115, GET_R_ANGLE_BY_ROW(6), 0, 0, 0, 0, 0, BLACK, colour);
            }
            break;
        case 3:
            if (colour == RED)
            {
                UI28_draw_arc(274, 115, GET_R_ANGLE_BY_ROW(6), 0, 0, 0, 0, 0, BLACK, colour);
            }
            else
            {
                UI28_draw_arc(274, 115, GET_R_ANGLE_BY_ROW(6), 0, 0, 0, 0, 0, BLACK, colour);
            }
            break;
        case 4:
            if (colour == RED)
            {
                UI28_draw_arc(34, 188, GET_R_ANGLE_BY_ROW(6), 0, 0, 0, 0, 0, BLACK, colour);
            }
            else
            {
                UI28_draw_arc(34, 188, GET_R_ANGLE_BY_ROW(6), 0, 0, 0, 0, 0, BLACK, colour);
            }
            break;
        case 5:
            if (colour == RED)
            {
                UI28_draw_arc(114, 188, GET_R_ANGLE_BY_ROW(6), 0, 0, 0, 0, 0, BLACK, colour);
            }
            else
            {
                UI28_draw_arc(114, 188, GET_R_ANGLE_BY_ROW(6), 0, 0, 0, 0, 0, BLACK, colour);
            }
            break;
        case 6:
            if (colour == RED)
            {
                UI28_draw_arc(194, 188, GET_R_ANGLE_BY_ROW(6), 0, 0, 0, 0, 0, BLACK, colour);
            }
            else
            {
                UI28_draw_arc(194, 188, GET_R_ANGLE_BY_ROW(6), 0, 0, 0, 0, 0, BLACK, colour);
            }
            break;
        default:
            break;
        }

        if (Dis_type /*洗碗机类型*/ == Dis_t_1 /*5继电器，2调速（催干剂 洗涤剂）*/)
        {
            if (len == 4)
            {
                break;
            }
        }
        else if (Dis_type /*洗碗机类型*/ == Dis_t_2 /*7继电器，无调速*/)
        {
            /* code */
        }
    }

    switch (Dis_type /*洗碗机类型*/)
    {
    case Dis_t_1 /*5继电器，2调速（催干剂 洗涤剂）*/:
        len = sprintf(&txet_[0], "%d", GearOutput[0] /*模拟量输出*/);
        PixelPageCache2D_OpenWin_customize(24, 16, 0);                                                  /*对二维像素页缓存-开窗(自定义)（窗口内bit全为0）*/
        PixelPageCache2D_Text_customize(24, 16, 0, 0, ui28_char_16, &txet_[0]);                         /*对二维像素页缓存的某一窗口刷字符串(自定义)*/
        ILI9340X_TwoColorChart(117, 217, 24, 16, BLACK, RED, (unsigned char *)&PixelPageCache2D[0][0]); /*对ILI9340X显示器的某一窗口刷双色图*/

        len = sprintf(&txet_[0], "%d", GearOutput[1] /*模拟量输出*/);
        PixelPageCache2D_OpenWin_customize(24, 16, 0);                                                  /*对二维像素页缓存-开窗(自定义)（窗口内bit全为0）*/
        PixelPageCache2D_Text_customize(24, 16, 0, 0, ui28_char_16, &txet_[0]);                         /*对二维像素页缓存的某一窗口刷字符串(自定义)*/
        ILI9340X_TwoColorChart(277, 217, 24, 16, BLACK, RED, (unsigned char *)&PixelPageCache2D[0][0]); /*对ILI9340X显示器的某一窗口刷双色图*/
        break;
    case Dis_t_2 /*7继电器，无调速*/:
        break;
    default:
        break;
    }
}
