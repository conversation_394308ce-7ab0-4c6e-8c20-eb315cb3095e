#include "lzmUI.h"

int MENU_ANIMATION_CUR_X_TAR[LZM_MENU_ITEM_NUM];  // 光标目标X(256倍)
int MENU_ANIMATION_CUR_Y_TAR[LZM_MENU_ITEM_NUM];  // 光标目标Y(256倍)
int MENU_ANIMATION_WIN_Y_TAR[LZM_MENU_ITEM_NUM];  // 窗口目标Y(256倍)
int MENU_ANIMATION_ORLL_Y_TAR[LZM_MENU_ITEM_NUM]; // 滚动条目标Y(256倍)

int MENU_ANIMATION_CUR_X[LZM_MENU_ITEM_NUM];  // 光标当前X(256倍)
int MENU_ANIMATION_CUR_Y[LZM_MENU_ITEM_NUM];  // 光标当前Y(256倍)
int MENU_ANIMATION_WIN_Y[LZM_MENU_ITEM_NUM];  // 窗口目标Y(256倍)
int MENU_ANIMATION_ORLL_Y[LZM_MENU_ITEM_NUM]; // 滚动条目标Y(256倍)

unsigned char MENU_ANIMATION_OVERAMPLITUDE[LZM_MENU_ITEM_NUM]; // 超幅程度值

/* 菜单左上角X坐标 */
const unsigned short lzm_menu_x_list[LZM_MENU_ITEM_NUM] =
    {
#define LZM_MENU_DEBUG_X(Lname, Lx, Ly, bLW, LH, LS, LR, Lvx, Lb, ...) Lx,
        LZM_MENU_ITEM_LIST
#undef LZM_MENU_DEBUG_X
};
/* 菜单左上角Y坐标 */
const unsigned short lzm_menu_y_list[LZM_MENU_ITEM_NUM] =
    {
#define LZM_MENU_DEBUG_X(Lname, Lx, Ly, bLW, LH, LS, LR, Lvx, Lb, ...) Ly,
        LZM_MENU_ITEM_LIST
#undef LZM_MENU_DEBUG_X
};
/* 菜单宽 */
const unsigned short lzm_menu_bLW_list[LZM_MENU_ITEM_NUM] =
    {
#define LZM_MENU_DEBUG_X(Lname, Lx, Ly, bLW, LH, LS, LR, Lvx, Lb, ...) bLW,
        LZM_MENU_ITEM_LIST
#undef LZM_MENU_DEBUG_X
};
/* 字高 */
const unsigned char lzm_menu_LH_list[LZM_MENU_ITEM_NUM] =
    {
#define LZM_MENU_DEBUG_X(Lname, Lx, Ly, bLW, LH, LS, LR, Lvx, Lb, ...) lzm_char_##LH,
        LZM_MENU_ITEM_LIST
#undef LZM_MENU_DEBUG_X
};
/* 边距 */
const unsigned char lzm_menu_LS_list[LZM_MENU_ITEM_NUM] =
    {
#define LZM_MENU_DEBUG_X(Lname, Lx, Ly, bLW, LH, LS, LR, Lvx, Lb, ...) LS,
        LZM_MENU_ITEM_LIST
#undef LZM_MENU_DEBUG_X
};
/* 显示行数 */
const unsigned char lzm_menu_LR_list[LZM_MENU_ITEM_NUM] =
    {
#define LZM_MENU_DEBUG_X(Lname, Lx, Ly, bLW, LH, LS, LR, Lvx, Lb, ...) LR,
        LZM_MENU_ITEM_LIST
#undef LZM_MENU_DEBUG_X
};
/* 数值位置x坐标 */
const unsigned short lzm_menu_Lvx_list[LZM_MENU_ITEM_NUM] =
    {
#define LZM_MENU_DEBUG_X(Lname, Lx, Ly, bLW, LH, LS, LR, Lvx, Lb, ...) Lvx,
        LZM_MENU_ITEM_LIST
#undef LZM_MENU_DEBUG_X
};
/* 滚动条宽 */
const unsigned char lzm_menu_Lb_list[LZM_MENU_ITEM_NUM] =
    {
#define LZM_MENU_DEBUG_X(Lname, Lx, Ly, bLW, LH, LS, LR, Lvx, Lb, ...) Lb,
        LZM_MENU_ITEM_LIST
#undef LZM_MENU_DEBUG_X
};
/* 菜单项文本 */
#define LZM_MENU_DEBUG_X(Lname, Lx, Ly, bLW, LH, LS, LR, Lvx, Lb, ...) const MENU_TEXT lzm_menu_TEXT##Lname##_list[] = __VA_ARGS__;
LZM_MENU_ITEM_LIST
#undef LZM_MENU_DEBUG_X

/* 菜单项总数 */
const unsigned char lzm_menu_total_list[LZM_MENU_ITEM_NUM] =
    {
#define LZM_MENU_DEBUG_X(Lname, Lx, Ly, bLW, LH, LS, LR, Lvx, Lb, ...) (sizeof(lzm_menu_TEXT##Lname##_list) / sizeof(MENU_TEXT)),
        LZM_MENU_ITEM_LIST
#undef LZM_MENU_DEBUG_X
};

/* 行字宽 */
#define LZM_MENU_DEBUG_X(Lname, Lx, Ly, bLW, LH, LS, LR, Lvx, Lb, ...) unsigned short lzm_menu_Row_width_TEXT##Lname##_list[(sizeof(lzm_menu_TEXT##Lname##_list) / sizeof(MENU_TEXT))] = 0;
LZM_MENU_ITEM_LIST
#undef LZM_MENU_DEBUG_X

// 通用动画插值函数(渐近动画)
// a:当前值地址
// a_trg:目标值地址
// n:(1：增量=差值/2)(2：增量=差值/4)(3：增量=差值/8)(4：增量=差值/16)(5：增量=差值/32).....
void animation(unsigned int *a, unsigned int *a_trg, unsigned int n)
{
    if (*a != *a_trg)
    {
        if ((*a - *a_trg) < 39 && (*a - *a_trg) > -39)
        {
            /* 当当前值与目标值差距小于0.15时(39/256)直接设置目标值 */
            *a = *a_trg;
        }
        else
        {
            *a += ((*a_trg >> n) - (*a >> n)); // 更新当前值
        }
    }
}

// 更新菜单
void Lzm_update_menu(enum LZM_MENU_e_ FontSize)
{
    int i;
    unsigned short CUR_X = MENU_ANIMATION_CUR_X[FontSize] >> 8;   // 光标当前X
    unsigned short CUR_Y = MENU_ANIMATION_CUR_Y[FontSize] >> 8;   // 光标当前Y
    unsigned short WIN_Y = MENU_ANIMATION_WIN_Y[FontSize] >> 8;   // 窗口目标Y
    unsigned short ORLL_Y = MENU_ANIMATION_ORLL_Y[FontSize] >> 8; // 滚动条目标Y

    unsigned short start_TEXT, start_WIN_Y, usY;
    // 计算窗口高度
    unsigned short usH_R = ((lzm_menu_LS_list[FontSize] << 1) + lzm_matrix_heigh_list[lzm_menu_LH_list[FontSize]]) * lzm_menu_LR_list[FontSize];
    // 计算当前行涉及的字节范围
    start_TEXT = WIN_Y / ((lzm_menu_LS_list[FontSize] << 1) + lzm_matrix_heigh_list[lzm_menu_LH_list[FontSize]]);
    start_WIN_Y = WIN_Y % ((lzm_menu_LS_list[FontSize] << 1) + lzm_matrix_heigh_list[lzm_menu_LH_list[FontSize]]);

    usY = lzm_menu_y_list[FontSize] - start_WIN_Y;
    /*对二维像素页缓存-开窗（窗口内bit全为0）*/
    PixelPageCache2D_OpenWin(lzm_menu_x_list[FontSize], lzm_menu_y_list[FontSize], lzm_menu_bLW_list[FontSize], usH_R, 0);
    /*刷字符串*/
    for (i = 0; i <= lzm_menu_LR_list[FontSize]; i++)
    {
        /*对二维像素页缓存的某一窗口刷字符串*/
        switch (FontSize)
        {
#define LZM_MENU_DEBUG_X(Lname, Lx, Ly, bLW, LH, LS, LR, Lvx, Lb, ...)                                                                                                                                          \
    case lzm_##Lname##_menu:                                                                                                                                                                                    \
        PixelPageCache2D_Text(lzm_menu_x_list[FontSize] + lzm_menu_LS_list[FontSize], usY, lzm_menu_LH_list[FontSize], lzm_menu_y_list[FontSize], usH_R, (char *)lzm_menu_TEXT##Lname##_list[start_TEXT].name); \
        PixelPageCache2D_Text(lzm_menu_Lvx_list[FontSize], usY, lzm_menu_LH_list[FontSize], lzm_menu_y_list[FontSize], usH_R, (char *)lzm_menu_TEXT##Lname##_list[start_TEXT].value);                           \
        break;
            LZM_MENU_ITEM_LIST
#undef LZM_MENU_DEBUG_X
        default:
            break;
        }
        start_TEXT++;
        usY += ((lzm_menu_LS_list[FontSize] << 1) + lzm_matrix_heigh_list[lzm_menu_LH_list[FontSize]]);
    }
    /*刷滚动条*/
    /*对二维像素页缓存-开窗*/
    PixelPageCache2D_OpenWin(lzm_menu_x_list[FontSize] + lzm_menu_bLW_list[FontSize] - lzm_menu_Lb_list[FontSize], lzm_menu_y_list[FontSize], lzm_menu_Lb_list[FontSize], ORLL_Y, 1);
}

// // 计算UTF-8字符串显示宽度
// unsigned int calc_string_width(const char *str)
// {
//     unsigned int width = 0;
//     while (*str)
//     {
//         if ((unsigned char)*str >= 0xE0)
//         { // 全角字符（UTF-8三字节）
//             width += 16;
//             str += 3; // 跳过全角字符的后续字节
//         }
//         else
//         { // 半角字符
//             width += 8;
//             str++;
//         }
//     }
//     return width;
// }

// /* 初始化菜单 */
// /* FontSize：要初始化的菜单 */
// /* cursor：光标位置 */
// void Lzm_Init_Menu(enum LZM_MENU_e_ FontSize, unsigned char cursor)
// {
//     //将背景填充
//     //
// }
