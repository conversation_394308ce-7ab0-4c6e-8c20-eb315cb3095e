#ifndef _UI28_ANIMATION_
#define _UI28_ANIMATION_
#include "UI28.h"
#if (UI28_ANIMATION_ENABLED == 1)

enum UI28_ANIMATION_ACTION_
{
	ui28_a_mot = 0, /* 运动方向（不移动） */
	ui28_a_up,		/* 运动方向（上） */
	ui28_a_below,	/* 运动方向（下） */
	ui28_a_left,	/* 运动方向（左） */
	ui28_a_right,	/* 运动方向（右） */
};

/*创建动画帧表*/
#define UI28_ANIMATION_FRAMES_ITEM_LIST                                                                                                                                      \
	UI28_ANIMATION_FRAMES_DEBUG_X(1 /*名字*/, {&Pic_36x36[PIC_36_36_water1].dat[0], &Pic_36x36[PIC_36_36_water2].dat[0], &Pic_36x36[PIC_36_36_water3].dat[0], &Pic_36x36[PIC_36_36_water4].dat[0]}) \
	UI28_ANIMATION_FRAMES_DEBUG_X(2 /*名字*/, {&Pic_45x45[0].dat[0], &Pic_45x45[1].dat[0], &Pic_45x45[2].dat[0], &Pic_45x45[3].dat[0],&Pic_45x45[4].dat[0], &Pic_45x45[5].dat[0], &Pic_45x45[6].dat[0], &Pic_45x45[7].dat[0],&Pic_45x45[8].dat[0], &Pic_45x45[9].dat[0], &Pic_45x45[10].dat[0], &Pic_45x45[11].dat[0],&Pic_45x45[12].dat[0], &Pic_45x45[13].dat[0], &Pic_45x45[14].dat[0], &Pic_45x45[15].dat[0]}) \
	UI28_ANIMATION_FRAMES_DEBUG_X(3 /*名字*/, {&Pic_45x45[16].dat[0], &Pic_45x45[17].dat[0], &Pic_45x45[18].dat[0], &Pic_45x45[19].dat[0],&Pic_45x45[20].dat[0], &Pic_45x45[21].dat[0], &Pic_45x45[22].dat[0], &Pic_45x45[23].dat[0]}) \
	UI28_ANIMATION_FRAMES_DEBUG_X(4 /*名字*/, {&Pic_45x45[24].dat[0], &Pic_45x45[25].dat[0], &Pic_45x45[26].dat[0], &Pic_45x45[27].dat[0],&Pic_45x45[28].dat[0], &Pic_45x45[29].dat[0], &Pic_45x45[30].dat[0], &Pic_45x45[31].dat[0]})
/* 动画帧表ID */
enum UI28_ANIMATION_FRAMES_e_
{
#define UI28_ANIMATION_FRAMES_DEBUG_X(Lname, ...) ui28_##Lname##_frames,
	UI28_ANIMATION_FRAMES_ITEM_LIST
#undef UI28_ANIMATION_FRAMES_DEBUG_X
		UI28_ANIMATION_FRAMES_ITEM_NUM,
};

/*创建动画表*/
#define UI28_ANIMATION_ITEM_LIST                                                                                                                                                                                                          \
	UI28_ANIMATION_DEBUG_X(1 /*名字*/, BLACK /*背景颜色*/, GREY /*文字颜色*/, 258 /* 动画X坐标 */, 157 /*动画Y坐标*/, 36 /*动画宽*/, 36 /*动画高*/, 10 /* 动画播放速度，越低越快（延时） */, ui28_a_mot /* 动画运动方向 */, 0 /* 每帧运动像素距离 */, ui28_1_frames /* 动画帧表 */) \
	UI28_ANIMATION_DEBUG_X(2 /*名字*/, BLACK /*背景颜色*/, WHITE /*文字颜色*/, 0 /* 动画X坐标 */, 0 /*动画Y坐标*/, 45 /*动画宽*/, 45 /*动画高*/, 1 /* 动画播放速度，越低越快（延时） */, ui28_a_right /* 动画运动方向 */, 17 /* 每帧运动像素距离 */, ui28_2_frames /* 动画帧表 */) \
	UI28_ANIMATION_DEBUG_X(3 /*名字*/, BLACK /*背景颜色*/, WHITE /*文字颜色*/, 272 /* 动画X坐标 */, 0 /*动画Y坐标*/, 45 /*动画宽*/, 45 /*动画高*/, 2 /* 动画播放速度，越低越快（延时） */, ui28_a_mot /* 动画运动方向 */, 0 /* 每帧运动像素距离 */, ui28_3_frames /* 动画帧表 */) \
	UI28_ANIMATION_DEBUG_X(4 /*名字*/, BLACK /*背景颜色*/, WHITE /*文字颜色*/, 272 /* 动画X坐标 */, 0 /*动画Y坐标*/, 45 /*动画宽*/, 45 /*动画高*/, 2 /* 动画播放速度，越低越快（延时） */, ui28_a_mot /* 动画运动方向 */, 0 /* 每帧运动像素距离 */, ui28_4_frames /* 动画帧表 */)
/* 动画ID */
enum UI28_ANIMATION_e_
{
#define UI28_ANIMATION_DEBUG_X(Aname, COLOR_0_, COLOR_1_, Ax, Ay, Aw, Ah, Asd, Ad, As, Aa) ui28_##Aname##_animation,
	UI28_ANIMATION_ITEM_LIST
#undef UI28_ANIMATION_DEBUG_X
		UI28_ANIMATION_ITEM_NUM,
};

/* 动画剩余次数表 */
extern volatile unsigned char ui28_animation_Ao_list[UI28_ANIMATION_ITEM_NUM];

/* 更新动画相册 */
void UI28_update_Animation(enum UI28_ANIMATION_e_ ANIMATION_);
/* 初始化动画 */
void ui28_Init_Animation(enum UI28_ANIMATION_e_ ANIMATION_);
/* 设置动画次数 */
void ui28_Play_Count_Animation(enum UI28_ANIMATION_e_ ANIMATION_, unsigned char Count);
#endif
#endif
