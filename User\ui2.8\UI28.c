#include "UI28.h"
#include <string.h>

/* 字高 */
const unsigned char ui28_matrix_heigh_list[UI28_MATRIX_ITEM_NUM] =
    {
#define UI28_MATRIX_DEBUG_X(heigh) heigh,
        UI28_MATRIX_ITEM_LIST
#undef UI28_MATRIX_DEBUG_X
};

/* 二维像素页缓存 */
volatile unsigned char PixelPageCache2D[UI28_UI_H][UI28_UI_W_Byte_count];

/**
 * @brief  字符像素宽计算
 * @param  usText ：字符串地址
 * @param  FontSize ：字符高度
 * @retval 宽度
 */
unsigned short UI28_character_pixel_width_calculation(enum UI28_MATRIX_e_ FontSize, char *usText)
{
    unsigned short wide_ = 0; // 宽
    if (usText == 0)
    {
        return 0;
    }

    while (*usText)
    {
        if ((unsigned char)*usText >= 0xE0)
        { // 全角字符（UTF-8三字节）
            wide_ += ui28_matrix_heigh_list[FontSize];
            usText += 3; // 跳过全角字符的后续字节
        }
        else
        { // 半角字符
            wide_ += (ui28_matrix_heigh_list[FontSize] >> 1);
            usText++;
        }
    }
    return wide_;
}

// 通用动画插值函数(渐近动画)
// a:当前值地址
// a_trg:目标值地址
// n:(1：增量=差值/2)(2：增量=差值/4)(3：增量=差值/8)(4：增量=差值/16)(5：增量=差值/32).....
void UI28_anima(int *a, int *a_trg, int n)
{
    if (*a != *a_trg)
    {
        if ((*a - *a_trg) < 39 && (*a - *a_trg) > -39)
        {
            /* 当当前值与目标值差距小于0.15时(39/256)直接设置目标值 */
            *a = *a_trg;
        }
        else
        {
            *a += ((*a_trg >> n) - (*a >> n)); // 更新当前值
        }
    }
}

// 0-128范围的透明度混合函数
// bg:背景色
// fg:前景色
// alpha：透明度0-128
// 返回：混合后的颜色
unsigned short alpha_blend_rgb565(unsigned short bg, unsigned short fg, unsigned char alpha)
{
    // 钳位透明度到0-128
    if (alpha > 128)
    {
        alpha = 128;
    }

    // 分解颜色通道
    short bg_r = bg >> 11, fg_r = fg >> 11;
    short bg_g = (bg >> 5) & 0x3F, fg_g = (fg >> 5) & 0x3F;
    short bg_b = bg & 0x1F, fg_b = fg & 0x1F;

    // 混合计算（整数运算优化）
    short r = (((short)alpha * (fg_r - bg_r)) + (bg_r << 7)) >> 7;
    short g = (((short)alpha * (fg_g - bg_g)) + (bg_g << 7)) >> 7;
    short b = (((short)alpha * (fg_b - bg_b)) + (bg_b << 7)) >> 7;

    // 重组为RGB565
    return (r << 11) | (g << 5) | b;
}

// // 变化——降
// void ui28_change_fall(unsigned char max, unsigned char min, unsigned char price)
// {
//     if (price > min)
//     {
//         price--;
//     }
//     else
//     {
//         price=max;
//     }
// }
// // 变化——升
// void ui28_change_rise(unsigned char max, unsigned char min, unsigned char price)
// {
//     if (price < max)
//     {
//         price++;
//     }
//     else
//     {
//         price=min;
//     }
// }

/**
 * @brief  对二维像素页缓存-开窗
 * @param  usX ：在二维像素页缓存的起点X坐标
 * @param  usY ：在二维像素页缓存的起点Y坐标
 * @param  usWidth ：窗口的宽度
 * @param  usHeight ：窗口的高度
 * @param  usBit ：0或1
 * @retval 无
 */
void PixelPageCache2D_OpenWin(unsigned short usX, unsigned short usY, unsigned short usWidth, unsigned short usHeight, unsigned char usBit)
{
    unsigned short y, byte;
    unsigned short start_byte, end_byte;
    unsigned char start_bit, end_bit;

    // 计算当前行涉及的字节范围
    start_byte = usX >> 3;
    start_bit = usX & 7;
    byte = usX + usWidth - 1;
    end_byte = byte >> 3;
    end_bit = byte & 7;
    if (usBit)
    {
        // 逐行处理窗口区域
        for (y = usY; y < usY + usHeight; y++)
        {
            // 遍历当前行的每个字节
            for (byte = start_byte; byte <= end_byte; byte++)
            {
                if (start_byte == end_byte)
                {
                    // 情况1：窗口在同一个字节内
                    PixelPageCache2D[y][byte] |= ((0xFF >> start_bit) & (0xFF << (7 - end_bit)));
                }
                else if (byte == start_byte)
                {
                    // 情况2：处理起始字节的部分位
                    PixelPageCache2D[y][byte] |= (0xFF >> start_bit);
                }
                else if (byte == end_byte)
                {
                    // 情况3：处理结束字节的部分位
                    PixelPageCache2D[y][byte] |= (0xFF << (7 - end_bit));
                }
                else
                {
                    // 情况4：中间完整字节，直接清零
                    PixelPageCache2D[y][byte] = 0xFF;
                }
            }
        }
    }
    else
    {
        // 逐行处理窗口区域
        for (y = usY; y < usY + usHeight; y++)
        {
            // 遍历当前行的每个字节
            for (byte = start_byte; byte <= end_byte; byte++)
            {
                if (start_byte == end_byte)
                {
                    // 情况1：窗口在同一个字节内
                    PixelPageCache2D[y][byte] &= ~((0xFF >> start_bit) & (0xFF << (7 - end_bit)));
                }
                else if (byte == start_byte)
                {
                    // 情况2：处理起始字节的部分位
                    PixelPageCache2D[y][byte] &= ~(0xFF >> start_bit);
                }
                else if (byte == end_byte)
                {
                    // 情况3：处理结束字节的部分位
                    PixelPageCache2D[y][byte] &= ~(0xFF << (7 - end_bit));
                }
                else
                {
                    // 情况4：中间完整字节，直接清零
                    PixelPageCache2D[y][byte] = 0x00;
                }
            }
        }
    }
}

/**
 * @brief  对二维像素页缓存的某一窗口刷双色图
 * @param  usX ：在二维像素页缓存的起点X坐标
 * @param  usY ：在二维像素页缓存的起点Y坐标
 * @param  usWidth ：窗口的宽度
 * @param  usHeight ：窗口的高度
 * @param  usY_R ：Y轴允许范围坐标
 * @param  usH_R ：Y轴允许范围高度
 * @param  usPicture ：图地址
 * @retval 无
 */
void PixelPageCache2D_TwoColorChart(unsigned short usX, unsigned short usY, unsigned short usWidth, unsigned short usHeight, unsigned short usY_R, unsigned short usH_R, unsigned char *usTwoColor)
{
    unsigned short y, byte;
    unsigned short start_byte;
    unsigned char start_bit;
    unsigned char dev_byte, assist_byte1, assist_byte2;

    // 计算双色图一行的字节数
    dev_byte = (usWidth + 7) >> 3;
    // 计算当前行涉及的字节范围
    start_byte = usX >> 3;
    start_bit = usX & 0x07;
    // 逐行处理窗口区域
    for (y = 0; y < usHeight; y++)
    {
        if ((y + usY) >= usY_R && (y + usY) < (usY_R + usH_R))
        {
            // 遍历当前行的每个字节
            for (byte = 0; byte < dev_byte; byte++)
            {
                assist_byte1 = usTwoColor[dev_byte * y + byte] >> start_bit;
                assist_byte2 = usTwoColor[dev_byte * y + byte] << (8 - start_bit);
                PixelPageCache2D[y + usY][byte + start_byte] = (PixelPageCache2D[y + usY][byte + start_byte] & (0xFF << (8 - start_bit))) + assist_byte1;
                PixelPageCache2D[y + usY][byte + start_byte + 1] = assist_byte2;
            }
        }
    }
}

/**
 * @brief  对二维像素页缓存的某一窗口刷字符串
 * @param  usX ：在二维像素页缓存的起点X坐标
 * @param  usY ：在二维像素页缓存的起点Y坐标
 * @param  FontSize ：字符高度
 * @param  usY_R ：Y轴允许范围坐标
 * @param  usH_R ：Y轴允许范围高度
 * @param  usText ：图地址
 * @retval 无
 */
void PixelPageCache2D_Text(unsigned short usX, unsigned short usY, enum UI28_MATRIX_e_ FontSize, unsigned short usY_R, unsigned short usH_R, char *usText)
{
    if (usText == 0)
    {
        return;
    }

    while (*usText)
    {
        if ((unsigned char)*usText >= 0xE0)
        { // 全角字符（UTF-8三字节）
            PixelPageCache2D_TwoColorChart(usX, usY, ui28_matrix_heigh_list[FontSize], ui28_matrix_heigh_list[FontSize], usY_R, usH_R, UI28_FontAddressAcquisition(usText, FontSize));
            usX += ui28_matrix_heigh_list[FontSize];
            usText += 3; // 跳过全角字符的后续字节
        }
        else
        { // 半角字符
            PixelPageCache2D_TwoColorChart(usX, usY, (ui28_matrix_heigh_list[FontSize] >> 1), ui28_matrix_heigh_list[FontSize], usY_R, usH_R, UI28_FontAddressAcquisition(usText, FontSize));
            usX += (ui28_matrix_heigh_list[FontSize] >> 1);
            usText++;
        }
    }
}

/**
 * @brief  对二维像素页缓存-开窗(自定义)
 * @param  usWidth ：窗口的宽度
 * @param  usHeight ：窗口的高度
 * @param  usBit ：0或1
 * @retval 无
 */
void PixelPageCache2D_OpenWin_customize(unsigned short usWidth, unsigned short usHeight, unsigned char usBit)
{
    unsigned short byte = ((usWidth + 7) >> 3) * usHeight;
    if (0 == usBit)
    {
        memset((char *)&PixelPageCache2D[0][0], 0, byte);
    }
    else
    {
        memset((char *)&PixelPageCache2D[0][0], 0xFF, byte);
    }
}

/**
 * @brief  对二维像素页缓存的某一窗口刷双色图(自定义)
 * @param  usWidthWin ：窗口的宽度
 * @param  usHeightWin ：窗口的高度
 * @param  usX ：在二维像素页缓存的起点X坐标
 * @param  usY ：在二维像素页缓存的起点Y坐标
 * @param  usWidth ：双色图的宽度
 * @param  usHeight ：双色图的高度
 * @param  usPicture ：图地址
 * @retval 无
 */
void PixelPageCache2D_TwoColorChart_customize(unsigned short usWidthWin, unsigned short usHeightWin, short usX, short usY, unsigned short usWidth, unsigned short usHeight, unsigned char *usTwoColor)
{
    short x, y, ymax, byte, x_byte;
    unsigned char start_bit;
    unsigned char dev_byte, Win_byte;
    unsigned char *TwoColor = usTwoColor;
    unsigned char *Win = (unsigned char *)&PixelPageCache2D[0][0];

    // 确定Y轴最大坐标
    ymax = usHeight + usY;
    if (ymax > usHeightWin)
    {
        ymax = usHeightWin;
    }
    // 计算双色图一行的字节数
    dev_byte = (usWidth + 7) >> 3;
    // 计算窗口一行的字节数
    Win_byte = (usWidthWin + 7) >> 3;
    // 起点Y坐标所在窗口行首地址
    if (usY > 0)
    {
        Win += (Win_byte * usY);
    }
    // 计算当前行涉及的字节范围
    start_bit = usX & 0x07;
    // 逐行处理窗口区域
    for (y = usY; y < ymax; y++)
    {
        x = usX;
        if (y >= 0)
        {
            // 遍历当前行的每个字节
            for (byte = 0; byte < dev_byte; byte++)
            {
                x_byte = x >> 3;
                if (x >= 0 && x < usWidthWin)
                {
                    if (start_bit == 0)
                    {
                        Win[x_byte] = TwoColor[byte];
                    }
                    else
                    {
                        Win[x_byte] = (Win[x_byte] & (0xFF << (8 - start_bit))) + (TwoColor[byte] >> start_bit);
                        // if ((x + 8) < usWidthWin)
                        if ((x_byte + 1) < Win_byte)
                        {
                            Win[x_byte + 1] = TwoColor[byte] << (8 - start_bit);
                        }
                    }
                }
                x += 8;
            }

            Win += Win_byte;
        }
        TwoColor += dev_byte;
    }
}

/**
 * @brief  对二维像素页缓存的某一窗口刷字符串(自定义)
 * @param  usWidthWin ：窗口的宽度
 * @param  usHeightWin ：窗口的高度
 * @param  usX ：在二维像素页缓存的起点X坐标
 * @param  usY ：在二维像素页缓存的起点Y坐标
 * @param  FontSize ：字的高度
 * @param  usText ：字符串地址
 * @retval 无
 */
void PixelPageCache2D_Text_customize(unsigned short usWidthWin, unsigned short usHeightWin, short usX, short usY, enum UI28_MATRIX_e_ FontSize, char *usText)
{
    if (usText == 0)
    {
        return;
    }

    while (*usText)
    {
        if ((unsigned char)*usText >= 0xE0)
        { // 全角字符（UTF-8三字节）
            PixelPageCache2D_TwoColorChart_customize(usWidthWin, usHeightWin, usX, usY, ui28_matrix_heigh_list[FontSize], ui28_matrix_heigh_list[FontSize], UI28_FontAddressAcquisition(usText, FontSize));
            usX += ui28_matrix_heigh_list[FontSize];
            usText += 3; // 跳过全角字符的后续字节
        }
        else
        { // 半角字符
            PixelPageCache2D_TwoColorChart_customize(usWidthWin, usHeightWin, usX, usY, (ui28_matrix_heigh_list[FontSize] >> 1), ui28_matrix_heigh_list[FontSize], UI28_FontAddressAcquisition(usText, FontSize));
            usX += (ui28_matrix_heigh_list[FontSize] >> 1);
            usText++;
        }
    }
}

#if (UI28_SCREEN_ENABLED == 1)
/*2.8寸屏幕初始化*/
void UI28_Init(void)
{
    ILI9340X_Init();                                              /*LI9340X初始化函数，如果要用到lcd，一定要调用这个函数*/
    ILI9340X_Clear(0, 0, UI28_UI_W, UI28_UI_H, UI28_INIT_COLOUR); /*以某种颜色进行清屏*/

#if (UI28_MENU_ENABLED == 1)
    ui28_Init_Menu_Engineering(); /* 工程初始化菜单（在系统上电执行一次） */
#endif
#if (UI28_ROLLING_ALBUM_ENABLED == 1)
    ui28_Init_Rolling_Album_Engineering(); /* 工程初始化滚动相册（在系统上电执行一次） */
#endif
#if (UI28_ROLLING_ALBUM_ENABLED == 1)
    ui28_Init_ProgressBar_Engineering(); /* 工程初始化进度条（在系统上电执行一次） */
#endif
}
/*2.8寸屏幕清屏，并关闭所有模块，一般切换界面时调用*/
void UI28_Clear_Screen(void)
{
    /*可以使用一些切换特效，如淡入淡出等。。。 这里简单使用关背光然后清屏*/
    // ILI9340X_BackLed_Control(DISABLE);                            /*背LCD背光灯*/
    ILI9340X_Clear(0, 0, UI28_UI_W, UI28_UI_H, UI28_INIT_COLOUR); /*以某种颜色进行清屏*/

#if (UI28_MENU_ENABLED == 1)
    ui28_menu_display = 0; /* 菜单显示标志位 */
#endif
#if (UI28_ROLLING_ALBUM_ENABLED == 1)
    ui28_Rolling_Album_display = 0; /* 滚动相册显示标志位 */
#endif
#if (UI28_ARC_ENABLED == 1)
    ui28_arc_loading_display = 0; /* 圆弧加载动画显示标志位 */
#endif
#if (UI28_TEXT_GRADIENT_ENABLED == 1)
    ui28_Text_Gradient_display = 0; /* 文本渐变显示标志位 */
#endif
#if (UI28_ANIMATION_ENABLED == 1)
    memset((char *)&ui28_animation_Ao_list[0], 0, UI28_ANIMATION_ITEM_NUM);
#endif
#if (UI28_PROGRESS_BAR_ENABLED == 1)
    ui28_ProgressBar_display = 0; /* 进度条显示标志位 */
#endif
    /*可以使用一些切换特效，如淡入淡出等。。。 这里简单使用关背光然后清屏*/
    ILI9340X_BackLed_Control(ENABLE); /*点亮LCD背光灯*/
}
/*2.8寸屏幕轮询10ms*/
void UI28_Polling(void)
{
#if (UI28_ROLLING_ALBUM_ENABLED == 1||UI28_TEXT_GRADIENT_ENABLED == 1||UI28_ANIMATION_ENABLED == 1||UI28_PROGRESS_BAR_ENABLED == 1)
    int i;
#endif

#if (UI28_MENU_ENABLED == 1)
    static volatile unsigned char ui28_menu_display_before = 0; /* 菜单显示标志位（上一次） */
#endif
#if (UI28_ROLLING_ALBUM_ENABLED == 1)
    static volatile uint_AlbumDis_t ui28_Rolling_Album_display_before = 0; /* 菜单显示标志位（上一次） */
#endif
#if (UI28_PROGRESS_BAR_ENABLED == 1)
    static volatile uint_progressDis_t ui28_ProgressBar_display_before = 0; /* 进度条显示标志位（上一次） */
#endif

#if (UI28_MENU_ENABLED == 1)
    if (ui28_menu_display)
    {
        if (ui28_menu_display_before == 0)
        {
            /* 菜单显示标志位从0到1，这里可以初始化菜单 */
            /* 初始化菜单 */
            ui28_Init_Menu();
        }
        else
        {
            /* 这里可以更新菜单画面 */
            /* 根据目标菜单更新当前 */
            Update_the_current_menu_based_on_the_target_menu();
        }
        /* 更新菜单 */
        UI28_update_menu();
    }
    ui28_menu_display_before = ui28_menu_display;
#endif
#if (UI28_ROLLING_ALBUM_ENABLED == 1)
    for (i = 0; i < UI28_ALBUM_ITEM_NUM; i++)
    {
        if ((ui28_Rolling_Album_display >> i) & 1)
        {
            if ((ui28_Rolling_Album_display_before >> i) & 1)
            {
                /* 这里可以更新相册画面 */
                /* 根据目标图片更新当前 */
                Update_the_current_image_based_on_the_target_image_Album(i);
            }
            else
            {
                /* 相册显示标志位从0到1，这里可以初始化相册 */
                /* 初始化滚动相册 */
                ui28_Init_Rolling_Album(i);
            }
            /* 更新滚动相册 */
            UI28_update_Rolling_Album(i);
        }
    }
    ui28_Rolling_Album_display_before = ui28_Rolling_Album_display;
#endif
#if (UI28_ARC_ENABLED == 1)
    if (ui28_arc_loading_display)
    {
        UI28_arc_loading(); /* 圆弧加载动画 */
    }
#endif
#if (UI28_TEXT_GRADIENT_ENABLED == 1)
    for (i = 0; i < UI28_TEXT_GRADIENT_ITEM_NUM; i++)
    {
        if ((ui28_Text_Gradient_display >> i) & 1)
        {
            /* 更新文本渐变 */
            UI28_update_text_gradient(i);
        }
    }
#endif
#if (UI28_ANIMATION_ENABLED == 1)
    for (i = 0; i < UI28_ANIMATION_ITEM_NUM; i++)
    {
        /* 更新动画相册 */
        UI28_update_Animation(i);
    }
#endif
#if (UI28_PROGRESS_BAR_ENABLED == 1)
    for (i = 0; i < UI28_PROGRESS_ITEM_NUM; i++)
    {
        if ((ui28_ProgressBar_display >> i) & 1)
        {
            if ((ui28_ProgressBar_display_before >> i) & 1)
            {
                /* 这里可以更新进度条 */
                /* 根据目标进度更新当前 */
                Update_current_status_based_on_target_progressBar(i);
            }
            else
            {
                /* 进度条显示标志位从0到1，这里可以初始化相册 */
                /* 初始化进度条 */
                ui28_Init_ProgressBar(i);
            }
            /* 更新进度条 */
            UI28_update_ProgressBar(i);
        }
    }
    ui28_ProgressBar_display_before = ui28_ProgressBar_display;
#endif
}
#endif
