#include "page_qr_code.h"
volatile const uint8_t my_array[2048] __attribute__((section(".my_section"))) = {
         0x00, 0x00, 0x00, 0x00, 0x00, 0x1f, 0x00, 0x07, 0xff, 0x81, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x00,
         0x00, 0x00, 0x00, 0x00, 0x00, 0x1f, 0x00, 0x07, 0xff, 0x81, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x00,
         0x00, 0x00, 0x00, 0x00, 0x00, 0x1f, 0x00, 0x07, 0xff, 0x81, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x00,
         0x00, 0x00, 0x00, 0x00, 0x00, 0x1f, 0x00, 0x07, 0xff, 0x81, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x00,
         0x00, 0x00, 0x00, 0x00, 0x00, 0x1f, 0x00, 0x07, 0xff, 0x81, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x00,
         0x00, 0x00, 0x00, 0x00, 0x00, 0x1f, 0x00, 0x07, 0xff, 0x81, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x00,
         0x00, 0x00, 0x00, 0x00, 0x00, 0x1f, 0x00, 0x07, 0xff, 0x81, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x00,
         0x01, 0xff, 0xff, 0xff, 0xf0, 0x1f, 0xfe, 0x07, 0xff, 0x81, 0xf8, 0x1f, 0xff, 0xff, 0xff, 0x80,
         0x01, 0xff, 0xff, 0xff, 0xf0, 0x1f, 0xfe, 0x07, 0xff, 0x81, 0xf8, 0x1f, 0xff, 0xff, 0xff, 0x80,
         0x01, 0xff, 0xff, 0xff, 0xf0, 0x1f, 0xfe, 0x07, 0xff, 0x81, 0xf8, 0x1f, 0xff, 0xff, 0xff, 0x80,
         0x01, 0xff, 0xff, 0xff, 0xf0, 0x1f, 0xfe, 0x07, 0xff, 0x81, 0xf8, 0x1f, 0xff, 0xff, 0xff, 0x80,
         0x01, 0xff, 0xff, 0xff, 0xf0, 0x1f, 0xfe, 0x07, 0xff, 0x81, 0xf8, 0x1f, 0xff, 0xff, 0xff, 0x80,
         0x01, 0xf0, 0x00, 0x01, 0xf0, 0x1f, 0x01, 0xf0, 0x1f, 0x81, 0xf8, 0x1f, 0x80, 0x00, 0x0f, 0x80,
         0x01, 0xf0, 0x00, 0x01, 0xf0, 0x1f, 0x01, 0xf0, 0x1f, 0x81, 0xf8, 0x1f, 0x80, 0x00, 0x0f, 0x80,
         0x01, 0xf0, 0x00, 0x01, 0xf0, 0x1f, 0x01, 0xf0, 0x1f, 0x81, 0xf8, 0x1f, 0x80, 0x00, 0x0f, 0x80,
         0x01, 0xf0, 0x00, 0x01, 0xf0, 0x1f, 0x01, 0xf0, 0x1f, 0x81, 0xf8, 0x1f, 0x80, 0x00, 0x0f, 0x80,
         0x01, 0xf0, 0x00, 0x01, 0xf0, 0x1f, 0x01, 0xf0, 0x1f, 0x81, 0xf8, 0x1f, 0x80, 0x00, 0x0f, 0x80,
         0x01, 0xf0, 0x00, 0x01, 0xf0, 0x1f, 0x01, 0xf0, 0x1f, 0x81, 0xf8, 0x1f, 0x80, 0x00, 0x0f, 0x80,
         0x01, 0xf0, 0x00, 0x01, 0xf0, 0x1f, 0x01, 0xf0, 0x00, 0x01, 0xf8, 0x1f, 0x80, 0x00, 0x0f, 0x80,
         0x01, 0xf0, 0x00, 0x01, 0xf0, 0x1f, 0x01, 0xff, 0xe0, 0x7f, 0xf8, 0x1f, 0x80, 0x00, 0x0f, 0x80,
         0x01, 0xf0, 0x00, 0x01, 0xf0, 0x1f, 0x01, 0xff, 0xe0, 0x7f, 0xf8, 0x1f, 0x80, 0x00, 0x0f, 0x80,
         0x01, 0xf0, 0x00, 0x01, 0xf0, 0x1f, 0x01, 0xff, 0xe0, 0x7f, 0xf8, 0x1f, 0x80, 0x00, 0x0f, 0x80,
         0x01, 0xf0, 0x00, 0x01, 0xf0, 0x1f, 0x01, 0xff, 0xe0, 0x7f, 0xf8, 0x1f, 0x80, 0x00, 0x0f, 0x80,
         0x01, 0xf0, 0x00, 0x01, 0xf0, 0x1f, 0x01, 0xff, 0xe0, 0x7f, 0xf8, 0x1f, 0x80, 0x00, 0x0f, 0x80,
         0x01, 0xf0, 0x00, 0x01, 0xf0, 0x1f, 0x00, 0x00, 0x00, 0x7f, 0xf8, 0x1f, 0x80, 0x00, 0x0f, 0x80,
         0x01, 0xf0, 0x00, 0x01, 0xf0, 0x1f, 0x00, 0x00, 0x1f, 0xff, 0xf8, 0x1f, 0x80, 0x00, 0x0f, 0x80,
         0x01, 0xf0, 0x00, 0x01, 0xf0, 0x1f, 0x00, 0x00, 0x1f, 0xff, 0xf8, 0x1f, 0x80, 0x00, 0x0f, 0x80,
         0x01, 0xf0, 0x00, 0x01, 0xf0, 0x1f, 0x00, 0x00, 0x1f, 0xff, 0xf8, 0x1f, 0x80, 0x00, 0x0f, 0x80,
         0x01, 0xf0, 0x00, 0x01, 0xf0, 0x1f, 0x00, 0x00, 0x1f, 0xff, 0xf8, 0x1f, 0x80, 0x00, 0x0f, 0x80,
         0x01, 0xf0, 0x00, 0x01, 0xf0, 0x1f, 0x00, 0x00, 0x1f, 0xff, 0xf8, 0x1f, 0x80, 0x00, 0x0f, 0x80,
         0x01, 0xf0, 0x00, 0x01, 0xf0, 0x1f, 0x00, 0x00, 0x1f, 0xff, 0xf8, 0x1f, 0x80, 0x00, 0x0f, 0x80,
         0x01, 0xff, 0xff, 0xff, 0xf0, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x1f, 0xff, 0xff, 0xff, 0x80,
         0x01, 0xff, 0xff, 0xff, 0xf0, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x1f, 0xff, 0xff, 0xff, 0x80,
         0x01, 0xff, 0xff, 0xff, 0xf0, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x1f, 0xff, 0xff, 0xff, 0x80,
         0x01, 0xff, 0xff, 0xff, 0xf0, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x1f, 0xff, 0xff, 0xff, 0x80,
         0x01, 0xff, 0xff, 0xff, 0xf0, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x1f, 0xff, 0xff, 0xff, 0x80,
         0x00, 0x00, 0x00, 0x00, 0x00, 0x1f, 0x01, 0xf0, 0x1f, 0x81, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x00,
         0x00, 0x00, 0x00, 0x00, 0x00, 0x1f, 0x01, 0xf0, 0x1f, 0x81, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x00,
         0x00, 0x00, 0x00, 0x00, 0x00, 0x1f, 0x01, 0xf0, 0x1f, 0x81, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x00,
         0x00, 0x00, 0x00, 0x00, 0x00, 0x1f, 0x01, 0xf0, 0x1f, 0x81, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x00,
         0x00, 0x00, 0x00, 0x00, 0x00, 0x1f, 0x01, 0xf0, 0x1f, 0x81, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x00,
         0x00, 0x00, 0x00, 0x00, 0x00, 0x1f, 0x01, 0xf0, 0x1f, 0x81, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x00,
         0x00, 0x00, 0x00, 0x00, 0x00, 0x1f, 0x00, 0x00, 0x1f, 0x81, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x00,
         0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x00, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
         0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x00, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
         0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x00, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
         0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x00, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
         0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x00, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
         0x00, 0x00, 0x00, 0x7f, 0xf0, 0x1f, 0x00, 0x00, 0x1f, 0xfe, 0x07, 0xff, 0x80, 0x00, 0x0f, 0x80,
         0x00, 0x00, 0x00, 0x7f, 0xf0, 0x1f, 0x01, 0xf0, 0x1f, 0xfe, 0x07, 0xff, 0x80, 0x00, 0x0f, 0x80,
         0x00, 0x00, 0x00, 0x7f, 0xf0, 0x1f, 0x01, 0xf0, 0x1f, 0xfe, 0x07, 0xff, 0x80, 0x00, 0x0f, 0x80,
         0x00, 0x00, 0x00, 0x7f, 0xf0, 0x1f, 0x01, 0xf0, 0x1f, 0xfe, 0x07, 0xff, 0x80, 0x00, 0x0f, 0x80,
         0x00, 0x00, 0x00, 0x7f, 0xf0, 0x1f, 0x01, 0xf0, 0x1f, 0xfe, 0x07, 0xff, 0x80, 0x00, 0x0f, 0x80,
         0x00, 0x00, 0x00, 0x7f, 0xf0, 0x1f, 0x01, 0xf0, 0x1f, 0xfe, 0x07, 0xff, 0x80, 0x00, 0x0f, 0x80,
         0x00, 0x00, 0x00, 0x7f, 0xf0, 0x1f, 0x01, 0xf0, 0x1f, 0xfe, 0x07, 0xff, 0x80, 0x00, 0x0f, 0x80,
         0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x7f, 0xf0, 0x1f, 0x81, 0xf8, 0x00, 0x7e, 0x00, 0x00, 0x3f,
         0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x7f, 0xf0, 0x1f, 0x81, 0xf8, 0x00, 0x7e, 0x00, 0x00, 0x3f,
         0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x7f, 0xf0, 0x1f, 0x81, 0xf8, 0x00, 0x7e, 0x00, 0x00, 0x3f,
         0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x7f, 0xf0, 0x1f, 0x81, 0xf8, 0x00, 0x7e, 0x00, 0x00, 0x3f,
         0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x7f, 0xf0, 0x1f, 0x81, 0xf8, 0x00, 0x7e, 0x00, 0x00, 0x3f,
         0x01, 0xf0, 0x00, 0x01, 0xf0, 0x00, 0x00, 0x00, 0x1f, 0x81, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x3f,
         0x01, 0xf0, 0x00, 0x01, 0xf0, 0x00, 0x00, 0x00, 0x1f, 0x81, 0xff, 0xff, 0x80, 0xf8, 0x0f, 0xff,
         0x01, 0xf0, 0x00, 0x01, 0xf0, 0x00, 0x00, 0x00, 0x1f, 0x81, 0xff, 0xff, 0x80, 0xf8, 0x0f, 0xff,
         0x01, 0xf0, 0x00, 0x01, 0xf0, 0x00, 0x00, 0x00, 0x1f, 0x81, 0xff, 0xff, 0x80, 0xf8, 0x0f, 0xff,
         0x01, 0xf0, 0x00, 0x01, 0xf0, 0x00, 0x00, 0x00, 0x1f, 0x81, 0xff, 0xff, 0x80, 0xf8, 0x0f, 0xff,
         0x01, 0xf0, 0x00, 0x01, 0xf0, 0x00, 0x00, 0x00, 0x1f, 0x81, 0xff, 0xff, 0x80, 0xf8, 0x0f, 0xff,
         0x01, 0xf0, 0x00, 0x01, 0xf0, 0x00, 0x00, 0x00, 0x1f, 0x81, 0xff, 0xff, 0x80, 0xf8, 0x0f, 0xff,
         0xfc, 0x0f, 0xff, 0xfc, 0x07, 0xff, 0xff, 0xf0, 0x1f, 0x80, 0x00, 0x00, 0x00, 0xf8, 0x00, 0x00,
         0xfc, 0x0f, 0xff, 0xfc, 0x07, 0xff, 0xff, 0xf0, 0x1f, 0x80, 0x00, 0x00, 0x00, 0xf8, 0x00, 0x00,
         0xfc, 0x0f, 0xff, 0xfc, 0x07, 0xff, 0xff, 0xf0, 0x1f, 0x80, 0x00, 0x00, 0x00, 0xf8, 0x00, 0x00,
         0xfc, 0x0f, 0xff, 0xfc, 0x07, 0xff, 0xff, 0xf0, 0x1f, 0x80, 0x00, 0x00, 0x00, 0xf8, 0x00, 0x00,
         0xfc, 0x0f, 0xff, 0xfc, 0x07, 0xff, 0xff, 0xf0, 0x1f, 0x80, 0x00, 0x00, 0x00, 0xf8, 0x00, 0x00,
         0xfc, 0x0f, 0xff, 0xfc, 0x07, 0xff, 0xff, 0xf0, 0x1f, 0x80, 0x00, 0x00, 0x00, 0xf8, 0x00, 0x00,
         0x00, 0x0f, 0xff, 0xfc, 0x00, 0x00, 0x7f, 0xf0, 0x00, 0x7e, 0x07, 0xff, 0xff, 0xff, 0xe0, 0x3f,
         0x00, 0x0f, 0xff, 0xfc, 0x00, 0x00, 0x7f, 0xf0, 0x00, 0x7e, 0x07, 0xff, 0xff, 0xff, 0xe0, 0x3f,
         0x00, 0x0f, 0xff, 0xfc, 0x00, 0x00, 0x7f, 0xf0, 0x00, 0x7e, 0x07, 0xff, 0xff, 0xff, 0xe0, 0x3f,
         0x00, 0x0f, 0xff, 0xfc, 0x00, 0x00, 0x7f, 0xf0, 0x00, 0x7e, 0x07, 0xff, 0xff, 0xff, 0xe0, 0x3f,
         0x00, 0x0f, 0xff, 0xfc, 0x00, 0x00, 0x7f, 0xf0, 0x00, 0x7e, 0x07, 0xff, 0xff, 0xff, 0xe0, 0x3f,
         0x00, 0x0f, 0xff, 0xfc, 0x00, 0x00, 0x7f, 0xf0, 0x00, 0x7e, 0x07, 0xff, 0xff, 0xff, 0xe0, 0x3f,
         0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x00, 0x00, 0x00, 0x7e, 0x07, 0xff, 0x80, 0x03, 0xff, 0xff,
         0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x00, 0x00, 0x00, 0x7e, 0x07, 0xff, 0x80, 0x03, 0xff, 0xff,
         0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x00, 0x00, 0x00, 0x7e, 0x07, 0xff, 0x80, 0x03, 0xff, 0xff,
         0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x00, 0x00, 0x00, 0x7e, 0x07, 0xff, 0x80, 0x03, 0xff, 0xff,
         0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x00, 0x00, 0x00, 0x7e, 0x07, 0xff, 0x80, 0x03, 0xff, 0xff,
         0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x00, 0x00, 0x00, 0x7e, 0x07, 0xff, 0x80, 0x03, 0xff, 0xff,
         0x00, 0x00, 0x00, 0x00, 0x00, 0x1f, 0xfe, 0x07, 0xff, 0xff, 0xf8, 0x00, 0x7f, 0xff, 0xff, 0x80,
         0x00, 0x00, 0x00, 0x00, 0x00, 0x1f, 0xfe, 0x07, 0xff, 0xff, 0xf8, 0x00, 0x7f, 0xff, 0xff, 0x80,
         0x00, 0x00, 0x00, 0x00, 0x00, 0x1f, 0xfe, 0x07, 0xff, 0xff, 0xf8, 0x00, 0x7f, 0xff, 0xff, 0x80,
         0x00, 0x00, 0x00, 0x00, 0x00, 0x1f, 0xfe, 0x07, 0xff, 0xff, 0xf8, 0x00, 0x7f, 0xff, 0xff, 0x80,
         0x00, 0x00, 0x00, 0x00, 0x00, 0x1f, 0xfe, 0x07, 0xff, 0xff, 0xf8, 0x00, 0x7f, 0xff, 0xff, 0x80,
         0x00, 0x00, 0x00, 0x00, 0x00, 0x1f, 0xfe, 0x07, 0xff, 0xff, 0xf8, 0x00, 0x7f, 0xff, 0xff, 0x80,
         0x01, 0xff, 0xff, 0xff, 0xf0, 0x1f, 0xfe, 0x07, 0xe0, 0x00, 0x00, 0x00, 0x7e, 0x03, 0xff, 0xff,
         0x01, 0xff, 0xff, 0xff, 0xf0, 0x1f, 0xfe, 0x07, 0xe0, 0x00, 0x00, 0x00, 0x7e, 0x03, 0xff, 0xff,
         0x01, 0xff, 0xff, 0xff, 0xf0, 0x1f, 0xfe, 0x07, 0xe0, 0x00, 0x00, 0x00, 0x7e, 0x03, 0xff, 0xff,
         0x01, 0xff, 0xff, 0xff, 0xf0, 0x1f, 0xfe, 0x07, 0xe0, 0x00, 0x00, 0x00, 0x7e, 0x03, 0xff, 0xff,
         0x01, 0xff, 0xff, 0xff, 0xf0, 0x1f, 0xfe, 0x07, 0xe0, 0x00, 0x00, 0x00, 0x7e, 0x03, 0xff, 0xff,
         0x01, 0xff, 0xff, 0xff, 0xf0, 0x1f, 0xfe, 0x07, 0xe0, 0x00, 0x00, 0x00, 0x7e, 0x03, 0xff, 0xff,
         0x01, 0xf0, 0x00, 0x01, 0xf0, 0x1f, 0xfe, 0x07, 0xff, 0x81, 0xff, 0xe0, 0x7e, 0x00, 0x00, 0x3f,
         0x01, 0xf0, 0x00, 0x01, 0xf0, 0x1f, 0xfe, 0x07, 0xff, 0x81, 0xff, 0xe0, 0x7e, 0x00, 0x00, 0x3f,
         0x01, 0xf0, 0x00, 0x01, 0xf0, 0x1f, 0xfe, 0x07, 0xff, 0x81, 0xff, 0xe0, 0x7e, 0x00, 0x00, 0x3f,
         0x01, 0xf0, 0x00, 0x01, 0xf0, 0x1f, 0xfe, 0x07, 0xff, 0x81, 0xff, 0xe0, 0x7e, 0x00, 0x00, 0x3f,
         0x01, 0xf0, 0x00, 0x01, 0xf0, 0x1f, 0xfe, 0x07, 0xff, 0x81, 0xff, 0xe0, 0x7e, 0x00, 0x00, 0x3f,
         0x01, 0xf0, 0x00, 0x01, 0xf0, 0x1f, 0xfe, 0x07, 0xff, 0x81, 0xff, 0xe0, 0x7e, 0x00, 0x00, 0x3f,
         0x01, 0xf0, 0x00, 0x01, 0xf0, 0x1f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7e, 0x00, 0x00, 0x3f,
         0x01, 0xf0, 0x00, 0x01, 0xf0, 0x1f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1f, 0xfe, 0x03, 0xe0, 0x3f,
         0x01, 0xf0, 0x00, 0x01, 0xf0, 0x1f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1f, 0xfe, 0x03, 0xe0, 0x3f,
         0x01, 0xf0, 0x00, 0x01, 0xf0, 0x1f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1f, 0xfe, 0x03, 0xe0, 0x3f,
         0x01, 0xf0, 0x00, 0x01, 0xf0, 0x1f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1f, 0xfe, 0x03, 0xe0, 0x3f,
         0x01, 0xf0, 0x00, 0x01, 0xf0, 0x1f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1f, 0xfe, 0x03, 0xe0, 0x3f,
         0x01, 0xf0, 0x00, 0x01, 0xf0, 0x1f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xe0, 0x3f,
         0x01, 0xf0, 0x00, 0x01, 0xf0, 0x1f, 0x01, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x00, 0xff, 0xff, 0xff,
         0x01, 0xf0, 0x00, 0x01, 0xf0, 0x1f, 0x01, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x00, 0xff, 0xff, 0xff,
         0x01, 0xf0, 0x00, 0x01, 0xf0, 0x1f, 0x01, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x00, 0xff, 0xff, 0xff,
         0x01, 0xf0, 0x00, 0x01, 0xf0, 0x1f, 0x01, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x00, 0xff, 0xff, 0xff,
         0x01, 0xf0, 0x00, 0x01, 0xf0, 0x1f, 0x01, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x00, 0xff, 0xff, 0xff,
         0x01, 0xf0, 0x00, 0x01, 0xf0, 0x1f, 0x00, 0x00, 0x1f, 0x81, 0xff, 0xe0, 0x00, 0x00, 0x0f, 0x80,
         0x01, 0xff, 0xff, 0xff, 0xf0, 0x1f, 0x00, 0x00, 0x1f, 0x81, 0xff, 0xe0, 0x7e, 0x00, 0x0f, 0x80,
         0x01, 0xff, 0xff, 0xff, 0xf0, 0x1f, 0x00, 0x00, 0x1f, 0x81, 0xff, 0xe0, 0x7e, 0x00, 0x0f, 0x80,
         0x01, 0xff, 0xff, 0xff, 0xf0, 0x1f, 0x00, 0x00, 0x1f, 0x81, 0xff, 0xe0, 0x7e, 0x00, 0x0f, 0x80,
         0x01, 0xff, 0xff, 0xff, 0xf0, 0x1f, 0x00, 0x00, 0x1f, 0x81, 0xff, 0xe0, 0x7e, 0x00, 0x0f, 0x80,
         0x01, 0xff, 0xff, 0xff, 0xf0, 0x1f, 0x00, 0x00, 0x1f, 0x81, 0xff, 0xe0, 0x7e, 0x00, 0x0f, 0x80,
         0x00, 0x00, 0x00, 0x00, 0x00, 0x1f, 0x00, 0x00, 0x1f, 0x80, 0x07, 0xe0, 0x7e, 0x00, 0x0f, 0x80,
         0x00, 0x00, 0x00, 0x00, 0x00, 0x1f, 0x01, 0xf0, 0x1f, 0xfe, 0x07, 0xe0, 0x7f, 0xf8, 0x0f, 0xff,
         0x00, 0x00, 0x00, 0x00, 0x00, 0x1f, 0x01, 0xf0, 0x1f, 0xfe, 0x07, 0xe0, 0x7f, 0xf8, 0x0f, 0xff,
         0x00, 0x00, 0x00, 0x00, 0x00, 0x1f, 0x01, 0xf0, 0x1f, 0xfe, 0x07, 0xe0, 0x7f, 0xf8, 0x0f, 0xff,
         0x00, 0x00, 0x00, 0x00, 0x00, 0x1f, 0x01, 0xf0, 0x1f, 0xfe, 0x07, 0xe0, 0x7f, 0xf8, 0x0f, 0xff,
         0x00, 0x00, 0x00, 0x00, 0x00, 0x1f, 0x01, 0xf0, 0x1f, 0xfe, 0x07, 0xe0, 0x7f, 0xf8, 0x0f, 0xff,
         0x00, 0x00, 0x00, 0x00, 0x00, 0x1f, 0x01, 0xf0, 0x1f, 0xfe, 0x07, 0xe0, 0x7f, 0xf8, 0x0f, 0xff};

/*
*********************************************************************************************************
*	函 数 名: page_root_load
*	形    参: 无
*	返 回 值: 无
*	功能说明: 根界面切换时调用
*********************************************************************************************************
*/
void page_qr_code_root_load(void)
{
}
/*
*********************************************************************************************************
*	函 数 名: page_view_load
*	形    参: 无
*	返 回 值: 无
*	功能说明: 交互界面切换时调用
*********************************************************************************************************
*/
void page_qr_code_view_load(void)
{
    UI28_Clear_Screen();                            /*2.8寸屏幕清屏，并关闭所有模块，一般切换界面时调用*/
    ui28_Play_Count_Animation(ui28_3_animation, 1); /* 设置动画次数 */
    ILI9340X_Clear(0, 45, 320, 5, DGREY2);          /*对ILI9340X显示器的某一窗口以某种颜色进行清屏*/
}
/*
*********************************************************************************************************
*	函 数 名: page_update
*	形    参: 无
*	返 回 值: 无
*	功能说明: 为交互界面时，会周期调用
*********************************************************************************************************
*/
void page_qr_code_update(void)
{
    if (ui28_animation_Ao_list[ui28_3_animation] == 0)
    {
        if (PV(int, USR_qr_code, 0) /*二维码*/==YES)
        {
            UI28_set_up_text_gradient_add(ui28_T14_tg, "扫码绑定"); /* 设置文本地址 */
        }
        else
        {
            UI28_set_up_text_gradient_add(ui28_T14_tg, "无此功能"); /* 设置文本地址 */
        }
        
        UI28_init_text_gradient_alpha(ui28_T14_tg);             /* 初始化渐变值 */
        UI28_update_text_gradient(ui28_T14_tg);                 /* 更新文本渐变 */
    }

    ILI9340X_TwoColorChart(96, 80, 128, 128, BLACK, WHITE, (uint8_t *)&my_array[0]); /*对ILI9340X显示器的某一窗口刷双色图*/
}
