#include "UI28_rolling_album.h"
#if (UI28_ROLLING_ALBUM_ENABLED == 1)

volatile uint_AlbumDis_t ui28_Rolling_Album_display = 0; /* 滚动相册显示标志位 */
#if (UI28_ROLLING_ALBUM_TRANSPARENCY_ENABLED == 1)
volatile unsigned short ui28_rolling_album_color_1[UI28_ALBUM_ITEM_NUM]; /* 滚动相册显示颜色 */
#endif
volatile unsigned char ui28_album_photo_cur_val[UI28_ALBUM_ITEM_NUM]; /* 相册当前光标值 */

volatile int ALBUM_ANIMATION_WIN_TAR[UI28_ALBUM_ITEM_NUM];  // 窗口目标中值(256倍)
volatile int ALBUM_ANIMATION_WIN_LAST[UI28_ALBUM_ITEM_NUM]; // 上一次 窗口当前中值(256倍)
volatile int ALBUM_ANIMATION_WIN[UI28_ALBUM_ITEM_NUM];      // 窗口当前中值(256倍)

volatile char ALBUM_ANIMATION_OVERAMPLITUDE; // 超幅程度值
/* 图片表 */
#define UI28_ALBUM_PHOTO_DEBUG_X(Lname, ...) const unsigned char *ui28_album_photo##Lname##_list[] = __VA_ARGS__;
UI28_ALBUM_PHOTO_ITEM_LIST
#undef UI28_ALBUM_PHOTO_DEBUG_X

/* 图片表总数 */
const unsigned char ui28_album_photo_total_list[UI28_ALBUM_PHOTO_ITEM_NUM] =
    {
#define UI28_ALBUM_PHOTO_DEBUG_X(Lname, ...) (sizeof(ui28_album_photo##Lname##_list) / sizeof(unsigned char *)),
        UI28_ALBUM_PHOTO_ITEM_LIST
#undef UI28_ALBUM_PHOTO_DEBUG_X
};

/* 相册背景颜色表 */
const unsigned short ui28_album_photo_COLOR_0_list[UI28_ALBUM_ITEM_NUM] =
    {
#define UI28_ALBUM_DEBUG_X(Lname, COLOR_0_, COLOR_1_, Lx, Ly, Lw, Lh, Ll, Ls, Ld, La) COLOR_0_,
        UI28_ALBUM_ITEM_LIST
#undef UI28_ALBUM_DEBUG_X
};

/* 相册文字颜色表 */
#if (UI28_ROLLING_ALBUM_VARIABLE_COLOR_ENABLED == 1)
volatile unsigned short ui28_album_photo_COLOR_1_list[UI28_ALBUM_ITEM_NUM] =
#else
const unsigned short ui28_album_photo_COLOR_1_list[UI28_ALBUM_ITEM_NUM] =
#endif
    {
#define UI28_ALBUM_DEBUG_X(Lname, COLOR_0_, COLOR_1_, Lx, Ly, Lw, Lh, Ll, Ls, Ld, La) COLOR_1_,
        UI28_ALBUM_ITEM_LIST
#undef UI28_ALBUM_DEBUG_X
};

#if (UI28_ROLLING_ALBUM_VARIABLE_COLOR_ENABLED == 1)
/* 上一次相册文字颜色表 */
volatile unsigned short ui28_album_photo_COLOR_1_list_LAST[UI28_ALBUM_ITEM_NUM];
#endif

/* 相册X坐标表 */
const unsigned short ui28_album_photo_Lx_list[UI28_ALBUM_ITEM_NUM] =
    {
#define UI28_ALBUM_DEBUG_X(Lname, COLOR_0_, COLOR_1_, Lx, Ly, Lw, Lh, Ll, Ls, Ld, La) Lx,
        UI28_ALBUM_ITEM_LIST
#undef UI28_ALBUM_DEBUG_X
};

/* 相册Y坐标表 */
const unsigned short ui28_album_photo_Ly_list[UI28_ALBUM_ITEM_NUM] =
    {
#define UI28_ALBUM_DEBUG_X(Lname, COLOR_0_, COLOR_1_, Lx, Ly, Lw, Lh, Ll, Ls, Ld, La) Ly,
        UI28_ALBUM_ITEM_LIST
#undef UI28_ALBUM_DEBUG_X
};

/* 相册宽表 */
const unsigned short ui28_album_photo_Lw_list[UI28_ALBUM_ITEM_NUM] =
    {
#define UI28_ALBUM_DEBUG_X(Lname, COLOR_0_, COLOR_1_, Lx, Ly, Lw, Lh, Ll, Ls, Ld, La) Lw,
        UI28_ALBUM_ITEM_LIST
#undef UI28_ALBUM_DEBUG_X
};

/* 相册高表 */
const unsigned short ui28_album_photo_Lh_list[UI28_ALBUM_ITEM_NUM] =
    {
#define UI28_ALBUM_DEBUG_X(Lname, COLOR_0_, COLOR_1_, Lx, Ly, Lw, Lh, Ll, Ls, Ld, La) Lh,
        UI28_ALBUM_ITEM_LIST
#undef UI28_ALBUM_DEBUG_X
};

/* 相册图片长表 */
const unsigned short ui28_album_photo_Ll_list[UI28_ALBUM_ITEM_NUM] =
    {
#define UI28_ALBUM_DEBUG_X(Lname, COLOR_0_, COLOR_1_, Lx, Ly, Lw, Lh, Ll, Ls, Ld, La) Ll,
        UI28_ALBUM_ITEM_LIST
#undef UI28_ALBUM_DEBUG_X
};

/* 相册间距表 */
const unsigned short ui28_album_photo_Ls_list[UI28_ALBUM_ITEM_NUM] =
    {
#define UI28_ALBUM_DEBUG_X(Lname, COLOR_0_, COLOR_1_, Lx, Ly, Lw, Lh, Ll, Ls, Ld, La) Ls,
        UI28_ALBUM_ITEM_LIST
#undef UI28_ALBUM_DEBUG_X
};

/* 相册滚动方向表 */
const unsigned char ui28_album_photo_Ld_list[UI28_ALBUM_ITEM_NUM] =
    {
#define UI28_ALBUM_DEBUG_X(Lname, COLOR_0_, COLOR_1_, Lx, Ly, Lw, Lh, Ll, Ls, Ld, La) Ld,
        UI28_ALBUM_ITEM_LIST
#undef UI28_ALBUM_DEBUG_X
};

/* 相册滚动图片表表 */
const unsigned char ui28_album_photo_La_list[UI28_ALBUM_ITEM_NUM] =
    {
#define UI28_ALBUM_DEBUG_X(Lname, COLOR_0_, COLOR_1_, Lx, Ly, Lw, Lh, Ll, Ls, Ld, La) La,
        UI28_ALBUM_ITEM_LIST
#undef UI28_ALBUM_DEBUG_X
};

// 根据光标更新目标
void Update_the_target_based_on_the_cursor_album(enum UI28_ALBUM_e_ ALBUM_)
{
    ALBUM_ANIMATION_WIN_TAR[ALBUM_] = (((ui28_album_photo_cur_val[ALBUM_] + 2) * (ui28_album_photo_Ll_list[ALBUM_] + ui28_album_photo_Ls_list[ALBUM_])) << 8) + ((ui28_album_photo_Ll_list[ALBUM_] + ui28_album_photo_Ls_list[ALBUM_]) << 7); // 窗口目标中值(256倍)
}

/* 根据目标图片更新当前 */
void Update_the_current_image_based_on_the_target_image_Album(enum UI28_ALBUM_e_ ALBUM_)
{
    UI28_anima((int *)&ALBUM_ANIMATION_WIN[ALBUM_], (int *)&ALBUM_ANIMATION_WIN_TAR[ALBUM_], 3); // 窗口目标中值(256倍)
    if (ALBUM_ANIMATION_OVERAMPLITUDE == 0)
    {
        return;
    }
    else if (ALBUM_ANIMATION_OVERAMPLITUDE > 127)
    {
        /* 上(左)调节超幅 */
        ALBUM_ANIMATION_WIN[ALBUM_] -= (ui28_album_photo_Ll_list[ALBUM_] << 7); // 窗口当前中值(256倍)
    }
    else
    {
        /* 下(右)调节超幅 */
        ALBUM_ANIMATION_WIN[ALBUM_] += (ui28_album_photo_Ll_list[ALBUM_] << 7); // 窗口当前中值(256倍)
    }
    ALBUM_ANIMATION_OVERAMPLITUDE = 0;
}

/* 更新滚动相册 */
void UI28_update_Rolling_Album(enum UI28_ALBUM_e_ ALBUM_)
{
    short WIN;
    short start_, start_WIN_, us_, Ll_;

    if (ALBUM_ANIMATION_WIN_LAST[ALBUM_] == ALBUM_ANIMATION_WIN[ALBUM_])
    {
        if (ui28_album_photo_COLOR_1_list_LAST[ALBUM_] == ui28_album_photo_COLOR_1_list[ALBUM_])
        {

            /* 全部都相等，就不用刷新了 */
            return;
        }
    }
    /* 更新上一次 */
    ALBUM_ANIMATION_WIN_LAST[ALBUM_] = ALBUM_ANIMATION_WIN[ALBUM_];
    ui28_album_photo_COLOR_1_list_LAST[ALBUM_] = ui28_album_photo_COLOR_1_list[ALBUM_];

    if (ui28_album_photo_Ld_list[ALBUM_] == 0)
    {
        /*滚动方向0：左右*/
        Ll_ = ui28_album_photo_Lw_list[ALBUM_];
    }
    else
    {
        /*滚动方向1：上下*/
        Ll_ = ui28_album_photo_Lh_list[ALBUM_];
    }
    WIN = (ALBUM_ANIMATION_WIN[ALBUM_] - (Ll_ << 7)) >> 8; // 窗口当前坐标

    // 计算当前窗口涉及的图片范围
    start_ = WIN / (ui28_album_photo_Ll_list[ALBUM_] + ui28_album_photo_Ls_list[ALBUM_]);
    start_WIN_ = WIN % (ui28_album_photo_Ll_list[ALBUM_] + ui28_album_photo_Ls_list[ALBUM_]);
    start_WIN_ = (ui28_album_photo_Ls_list[ALBUM_] >> 1) - start_WIN_;

    /*窗口原先就偏移了2行*/
    start_ -= 2;
    /*对二维像素页缓存-开窗(自定义)（窗口内bit全为0）*/
    PixelPageCache2D_OpenWin_customize(ui28_album_photo_Lw_list[ALBUM_], ui28_album_photo_Lh_list[ALBUM_], 0);
    /*刷图片*/
    for (us_ = start_WIN_; us_ < Ll_; us_ += (ui28_album_photo_Ll_list[ALBUM_] + ui28_album_photo_Ls_list[ALBUM_]))
    {
        if (start_ >= 0 && start_ < ui28_album_photo_total_list[ui28_album_photo_La_list[ALBUM_]])
        {
            /*对二维像素页缓存的某一窗口刷图*/
            switch (ui28_album_photo_La_list[ALBUM_])
            {
#define UI28_ALBUM_PHOTO_DEBUG_X(Lname, ...)                                                                                                                                                                                                                   \
    case ui28_##Lname##_album_photo:                                                                                                                                                                                                                           \
        if (ui28_album_photo_Ld_list[ALBUM_] == 0 /*滚动方向0：左右 1：上下*/)                                                                                                                                                                                 \
        {                                                                                                                                                                                                                                                      \
            PixelPageCache2D_TwoColorChart_customize(ui28_album_photo_Lw_list[ALBUM_], ui28_album_photo_Lh_list[ALBUM_], us_, 0, ui28_album_photo_Ll_list[ALBUM_], ui28_album_photo_Lh_list[ALBUM_], (unsigned char *)ui28_album_photo##Lname##_list[start_]); \
        }                                                                                                                                                                                                                                                      \
        else                                                                                                                                                                                                                                                   \
        {                                                                                                                                                                                                                                                      \
            PixelPageCache2D_TwoColorChart_customize(ui28_album_photo_Lw_list[ALBUM_], ui28_album_photo_Lh_list[ALBUM_], 0, us_, ui28_album_photo_Lw_list[ALBUM_], ui28_album_photo_Ll_list[ALBUM_], (unsigned char *)ui28_album_photo##Lname##_list[start_]); \
        }                                                                                                                                                                                                                                                      \
        break;
                UI28_ALBUM_PHOTO_ITEM_LIST
#undef UI28_ALBUM_PHOTO_DEBUG_X
            default:
                break;
            }
        }
        start_++;
    }

#if (UI28_ROLLING_ALBUM_TRANSPARENCY_ENABLED == 1)
    ILI9340X_TwoColorChart(ui28_album_photo_Lx_list[ALBUM_], ui28_album_photo_Ly_list[ALBUM_], ui28_album_photo_Lw_list[ALBUM_], ui28_album_photo_Lh_list[ALBUM_], ui28_album_photo_COLOR_0_list[ALBUM_], ui28_rolling_album_color_1[ALBUM_], (uint8_t *)&PixelPageCache2D[0][0]);
#else
    ILI9340X_TwoColorChart(ui28_album_photo_Lx_list[ALBUM_], ui28_album_photo_Ly_list[ALBUM_], ui28_album_photo_Lw_list[ALBUM_], ui28_album_photo_Lh_list[ALBUM_], ui28_album_photo_COLOR_0_list[ALBUM_], ui28_album_photo_COLOR_1_list[ALBUM_], (uint8_t *)&PixelPageCache2D[0][0]);
#endif
}
/* 工程初始化滚动相册（在系统上电执行一次） */
void ui28_Init_Rolling_Album_Engineering(void)
{
    int i;
    for (i = 0; i < UI28_ALBUM_ITEM_NUM; i++)
    {
        ALBUM_ANIMATION_WIN_TAR[i] = ((ui28_album_photo_Ll_list[i] + ui28_album_photo_Ls_list[i]) << 9) + ((ui28_album_photo_Ll_list[i] + ui28_album_photo_Ls_list[i]) << 7);
    }

#if (UI28_ROLLING_ALBUM_TRANSPARENCY_ENABLED == 1)
    for (i = 0; i < UI28_ALBUM_ITEM_NUM; i++)
    {
        ui28_rolling_album_color_1[i] = ui28_album_photo_COLOR_1_list[i];
    }
#endif
}
/* 初始化滚动相册 */
void ui28_Init_Rolling_Album(enum UI28_ALBUM_e_ ALBUM_)
{
#if (UI28_ROLLING_ALBUM_TRANSPARENCY_ENABLED == 1)
    int i;
#endif
    ALBUM_ANIMATION_WIN_LAST[ALBUM_] = 0;                                                                     // 上一次 窗口当前中值(256倍)
    ALBUM_ANIMATION_WIN[ALBUM_] = (ui28_album_photo_Ll_list[ALBUM_] + ui28_album_photo_Ls_list[ALBUM_]) << 8; // 窗口当前Y(256倍)
#if (UI28_ROLLING_ALBUM_TRANSPARENCY_ENABLED == 1)
    for (i = 0; i < UI28_ALBUM_ITEM_NUM; i++)
    {
        ui28_rolling_album_color_1[i] = ui28_album_photo_COLOR_1_list[i];
    }
#endif
}

/* 菜单显示开关（关实际只是不刷新了） */
void UI28_display_onoff_album(enum UI28_ALBUM_e_ ALBUM_, unsigned char _onoff_)
{
    if (_onoff_ == 0)
    {
        ui28_Rolling_Album_display &= ~(1UL << ALBUM_);
    }
    else
    {
        ui28_Rolling_Album_display |= (1UL << ALBUM_);
    }
}

/* 设置光标位置 */
void UI28_change_cursor_value_album(enum UI28_ALBUM_e_ ALBUM_, unsigned char cur_val)
{
    if (cur_val < ui28_album_photo_total_list[ui28_album_photo_La_list[ALBUM_]])
    {
        ui28_album_photo_cur_val[ALBUM_] = cur_val;
    }
    else
    {
        ui28_album_photo_cur_val[ALBUM_] = ui28_album_photo_total_list[ui28_album_photo_La_list[ALBUM_]] - 1;
    }
    Update_the_target_based_on_the_cursor_album(ALBUM_); // 根据光标更新目标
}

/* 获取光标位置 */
unsigned char UI28_obtain_cursor_value_album(enum UI28_ALBUM_e_ ALBUM_)
{
    return ui28_album_photo_cur_val[ALBUM_];
}

/* 单步调节（上下） */
unsigned char UI28_single_step_album(enum UI28_ALBUM_e_ ALBUM_, enum UI28_STEP_ step__)
{
    if ((ui28_Rolling_Album_display >> ALBUM_) & 1)
    {
        if (step__ == ui28_up)
        {
            /* 单步调节（上） */
            if (ui28_album_photo_cur_val[ALBUM_] > 0)
            {
                ui28_album_photo_cur_val[ALBUM_]--;
            }
            else
            {
                ALBUM_ANIMATION_OVERAMPLITUDE--;
            }
        }
        else
        {
            /* 单步调节（下） */
            if (ui28_album_photo_cur_val[ALBUM_] < (ui28_album_photo_total_list[ui28_album_photo_La_list[ALBUM_]] - 1))
            {
                ui28_album_photo_cur_val[ALBUM_]++;
            }
            else
            {
                ALBUM_ANIMATION_OVERAMPLITUDE++;
            }
        }
        Update_the_target_based_on_the_cursor_album(ALBUM_); // 根据光标更新目标
    }
    return ui28_album_photo_cur_val[ALBUM_];
}
#if (UI28_ROLLING_ALBUM_TRANSPARENCY_ENABLED == 1)
/* 更改透明度 */
void UI28_change_transparency_album(enum UI28_ALBUM_e_ ALBUM_, unsigned char alpha)
{
    ui28_rolling_album_color_1[ALBUM_] = alpha_blend_rgb565(ui28_album_photo_COLOR_0_list[ALBUM_], ui28_album_photo_COLOR_1_list[ALBUM_], alpha);
}
#endif
#if (UI28_ROLLING_ALBUM_VARIABLE_COLOR_ENABLED == 1)
/* 相册更改文字颜色 */
void UI28_change_color_album(enum UI28_ALBUM_e_ ALBUM_, unsigned short color)
{
    ui28_album_photo_COLOR_1_list[ALBUM_] = color;
}
#endif
#endif