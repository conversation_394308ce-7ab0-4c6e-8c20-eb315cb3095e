--cpu Cortex-M3
".\obj\main.o"
".\obj\air32f10x_it.o"
".\obj\system_air32f10x.o"
".\obj\bsp.o"
".\obj\bsp_timer.o"
".\obj\bsp_uart_fifo.o"
".\obj\bsp_fifo.o"
".\obj\bsp_key.o"
".\obj\bsp_crc.o"
".\obj\bsp_utility.o"
".\obj\bsp_temp.o"
".\obj\bsp_ntc.o"
".\obj\bsp_signal.o"
".\obj\bsp_beep.o"
".\obj\bsp_tim_pwm.o"
".\obj\bsp_boot.o"
".\obj\pro_cus_com.o"
".\obj\pro_hardware.o"
".\obj\pro_workflow.o"
".\obj\pro_error.o"
".\obj\pro_pagekey.o"
".\obj\page.o"
".\obj\page_check.o"
".\obj\page_debug.o"
".\obj\page_developer.o"
".\obj\page_parameter.o"
".\obj\page_run.o"
".\obj\page_standby.o"
".\obj\page_start.o"
".\obj\page_statistics.o"
".\obj\page_qr_code.o"
".\obj\parame.o"
".\obj\flash_eeprom.o"
".\obj\ui28.o"
".\obj\bsp_ili9340x_lcd.o"
".\obj\ui28_font_14.o"
".\obj\ui28_font_16.o"
".\obj\ui28_font_18.o"
".\obj\ui28_font_20.o"
".\obj\ui28_font_24.o"
".\obj\ui28_font_28.o"
".\obj\ui28_font_32.o"
".\obj\ui28_font_36.o"
".\obj\ui28_font_72.o"
".\obj\ui28_matrix.o"
".\obj\ui28_menu.o"
".\obj\ui28_auxiliary_table.o"
".\obj\ui28_rolling_album.o"
".\obj\ui28_arc.o"
".\obj\ui28_pic_120x120.o"
".\obj\ui28_pic_32x32.o"
".\obj\ui28_pic_22x30.o"
".\obj\ui28_pic_14x18.o"
".\obj\ui28_pic_48x88.o"
".\obj\ui28_pic_36x36.o"
".\obj\ui28_pic_40x20.o"
".\obj\ui28_pic_45x45.o"
".\obj\ui28_pic_70x70.o"
".\obj\ui28_animation.o"
".\obj\ui28_progress_bar.o"
".\obj\startup_air32f10x.o"
".\obj\misc.o"
".\obj\air32f10x_adc.o"
".\obj\air32f10x_bkp.o"
".\obj\air32f10x_can.o"
".\obj\air32f10x_cec.o"
".\obj\air32f10x_crc.o"
".\obj\air32f10x_dac.o"
".\obj\air32f10x_dbgmcu.o"
".\obj\air32f10x_dma.o"
".\obj\air32f10x_exti.o"
".\obj\air32f10x_flash.o"
".\obj\air32f10x_fsmc.o"
".\obj\air32f10x_gpio.o"
".\obj\air32f10x_i2c.o"
".\obj\air32f10x_iwdg.o"
".\obj\air32f10x_otp.o"
".\obj\air32f10x_pwr.o"
".\obj\air32f10x_rcc.o"
".\obj\air32f10x_rcc_ex.o"
".\obj\air32f10x_rtc.o"
".\obj\air32f10x_sdio.o"
".\obj\air32f10x_spi.o"
".\obj\air32f10x_tim.o"
".\obj\air32f10x_trng.o"
".\obj\air32f10x_usart.o"
".\obj\air32f10x_wwdg.o"
--library_type=microlib --strict --scatter ".\AIR.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list ".\AIR.map" -o .\OBJ\AIR.axf