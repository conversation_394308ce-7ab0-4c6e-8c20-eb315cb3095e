#include "UI28_arc.h"
#if (UI28_ARC_ENABLED == 1)
/* X为120的Y度数表(1-45度) */
const unsigned char Y_Degree_Chart_with_X_of_120[45] = {2, 4, 6, 8, 11, 13, 15, 17, 19, 21, 23, 26, 28, 30, 32, 34, 37, 39, 41, 44, 46, 48, 51, 53, 56, 59, 61, 64, 67, 69, 72, 75, 78, 81, 84, 87, 90, 94, 97, 101, 104, 108, 112, 116, 120};

/* 根据角度绘制表 */
/* degree_:角度*/
/* Dr_:半径*/
/* list:表*/
void UI28_Draw_a_table_based_on_the_angle_arc(unsigned short degree_, unsigned char Dr_, unsigned char *list)
{
    short X1, Y1, X2, Y2;
    unsigned char ds;
    unsigned short us;
    short usX_Current, usY_Current, usX_;

    int lError_X = 0, lError_Y = 0, lDelta_X, lDelta_Y, lDistance;
    int lIncrease_X, lIncrease_Y;

    if (degree_ == 0)
    {
        memset((char *)list, Dr_, Dr_);
        return;
    }
    else if (degree_ == 180)
    {
        memset((char *)list, (Dr_ - 1), Dr_);
        return;
    }
    else if (degree_ > 0 && degree_ < 90)
    {
        X1 = Dr_;
        Y1 = Dr_;
        if (degree_ <= 45)
        {
            X2 = X1 + 119;
            Y2 = Y1 + Y_Degree_Chart_with_X_of_120[degree_ - 1] - 1;
        }
        else
        {
            X2 = X1 + Y_Degree_Chart_with_X_of_120[(89 - degree_)] - 1;
            Y2 = Y1 + 119;
        }
    }
    else if (degree_ > 90 && degree_ < 180)
    {
        X1 = Dr_ - 1;
        Y1 = Dr_;
        if (degree_ >= 135)
        {
            X2 = X1 - 119;
            Y2 = Y1 + Y_Degree_Chart_with_X_of_120[(179 - degree_)] - 1;
        }
        else
        {
            X2 = Dr_ - Y_Degree_Chart_with_X_of_120[(degree_ - 91)];
            Y2 = Y1 + 119;
        }
    }
    else if (degree_ > 180 && degree_ < 270)
    {
        X1 = Dr_ - 1;
        Y1 = X1;
        if (degree_ <= 225)
        {
            X2 = X1 - 119;
            Y2 = Dr_ - Y_Degree_Chart_with_X_of_120[(degree_ - 181)];
        }
        else
        {
            X2 = Dr_ - Y_Degree_Chart_with_X_of_120[(269 - degree_)];
            Y2 = Y1 - 119;
        }
    }
    else if (degree_ > 270 && degree_ < 360)
    {
        X1 = Dr_;
        Y1 = Dr_ - 1;
        if (degree_ >= 315)
        {
            X2 = X1 + 119;
            Y2 = Dr_ - Y_Degree_Chart_with_X_of_120[(359 - degree_)];
        }
        else
        {
            X2 = X1 + Y_Degree_Chart_with_X_of_120[(degree_ - 271)] - 1;
            Y2 = Y1 - 119;
        }
    }

    lDelta_X = X2 - X1; // 计算坐标增量
    lDelta_Y = Y2 - Y1;

    usX_Current = X1;
    usY_Current = Y1;
    usX_ = usX_Current - 1;
    ds = 0;

    if (lDelta_X > 0)
    {
        lIncrease_X = 1; // 设置单步方向
    }
    else
    {
        lIncrease_X = -1;
        lDelta_X = -lDelta_X;
    }

    if (lDelta_Y > 0)
    {
        lIncrease_Y = 1; // 设置单步方向
    }
    else
    {
        lIncrease_Y = -1;
        lDelta_Y = -lDelta_Y;
    }

    if (lDelta_X > lDelta_Y)
    {
        lDistance = lDelta_X; // 选取基本增量坐标轴
    }
    else
    {
        lDistance = lDelta_Y;
    }

    for (us = 0; us <= lDistance + 1; us++) // 画线输出
    {
        if (ds < Dr_)
        {
            if (usY_Current >= 0 && usY_Current < (Dr_ << 1))
            {
                if (lIncrease_X > 0)
                {
                    list[ds] = usY_Current;
                }
                else
                {
                    list[Dr_ - 1 - ds] = usY_Current;
                }

                if (usX_ != usX_Current)
                {
                    usX_ = usX_Current;
                    // 地址++
                    ds++;
                }
            }
            else
            {
                if (usY_Current < 0)
                {
                    usY_Current = 0;
                }
                else
                {
                    usY_Current = Dr_ << 1;
                }

                break;
            }
        }
        else
        {
            return;
        }

        lError_X += lDelta_X;
        lError_Y += lDelta_Y;

        if (lError_X > lDistance)
        {
            lError_X -= lDistance;
            usX_Current += lIncrease_X;
        }

        if (lError_Y > lDistance)
        {
            lError_Y -= lDistance;
            usY_Current += lIncrease_Y;
        }
    }

    // 死循环将上一个地址的东西存入下一个地址
    while (ds < Dr_)
    {
        if (lIncrease_X > 0)
        {
            list[ds] = usY_Current;
        }
        else
        {
            list[Dr_ - ds - 1] = usY_Current;
        }
        ds++;
    }
}

// 绘制表
void UI28_Draw_a_table_arc(unsigned short start_degree, unsigned short end_degree, unsigned char Dr_, unsigned char *start_line_list, unsigned char *end_line_list)
{
    unsigned short diam_ = (unsigned short)Dr_ << 1; // 直径

    if (start_degree == end_degree)
    {
        /* 若相等，则为整圆 */
        memset((char *)start_line_list, 0xFF, diam_);
        memset((char *)end_line_list, 0, diam_);
    }
    else
    {
        if (start_degree > 90 && start_degree < 270)
        {
            UI28_Draw_a_table_based_on_the_angle_arc(start_degree, Dr_, end_line_list);
            if (end_degree > 90 && end_degree < 270)
            {
                /* 都在左侧 */
                /* 起始线为(上1下0)，终止线为(上0下1) */
                UI28_Draw_a_table_based_on_the_angle_arc(end_degree, Dr_, start_line_list);
                if (start_degree > end_degree)
                {
                    /* 右侧都为0 */
                    memset((char *)(start_line_list + Dr_), 0, Dr_);
                    memset((char *)(end_line_list + Dr_), 0xFF, Dr_);
                }
                else
                {
                    /* 右侧都为1 */
                    memset((char *)(start_line_list + Dr_), 0xFF, Dr_);
                    memset((char *)(end_line_list + Dr_), 0, Dr_);
                }
            }
            else if (end_degree < 90 || end_degree > 270)
            {
                /* (上1下0) */
                UI28_Draw_a_table_based_on_the_angle_arc(end_degree, Dr_, (end_line_list + Dr_));
                memset((char *)start_line_list, 0xFF, diam_);
            }
            else if (end_degree == 90)
            {
                /* (上1下0) */
                memset((char *)(end_line_list + Dr_), 0xFF, Dr_);
                memset((char *)start_line_list, 0xFF, diam_);
            }
            else if (end_degree == 270)
            {
                /* (上1下0) */
                memset((char *)(end_line_list + Dr_), 0, Dr_);
                memset((char *)start_line_list, 0xFF, diam_);
            }
        }
        else if (start_degree < 90 || start_degree > 270)
        {
            UI28_Draw_a_table_based_on_the_angle_arc(start_degree, Dr_, (start_line_list + Dr_));
            if (end_degree < 90 || end_degree > 270)
            {
                /* 都在右侧 */
                /* 起始线为(上0下1)，终止线为(上1下0) */
                UI28_Draw_a_table_based_on_the_angle_arc(end_degree, Dr_, (end_line_list + Dr_));
                if ((start_degree > end_degree && (start_degree < 90 || end_degree > 270)) || (start_degree < 90 && end_degree > 270))
                {
                    /* 左侧都为0 */
                    memset((char *)start_line_list, 0, Dr_);
                    memset((char *)end_line_list, 0xFF, Dr_);
                }
                else
                {
                    /* 左侧都为1 */
                    memset((char *)start_line_list, 0xFF, Dr_);
                    memset((char *)end_line_list, 0, Dr_);
                }
            }
            else if (end_degree > 90 && end_degree < 270)
            {
                /* (上0下1) */
                UI28_Draw_a_table_based_on_the_angle_arc(end_degree, Dr_, start_line_list);
                memset((char *)end_line_list, 0xFF, diam_);
            }
            else if (end_degree == 90)
            {
                /* (上0下1) */
                memset((char *)start_line_list, 0xFF, Dr_);
                memset((char *)end_line_list, 0, diam_);
            }
            else if (end_degree == 270)
            {
                /* (上0下1) */
                memset((char *)start_line_list, 0, Dr_);
                memset((char *)end_line_list, 0xFF, Dr_);
                memset((char *)(end_line_list + Dr_), 0, Dr_);
            }
        }
        else if (start_degree == 90)
        {
            if (end_degree < 90 || end_degree > 270)
            {
                /* 终止线为(上1下0) */
                UI28_Draw_a_table_based_on_the_angle_arc(end_degree, Dr_, (end_line_list + Dr_));
                /* 左侧都为0 */
                memset((char *)start_line_list, 0, diam_);
                memset((char *)(start_line_list + Dr_), 0xFF, diam_);
                memset((char *)end_line_list, 0xFF, Dr_);
            }
            else if (end_degree > 90 && end_degree < 270)
            {
                /* 终止线为(上0下1) */
                UI28_Draw_a_table_based_on_the_angle_arc(end_degree, Dr_, start_line_list);
                /* 右侧都为1 */
                memset((char *)(start_line_list + Dr_), 0xFF, Dr_);
                memset((char *)end_line_list, 0xFF, Dr_);
                memset((char *)(end_line_list + Dr_), 0, Dr_);
            }
            else if (end_degree == 270)
            {
                /* 左侧都为0 */
                memset((char *)start_line_list, 0, Dr_);
                memset((char *)end_line_list, 0xFF, diam_);
                /* 右侧都为1 */
                memset((char *)(start_line_list + Dr_), 0xFF, Dr_);
            }
        }
        else if (start_degree == 270)
        {
            if (end_degree < 90 || end_degree > 270)
            {
                /* 终止线为(上1下0) */
                UI28_Draw_a_table_based_on_the_angle_arc(end_degree, Dr_, (end_line_list + Dr_));
                /* 左侧都为1 */
                memset((char *)start_line_list, 0xFF, diam_);
                memset((char *)end_line_list, 0, Dr_);
            }
            else if (end_degree > 90 && end_degree < 270)
            {
                /* 终止线为(上0下1) */
                UI28_Draw_a_table_based_on_the_angle_arc(end_degree, Dr_, start_line_list);
                /* 右侧都为0 */
                memset((char *)(start_line_list + Dr_), 0, Dr_);
                memset((char *)end_line_list, 0, diam_);
            }
            else if (end_degree == 90)
            {
                /* 左侧都为1 */
                memset((char *)start_line_list, 0xFF, diam_);
                /* 右侧都为0 */
                memset((char *)end_line_list, 0, Dr_);
                memset((char *)(end_line_list + Dr_), 0xFF, Dr_);
            }
        }
    }
}

// 根据表判断坐标
unsigned char UI28_Determine_the_coordinates_based_on_the_table_arc(unsigned short X, unsigned short Y, unsigned short diam_, unsigned char *start_line_list, unsigned char *end_line_list, unsigned short start_degree, unsigned short end_degree)
{
    if (Y >= start_line_list[X])
    {
        if (Y <= end_line_list[X])
        {
            /* 0 */
            return 0;
        }
    }
    else if (Y < start_line_list[X])
    {
        if (Y >= end_line_list[X])
        {
            /* 1 */
            return 1;
        }
    }

    if (start_line_list[X] == end_line_list[X])
    {
        if (start_degree < end_degree)
        {
            /* 1 */
            return 1;
        }
        else
        {
            /* 0 */
            return 0;
        }
    }
    else if (start_line_list[X] < end_line_list[X])
    {
        /* 1 */
        return 1;
    }
    else
    {
        /* 0 */
        return 0;
    }
}

// 判断坐标是否在圆内
unsigned char UI28_Determine_whether_the_coordinates_are_within_the_circle_arc(unsigned short X, unsigned short Y, unsigned char *ucR)
{
    unsigned short Y_ = Y;
    unsigned short diam_ = (unsigned short)ucR[0] << 1; // 直径
    if (Y >= ucR[0])
    {
        Y_ = diam_ - Y;
    }
    else
    {
        Y_ = Y + 1;
    }

    if (X >= ucR[Y_] && X < (diam_ - ucR[Y_]))
    {
        /* 1 */
        return 1;
    }
    else
    {
        /* 0 */
        return 0;
    }
}

// 根据圆环判断坐标
unsigned char Determine_coordinates_based_on_the_circular_ring(unsigned short X, unsigned short Y, unsigned char *ucR_outside, unsigned char *ucR_within)
{
    /* 先判断外圆内 */
    if (UI28_Determine_whether_the_coordinates_are_within_the_circle_arc(X, Y, ucR_outside))
    {
        if (ucR_within != 0)
        {
            unsigned short bias_ = ucR_outside[0] - ucR_within[0];
            unsigned short most_ = ucR_outside[0] + ucR_within[0];
            /* 先判断内圆 */
            if (X >= bias_ && X < most_)
            {
                if (Y >= bias_ && Y < most_)
                {
                    if (UI28_Determine_whether_the_coordinates_are_within_the_circle_arc((X - bias_), (Y - bias_), ucR_within))
                    {
                        /* 0 */
                        return 0;
                    }
                }
            }
        }
        /* 1 */
        return 1;
    }
    /* 0 */
    return 0;
}

/* 绘制圆弧 */
/* usX:外圆左上角坐标X*/
/* usY:外圆左上角坐标Y*/
/* ucR_outside:R角表地址(外)*/
/* ucR_within:R角表地址(内)*/
/* start_degree:起始角度（0-359）*/
/* end_degree:终止角度（0-359）*/
/* start_shield_degree:起始屏蔽角度（0-359）*/
/* end_shield_degree:终止屏蔽角度（0-359）*/
/* usColor_0:背景颜色*/
/* usColor_1:圆弧颜色*/
void UI28_draw_arc(unsigned short usX, unsigned short usY, unsigned char *ucR_outside, unsigned char *ucR_within, unsigned short start_degree, unsigned short end_degree, unsigned short start_shield_degree, unsigned short end_shield_degree, unsigned short usColor_0, unsigned short usColor_1)
{
    unsigned short diam_ = (unsigned short)ucR_outside[0] << 1;                           // 直径
    unsigned char *start_line_list = (unsigned char *)&PixelPageCache2D[0][0];            // (上0下1)表
    unsigned char *end_line_list = start_line_list + diam_;                               // (上1下0)表
    unsigned char *start_shield_line_list = end_line_list + diam_;                        // (上0下1)屏蔽表
    unsigned char *end_shield_line_list = start_shield_line_list + diam_;                 // (上1下0)屏蔽表
    unsigned short *color_Cache_Table = (unsigned short *)(end_shield_line_list + diam_); // 颜色缓存表
    unsigned short X, Y, number, X_;

    // 绘制表
    UI28_Draw_a_table_arc(start_degree, end_degree, ucR_outside[0], start_line_list, end_line_list);
    // 绘制表
    UI28_Draw_a_table_arc(start_shield_degree, end_shield_degree, ucR_outside[0], start_shield_line_list, end_shield_line_list);

    for (Y = 0; Y < diam_; Y++)
    {
        number = 0;
        X_ = 0;
        for (X = 0; X < diam_; X++)
        {
            if (Determine_coordinates_based_on_the_circular_ring(X, Y, ucR_outside, ucR_within))
            {
                if (UI28_Determine_the_coordinates_based_on_the_table_arc(X, (diam_ - Y - 1), diam_, start_shield_line_list, end_shield_line_list, start_shield_degree, end_shield_degree))
                {
                    if (UI28_Determine_the_coordinates_based_on_the_table_arc(X, (diam_ - Y - 1), diam_, start_line_list, end_line_list, start_degree, end_degree))
                    {
                        /* 1 */
                        color_Cache_Table[number] = usColor_1;
                    }
                    else
                    {
                        /* 0 */
                        color_Cache_Table[number] = usColor_0;
                    }
                    number++;

                    if ((X + 1) < diam_)
                    {
                        continue; // 跳过当次循环，进入下次循环
                    }
                }
            }
            if (number != 0)
            {
                ILI9340X_Picture(X_ + usX, Y + usY, number, 1, color_Cache_Table); // 对ILI9340X显示器的某一窗口刷图
                number = 0;
            }
            X_ = X + 1;
        }
    }
}

volatile unsigned char ui28_arc_loading_display = 0; /* 圆弧加载动画显示标志位 */
#if (UI28_ARC_LOADING_SET_UP == 1)
volatile unsigned short ui28_arc_loading_usX;     /* 圆弧加载动画 外圆左上角坐标X */
volatile unsigned short ui28_arc_loading_usY;     /* 圆弧加载动画 外圆左上角坐标Y */
volatile unsigned char *ui28_arc_loading_outside; /* 圆弧加载动画 R角表地址(外) */
volatile unsigned char *ui28_arc_loading_within;  /* 圆弧加载动画 R角表地址(内) */
volatile unsigned short ui28_arc_loading_Color_0; /* 圆弧加载动画 背景颜色 */
volatile unsigned short ui28_arc_loading_Color_1; /* 圆弧加载动画 圆弧颜色 */
#endif
/* 圆弧加载动画 */
void UI28_arc_loading(void)
{
    int a;                                  // 终止到起始的距离（顺时针）
    int b;                                  // 起始到终止的距离（顺时针）
    static uint8_t Action_steps = 0;        // 动作步
    static int current_start = 30720;       // 当前起始角度120 * 256
    static int current_termination = 15360; // 当前终止角度60 * 256

    // 1.计算距离
    if (current_termination > current_start)
    {
        a = current_termination - current_start;
        b = 92160 - a;
    }
    else
    {
        b = current_start - current_termination;
        a = 92160 - b;
    }

    // 2.判断当前动作步
    if (a > 76800) // 距离足够大的了300 * 256
    {
        Action_steps = 0;
    }
    else if (a > 76544) // 距离中间299 * 256
    {
        Action_steps = 2;
    }
    else if (a < 38400) // 距离足够小的了150 * 256
    {
        Action_steps = 1;
    }

    // 3.动作步更新角度值
    if (Action_steps == 0) // 距离足够大的了
    {
        a = a >> 6;
        current_termination -= a; // 当前终止
    }
    else if (Action_steps == 2) // 距离中间
    {
        a = a >> 7;
        b = b >> 7;
        current_termination -= a; // 当前终止
        current_termination -= 256;
        current_start -= a; // 当前起始
    }
    else if (Action_steps == 1) // 距离足够小的了
    {
        b = b >> 6;
        current_start -= b; // 当前起始
    }

    // 4.将角度值限幅
    if (current_start < 0)
    {
        current_start += 92160;
    }
    if (current_termination < 0)
    {
        current_termination += 92160;
    }

    // 5。更新当前画面
#if (UI28_ARC_LOADING_SET_UP == 1)
    UI28_draw_arc(ui28_arc_loading_usX, ui28_arc_loading_usY, (unsigned char *)ui28_arc_loading_outside, (unsigned char *)ui28_arc_loading_within, (unsigned short)(current_start >> 8), (unsigned short)(current_termination >> 8), 0, 0, ui28_arc_loading_Color_0, ui28_arc_loading_Color_1);
#else
#define UI28_ARC_LOADING_DEBUG_X(usX, usY, ucR_outside, ucR_within, usColor_0, usColor_1) \
    UI28_draw_arc(usX, usY, GET_R_ANGLE_BY_ROW(ucR_outside), GET_R_ANGLE_BY_ROW(ucR_within), (unsigned short)(current_start >> 8), (unsigned short)(current_termination >> 8), 0, 0, usColor_0, usColor_1);
    UI28_ARC_LOADING_ITEM_LIST
#undef UI28_ARC_LOADING_DEBUG_X
#endif
}

/* 圆弧加载动画显示开关（关，实际只是不刷新了） */
void UI28_display_onoff_arc_loading(unsigned char _onoff_)
{
    ui28_arc_loading_display = _onoff_;
}

#if (UI28_ARC_LOADING_SET_UP == 1)
/* 设置圆弧加载动画 */
void UI28_set_up_arc_loading(unsigned short usX, unsigned short usY, unsigned char *ucR_outside, unsigned char *ucR_within, unsigned short usColor_0, unsigned short usColor_1)
{
    ui28_arc_loading_usX = usX;             /* 圆弧加载动画 外圆左上角坐标X */
    ui28_arc_loading_usY = usY;             /* 圆弧加载动画 外圆左上角坐标Y */
    ui28_arc_loading_outside = ucR_outside; /* 圆弧加载动画 R角表地址(外) */
    ui28_arc_loading_within = ucR_within;   /* 圆弧加载动画 R角表地址(内) */
    ui28_arc_loading_Color_0 = usColor_0;   /* 圆弧加载动画 背景颜色 */
    ui28_arc_loading_Color_1 = usColor_1;   /* 圆弧加载动画 圆弧颜色 */
}
#endif
#endif
