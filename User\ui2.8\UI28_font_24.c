#include "UI28.h"
struct FONT24_ASCII const Font24_Ascii[] =
{
  {
    " ",0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
         0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
         0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,  /* 0 */ 
  }
};

struct FONT24_CHINESE const Font24_Chinese[] =
{
    "关",0x00,0x00,0x00,0x01,0x01,0x80,0x01,0x81,0x80,0x00,0xC3,0x00,0x00,0xC3,0x00,0x00,
    0x87,0x00,0x1F,0xFF,0xF8,0x00,0x18,0x00,0x00,0x18,0x00,0x00,0x18,0x00,0x00,0x18,
    0x00,0x00,0x18,0x00,0x3F,0xFF,0xFC,0x3F,0xFF,0xFC,0x00,0x3C,0x00,0x00,0x3C,0x00,
    0x00,0x66,0x00,0x00,0xE3,0x80,0x01,0xC1,0xE0,0x07,0x80,0xF8,0x3E,0x00,0x3C,0x18,
    0x00,0x08,0x00,0x00,0x00,0x00,0x00,0x00,//0 关

    "前",0x00,0x00,0x80,0x01,0x80,0xC0,0x00,0xC1,0x80,0x00,0x81,0x80,0x7F,0xFF,0xFE,0x7F,
    0xFF,0xFE,0x00,0x00,0x00,0x00,0x00,0x18,0x1F,0xF0,0x18,0x18,0x33,0x18,0x18,0x33,
    0x18,0x18,0x33,0x18,0x1F,0xF3,0x18,0x18,0x33,0x18,0x18,0x33,0x18,0x1F,0xF3,0x18,
    0x1F,0xF3,0x18,0x18,0x33,0x18,0x18,0x30,0x18,0x18,0x30,0x18,0x18,0xF0,0x78,0x18,
    0xF0,0x70,0x18,0x00,0x00,0x00,0x00,0x00,//1 前

    "开",0x00,0x00,0x00,0x00,0x00,0x00,0x1F,0xFF,0xF8,0x00,0xC1,0x80,0x00,0xC1,0x80,0x00,
    0xC1,0x80,0x00,0xC1,0x80,0x00,0xC1,0x80,0x00,0xC1,0x80,0x00,0xC1,0x80,0x7F,0xFF,
    0xFE,0x7F,0xFF,0xFE,0x00,0xC1,0x80,0x00,0xC1,0x80,0x00,0xC1,0x80,0x01,0x81,0x80,
    0x01,0x81,0x80,0x03,0x81,0x80,0x07,0x01,0x80,0x0E,0x01,0x80,0x3C,0x01,0x80,0x18,
    0x01,0x80,0x00,0x00,0x00,0x00,0x00,0x00,//2 开

    "当",0x00,0x00,0x00,0x00,0x18,0x00,0x00,0x18,0x10,0x0C,0x18,0x38,0x0E,0x18,0x30,0x07,
    0x18,0x60,0x03,0x18,0xE0,0x02,0x18,0x40,0x00,0x18,0x00,0x1F,0xFF,0xF8,0x1F,0xFF,
    0xF8,0x00,0x00,0x18,0x00,0x00,0x18,0x00,0x00,0x18,0x1F,0xFF,0xF8,0x0F,0xFF,0xF8,
    0x00,0x00,0x18,0x00,0x00,0x18,0x00,0x00,0x18,0x1F,0xFF,0xF8,0x1F,0xFF,0xF8,0x00,
    0x00,0x18,0x00,0x00,0x18,0x00,0x00,0x00,//3 当

    "总",0x00,0x00,0x00,0x00,0x01,0x00,0x03,0x03,0x80,0x01,0x83,0x00,0x00,0x83,0x00,0x07,
    0xFF,0xE0,0x0F,0xFF,0xE0,0x0C,0x00,0x60,0x0C,0x00,0x60,0x0C,0x00,0x60,0x0C,0x00,
    0x60,0x0F,0xFF,0xE0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x30,0x20,0x19,0x18,0x70,
    0x19,0x0C,0x38,0x31,0x08,0x1C,0x31,0x00,0x8C,0x31,0x80,0xC0,0x21,0xFF,0xC0,0x01,
    0xFF,0x80,0x00,0x00,0x00,0x00,0x00,0x00,//4 总

    "累",0x00,0x00,0x00,0x0F,0xFF,0xF8,0x0F,0xFF,0xF0,0x08,0x18,0x10,0x0C,0x18,0x30,0x0F,
    0xFF,0xF0,0x08,0x18,0x10,0x0C,0x18,0x30,0x0F,0xFF,0xF8,0x00,0xE0,0xC0,0x01,0xC1,
    0xC0,0x0F,0xFF,0x00,0x06,0x3C,0xC0,0x00,0xF0,0xE0,0x03,0xE7,0xF8,0x1F,0xFF,0xDC,
    0x18,0x18,0x00,0x01,0x18,0x80,0x07,0x18,0xF0,0x1E,0x18,0x3C,0x38,0x78,0x18,0x00,
    0x78,0x00,0x00,0x00,0x00,0x00,0x00,0x00,//5 累

    "计",0x00,0x00,0x00,0x08,0x01,0x80,0x1C,0x01,0x80,0x0E,0x01,0x80,0x07,0x01,0x80,0x03,
    0x01,0x80,0x00,0x01,0x80,0x00,0x01,0x80,0x7E,0xFF,0xFE,0x7E,0xFF,0xFE,0x06,0x01,
    0x80,0x06,0x01,0x80,0x06,0x01,0x80,0x06,0x01,0x80,0x06,0x01,0x80,0x06,0x41,0x80,
    0x06,0xC1,0x80,0x07,0x81,0x80,0x07,0x01,0x80,0x0E,0x01,0x80,0x04,0x01,0x80,0x00,
    0x01,0x80,0x00,0x00,0x00,0x00,0x00,0x00,//6 计

    "门",0x04,0x00,0x00,0x0E,0x00,0x00,0x06,0x3F,0xFC,0x07,0x3F,0xFC,0x02,0x00,0x0C,0x00,
    0x00,0x0C,0x30,0x00,0x0C,0x30,0x00,0x0C,0x30,0x00,0x0C,0x30,0x00,0x0C,0x30,0x00,
    0x0C,0x30,0x00,0x0C,0x30,0x00,0x0C,0x30,0x00,0x0C,0x30,0x00,0x0C,0x30,0x00,0x0C,
    0x30,0x00,0x0C,0x30,0x00,0x0C,0x30,0x00,0x0C,0x30,0x00,0x1C,0x30,0x00,0x78,0x30,
    0x00,0x70,0x00,0x00,0x00,0x00,0x00,0x00,//7 门
};

UI28_MATRIX_XX_QUAN_LIST(24)

